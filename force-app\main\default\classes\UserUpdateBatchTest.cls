@isTest
private class UserUpdateBatchTest {
    static testMethod void testMethod1() {
        Profile p = [select Id from Profile where id =: System.Label.ProfileId_SystemAdmin];
        User u1 = new User(Test_staff__c = true);
        u1.LastName = '_サンブリッジ';
        u1.FirstName = 'あ';
        u1.Alias = 'あ';
        u1.Email = '<EMAIL>';
        u1.Username = '<EMAIL>';
        u1.CommunityNickname = 'あ';
        u1.IsActive = true;
        u1.EmailEncodingKey = 'ISO-2022-JP';
        u1.TimeZoneSidKey = 'Asia/Tokyo';
        u1.LocaleSidKey = 'ja_JP';
        u1.LanguageLocaleKey = 'ja';
        u1.ProfileId = p.Id;
        u1.Job_Category__c = '销售服务';
        u1.Province__c = '東京';
        u1.Stay_or_not__c = '在职';
        u1.PBI_Enable__c = true;
        u1.QuitDate__c = Date.today().addDays(-10);
        u1.SendToComPlat__c = false;

        System.runAs(new User(Id = Userinfo.getUserId())) {
            System.Test.startTest();
            insert u1;
            Database.executeBatch(new UserUpdateBatch(), 100);
            System.Test.stopTest();
        }

    }
}