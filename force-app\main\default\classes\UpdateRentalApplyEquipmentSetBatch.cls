global class UpdateRentalApplyEquipmentSetBatch implements Database.Batchable<sObject>, Database.Stateful {

    private static final Integer MAXERRORCNT = 20; // 邮件表单位最大错误信息显示数量
    private Date tdy = Date.today();
    private Date timeLine = Date.newInstance(2019, 7, 1);

    private ErrorBean eb = new ErrorBean(); // 邮件发送ERRORBEAN
    // private UpdateRentalApplyBatch.ScBean scB;
    private String scErrorMessage = ''; // 添加计划错误信息

    /**
     * コンスタント
     */
    global UpdateRentalApplyEquipmentSetBatch() {
        Integer i = 0;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
        i ++;
    }

    /**
     * startには、queryを実行、备品申请借出历史を検索
     */
    global Database.QueryLocator start(Database.BatchableContext BC) {
        // 20240306 ljh 断舍离 注释了
        /*scB = UpdateRentalApplyBatch.setSc('UpdateRentalApplyEquipmentSetSchedule', 9, 23, 0, '0 30 2', null);
        if (System.Test.isRunningTest() == false) {
            Boolean haveError = false;
            try {
                system.schedule(scB.scName, scB.scTime, new UpdateRentalApplyEquipmentSetSchedule());
            }
            catch(Exception e) {
                haveError = true;
            }
            // 如果哟同名的Batch计划删除已存在的计划重新设置
            if (haveError) {
                haveError = false;
                for(CronTrigger ct : [SELECT Id,CronJobDetailId,CronExpression,CronJobDetail.Name
                                        FROM CronTrigger
                                       WHERE CronJobDetail.Name = :scB.scName]){
                    System.abortJob(ct.Id);
                }
                try {
                    system.schedule(scB.scName, scB.scTime, new UpdateRentalApplyEquipmentSetSchedule());
                }
                catch(Exception e) {
                    scErrorMessage = e.getMessage() + e.getStackTraceString() + '<br/>';
                }
            }
        }*/

        return Database.getQueryLocator(
            [select Id from User where Id = :UserInfo.getUserId()]
        );
    }

    global void execute(Database.BatchableContext BC, List<SObject> userList) {
        Map<Id, Rental_Apply_Equipment_Set__c> needUpdateMap = new Map<Id, Rental_Apply_Equipment_Set__c>();
        // 获取需要更新Text字段的备品借出一览
        List<Rental_Apply_Equipment_Set__c> raesList = getRaes(null);
        Set<Id> raesIdSet = new Set<Id>();
        for (Rental_Apply_Equipment_Set__c raes: raesList) {
            Rental_Apply_Equipment_Set__c raes1 = raes;
            if (needUpdateMap.containsKey(raes1.Id)) {
                raes1 = needUpdateMap.get(raes1.Id);
            }
            Rental_Apply_Equipment_Set__c upd = UpdateRentalApplyEquipmentSetBatch.setRAES(raes1, raesIdSet);
            if (upd != null) {
                needUpdateMap.put(upd.Id, upd);
            }
        }

        // 上面的逻辑是对应备品借出一览公式字段和Text的更新（既存逻辑）


        // 以下逻辑是更新备品借出申请一览的工作日的逻辑
        // 获取需要更新工作日的备品借出一览
        raesList = [SELECT Id
                      , Bollow_Date__c
                   FROM Rental_Apply_Equipment_Set__c
                  WHERE Rental_Apply__r.Request_demo_day__c >= :timeLine
                    AND Bollow_Date__c != null
                    AND (Bollow_Date_Add_7_WD__c = null OR Bollow_Date_Add_10_WD__c = null)
                  order by Id Limit :Integer.valueOf(System.Label.UpdateRentalApplyEquipmentSetBatchLimit)];
        System.debug('raesList.size()' + raesList.size());
        if (raesList.size() > 0) {
            Date minDate = Date.newInstance(4000, 12, 31);
            Date maxDate = Date.newInstance(1700, 1, 1);
            for (Rental_Apply_Equipment_Set__c raesd : raesList) {
                if (minDate > raesd.Bollow_Date__c) {
                    minDate = raesd.Bollow_Date__c;
                }
                if (maxDate < raesd.Bollow_Date__c) {
                    maxDate = raesd.Bollow_Date__c;
                }
            }
            List<OlympusCalendar__c> ocList = [SELECT Id
                        , Date__c
                     FROM OlympusCalendar__c
                    WHERE Date__c >= :minDate
                      AND Date__c <= :maxDate.addDays(30)
                      AND IsWorkDay__c = 1
                    ORDER BY Date__c ASC];
            Map<Date, Date> dateadd7Map = new Map<Date, Date>();
            Map<Date, Date> dateadd10Map = new Map<Date, Date>();
            Integer ocListSize = ocList.size();
            if (ocListSize > 7) {
                for (Integer i = 0; i < ocListSize - 7; i ++) {
                    dateadd7Map.put(ocList[i].Date__c, ocList[i + 7].Date__c);
                    if(i + 10 < ocListSize) {
                        dateadd10Map.put(ocList[i].Date__c, ocList[i + 10].Date__c);
                    }
                }
                List<Rental_Apply_Equipment_Set__c> hDayRaesList = new List<Rental_Apply_Equipment_Set__c>();
                for (Rental_Apply_Equipment_Set__c raesd : raesList) {
                    if (dateadd7Map.containsKey(raesd.Bollow_Date__c)) {
                        Rental_Apply_Equipment_Set__c raes1 = raesd;
                        // 如果工作日和Text字段都需要更新。需要更新在一个实例里面，不然会覆盖
                        if (needUpdateMap.containsKey(raes1.Id)) {
                            raes1 = needUpdateMap.get(raes1.Id);
                        }
                        else {
                            needUpdateMap.put(raes1.Id, raes1);
                        }
                        raes1.Bollow_Date_Add_7_WD__c = dateadd7Map.get(raesd.Bollow_Date__c);
                        if(dateadd10Map.containsKey(raes1.Bollow_Date__c)) {
                            raes1.Bollow_Date_Add_10_WD__c = dateadd10Map.get(raes1.Bollow_Date__c);
                        }
                    }
                    else {
                        hDayRaesList.add(raesd);
                    }
                }
                if (hDayRaesList.size() > 0) {
                    for (Integer i = 0; i < hDayRaesList.size(); i ++) {
                        for (Integer j = 0; j < ocListSize; j ++) {
                            if (j != ocListSize - 1
                                    && (ocList[j].Date__c <  hDayRaesList[i].Bollow_Date__c
                                          || (j == 0 && ocList[j].Date__c >  hDayRaesList[i].Bollow_Date__c)
                                      )
                                    && ocList[j + 1].Date__c > hDayRaesList[i].Bollow_Date__c) {
                                Rental_Apply_Equipment_Set__c raes1 = hDayRaesList[i];
                                if (needUpdateMap.containsKey(raes1.Id)) {
                                    raes1 = needUpdateMap.get(raes1.Id);
                                }
                                else {
                                    needUpdateMap.put(raes1.Id, raes1);
                                }
                                raes1.Bollow_Date_Add_7_WD__c = dateadd7Map.get(ocList[j].Date__c);
                                if(dateadd10Map.containsKey(ocList[j].Date__c)) {
                                    raes1.Bollow_Date_Add_10_WD__c = dateadd10Map.get(ocList[j].Date__c);
                                }
                                break;
                            }
                        }
                    }
                }
                System.debug('raesList.size()' + raesList);
            }
        }

        if (!needUpdateMap.isEmpty()) {
            Database.SaveResult[] saveRes = Database.update(needUpdateMap.values(), false);
            // 备品借出一览更新保存Error
            eb.setError(saveRes, MAXERRORCNT, Rental_Apply_Equipment_Set__c.sObjectType);
        }

        if (raesIdSet.size() > 0) {
            raesList = getRaes(raesIdSet);
            needUpdateMap = new Map<Id, Rental_Apply_Equipment_Set__c>();
            for (Rental_Apply_Equipment_Set__c raes : raesList) {
                Rental_Apply_Equipment_Set__c raes1 = raes;
                Rental_Apply_Equipment_Set__c upd = UpdateRentalApplyEquipmentSetBatch.setRAES(raes1, raesIdSet);
                if (upd != null) {
                    needUpdateMap.put(upd.Id, upd);
                }
            }
            if (!needUpdateMap.isEmpty()) {
                Database.SaveResult[] saveRes = Database.update(needUpdateMap.values(), false);
                // 备品借出一览更新保存Error
                eb.setError(saveRes, MAXERRORCNT, Rental_Apply_Equipment_Set__c.sObjectType);
            }
        }

        // xiongyl--start
        // 因为犯规点数有集计SetOrderPoint_sum_Text__c字段。所以需要再SetOrderPoint_sum_Text__c更新后再计算犯规点数
        List<AggregateResult> points = [select SUM(RequestOrderPoint_sum__c) fp, OwnerId userId
                from Rental_Apply__c where RequestOrderPoint_sum__c > 0 group by OwnerId];
        Map<String, Integer> pointMap = new Map<String,integer>();
        for(AggregateResult point : points){
            pointMap.put(String.valueOf(point.get('userId')), Integer.valueOf(point.get('fp')));
        }
        /* 20220615 ljh Id != 解决 
        ** 邮件报错 User:用户:INSUFFICIENT_ACCESS_OR_READONLY:():SalesforceIQIntegration User cannot be edited
        */
        List<User> users = [select Id, Foul_Points__c from User where Id !=:System.label.rentalApply_user1 
                            AND Id !=:System.Label.rentalApply_user2 
                            AND Id !=:System.label.rentalApply_user3
                            AND Id !=:System.Label.rentalApply_user4
                            AND Id !=:System.label.rentalApply_user5
                            ];
        //niwu  005100000016ykkAAA -00510000007iWvrAAE -0051000000D9fP3AAJ -0051000000DZdlJAAT - 0051000000DbxVkAAJ
        List<User> updList = new List<User>();
        for (User usr : users) {
            if (usr.Foul_Points__c <> 0 || pointMap.containsKey(usr.id) == true) {
                usr.Foul_Points__c = pointMap.get(usr.id) == null ? 0 : pointMap.get(usr.id);
                updList.add(usr);
            }
        }
        if (updList.size() > 0) {
            Database.SaveResult[] saveRes = Database.update(updList, false);
            // USER更新Error
            eb.setError(saveRes, MAXERRORCNT, User.sObjectType);
        }
        // xiongyl--end
//bp2
//        List<Equipment_Set_Detail__c> esdList =
//            [select Id,Asset_condition__c,Asset_condition_Text__c,
//                    Serial_Lot__c,Serial_Lot_text__c,
//                    Asset__r.Loaner_accsessary__c, Loaner_accsessary_text__c,
//                    Active_judgement_select__c,Active_judgement_text__c,
//                    Last_Reserve_RAES_Detail_RAES_F__c, Last_Reserve_RAES_Detail_RAES_Id__c,
//                    Equipment_Set_Last_Reserve_RAES_F__c, Equipment_Set_Last_Reserve_RAES_Id__c
//               from Equipment_Set_Detail__c
//              where Asset_condition_Changed__c = true
//              order by Id limit 4000];

//        List<Equipment_Set_Detail__c> updateList2 = UpdateRentalApplyEquipmentSetBatch.setESD(esdList);
//        if (!updateList2.isEmpty()) update updateList2;
    }

    public static List<Rental_Apply_Equipment_Set__c> getRaes(Set<Id> idSet) {
        String soql = 'SELECT Id'
                         + ', Repair_Status1__c'
                         + ', Repair_Status2__c'
                         + ', Repair_Status_Text__c'
                         + ', Final_reply_day__c'
                         + ', Final_reply_day_text__c'
                         + ', Received_Confirm_NG_Not_Return__c'
                         + ', Received_Confirm_NG_Not_Return_Text__c'
                         + ', Received_Confirm_Status_F__c'
                         + ', Received_Confirm_Status_Text__c'
                         + ', NG_Final_reply_day_Text__c'
                         + ', NG_Final_reply_day_F__c'
                         + ', Yizhouweixiu_Final_reply_day_Text__c'
                         + ', Yizhouweixiu_Final_reply_day_F__c'
                         + ', Extend_Final_reply_day_Text__c'
                         + ', Extend_Final_reply_day_F__c'
                         + ', QIS_Final_reply_day_Text__c'
                         + ', QIS_Final_reply_day_F__c'
                         + ', Repair_cancel_Final_reply_day_Text__c'
                         + ', Repair_cancel_Final_reply_day_F__c'
                         + ', Return_to_office_Final_reply_day_Text__c'
                         + ', Return_to_office_Final_reply_day_F__c'
                         + ', Repair_delete_Final_reply_day_Text__c'
                         + ', Repair_delete_Final_reply_day_F__c'
                         + ', Yigoudaihuo_Final_reply_day_Text__c'
                         + ', Yigoudaihuo_Final_reply_day_F__c'
                         + ', Guzhangpaicha_Final_reply_day_Text__c'
                         + ', Guzhangpaicha_Final_reply_day_F__c'
                         + ', Repair_Agreed_Quotation_F__c'
                         + ', Repair_Agreed_Quotation_Text__c'
                         + ', Return_to_office_Final_reply_day_U_RC__c'
                         + ', Return_to_office_Final_reply_day_U_RC_F__c'
                         + ', Extend_Date__c'
                         + ', Extend_Date_F__c'
                         + ', Received_NG_ReAssign_Text__c'
                         + ', Received_NG_ReAssign__c'
                //【FY23大及巨大课题】长假备品借用延期开发 2022/12/27 start xxf
                         + ', Final_reply_day_Holiday_backup__c'
                         + ', NG_Final_reply_day_F_Holiday_backup__c'
                         + ', NG_Final_reply_day_Text_Holiday_backup__c'
                         + ', Yizhouweixiu_Final_reply_day_F_Holiday__c'
                         + ', Yizhouweixiu_Final_reply_day_TextHoliday__c'
                         + ', Extend_Final_reply_day_F_Holiday_backup__c'
                         + ', Extend_Final_reply_day_Text_Holiday_back__c'
                         + ', QIS_Final_reply_day_F_Holiday_backup__c'
                         + ', QIS_Final_reply_day_Text_Holiday_backup__c'
                         + ', Repair_cancel_Final_reply_day_F_Holiday__c'
                         + ', Repair_cancel_Final_reply_day_Text_Holid__c'
                         + ', Return_to_office_Final_reply_day_F_Ho__c'
                         + ', Return_to_office_Final_reply_day_Text_Ho__c'
                         + ', Repair_delete_Final_reply_day_F_Holiday__c'
                         + ', Repair_delete_Final_reply_day_Text_Ho__c'
                         + ', Yigoudaihuo_Final_reply_day_F_Holiday__c'
                         + ', Yigoudaihuo_Final_reply_day_Text_Holiday__c'
                         + ', FGuzhangpaicha_Final_reply_day_F_Holiday__c'
                         + ', Guzhangpaicha_Final_reply_day_Text_Holid__c'
                         + ', Return_to_office_Final_reply_day_U_RC_Ho__c'
                         + ', Return_to_office_Final_reply_day_U_RC_FH__c'
                //【FY23大及巨大课题】长假备品借用延期开发 2022/12/27 end xxf
                +' FROM Rental_Apply_Equipment_Set__c'
                +' WHERE ' + (idSet == null ? '' : 'Id = :idSet AND (')
                        + '('
                        +'  ('
                            +    '(Rental_Apply__r.Repair__c = null and Repair_Status_Text__c <> \'\')'
                            +' OR (Rental_Apply__r.Repair__c <> null and Repair_Status_Text__c = \'\')'
                            +' OR (Rental_Apply__r.Repair__c <> null and Repair_Status_Changed__c = \'1\')' // 用来判断Repair_Status_Text__c 字段有变更的变更字段
                            +' OR Final_reply_day_Changed__c = \'1\'' // 用来判断Final_reply_day_text__c 字段有变更的变更字段
                        +' )'
                        +' AND Cancel_Select__c = False'
                        +' )'
                        +' OR ForBatch_Changed_F__c = true' // 用来判断Received_Confirm_NG_Not_Return_Text__c
                                                         //        Received_Confirm_Status_Text__c
                                                         //        SetOrderPoint_sum_Text__c有变更的字段
                        +' OR ForBatch_Changed1_F__c = true' // Text(NG_Final_reply_day_Text__c) != Text(NG_Final_reply_day_F__c)
                                                          // || Text(Yizhouweixiu_Final_reply_day_Text__c) != Text(Yizhouweixiu_Final_reply_day_F__c)
                        +' OR ForBatch_Changed2_F__c = true '// Text(Repair_cancel_Final_reply_day_Text__c) != Text(Repair_cancel_Final_reply_day_F__c)
                                                          // || Text(Return_to_office_Final_reply_day_Text__c) != Text(Return_to_office_Final_reply_day_F__c)
                        +' OR ForBatch_Changed3_F__c = true '// Text(QIS_Final_reply_day_Text__c) != Text(QIS_Final_reply_day_F__c)
                        +' OR ForBatch_Changed4_F__c = true '// Text(Repair_delete_Final_reply_day_Text__c) != Text(Repair_delete_Final_reply_day_F__c)
                                                          // || Text(Yigoudaihuo_Final_reply_day_Text__c) != Text(Yigoudaihuo_Final_reply_day_F__c)
                        +' OR ForBatch_Changed5_F__c = true '//  Text(Extend_Final_reply_day_Text__c) != Text(Extend_Final_reply_day_F__c)
                        + (idSet == null ? '' : ')')
                +' order by Id Limit ' + System.Label.UpdateRentalApplyEquipmentSetBatchLimit;
        System.debug(soql);
        return Database.query(soql);
    }

    global void finish(Database.BatchableContext BC) {
        Boolean haveError = false;
        String body = scErrorMessage;
        // UpdateRentalApplyBatch.removeOtherSc('UpdateRentalApplyEquipmentSetSchedule', scB.scName); // 20240306 ljh 断舍离 
        // 20240306 ljh 优化apex计划 start
        Id execBTId = Database.executeBatch(new UpdateRentalApplyBatch(), 200);
        // 20240306 ljh 优化apex计划 end
        for (Id objId : eb.messageMap.keySet()) {
            haveError = true;
            // 20240306 ljh 顺手提示信息优化了 start
            // body += eb.messageMap.get(objId) + '<br/>';
            body += 'objId:'+objId+'~'+eb.messageMap.get(objId) + '<br/>';
            // 20240306 ljh 顺手提示信息优化了 end
        }
        if (eb.overMax) {
            body += ':Over ' + MAXERRORCNT + 'Record<br/>';
        }
        if (haveError == true || String.isNotBlank(scErrorMessage)) {
            // BatchユーザId
            String batchUserId = System.Label.Batch_User_Id;
            List<User> us = [Select Id,NAme,Email From User Where Id =: batchUserId];
            if (!us.isEmpty()) {
                User use = us[0];
                if (String.isNotBlank(use.Email)) {
                    List<String> MailCc;
                    if (System.Label.Batch_Error_Send_To_CC != 'null') {
                        MailCc = System.Label.Batch_Error_Send_To_CC.split(',');
                    }
                    if (System.Test.isRunningTest() == false) {
                        FixtureUtil.sendMessage(batchUserId,
                                                MailCc,
                                                'UpdateRentalApplyEquipmentSetBatch Error',
                                                 body
                                                );
                    }
                }
            }
        }
        
    }

    // RentalApplyWebService 里有在使用setRAES方法
    public static Rental_Apply_Equipment_Set__c setRAES(Rental_Apply_Equipment_Set__c obj) {
        return setRAES(obj, null);
    }

    public static Rental_Apply_Equipment_Set__c setRAES(Rental_Apply_Equipment_Set__c obj, Set<Id> raesIdSet) {
        Rental_Apply_Equipment_Set__c raes = new Rental_Apply_Equipment_Set__c(id = obj.Id);
        Boolean hasChange = false;

        /************************************ 第一次更新字段（需要第二次更新的字段） Start ******************************************/
        // Return_to_office_Final_reply_day_F__c有使用Repair_Agreed_Quotation_Text__c字段
        if (equalNullCheck(obj.Repair_Agreed_Quotation_Text__c, obj.Repair_Agreed_Quotation_F__c) || obj.Repair_Agreed_Quotation_Text__c != obj.Repair_Agreed_Quotation_F__c) {
            raes.Repair_Agreed_Quotation_Text__c = obj.Repair_Agreed_Quotation_F__c;
            hasChange = true;
            if (raesIdSet != null) {
                raesIdSet.add(raes.Id);
            }
        }
        // 字段Return_to_office_Final_reply_day_F__c有使用Return_to_office_Final_reply_day_U_RC__c字段
        if (equalNullCheck(obj.Return_to_office_Final_reply_day_U_RC__c, obj.Return_to_office_Final_reply_day_U_RC_F__c) || obj.Return_to_office_Final_reply_day_U_RC__c != obj.Return_to_office_Final_reply_day_U_RC_F__c) {
            raes.Return_to_office_Final_reply_day_U_RC__c = obj.Return_to_office_Final_reply_day_U_RC_F__c;
            //【FY23大及巨大课题】长假备品借用延期开发 2022/12/27 start xxf
            raes.Return_to_office_Final_reply_day_U_RC_Ho__c = obj.Return_to_office_Final_reply_day_U_RC_FH__c;
            //【FY23大及巨大课题】长假备品借用延期开发 2022/12/27 end xxf
            hasChange = true;
            if (raesIdSet != null) {
                raesIdSet.add(raes.Id);
            }
        }

        /************************************ 第一次更新字段（需要第二次更新的字段） End ******************************************/

        /************************************ 第二次更新字段 OR 只更新一次就可以的字段 Start ******************************************/

        if (equalNullCheck(obj.Repair_Status_Text__c, obj.Repair_Status1__c) || obj.Repair_Status_Text__c != obj.Repair_Status1__c) {
            raes.Repair_Status_Text__c = obj.Repair_Status1__c;
            // DB202309405058 长假备品借用延期修复 2023/09/19 Start
            if (obj.Repair_Status1__c == '0.删除' || obj.Repair_Status1__c == '0.取消') {
                raes.Condition_X21_Days__c = 0;
                raes.Condition_Seven_Days__c = 0;
                raes.Condition_Final_Reply_Days__c = 0;
                raes.Delay_Final_Reply_Days__c = 0;
            }
            // DB202309405058 长假备品借用延期修复 2023/09/19 End
            hasChange = true;
        }
        if (equalNullCheck(obj.Final_reply_day_text__c, obj.Final_reply_day__c) || obj.Final_reply_day_text__c != obj.Final_reply_day__c) {
            raes.Final_reply_day_text__c = obj.Final_reply_day__c;
            hasChange = true;
        }
        if (equalNullCheck(obj.Received_Confirm_NG_Not_Return_Text__c, obj.Received_Confirm_NG_Not_Return__c) || obj.Received_Confirm_NG_Not_Return_Text__c != obj.Received_Confirm_NG_Not_Return__c) {
            raes.Received_Confirm_NG_Not_Return_Text__c = obj.Received_Confirm_NG_Not_Return__c;
            hasChange = true;
        }
        if (equalNullCheck(obj.Received_Confirm_Status_Text__c, obj.Received_Confirm_Status_F__c) || obj.Received_Confirm_Status_Text__c != obj.Received_Confirm_Status_F__c) {
            raes.Received_Confirm_Status_Text__c = obj.Received_Confirm_Status_F__c;
            hasChange = true;
        }
        if (equalNullCheck(obj.QIS_Final_reply_day_Text__c, obj.QIS_Final_reply_day_F__c) || obj.QIS_Final_reply_day_Text__c != obj.QIS_Final_reply_day_F__c) {
            raes.QIS_Final_reply_day_Text__c = obj.QIS_Final_reply_day_F__c;
            hasChange = true;
        }
        /******************************* Min_Final_reply_day_F__c使用字段 Start Min_Final_reply_day_Text__c 的更新在Handler里 **************************************/
        // Min_Final_reply_day_F__c有使用Yizhouweixiu_Final_reply_day_Text__c字段
        if (equalNullCheck(obj.Yizhouweixiu_Final_reply_day_Text__c, obj.Yizhouweixiu_Final_reply_day_F__c) || obj.Yizhouweixiu_Final_reply_day_Text__c != obj.Yizhouweixiu_Final_reply_day_F__c) {
            raes.Yizhouweixiu_Final_reply_day_Text__c = obj.Yizhouweixiu_Final_reply_day_F__c;
            hasChange = true;
        }
        // Min_Final_reply_day_F__c有使用Extend_Final_reply_day_Text__c字段
        if (equalNullCheck(obj.Extend_Final_reply_day_Text__c, obj.Extend_Final_reply_day_F__c) || obj.Extend_Final_reply_day_Text__c != obj.Extend_Final_reply_day_F__c) {
            raes.Extend_Final_reply_day_Text__c = obj.Extend_Final_reply_day_F__c;
            hasChange = true;
        }
        // Min_Final_reply_day_F__c有使用NG_Final_reply_day_F__c字段
        if (equalNullCheck(obj.NG_Final_reply_day_Text__c, obj.NG_Final_reply_day_F__c) || obj.NG_Final_reply_day_Text__c != obj.NG_Final_reply_day_F__c) {
            raes.NG_Final_reply_day_Text__c = obj.NG_Final_reply_day_F__c;
            hasChange = true;
        }
        // Min_Final_reply_day_F__c有使用Repair_cancel_Final_reply_day_Text__c字段
        if (equalNullCheck(obj.Repair_cancel_Final_reply_day_Text__c, obj.Repair_cancel_Final_reply_day_F__c) || obj.Repair_cancel_Final_reply_day_Text__c != obj.Repair_cancel_Final_reply_day_F__c) {
            raes.Repair_cancel_Final_reply_day_Text__c = obj.Repair_cancel_Final_reply_day_F__c;
            hasChange = true;
        }
        // Min_Final_reply_day_F__c有使用Return_to_office_Final_reply_day_Text__c字段
        if (equalNullCheck(obj.Return_to_office_Final_reply_day_Text__c, obj.Return_to_office_Final_reply_day_F__c) || obj.Return_to_office_Final_reply_day_Text__c != obj.Return_to_office_Final_reply_day_F__c) {
            raes.Return_to_office_Final_reply_day_Text__c = obj.Return_to_office_Final_reply_day_F__c;
            hasChange = true;
        }
        // Min_Final_reply_day_F__c有使用Repair_delete_Final_reply_day_Text__c字段
        if (equalNullCheck(obj.Repair_delete_Final_reply_day_Text__c, obj.Repair_delete_Final_reply_day_F__c) || obj.Repair_delete_Final_reply_day_Text__c != obj.Repair_delete_Final_reply_day_F__c) {
            raes.Repair_delete_Final_reply_day_Text__c = obj.Repair_delete_Final_reply_day_F__c;
            hasChange = true;
        }
        // Min_Final_reply_day_F__c有使用Yigoudaihuo_Final_reply_day_Text__c字段
        if (equalNullCheck(obj.Yigoudaihuo_Final_reply_day_Text__c, obj.Yigoudaihuo_Final_reply_day_F__c) || obj.Yigoudaihuo_Final_reply_day_Text__c != obj.Yigoudaihuo_Final_reply_day_F__c) {
            raes.Yigoudaihuo_Final_reply_day_Text__c = obj.Yigoudaihuo_Final_reply_day_F__c;
            hasChange = true;
        }
        // Min_Final_reply_day_F__c有使用Guzhangpaicha_Final_reply_day_Text__c字段
        if (equalNullCheck(obj.Guzhangpaicha_Final_reply_day_Text__c, obj.Guzhangpaicha_Final_reply_day_F__c) || obj.Guzhangpaicha_Final_reply_day_Text__c != obj.Guzhangpaicha_Final_reply_day_F__c) {
            raes.Guzhangpaicha_Final_reply_day_Text__c = obj.Guzhangpaicha_Final_reply_day_F__c;
            hasChange = true;
        }
        if (equalNullCheck(obj.Extend_Date__c, obj.Extend_Date_F__c) || obj.Extend_Date__c != obj.Extend_Date_F__c) {
            raes.Extend_Date__c = obj.Extend_Date_F__c;
            hasChange = true;
        }
        if (equalNullCheck(obj.Received_NG_ReAssign_Text__c, obj.Received_NG_ReAssign__c) || obj.Received_NG_ReAssign_Text__c != obj.Received_NG_ReAssign__c) {
            raes.Received_NG_ReAssign_Text__c = obj.Received_NG_ReAssign__c;
            hasChange = true;
        }
        /******************************* Min_Final_reply_day_F__c使用字段 End **************************************/

        /************************************ 第二次更新字段 OR 只更新一次就可以的字段 End ******************************************/

        //【FY23大及巨大课题】长假备品借用延期开发 2022/12/27 start xxf
        if (equalNullCheck(obj.QIS_Final_reply_day_Text_Holiday_backup__c, obj.QIS_Final_reply_day_F_Holiday_backup__c) || obj.QIS_Final_reply_day_Text_Holiday_backup__c != obj.QIS_Final_reply_day_F_Holiday_backup__c) {
            raes.QIS_Final_reply_day_Text_Holiday_backup__c = obj.QIS_Final_reply_day_F_Holiday_backup__c;
            hasChange = true;
        }
        if (equalNullCheck(obj.Yizhouweixiu_Final_reply_day_TextHoliday__c, obj.Yizhouweixiu_Final_reply_day_F_Holiday__c) || obj.Yizhouweixiu_Final_reply_day_TextHoliday__c != obj.Yizhouweixiu_Final_reply_day_F_Holiday__c) {
            raes.Yizhouweixiu_Final_reply_day_TextHoliday__c = obj.Yizhouweixiu_Final_reply_day_F_Holiday__c;
            hasChange = true;
        }
        if (equalNullCheck(obj.Extend_Final_reply_day_Text_Holiday_back__c, obj.Extend_Final_reply_day_F_Holiday_backup__c) || obj.Extend_Final_reply_day_Text_Holiday_back__c != obj.Extend_Final_reply_day_F_Holiday_backup__c) {
            raes.Extend_Final_reply_day_Text_Holiday_back__c = obj.Extend_Final_reply_day_F_Holiday_backup__c;
            hasChange = true;
        }
        if (equalNullCheck(obj.NG_Final_reply_day_Text_Holiday_backup__c, obj.NG_Final_reply_day_F_Holiday_backup__c) || obj.NG_Final_reply_day_Text_Holiday_backup__c != obj.NG_Final_reply_day_F_Holiday_backup__c) {
            raes.NG_Final_reply_day_Text_Holiday_backup__c = obj.NG_Final_reply_day_F_Holiday_backup__c;
            hasChange = true;
        }
        if (equalNullCheck(obj.Repair_cancel_Final_reply_day_Text_Holid__c, obj.Repair_cancel_Final_reply_day_F_Holiday__c) || obj.Repair_cancel_Final_reply_day_Text_Holid__c != obj.Repair_cancel_Final_reply_day_F_Holiday__c) {
            raes.Repair_cancel_Final_reply_day_Text_Holid__c = obj.Repair_cancel_Final_reply_day_F_Holiday__c;
            hasChange = true;
        }
        if (equalNullCheck(obj.Return_to_office_Final_reply_day_Text_Ho__c, obj.Return_to_office_Final_reply_day_F_Ho__c) || obj.Return_to_office_Final_reply_day_Text_Ho__c != obj.Return_to_office_Final_reply_day_F_Ho__c) {
            raes.Return_to_office_Final_reply_day_Text_Ho__c = obj.Return_to_office_Final_reply_day_F_Ho__c;
            hasChange = true;
        }
        if (equalNullCheck(obj.Repair_delete_Final_reply_day_Text_Ho__c, obj.Repair_delete_Final_reply_day_F_Holiday__c) || obj.Repair_delete_Final_reply_day_Text_Ho__c != obj.Repair_delete_Final_reply_day_F_Holiday__c) {
            raes.Repair_delete_Final_reply_day_Text_Ho__c = obj.Repair_delete_Final_reply_day_F_Holiday__c;
            hasChange = true;
        }
        if (equalNullCheck(obj.Yigoudaihuo_Final_reply_day_Text_Holiday__c, obj.Yigoudaihuo_Final_reply_day_F_Holiday__c) || obj.Yigoudaihuo_Final_reply_day_Text_Holiday__c != obj.Yigoudaihuo_Final_reply_day_F_Holiday__c) {
            raes.Yigoudaihuo_Final_reply_day_Text_Holiday__c = obj.Yigoudaihuo_Final_reply_day_F_Holiday__c;
            hasChange = true;
        }
        if (equalNullCheck(obj.Guzhangpaicha_Final_reply_day_Text_Holid__c, obj.FGuzhangpaicha_Final_reply_day_F_Holiday__c) || obj.Guzhangpaicha_Final_reply_day_Text_Holid__c != obj.FGuzhangpaicha_Final_reply_day_F_Holiday__c) {
            raes.Guzhangpaicha_Final_reply_day_Text_Holid__c = obj.FGuzhangpaicha_Final_reply_day_F_Holiday__c;
            hasChange = true;
        }
        //【FY23大及巨大课题】长假备品借用延期开发 2022/12/27 end xxf

        if (hasChange) {
            return raes;
        } else {
            return null;
        }
        //return null; //; zzm 20250613 api版本升级
    }

    private static Boolean equalNullCheck(Object obj, Object obj1) {
        return obj == null && obj1 != null || obj1 == null && obj != null;
    }

//bp2
//    public static List<Equipment_Set_Detail__c> setESD(List<Equipment_Set_Detail__c> esdList) {
//        List<Equipment_Set_Detail__c> updateList = new List<Equipment_Set_Detail__c>();
//        for (SObject obj: esdList) {
//            Boolean needUpdate = false;
//            Equipment_Set_Detail__c esd = (Equipment_Set_Detail__c) obj;
//            if (esd.Asset_condition__c != esd.Asset_condition_Text__c) {
//                esd.Asset_condition_Text__c = esd.Asset_condition__c;
//                needUpdate = true;
//            }
//            if (esd.Serial_Lot__c != esd.Serial_Lot_text__c) {
//                esd.Serial_Lot_text__c = esd.Serial_Lot__c;
//                needUpdate = true;
//            }
//            if (esd.Asset__r.Loaner_accsessary__c != esd.Loaner_accsessary_text__c) {
//                esd.Loaner_accsessary_text__c = esd.Asset__r.Loaner_accsessary__c;
//                needUpdate = true;
//            }
//            if (esd.Active_judgement_select__c != esd.Active_judgement_text__c) {
//                esd.Active_judgement_text__c = esd.Active_judgement_select__c;
//                needUpdate = true;
//            }
//            if (esd.Last_Reserve_RAES_Detail_RAES_F__c != esd.Last_Reserve_RAES_Detail_RAES_Id__c) {
//                esd.Last_Reserve_RAES_Detail_RAES_Id__c = esd.Last_Reserve_RAES_Detail_RAES_F__c;
//                needUpdate = true;
//            }
//            if (esd.Equipment_Set_Last_Reserve_RAES_F__c != esd.Equipment_Set_Last_Reserve_RAES_Id__c) {
//                esd.Equipment_Set_Last_Reserve_RAES_Id__c = esd.Equipment_Set_Last_Reserve_RAES_F__c;
//                needUpdate = true;
//            }
//            if (needUpdate) updateList.add(esd);
//        }
//        return updateList;
//    }
/************************* Inner Class ******************************/
   public class ErrorBean{
        // public String objectName;
        // public String objectLabel;
        public Map<Id, String> messageMap;
        public Boolean overMax;
        // public ErrorBean(Schema.sObjectType obj) {
        //     objectName = obj.getDescribe().getName();
        //     objectLabel = obj.getDescribe().getLabel();
        //     messageMap = new Map<Id, String>();
        //     overMax = false;
        // }
        public ErrorBean() {
          messageMap = new Map<Id, String>();
          overMax = false;
        }
        public void setError (Database.SaveResult[] saveRes, Integer maxCut, Schema.sObjectType obj) {
            if (messageMap.keySet().size() <= maxCut && overMax == false) {
                String objectName = obj.getDescribe().getName();
                String objectLabel = obj.getDescribe().getLabel();
                for (Database.SaveResult saveRe : saveRes) {
                    if (!saveRe.isSuccess()) {
                        if (!messageMap.containsKey(saveRe.getId())) {
                            if (messageMap.keySet().size() >= maxCut) {
                                overMax = true;
                                break;
                            }
                            for (Database.Error err : saveRe.getErrors()) {

                                String message = objectName + ':'
                                               + objectLabel + ':'
                                               + err.getStatusCode() + ':'
                                               + err.getFields() + ':'
                                               + err.getMessage();
                                // 数据里面有复数错误信息的话只获取第一条
                                messageMap.put(saveRe.getId(), message);
                                break;
                            }
                        }
                    }
                }
            }
        }
    }
}