@isTest
private class TransferShippmentReceived3ControllerTest {
        static TransferTestDataFactory Factory;
        static void setupTestData1() { 
                Factory = new TransferTestDataFactory();
                Factory.CreateTa('CenterToOther');
                TransferApply__c raObj1 = [SELECT Id,Name,OwnerId,Status__c,Status_Text__c,Request_time__c,Request_approval_time__c  FROM TransferApply__c WHERE RecordType.DeveloperName = 'CenterToOther' limit 1];       
                raObj1.Status__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
                raObj1.Status_Text__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
                raObj1.Request_time__c = System.now();
                raObj1.Request_approval_time__c = System.now();
                update raObj1;
                List<TransferApplyDetail__c> erList;
                erList = [select Id,Shipment_request_time2__c,StockDown__c,StockDown_time__c,StockDown_staff__c,
                        Shipment_Status_Text__c,Inspection_result__c,Pre_inspection_time__c,Inspection_staff__c
                            from TransferApplyDetail__c WHERE TransferApply__c = :raObj1.Id]; 
                for(TransferApplyDetail__c tad:erList){
                    tad.Shipment_request_time2__c = Date.toDay();
                    tad.StockDown__c = true;
                    tad.StockDown_time__c = Date.toDay();
                    tad.StockDown_staff__c = Userinfo.getUserId();
                    tad.Shipment_Status_Text__c = FixtureUtil.raesdStatusMap.get(FixtureUtil.HistoryStatus.Yi_Xia_Jia.ordinal());
                    tad.Inspection_result__c = 'OK';
                    tad.Pre_inspection_time__c = System.now();
                    tad.Inspection_staff__c = Userinfo.getUserId();
                }
                update erList;
        }
        static testMethod void test_init_error() {
                setupTestData1();
                Test.startTest();
                List<TransferApply__c> taList = [SELECT Id,OwnerId,Status__c FROM TransferApply__c]; 
                List<TransferApplySummary__c> esList;
                esList = [select TransferApply__r.Loaner_centre_mail_address__c, TransferApply__r.Name, TransferApply__c, Id, Name
                                from TransferApplySummary__c WHERE TransferApply__c = :taList[0].Id ];
                //&type=Shippment&open=web
                PageReference ref1 = new PageReference('/apex/TransferShippmentReceived3?id=' + '1234');
                Test.setCurrentPage(ref1);
                TransferShippmentReceived3Controller controller = new TransferShippmentReceived3Controller();
                controller.init();
                Test.stopTest();
                System.assertEquals(0, controller.esdList.size());
        }
	static testMethod  void test_save() {
                setupTestData1();
                Test.startTest();

                FixtureDeliverySlip__c slip = new FixtureDeliverySlip__c();
                slip.Name = '123dumy';//运输单号
                slip.DeliveryType__c = '发货';
                slip.Distributor_method__c = '陆运';
                slip.DeliveryCompany__c = '利讯';
                slip.Wh_Staff__c = Userinfo.getUserId();
                slip.Shippment_loaner_time__c = System.now();
                slip.Combine_Pack__c = '123';
                insert slip;

                List<TransferApply__c> taList = [SELECT Id,Name,OwnerId,Status__c FROM TransferApply__c]; 
                List<TransferApplySummary__c> esList;
                esList = [select TransferApply__r.Loaner_centre_mail_address__c, TransferApply__r.Name, TransferApply__c, Id, Name
                        from TransferApplySummary__c WHERE TransferApply__c = :taList[0].Id ];
                System.assertEquals(1, esList.size());
                PageReference ref = new PageReference('/apex/TransferShippmentReceived3?id=' + esList[0].id);
                Test.setCurrentPage(ref);
                TransferShippmentReceived3Controller controller = new TransferShippmentReceived3Controller();
                controller.init();
                //扫码
                controller.ApplyId = taList[0].Id+','+taList[0].Name;
                controller.CheckedId = esList[0].Id;
                controller.filljsQRAction();

                //追加
                controller.CheckedId = esList[0].Id;
                controller.ShippmentAdd();
                controller.ShippmentDetail();

                //删除
                controller.UnCheckedId = esList[0].Id;
                controller.deleteDetail();

                //追加
                controller.CheckedId = esList[0].Id;
                controller.ShippmentAdd();
                controller.ShippmentDetail();

                //运输单填写
                controller.slip.Name = '123dumy';
                controller.searchSlip();
                //保存
                controller.save();
                Test.stopTest();
                /*List<TransferApplyDetail__c> erList;
                erList = [select DeliverySlip__c
                        from TransferApplyDetail__c WHERE TransferApply__c = :taList[0].Id];
                System.assertEquals(slip.Id, erList[0].DeliverySlip__c);
                List<TransferApply__c> raList;
                raList = [select DeliverySlip__c
                        from TransferApply__c WHERE  Id = :taList[0].Id];
                System.assertEquals(slip.Id, raList[0].DeliverySlip__c);*/
        }
}