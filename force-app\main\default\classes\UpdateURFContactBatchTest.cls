@isTest
private class UpdateURFContactBatchTest {
    @testSetup
    private static void setupTestData() {
        Profile p = [select Id from Profile where id =:System.Label.ProfileId_SystemAdmin];
        User MacOwner = new User(Test_staff__c = true, LastName = 'hp1', FirstName = 'owner1', Alias = 'hp', CommunityNickname = 'hpOwner1', Email = '<EMAIL>',Job_Category__c = '销售服务', Username = 'olympus_hpowner@sunbridge.com1', IsActive = true, EmailEncodingKey = 'ISO-2022-JP', TimeZoneSidKey = 'Asia/Tokyo', LocaleSidKey = 'ja_JP', LanguageLocaleKey = 'ja', ProfileId = p.id);
        insert MacOwner;
        Account hospital = new Account();
        hospital.recordtypeId = 
            Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('HP').getRecordTypeId();
            
        hospital.Name = 'test hospita/l';
        insert hospital;
        
        // 戦略科室を得る
        List<Account> strategicDep = [SELECT ID, Name FROM Account WHERE parentId = :hospital.Id AND recordType.DeveloperName = 'Department_Class_GI'];
        
        // 診療科を作る
        Account dep = new Account();
        dep.recordtypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_GI').getRecordTypeId();
        dep.Name = 'test de/p';
        dep.ParentId = strategicDep[0].Id;
        dep.Department_Class__c = strategicDep[0].Id;
        dep.Hospital__c = hospital.Id;
        insert dep;
        
        // 製品を作る
        Product2 productA = new Product2( Name='テスト商品', Maintenance_Price_Year__c = 12000, Manual_Entry__c = false,IsActive=true,Family='GI',
                Fixture_Model_No__c='n01',Serial_Lot_No__c='S/N tracing',
                Fixture_Model_No_T__c = 'n01',
                ProductCode_Ext__c='pc01');
        insert productA;
        
        // 维修合同を作成する
        Maintenance_Contract__c contract = new Maintenance_Contract__c();
        contract.Name = 'tect contract';
        contract.Hospital__c = hospital.Id;
        contract.Department_Class__c = strategicDep[0].Id;
        contract.Department__c = dep.Id;
        contract.Service_Contract_Staff__c = MacOwner.Id;
        contract.Payment_Plan_Sum_First__c = 1;
        contract.Status__c = '契約';
        contract.Maintenance_Contract_No__c = '11123';
        contract.URF_Contract__c = true;
        contract.agree_Upper_limit__c = true;
         contract.recordtypeId = 
             Schema.SObjectType.Maintenance_Contract__c.getRecordTypeInfosByDeveloperName().get('NewMaintenance_Contract').getRecordTypeId();
        contract.Not_Upper_limit_reason__c = ' 1';
        contract.Contract_Start_Date__c = Date.today().addDays( -10);   
        contract.Contract_End_Date__c = Date.today().addDays( 5);       
        contract.SalesOfficeCode_selection__c = '北京RC';
        insert new list<Maintenance_Contract__c> {contract};
        Maintenance_Contract_Estimate__c contactEsti1 = new Maintenance_Contract_Estimate__c(
                Name = 'contract estimate 1',
                RecordtypeId = 
                         Schema.SObjectType.Maintenance_Contract_Estimate__c.getRecordTypeInfosByDeveloperName().get('NewMaintenance_Quote').getRecordTypeId(),
                Contract_Esti_Start_Date__c = Date.today(),
                Contract_Range__c = 2,
                Maintenance_Contract__c = contract.Id,
            Asset_Sum_Price__c = 1000,
            mainTalksTime__c = 1,
            talksStartDate__c = date.today(),
             Discount_reason__c ='1',
            Improve_ConsumptionRate_Idea__c = '1',
            NewEstimation_Amount__c = 100
            );
        insert contactEsti1;
        contract.Estimation_Id__c = contactEsti1.id;
        update contract;
        
        Asset asset = new Asset();
        // Asset assetA1 = new Asset(Asset_Owner__c = 'Olympus');
        asset.RecordTypeId = System.Label.Asset_RecordType;
        asset.SerialNumber = 'ass01';
        asset.Name = 'ass01';
        asset.AccountId = dep.Id;
        asset.Department_Class__c = strategicDep[0].Id;
        asset.Hospital__c = hospital.Id;
        asset.Product2Id = productA.Id;
        asset.Quantity = 1;
        asset.Status = '有库存';
        asset.Manage_type__c = '个体管理';
        asset.Loaner_accsessary__c = false;
        asset.Out_of_wh__c = 0;
        asset.Salesdepartment__c = '1.华北营业本部';
        asset.Internal_asset_location__c = '北京 备品中心';
        asset.Product_category__c = 'GI';
        asset.Equipment_Type__c = '产品试用';
        asset.SalesProvince__c = '北京';
        asset.CurrentContract__c = contract.Id;
        asset.CurrentContract_Asset_Price__c = 0;
        //System.Test.startTest();
        insert new Asset[] {asset};
        //System.Test.stopTest();
        Maintenance_Contract_Asset_Estimate__c mcae1 = new Maintenance_Contract_Asset_Estimate__c();
        mcae1.Asset__c = asset.Id;
        mcae1.Maintenance_Contract_Estimate__c = contactEsti1.Id;
        mcae1.ifHaveleftInPrevious__c = true;
        mcae1.Estimate_List_Price__c = 1000;
        mcae1.Check_Result__c = 'OK';
        mcae1.Series_RepairCount__c = 1;
        mcae1.Asset_RepairCount__c = 1;
        mcae1.Series_MaxRepairCount__c = 1;
        mcae1.Asset_MaxRepairCount__c = 1;

        insert mcae1;
        Maintenance_Contract_Asset__c contractasset = new Maintenance_Contract_Asset__c();
        contractasset.Asset__c = asset.Id;
        contractasset.Maintenance_Contract__c = contract.Id;
        contractasset.Estimate_List_Price_All_Manual__c = 1000;
        contractasset.Maintenance_Contract_Asset_Estimate__c = mcae1.id;
        insert new list<Maintenance_Contract_Asset__c> {contractasset };

        contract = [select id,Status__c from Maintenance_Contract__c WHERE id = :contract.Id];
        System.assertEquals('契約', contract.Status__c);


        // Id execBTId = Database.executeBatch(new UpdateURFContactBatch(contract.Id), 5);
        // contract = [select id,Status__c from Maintenance_Contract__c WHERE id = :contract.Id];
        // System.assertEquals('契約満了', contract.Status__c);
    }

    @isTest
    private static void testMethod1() {
    	Maintenance_Contract__c mc = [select id from Maintenance_Contract__c WHERE URF_Contract__c = true limit 1];
         System.Test.startTest();
    	Database.executeBatch(new UpdateURFContactBatch(mc.Id),10);
         System.Test.stopTest();
    }
}