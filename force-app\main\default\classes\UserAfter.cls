public without sharing class UserAfter {
    public UserAfter() {
        
    }
    //刷新公用小组
    //1.先从系统中查出所有可能更新的小组成员
    //2.统计更新后所有需要新增的小组成员。系统中已存在则不新增
    //3.统计更新前用户所添加的小组，如果存在更新后用户不需要的小组，则删除
    public static void ETENGBatch(List<User> newList,Map<Id,User> newMap,List<User> oldList,Map<Id,User> oldMap){
    	//小组列表
        List<Group> glist = [SELECT id,Name from  Group where Name like 'ET%' OR Name like 'ENG%' OR Name='a5：全国询价助理'];
        //新增小组成员列表
        Set<GroupMember> gmlist = new Set<GroupMember>();
        //所有新用户Id
        Set<String> uId = new Set<String>();
        //系统中已有小组成员列表
        List<GroupMember> oldGmList = new List<GroupMember>();
        //小组Map
        Map<String,String> gMap = new Map<String,String>();
        //系统中已有小组成员Map
        Map<String,String> oldGmMap = new Map<String,String>();
        //需要删除的小组成员
        Set<GroupMember> delGmList = new Set<GroupMember>();
        for(Group g:glist){
            if(String.isNotBlank(g.Name)){
                gMap.put(g.Name,g.Id);
            }
        }
        System.debug('获取到小组:'+gMap);

        for(User user : newList) {
        	uId.add(user.Id);
        }
        //目前有可能进行新增/删除的所有小组成员
        oldGmList = [Select Id,groupid,userOrGroupId From GroupMember where userOrGroupId in :uId];
        for(GroupMember gm:oldGmList){
        	//key   = 小组ID+'_'+用户ID
        	//value = 小组成员ID
        	oldGmMap.put(gm.groupid+'_'+gm.userOrGroupId,gm.Id);
        }
        Map<String,GroupMember> newGmMap=new Map<String,GroupMember>();
        for(User user : newList) {
            System.debug('新增ET/ENG专员权限配置:');
            System.debug('当前用户ID：'+user.Id);
            System.debug('当前用户SFDC职种：'+user.SFDCPosition_C__c);
            System.debug('当前用户负责产品：'+user.Product_specialist_incharge_product__c);
            System.debug('当前用户工作地：'+user.Work_Location__c);


            // //测试中!
            // if(user.Id!='0056D000006hptLQAQ'){
                // break;
            // }
            User oldUser=null;
            if(oldMap!=null){
				oldUser=oldMap.get(user.Id);
            }
            if(oldUser!=null){
	            System.debug('原用户ID：'+oldUser.Id);
	            System.debug('原用户SFDC职种：'+oldUser.SFDCPosition_C__c);
	            System.debug('原用户负责产品：'+oldUser.Product_specialist_incharge_product__c);
	            System.debug('原用户工作地：'+oldUser.Work_Location__c);
            }

            //SFDC职种,负责产品,工作地,启用。都没有变更，则跳过。
            // if(oldUser!=null&&user.SFDCPosition_C__c==oldUser.SFDCPosition_C__c
            // 	&&user.Product_specialist_incharge_product__c==oldUser.Product_specialist_incharge_product__c
            // 	&&user.Work_Location__c==oldUser.Work_Location__c
            // 	&&user.IsActive==oldUser.IsActive){
            // 	System.debug('无相关内容修改，跳过！');
            // 	continue;
            // }
            //当前用户启用，则可能需要新增小组成员
            if(user.IsActive==true){
            	System.debug('开始更新：');
	            //担当权限（SFDC职种=推广、负责产品(主)、工作地）,当前用户以及CL6,CL5,CL4,CL3:ET--->ET小组,ENG--->ENG小组,ET/ENG：ET小组\ENG小组
	            if(user.SFDCPosition_C__c=='销售推广'&&!String.isBlank(user.Product_specialist_incharge_product__c)&&!String.isBlank(user.Work_Location__c)){
	                System.debug('进入1：');
	                //当前用户
	                String groupName='';
	                String groupName2='';
	                if(user.Product_specialist_incharge_product__c=='ET'){
	                    groupName='ET'+user.Work_Location__c;
	                }else if(user.Product_specialist_incharge_product__c=='ENG'){
	                    groupName='ENG'+user.Work_Location__c;
	                }else if(user.Product_specialist_incharge_product__c=='ET/ENG'){
	                    groupName='ET'+user.Work_Location__c;
	                    groupName2='ENG'+user.Work_Location__c;
	                }
	                if(String.isNotBlank(groupName)&&gMap.containsKey(groupName)){
	                    GroupMember mb = new GroupMember();
	                    mb.groupid =  gMap.get(groupName);
	                    mb.userOrGroupId = user.Id;
	                    //对比系统中是否已存在小组成员，已存在则不需要新增
	                    String oldGmKey=mb.groupid+'_'+mb.userOrGroupId;
	                	newGmMap.put(oldGmKey,mb);
	                    if (!oldGmMap.containsKey(oldGmKey)) {
	                		gmlist.add(mb);
	                		System.debug('获取到小组1:'+gMap.get(groupName));
	                	}
	                    
	                }

	                if(String.isNotBlank(groupName2)&&gMap.containsKey(groupName2)){
	                    GroupMember mb = new GroupMember();
	                    mb.groupid =  gMap.get(groupName2);
	                    mb.userOrGroupId = user.Id;
	                    //对比系统中是否已存在小组成员，已存在则不需要新增
	                    String oldGmKey=mb.groupid+'_'+mb.userOrGroupId;
	                	newGmMap.put(oldGmKey,mb);
	                    if (!oldGmMap.containsKey(oldGmKey)) {
	                		gmlist.add(mb);
	                		System.debug('获取到小组11:'+gMap.get(groupName2));
	                	}
	                }
	            }else if(user.SFDCPosition_C__c=='营业助理'&&!String.isBlank(user.Product_specialist_incharge_product__c)&&!String.isBlank(user.Work_Location__c)){
	                
	                System.debug('进入2：');
	                String groupName='';
		            String groupName2='';
	                if(user.Product_specialist_incharge_product__c=='GI'||user.Product_specialist_incharge_product__c=='ET'){
	                    groupName='ET'+user.Work_Location__c;
	                }else if(user.Product_specialist_incharge_product__c=='SP'||user.Product_specialist_incharge_product__c=='ENG'){
	                    groupName='ENG'+user.Work_Location__c;
	                }else if(user.Product_specialist_incharge_product__c=='ET/ENG'||user.Product_specialist_incharge_product__c=='ALL'){
	                    groupName='ET'+user.Work_Location__c;
	                    groupName2='ENG'+user.Work_Location__c;
	                }
	                System.debug('匹对小组：'+groupName);
	                System.debug('匹对小组：'+gMap.get(groupName));
	                if(String.isNotBlank(groupName)&&gMap.containsKey(groupName)){
	                    GroupMember mb = new GroupMember();
	                    mb.groupid =  gMap.get(groupName);
	                    mb.userOrGroupId = user.Id;
	                    //对比系统中是否已存在小组成员，已存在则不需要新增
	                    String oldGmKey=mb.groupid+'_'+mb.userOrGroupId;
	                	newGmMap.put(oldGmKey,mb);
	                    if (!oldGmMap.containsKey(oldGmKey)) {
	                		gmlist.add(mb);
	                		System.debug('获取到小组6:'+gMap.get(groupName));
	                	}
	                }
	                if(String.isNotBlank(groupName2)&&gMap.containsKey(groupName2)){
	                    GroupMember mb = new GroupMember();
	                    mb.groupid =  gMap.get(groupName2);
	                    mb.userOrGroupId = user.Id;
	                    //对比系统中是否已存在小组成员，已存在则不需要新增
	                    String oldGmKey=mb.groupid+'_'+mb.userOrGroupId;
	                	newGmMap.put(oldGmKey,mb);
	                    if (!oldGmMap.containsKey(oldGmKey)) {
	                		gmlist.add(mb);
	                		System.debug('获取到小组66:'+gMap.get(groupName2));
	                	}
	                }
	            }



	            if(user.SFDCPosition_C__c=='营业助理'){
	                System.debug('进入3：');
	                String groupName='a5：全国询价助理';
	                if(String.isNotBlank(groupName)&&gMap.containsKey(groupName)){
	                    GroupMember mb = new GroupMember();
	                    mb.groupid =  gMap.get(groupName);
	                    mb.userOrGroupId = user.Id;
	                    //对比系统中是否已存在小组成员，已存在则不需要新增
	                    String oldGmKey=mb.groupid+'_'+mb.userOrGroupId;
	                	newGmMap.put(oldGmKey,mb);
	                    if (!oldGmMap.containsKey(oldGmKey)) {
	                		gmlist.add(mb);
	                		System.debug('获取到小组7:'+gMap.get(groupName));
	                	}
	                }
	            }else if(oldUser!=null&&oldUser.SFDCPosition_C__c=='营业助理'){
	            	//是否需要删除
	            	String groupName='a5：全国询价助理';
	                if(String.isNotBlank(groupName)&&gMap.containsKey(groupName)){
	                	GroupMember mb = new GroupMember();
	                    mb.groupid =  gMap.get(groupName);
	                    mb.userOrGroupId = user.Id;
	                    //对比系统中是否已存在小组成员，存在则去需要删除
	                    String oldGmKey=mb.groupid+'_'+mb.userOrGroupId;
	                    if (oldGmMap.containsKey(oldGmKey)) {
	                    	mb.Id=oldGmMap.get(oldGmKey);
	                		delGmList.add(mb);
	            			System.debug('删除小组成员3:'+mb);
	                	}
	                }
	            }


	            System.debug('筛选删除小组：');
	            //检查更新后是否需要删除原小组
	            if(oldUser!=null){
					//担当权限（SFDC职种=推广、负责产品(主)、工作地）,当前用户以及CL6,CL5,CL4,CL3:ET--->ET小组,ENG--->ENG小组,ET/ENG：ET小组\ENG小组
		            if(oldUser.SFDCPosition_C__c=='销售推广'&&!String.isBlank(oldUser.Product_specialist_incharge_product__c)&&!String.isBlank(oldUser.Work_Location__c)){
		                System.debug('进入1：');
		                //当前用户
		                String groupName='';
		                String groupName2='';
		                if(oldUser.Product_specialist_incharge_product__c=='ET'){
		                    groupName='ET'+oldUser.Work_Location__c;
		                }else if(oldUser.Product_specialist_incharge_product__c=='ENG'){
		                    groupName='ENG'+oldUser.Work_Location__c;
		                }else if(oldUser.Product_specialist_incharge_product__c=='ET/ENG'){
		                    groupName='ET'+oldUser.Work_Location__c;
		                    groupName2='ENG'+oldUser.Work_Location__c;
		                }
		                if(String.isNotBlank(groupName)&&gMap.containsKey(groupName)){
		                    GroupMember mb = new GroupMember();
		                    mb.groupid =  gMap.get(groupName);
		                    mb.userOrGroupId = oldUser.Id;
		                    //系统中已存在小组成员,并且在更新后，也不需要这个小组成员，就删除
		                    String oldGmKey=mb.groupid+'_'+mb.userOrGroupId;
		                    if (oldGmMap.containsKey(oldGmKey)&&!newGmMap.containsKey(oldGmKey)) {
		                    	mb.Id=oldGmMap.get(oldGmKey);
		                		delGmList.add(mb);
		                		System.debug('获取到小组1:'+gMap.get(groupName));
		                	}
		                }
		                
		                if(String.isNotBlank(groupName2)&&gMap.containsKey(groupName2)){
		                    GroupMember mb = new GroupMember();
		                    mb.groupid =  gMap.get(groupName2);
		                    mb.userOrGroupId = user.Id;
		                    //系统中已存在小组成员,并且在更新后，也不需要这个小组成员，就删除
		                    String oldGmKey=mb.groupid+'_'+mb.userOrGroupId;
		                    if (oldGmMap.containsKey(oldGmKey)&&!newGmMap.containsKey(oldGmKey)) {
		                    	mb.Id=oldGmMap.get(oldGmKey);
		                		delGmList.add(mb);
		                		System.debug('获取到小组11:'+gMap.get(groupName2));
		                	}
		                }
		            }else if(oldUser.SFDCPosition_C__c=='营业助理'&&!String.isBlank(oldUser.Product_specialist_incharge_product__c)&&!String.isBlank(oldUser.Work_Location__c)){
		                
		                System.debug('进入2：');
		                String groupName='';
			            String groupName2='';
		                if(oldUser.Product_specialist_incharge_product__c=='GI'||oldUser.Product_specialist_incharge_product__c=='ET'){
		                    groupName='ET'+oldUser.Work_Location__c;
		                }else if(oldUser.Product_specialist_incharge_product__c=='SP'||oldUser.Product_specialist_incharge_product__c=='ENG'){
		                    groupName='ENG'+oldUser.Work_Location__c;
		                }else if(oldUser.Product_specialist_incharge_product__c=='ET/ENG'||oldUser.Product_specialist_incharge_product__c=='ALL'){
		                    groupName='ET'+oldUser.Work_Location__c;
		                    groupName2='ENG'+oldUser.Work_Location__c;
		                }
		                System.debug('匹对小组：'+groupName);
		                System.debug('匹对小组：'+gMap.get(groupName));
		                if(String.isNotBlank(groupName)&&gMap.containsKey(groupName)){
		                    GroupMember mb = new GroupMember();
		                    mb.groupid =  gMap.get(groupName);
		                    mb.userOrGroupId = oldUser.Id;
		                    //系统中已存在小组成员,并且在更新后，也不需要这个小组成员，就删除
		                    String oldGmKey=mb.groupid+'_'+mb.userOrGroupId;
		                    if (oldGmMap.containsKey(oldGmKey)&&!newGmMap.containsKey(oldGmKey)) {
		                    	mb.Id=oldGmMap.get(oldGmKey);
		                		delGmList.add(mb);
		                		System.debug('获取到小组6:'+gMap.get(groupName));
		                	}
		                }
		                if(String.isNotBlank(groupName2)&&gMap.containsKey(groupName2)){
		                    GroupMember mb = new GroupMember();
		                    mb.groupid =  gMap.get(groupName2);
		                    mb.userOrGroupId = oldUser.Id;
		                    //系统中已存在小组成员,并且在更新后，也不需要这个小组成员，就删除
		                    String oldGmKey=mb.groupid+'_'+mb.userOrGroupId;
		                    if (oldGmMap.containsKey(oldGmKey)&&!newGmMap.containsKey(oldGmKey)) {
		                    	mb.Id=oldGmMap.get(oldGmKey);
		                		delGmList.add(mb);
		                		System.debug('获取到小组66:'+gMap.get(groupName2));
		                	}
		                }
		            }
	            }
            }else if(oldUser!=null&&oldUser.IsActive==true){
            	System.debug('更新后用户未启用(匹配更新前用户的小组，进行删除)：');
            	if(oldUser.SFDCPosition_C__c=='销售推广'&&!String.isBlank(oldUser.Product_specialist_incharge_product__c)&&!String.isBlank(oldUser.Work_Location__c)){
	                System.debug('进入1：');
	                //当前用户
	                String groupName='';
	                String groupName2='';
	                if(oldUser.Product_specialist_incharge_product__c=='ET'){
	                    groupName='ET'+oldUser.Work_Location__c;
	                }else if(oldUser.Product_specialist_incharge_product__c=='ENG'){
	                    groupName='ENG'+oldUser.Work_Location__c;
	                }else if(oldUser.Product_specialist_incharge_product__c=='ET/ENG'){
	                    groupName='ET'+oldUser.Work_Location__c;
	                    groupName2='ENG'+oldUser.Work_Location__c;
	                }
	                if(String.isNotBlank(groupName)&&gMap.containsKey(groupName)){

	                    GroupMember mb = new GroupMember();
	                    mb.groupid =  gMap.get(groupName);
	                    mb.userOrGroupId = oldUser.Id;
	                    //对比系统中是否已存在小组成员，已存在则需要删除
	                    String oldGmKey=mb.groupid+'_'+mb.userOrGroupId;
	                    if (oldGmMap.containsKey(oldGmKey)) {
	                    	mb.Id=oldGmMap.get(oldGmKey);
	                		delGmList.add(mb);
	                		System.debug('获取到小组1:'+gMap.get(groupName));
	                	}
	                    
	                }
	                if(String.isNotBlank(groupName2)&&gMap.containsKey(groupName2)){

	                    GroupMember mb = new GroupMember();
	                    mb.groupid =  gMap.get(groupName2);
	                    mb.userOrGroupId = oldUser.Id;
	                    //对比系统中是否已存在小组成员，已存在则需要删除
	                    String oldGmKey=mb.groupid+'_'+mb.userOrGroupId;
	                    if (oldGmMap.containsKey(oldGmKey)) {
	                    	mb.Id=oldGmMap.get(oldGmKey);
	                		delGmList.add(mb);
	                		System.debug('获取到小组11:'+gMap.get(groupName));
	                	}
	                }
	            }else if(oldUser.SFDCPosition_C__c=='营业助理'&&!String.isBlank(oldUser.Product_specialist_incharge_product__c)&&!String.isBlank(oldUser.Work_Location__c)){
	                
	                System.debug('进入2：');
	                String groupName='';
		            String groupName2='';
	                if(oldUser.Product_specialist_incharge_product__c=='GI'||oldUser.Product_specialist_incharge_product__c=='ET'){
	                    groupName='ET'+oldUser.Work_Location__c;
	                }else if(oldUser.Product_specialist_incharge_product__c=='SP'||oldUser.Product_specialist_incharge_product__c=='ENG'){
	                    groupName='ENG'+oldUser.Work_Location__c;
	                }else if(oldUser.Product_specialist_incharge_product__c=='ET/ENG'||oldUser.Product_specialist_incharge_product__c=='ALL'){
	                    groupName='ET'+oldUser.Work_Location__c;
	                    groupName2='ENG'+oldUser.Work_Location__c;
	                }
	                System.debug('匹对小组：'+groupName);
	                System.debug('匹对小组：'+gMap.get(groupName));
	                if(String.isNotBlank(groupName)&&gMap.containsKey(groupName)){
	                    GroupMember mb = new GroupMember();
	                    mb.groupid =  gMap.get(groupName);
	                    mb.userOrGroupId = oldUser.Id;
	                    //对比系统中是否已存在小组成员，已存在则需要删除
	                    String oldGmKey=mb.groupid+'_'+mb.userOrGroupId;
	                    if (oldGmMap.containsKey(oldGmKey)) {
	                    	mb.Id=oldGmMap.get(oldGmKey);
	                		delGmList.add(mb);
	                		System.debug('获取到小组6:'+gMap.get(groupName));
	                	}
	                }
	                if(String.isNotBlank(groupName2)&&gMap.containsKey(groupName2)){
	                    GroupMember mb = new GroupMember();
	                    mb.groupid =  gMap.get(groupName2);
	                    mb.userOrGroupId = oldUser.Id;
	                    //对比系统中是否已存在小组成员，已存在则需要删除
	                    String oldGmKey=mb.groupid+'_'+mb.userOrGroupId;
	                    if (oldGmMap.containsKey(oldGmKey)) {
	                    	mb.Id=oldGmMap.get(oldGmKey);
	                		delGmList.add(mb);
	                		System.debug('获取到小组66:'+gMap.get(groupName2));
	                	}
	                }
	            }

	            if(oldUser.SFDCPosition_C__c=='营业助理'){
	                System.debug('进入3：');
	                String groupName='a5：全国询价助理';
	                if(String.isNotBlank(groupName)&&gMap.containsKey(groupName)){
	                    GroupMember mb = new GroupMember();
	                    mb.groupid =  gMap.get(groupName);
	                    mb.userOrGroupId = oldUser.Id;
	                    //对比系统中是否已存在小组成员，已存在则需要删除
	                    String oldGmKey=mb.groupid+'_'+mb.userOrGroupId;
	                    if (oldGmMap.containsKey(oldGmKey)) {
	                    	mb.Id=oldGmMap.get(oldGmKey);
	                		delGmList.add(mb);
	                		System.debug('获取到小组7:'+gMap.get(groupName));
	                	}
	                }
	            }
	            System.debug('用户未启用，删除小组：'+delGmList);
            }
        }


        System.debug('更新结束：'+gmlist);
        if(gmlist.size()>0){
        	List<GroupMember> newGmList=new List<GroupMember>();
        	for(GroupMember g:gmlist){
        		newGmList.add(g);
        	}

            insert newGmList;
            
        }
        if(delGmList.size()>0){
        	List<GroupMember> delGmL=new List<GroupMember>();
         	for(GroupMember g:delGmList){
        		delGmL.add(g);
        	}
        	delete delGmL;
        }
        System.debug('最终确认新增的小组成员:'+gmlist);
        System.debug('最终确认删除的小组成员:'+delGmList);
    }

	
}