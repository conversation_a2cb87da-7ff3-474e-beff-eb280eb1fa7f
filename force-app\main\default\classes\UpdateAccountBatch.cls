// 这个batch 仅用作清空FSE 主担当课题 LZHU-BRH499 使用，用后请注释删除掉
public class UpdateAccountBatch implements Database.Batchable<sObject> {
    public UpdateAccountBatch() {
    }
    public Database.QueryLocator start(Database.BatchableContext BC) {
        string query = ' select id,FSE_Main__c,FSE_Extend_viceLeaderStr__c, Extend_Leader_Str__c '+
            'from account where recordType.developerName =\'HP\' ';
        query +='and (FSE_Main__c != null or FSE_Extend_viceLeaderStr__c !=null or Extend_Leader_Str__c != null ) ';
        query += 'limit 4900';
        return Database.getQueryLocator(query);
    }
    public void execute(Database.BatchableContext BC, List<account> hpList) {
        for(account tempHP : hpList){
            tempHP.FSE_Main__c = null;
            tempHP.FSE_Extend_viceLeaderStr__c = null;
            tempHP.Extend_Leader_Str__c = null;
        }
        update hpList;
    }
    public void finish(Database.BatchableContext BC) {
    }
}