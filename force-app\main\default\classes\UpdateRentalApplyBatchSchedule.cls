/**
Database.executeBatch(new UpdateRentalApplyBatch());
system.schedule('UpdateRentalApplyBatchSchedule01','0 30 1 * * ? 2015-2035', new UpdateRentalApplyBatchSchedule());
*/
global class UpdateRentalApplyBatchSchedule implements Schedulable {

    // 每个小时的15，30，45，00分执行，即15分钟执行一次
    global void execute(SchedulableContext SC) {
        // 10备品借出申请历史ずつ処理する
        Id execBTId = Database.executeBatch(new UpdateRentalApplyBatch(), 200);
    }
}