global with sharing class TransferApplySelectDetailSubController extends CreateRelationListPagingCtrlBase {
    public override Integer getSearchNumMax() {
        //各ページに制御あれば、最大件数を指定する
        // searchNumMax = Integer.valueOf(Label.Product_Select_Limit);
        // searchNumMax = 20;
        pagesize = '20';
        return searchNumMax;
    }

    /* 選択されたデータ取得用Soql　Fromから*/
    public override String getSelectedDataSql() {
        // オブジェクトAPI名
        selectedDataSql = '';
        for(String field:FIELD_NAME_MAP.keySet()) {
            myComponentController.columnRightRW.put(field, 'r');
        }
        myComponentController.columnRightRW.put('OneToOneAccessory__c', 'r');
        myComponentController.columnRightRW.put('VF_TransferCount__c', 'r');
        myComponentController.columnRightRW.put('SalesProvince_After__c', 'w');
        myComponentController.columnRightRW.put('SalesBU2__c', 'w');
        myComponentController.columnRightRW.put('EquipmentSet_Managment_Code_After__c', 'w');
        myComponentController.columnRightRW.put('Equipment_Type_After__c', 'w');
        myComponentController.columnRightRW.put('WH_location__c', 'w');
        myComponentController.columnRightRW.put('IsPerfect__c', 'w');
        // 20210813 ljh 管理编码 add start
        myComponentController.columnRightRW.put('CodeKey__c', 'r');
        myComponentController.columnRightRW.put('Internal_asset_location_After__c', 'r');
        myComponentController.columnRightRW.put('Product_category__c', 'r');
        // 20210813 ljh 管理编码 add end
        
        return selectedDataSql;
    }

    public override String getOriginObjName() {
        // オブジェクトAPI名
        originObjName = 'TransferApplyDetail__c';
        return originObjName;
    }
    public override String getOriginObjColumns() {
        // 項目セット
        originObjColumns = myComponentController.strColumus ;
        return originObjColumns;
    }

    public override String getObjName() {
        // オブジェクトAPI名
        objName = 'TransferApplyDetail__c';
        return objName;
    }
    public override String getColumnLeftFieldSetName() {
        // 左の項目セット
        columnLeftFieldSetName = '';
        return columnLeftFieldSetName;
    }
    public override String getColumnRightFieldSetName() {
        // 右の項目セット
        columnRightFieldSetName = 'DetailBulkUpdate';
        return columnRightFieldSetName;
    }

    public override List<String> getColumnFieldList() {
        // strColumus 里加 field
        // FixtureUtil#raesdGroupByAssetId()の項目も必要
        return new List<String>{'TransferApplySummary__c', 'LastModifiedDate', 'Name'
                                , 'Arrival_in_wh__c','Arrival_wh_Flag_F__c','Arrival_wh_Abandoned_Flag_F__c'
                                , 'CodeKey__c' // 20210814 ljh 管理编码
                                , 'Internal_asset_location_After__c' // 20210814 ljh 管理编码
                                , 'Product_category__c' // 20210814 ljh 管理编码
                            };
    }

    public override String getFKColumnField() {
        // getObjName 连 getOriginObjName 的 FK
        return 'Asset__c';
    }

    public override String getRecordTypeId() {
        //ページレイアウトを収得するのレコードタイプ
        recordTypeId = '';
        return recordTypeId;
    }

    // ページコントローラに検索処理は、WhereSoql作成のみ、パラメータとして、コンポーネントに渡される
    public override String getSqlWhereStr() {
        sqlWhereStr = '';

        if (getIsNeedRunSearch()) {
            //update by rentx 2021-05-07 start 1802 
            // sqlWhereStr = this.makeSoql(keyword, assetnumber);
            sqlWhereStr = this.makeSoql(keyword, assetnumber, isPerfect);
            //update by rentx 2021-05-07 end 1802
        }

        return sqlWhereStr;

    }

    public override String getOrderbyStr() {
        String ordStr = 'order by TransferApplySummary__r.Name, TransferApplySummary__c, Name';
        return ordStr;
    }

    public override Boolean getIsNeedRunSearch() {
        return true;
    }

    /*****************検索用******************/


    /*****************ソート時再検索条件（画面からの入力条件を無視するため）******************/
    public String keyword { get; set; }
    public String assetnumber {get; set;}
    public TransferApply__c parentObj { get; private set; }
    public Boolean editMode {get;set;}
    //add by rentx 1802 2021-04-07 start
    // public Boolean isPerfect {get;set;}
    public String isPerfect {get;set;}
    public List<SelectOption> options {get;set;}
    //add by rentx 1802 2021-04-07 end

    //20210812 ljh 管理编码 start
    public transient static Map<String,String> locationMap;
    static {
        locationMap = new Map<String, String> {
            '北京 备品中心' => 'BJ',
            '上海 备品中心' => 'SH',
            '广州 备品中心' => 'GZ'
        };
    }
    //20210812 ljh 管理编码 end
    public TransferApplySelectDetailSubController() {
        parentId = ApexPages.currentPage().getParameters().get('id');
        editMode = false;
        //add by rentx 1802 2021-04-08 start
        String NONE = system.label.StartTrading_None;
        options = new List<SelectOption> { new SelectOption(NONE, NONE) };
        options.add(new SelectOption('是', '是'));
        options.add(new SelectOption('否', '否'));
        //add by rentx 1802 2021-04-08 end
        
        //借出备品一览の情報を取得
        if (!String.isBlank(this.parentId)) {
            List<TransferApply__c> taList = [
                SELECT Id
                     , Add_Approval_Status__c
                     , Status__c
                     , RecordType.DeveloperName
                  FROM TransferApply__c
                 WHERE Id=:parentId
            ];
            if(taList.isEmpty()) {
                throw new ControllerUtil.myException('参数错误：请指定Id。');
            }
            else{
                parentObj = taList[0];
            }
        } else {
            throw new ControllerUtil.myException('参数错误：请指定Id。');
        }
    }

    public void init() {
        isNeedSearchFirst = true;
        //searchOppSetParam();
        getSqlWhereStr();
    }

    // 所在地区(本部)
    public List<SelectOption> salesdepartmentOpts {
        get {
            if (salesdepartmentOpts == null) {
                salesdepartmentOpts = getPickList('TransferApplyDetail__c', 'SalesBU__c');
            }
            return salesdepartmentOpts;
        }
        set;
    }
    // 所在地区(省)
    public List<SelectOption> salesProvinceOpts {
        get {
            if (salesProvinceOpts == null) {
                salesProvinceOpts = getPickList('TransferApplyDetail__c', 'SalesProvince__c');
            }
            return salesProvinceOpts;
        }
        set;
    }
    // 备品分类
    public List<SelectOption> equipmentTypeOpts {
        get {
            if (equipmentTypeOpts == null) {
                equipmentTypeOpts = getPickList('TransferApplyDetail__c', 'Equipment_Type__c');
            }
            return equipmentTypeOpts;
        }
        set;
    }

    public PageReference searchOpp() {
        //searchOppSetParam();

        if (!getIsNeedRunSearch()) {
            return null;
        }
        // 選択済みの製品を取得
        myComponentController.getSelectedDataInfo();

        getSqlWhereStr();
        // コンポーネントにSoqlを発行して、ページングする
        myComponentController.searchAndPaging();
        return null;
    }

    public void changeEdit() {
        editMode = !editMode;
        for(WrapperInfo wi:viewList) {
            TransferApplyDetail__c tad =(TransferApplyDetail__c) wi.sobj;
            wi.canEdit = editMode && !tad.Arrival_in_wh__c && (tad.Arrival_wh_Flag_F__c || tad.Arrival_wh_Abandoned_Flag_F__c);
        }
    }
    public PageReference savePage() {
        if(save()){
            searchOpp();
            ApexPages.addMessage(new ApexPages.Message(ApexPages.severity.INFO, '保存完了'));
        }
        return null;
    }
    public Boolean save() {
        // CTOM增加BU省 hql 20250606
            Map<String,String> assetMapping = new Map<String,String>();
            assetMapping.put('0.备品中心','0.备品中心');
            assetMapping.put('1-1.GI(北京,河北,天津,山东,西蒙,东蒙)','1.华北营业本部');
            assetMapping.put('1-2.GI(辽宁,黑龙江,吉林)','2.东北营业本部');
            assetMapping.put('1-3.GI(新疆,甘肃,宁夏,青海,陕西,山西,河南)','3.西北营业本部');
            assetMapping.put('1-4.GI(江西,福建,江苏,浙江,上海,安徽)','4.华东营业本部');
            assetMapping.put('1-5.GI(深圳,海南,粤东,广东,湖北,湖南,广西)','5.华南营业本部');
            assetMapping.put('1-6.GI(川藏,重庆,贵州,云南)','6.西南营业本部');
            assetMapping.put('2-1.RESP(山东,北京,天津,河北)','1.华北营业本部');
            assetMapping.put('2-2.RESP(黑龙江,辽宁,吉林)','2.东北营业本部');
            assetMapping.put('2-3.RESP(河南,新疆,山西,陕西,甘肃,宁夏,青海,内蒙古)','3.西北营业本部');
            assetMapping.put('2-4.RESP(江苏,上海,福建,浙江,安徽,江西)','4.华东营业本部');
            assetMapping.put('2-5.RESP(海南,广东,广西,湖北,湖南,深圳)','5.华南营业本部');
            assetMapping.put('2-6.RESP(四川,西藏,贵州,云南,重庆)','6.西南营业本部');
            assetMapping.put('3-1.SP(山东,河北,北京,天津,中蒙)','1.华北营业本部');
            assetMapping.put('3-2.SP(辽宁,吉林,黑龙江,东蒙)','2.东北营业本部');
            assetMapping.put('3-3.SP(河南,山西,新疆,陕西,甘肃,宁夏,青海,西蒙)','3.西北营业本部');
            assetMapping.put('3-4.SP(安徽,江苏,浙江,江西,福建,上海)','4.华东营业本部');
            assetMapping.put('3-5.SP(海南,广东,湖北,湖南,广西)','5.华南营业本部');
            assetMapping.put('3-6.SP(川藏,重庆,贵州,云南)','6.西南营业本部');
            assetMapping.put('4-1.MS(北京,天津,河北,内蒙古,山东)','1.华北营业本部');
            assetMapping.put('4-2.MS(黑龙江,吉林,辽宁)','2.东北营业本部');  
            assetMapping.put('4-3.MS(河南,新疆,陕西,甘肃,山西,宁夏,青海)','3.西北营业本部'); 
            assetMapping.put('4-4.MS(上海,浙江,江苏,安徽,福建,江西)','4.华东营业本部'); 
            assetMapping.put('4-5.MS(广西,湖北,湖南,广东,深圳,海南)','5.华南营业本部');
            assetMapping.put('4-6.MS(川藏,贵州,云南,重庆)','6.西南营业本部');  
            assetMapping.put('5-1.GIET(北京,天津,河北,西蒙,东蒙,山东)','1.华北营业本部'); 
            assetMapping.put('5-2.GIET(黑龙江,吉林,辽宁)','2.东北营业本部'); 
            assetMapping.put('5-3.GIET(河南,新疆,陕西,山西,甘肃,宁夏,青海)','3.西北营业本部');
            assetMapping.put('5-4.GIET(江苏,上海,江西,浙江,安徽,福建)','4.华东营业本部');  
            assetMapping.put('5-5.GIET(深圳,海南,广东,广西,湖北,湖南)','5.华南营业本部'); 
            assetMapping.put('5-6.GIET(贵州,云南,川藏,重庆)','6.西南营业本部'); 
            assetMapping.put('7.GI(市场)','7.GIR市场本部');
            assetMapping.put('8.SP(市场)','8.SP市场本部');  
            assetMapping.put('9.医学事务本部','9.医学事务本部'); 
            assetMapping.put('10.MS BU','10.服务本部'); 
            assetMapping.put('11.专业教育事务本部','11.专业教育事务本部');
            assetMapping.put('14.市场宣传及技能培训本部','14.医疗人才教育培训本部');  
            assetMapping.put('15.医疗法规事务和质量管理本部','15.医疗法规事务和质量管理本部'); 
            assetMapping.put('16.基建及解决方案本部','16.Solution本部'); 
        Boolean done = false;
        List<TransferApplyDetail__c> updateTadList = new List<TransferApplyDetail__c>();
        // 20210812 ljh 管理编码 add start
        Set<Id> tadClearId = new Set<Id>();
        Map<Id, TransferApplyDetail__c> codMap = new Map<Id, TransferApplyDetail__c>();
        Map<String, List<TransferApplyDetail__c>> codeNewMap = new Map<String, List<TransferApplyDetail__c>>();
        Map<String, List<TransferApplyDetail__c>> fmnMap = new Map<String, List<TransferApplyDetail__c>>();
        List<Asset_EquipmentSet_Managment_Code__c> aesmUList = new List<Asset_EquipmentSet_Managment_Code__c>();
        Set<Id> xl0TadId = new Set<Id>();
        List<TransferApplyDetail__c> tadBMList = new List<TransferApplyDetail__c>();
        List<TransferApplyDetail__c> tadBMNewList = new List<TransferApplyDetail__c>();// 20211122 ljh SFDC-C8W3HW
        String xl0 = System.Label.xl0String;
        List<String> xl0List = xl0.split(';');
        // 20210812 ljh 管理编码 add end
        Savepoint sp = Database.setSavepoint();
        try {
            for(WrapperInfo wf : viewList) {
                if(wf.check) {
                    TransferApplyDetail__c tad = (TransferApplyDetail__c) wf.sobj;
                    updateTadList.add(tad);
                    // 20210813 ljh 管理编码 add start
                    // 需要编码的
                    if(tad.Loaner_accsessary__c == false){ 
                        // 20211122 ljh SFDC-C8W3HW
                        // if((String.isNotBlank(tad.Equipment_Type_After__c) 
                        //     && ((tad.Equipment_Type__c == '检测用备品' && tad.Equipment_Type_After__c != '检测用备品') || (tad.Equipment_Type__c != '检测用备品' && tad.Equipment_Type_After__c == '检测用备品'))
                        //     )
                        //     ||(String.isNotBlank(tad.Salesdepartment_After__c) && tad.Salesdepartment_After__c != tad.Salesdepartment__c)
                        // ){
                            List<TransferApplyDetail__c> tadFsList = new List<TransferApplyDetail__c>();
                            if(fmnMap.containsKey(tad.Fixture_Model_No__c)){
                                tadFsList = fmnMap.get(tad.Fixture_Model_No__c);
                                tadFsList.add(tad);
                                fmnMap.put(tad.Fixture_Model_No__c,tadFsList);
                            }else{
                                tadFsList = new List<TransferApplyDetail__c>();
                                tadFsList.add(tad);
                                fmnMap.put(tad.Fixture_Model_No__c,tadFsList);
                            }
                        // 20211122 ljh SFDC-C8W3HW
                        // }else if(String.isNotBlank(tad.CodeKey__c)){
                        //     tadClearId.add(tad.Id);
                        // }
                        // 20211122 ljh SFDC-C8W3HW
                    }
                    // 20210813 ljh 管理编码 add end
                }
            }
            if(fmnMap.size() > 0){
                List<Fixture_Set__c> fsList =  [SELECT Id, Name, Loaner_categoryII__c 
                 FROM Fixture_Set__c WHERE Name IN:fmnMap.keySet()];
                for(Fixture_Set__c fs:fsList){
                    if(xl0List.contains(fs.Loaner_categoryII__c)){
                        for(TransferApplyDetail__c tadfs:fmnMap.get(fs.Name)){
                            xl0TadId.add(tadfs.Id);
                        }
                    }
                }
                for(String fmn:fmnMap.keySet()){
                    tadBMList.addAll(fmnMap.get(fmn));
                }
                // 20211122 ljh SFDC-C8W3HW  add start
                // 重新设计需要编码的tadBMNewList
                for(TransferApplyDetail__c tad:tadBMList){
                    System.debug('====================tad'+tad);
                    String SalesdepartmentS = String.isNotBlank(tad.SalesBU2__c)?tad.SalesBU2__c:tad.SalesBU__c;
                    Integer SalesdepartmentI;
                    if(String.isNotBlank(SalesdepartmentS)){
                        String Salesdepartment = extractFirstNumber(SalesdepartmentS);
                        SalesdepartmentI = Integer.valueOf(Salesdepartment);
                    }
                    // 1.备品分类 改变重新编码
                    if(String.isNotBlank(tad.Equipment_Type_After__c)   &&  tad.Equipment_Type__c != '检测用备品' && tad.Equipment_Type_After__c == '检测用备品'  
                    ){
                        tadBMNewList.add(tad);
                    // 2.备品分类 不改变（非检测）本部改变 重新编码
                    }else if( String.isNotBlank(tad.SalesBU2__c) && tad.SalesBU2__c != tad.SalesBU__c){
                        tadBMNewList.add(tad);
                    }else {
                        // 3.备品分类 不改变（非检测）本部不改变  0系改变重新编码
                        // 原来系列 第三位 是否是0
                        String IsCode0 = ''; 
                        if(String.isNotBlank(tad.EquipmentSet_Managment_Code__c)){
                            IsCode0 = tad.EquipmentSet_Managment_Code__c.substring(2,3);
                        }
                        Boolean IsCode0B  = xl0TadId.contains(tad.Id);
                        if(String.isNotBlank(IsCode0)&& ((IsCode0 == '0' && IsCode0B == false)||(IsCode0 != '0' && IsCode0B == true))
                            ){
                            tadBMNewList.add(tad);
                        }else{
                            // 4.备品分类 不改变（非检测）本部不改变  0系不改变(0系) 0本部 存放地改变 重新编码
                            // 存放地改变 暂时认为 只有备品中心调拨到备品中心才发生改变。  
                            if(IsCode0B){
                                if(SalesdepartmentI != null && SalesdepartmentI == 0  && parentObj.RecordType.DeveloperName == 'CenterToCenter'){
                                    tadBMNewList.add(tad);
                                }
                            }else{
                                // 5\6\7.备品分类 不改变（非检测）本部不改变  0系不改变(！0系) 0本部和7-11本部 存放地改变 重新编码
                                if(parentObj.RecordType.DeveloperName == 'CenterToCenter' && SalesdepartmentI != null && (SalesdepartmentI == 0 || (SalesdepartmentI > 6 && SalesdepartmentI < 12) || SalesdepartmentI == 16)){
                                    tadBMNewList.add(tad);
                                }else if(String.isNotBlank(tad.CodeKey__c)){
                                    tadClearId.add(tad.Id);
                                }
                            }
                        }
                        
                    } 
                }
                // 20211122 ljh SFDC-C8W3HW  add start end
                // for(TransferApplyDetail__c tad:tadBMList){
                for(TransferApplyDetail__c tad:tadBMNewList){
                    String SalesdepartmentS = String.isNotBlank(tad.SalesBU2__c)?tad.SalesBU2__c:tad.SalesBU__c;
                    if(String.isNotBlank(SalesdepartmentS)){
                            String Salesdepartment = extractFirstNumber(SalesdepartmentS);
                            Integer SalesdepartmentI = Integer.valueOf(Salesdepartment);
                            if(SalesdepartmentI < 12 || SalesdepartmentI == 16){
                                // key一览明细本次  key1一览明细上次
                                // 备品分类、本部、是否0系列、产品分类(GI/SP)、存放地
                                List<TransferApplyDetail__c> tadTempList = new List<TransferApplyDetail__c>();
                                String key = '';
                                // 1. 备品分类
                                String EquipmentType = String.isNotBlank(tad.Equipment_Type_After__c)?tad.Equipment_Type_After__c:tad.Equipment_Type__c;
                                if(EquipmentType == '检测用备品'){
                                    key += 'JC;'+locationMap.get(tad.Internal_asset_location_After__c);    
                                }else{
                                    key += 'NJC;';
                                    //2.本部 
                                    key += Salesdepartment+';';
                                    //3.是否0系列
                                    // 原管理编码的第三可知是否是0系列
                                    // String IsCode0 = tad.EquipmentSet_Managment_Code__c.substring(2,3);
                                    // 0系列 还是看 型号
                                    Boolean IsCode0  = xl0TadId.contains(tad.Id);
                                    // if(IsCode0 == '0'){
                                    if(IsCode0){
                                        if(SalesdepartmentI == 0 || SalesdepartmentI == 10){
                                            key += '0;'+locationMap.get(tad.Internal_asset_location_After__c);
                                        }else{
                                            key += '0';
                                        }
                                    }else{
                                        key += 'N0;';
                                        if(SalesdepartmentI == 0 || SalesdepartmentI > 6 ){
                                            key += tad.Product_category__c+';'+locationMap.get(tad.Internal_asset_location_After__c);
                                        }else if(SalesdepartmentI < 7){
                                            key += tad.Product_category__c;
                                        }
                                    }
                                }
                                // key
                                if(String.isBlank(tad.CodeKey__c) || (!String.isBlank(tad.CodeKey__c) && tad.CodeKey__c != key)){
                                    if(codeNewMap.containsKey(key)){
                                        tadTempList = codeNewMap.get(key);
                                        tadTempList.add(tad);
                                        codeNewMap.put(key,tadTempList);
                                    }else{
                                        tadTempList = new List<TransferApplyDetail__c>();
                                        tadTempList.add(tad);
                                        codeNewMap.put(key,tadTempList);
                                    }
                                }
                            }else if(String.isNotBlank(tad.CodeKey__c)){
                                // 本部大于等于 12清空管理编码
                                tadClearId.add(tad.Id);
                            }
                    }
                }
            }
            // 20210813 ljh 管理编码 add start
            if(codeNewMap.size() > 0){
                List<Asset_EquipmentSet_Managment_Code__c> aemCodeList = [SELECT Id, key__c, code__c,   
                         LastNo__c, keyName__c, isSpecial__c, MaxLastNo__c FROM Asset_EquipmentSet_Managment_Code__c
                        WHERE key__c IN :codeNewMap.keySet() for update];
                for(Asset_EquipmentSet_Managment_Code__c aem:aemCodeList){
                    Integer code = Integer.valueOf(aem.LastNo__c);
                    String codeManange;
                    // String codeString = 
                    if(aem.LastNo__c == aem.MaxLastNo__c){
                        // 无法编码 抛出异常
                        throw new ControllerUtil.myException('编码已满，暂时无法编码。');
                    }else if(Integer.valueOf(aem.MaxLastNo__c)-Integer.valueOf(aem.LastNo__c) < codeNewMap.get(aem.key__c).size()){
                        // 不足 抛出异常
                        throw new ControllerUtil.myException('编码内存不足，暂时无法编码。');
                    }else{
                        //正常编码
                        for(TransferApplyDetail__c tad2:codeNewMap.get(aem.key__c)){
                           if(code == 19999 || code == 29999 || code == 39999
                               || code == 39999 || code == 49999 || code == 59999
                                ){
                                code = code + 2;
                            }else{
                                code = code + 1;
                            }
                            if(aem.isSpecial__c){
                                codeManange = aem.code__c + String.valueOf(code).leftpad(5, '0').subString(0,1)+'-'+String.valueOf(code).leftpad(5, '0').substring(1,5);
                            }else{
                                codeManange = aem.code__c +'-'+String.valueOf(code).leftpad(5, '0').substring(1,5);
                            }
                            TransferApplyDetail__c tad3 = new TransferApplyDetail__c();
                            tad3.Id = tad2.Id;
                            tad3.EquipmentSet_Managment_Code_After__c= codeManange;
                            tad3.CodeKey__c = aem.key__c;
                            codMap.put(tad2.Id,tad3); 
                        }
                        Asset_EquipmentSet_Managment_Code__c aesm = new Asset_EquipmentSet_Managment_Code__c();
                        aesm.Id = aem.Id;
                        aesm.LastNo__c = code;
                        aesm.EquipmentSet_Managment_Code__c = codeManange;
                        aesm.Edit_staff__c = UserInfo.getUserId();
                        aesmUList.add(aesm);
                    }
                }
            }
            // 20210813 ljh 管理编码 add end
            if(!updateTadList.isEmpty()) {
                List<String> errorList = modifiedByOthers(updateTadList);
                if(errorList.isEmpty()) {
                    //20210813 ljh 管理编码 start
                    for(TransferApplyDetail__c utad:updateTadList){
                        if(tadClearId.contains(utad.Id)){
                           utad.EquipmentSet_Managment_Code_After__c = ''; //有值才拷贝到保有设备
                           utad.CodeKey__c = '';
                        }
                        if(codMap.containsKey(utad.Id)){
                           utad.EquipmentSet_Managment_Code_After__c = codMap.get(utad.Id).EquipmentSet_Managment_Code_After__c; 
                           utad.CodeKey__c = codMap.get(utad.Id).CodeKey__c; 
                        }
                        if(assetMapping.get(utad.SalesBU__c)!=null){
                            utad.Salesdepartment__c = assetMapping.get(utad.SalesBU__c);
                        }else{
                            System.debug('assetMapping不存在值：' + utad.SalesBU__c);
                        }
                        if(assetMapping.get(utad.SalesBU2__c)!=null){
                            utad.Salesdepartment_After__c = assetMapping.get(utad.SalesBU2__c);
                        }else{
                            System.debug('assetMapping不存在值：' + utad.SalesBU2__c);
                        }
                    }
                    //20210813 ljh 管理编码 end
                    update updateTadList;
                    update aesmUList; //20210809 ljh 管理编码 
                    done = true;
                }
                else {
                    for(String err:errorList) {
                        ApexPages.addMessage(new ApexPages.Message(ApexPages.severity.ERROR, err));
                    }
                    return false;
                }
            }
        }
        catch (Exception ex) {
            System.debug(ex.getStackTraceString());
            ApexPages.addMessages(ex);
            Database.rollback(sp);
        }
        return done;
    }
    private List<String> modifiedByOthers(List<TransferApplyDetail__c> objs) {
        List<String> errorList = new List<String>();
        Map<Id, TransferApplyDetail__c> objMap = new Map<Id, TransferApplyDetail__c>([
            SELECT Id, LastModifiedDate, LastModifiedBy.Name
            FROM TransferApplyDetail__c
            WHERE Id IN: objs
        ]);
        for(TransferApplyDetail__c obj:objs) {
            if(!objMap.containsKey(obj.Id)) {
                errorList.add(obj.Name + '不存在！');
            }
            else if(obj.LastModifiedDate != objMap.get(obj.Id).LastModifiedDate) {
                errorList.add(obj.Name + ' 已被 ' + objMap.get(obj.Id).LastModifiedBy.Name + ' 修改，请刷新画面重试！' );
            }
        }
        return errorList;
    }

    public PageReference turnback() {
        PageReference ret = null;
        if (!String.isBlank(this.parentId)) {
            ret = new PageReference('/' + this.parentId);
        }
        return ret;
    }

    //update by rentx 2021-05-07 start 1802
    // private String makeSoql(String keyword, String assetnumber) {
    private String makeSoql(String keyword, String assetnumber, String isPerfect) {
    //update by rentx 2021-05-07 end 1802
        // 検索条件
        String dateToday = String.valueOf(Date.today());

        String soql ='';
        soql += ' where TransferApply__c = \'' + String.escapeSingleQuotes(parentId) + '\'';
        soql += ' and Cancel_Select__c = false';

        if (!String.isBlank(keyword)) {
            String[] vals = keyword.split(' ');
             soql += ' and (';
             String fmodelno = '';
            for (String v : vals) {
                v = String.escapeSingleQuotes(v.replace('%', '\\%').replace('*', '%'));
                fmodelno += ' Asset__r.Product2.Fixture_Model_No_T__c like \'' + v + '\' ';
                fmodelno += ' or Asset__r.Product2.Name like \'' + v + '\' ';
                fmodelno += 'or';
            }
            fmodelno = fmodelno.removeEnd('or');
            soql += fmodelno + ' )';
        }

        if (!String.isBlank(assetnumber)) {
            String num = String.escapeSingleQuotes(assetnumber.replace('%', '\\%').replace('*', '%'));
            soql += ' and Asset__r.Internal_Asset_number_key__c like\'' + num + '\' ';
        }

        //add by rentx 2021-05-07 start 1802 
        if (isPerfect != null && isPerfect == '是') {
            soql += ' and IsPerfect__c = true ';
        }else if (isPerfect != null && isPerfect == '否') {
            soql += ' and IsPerfect__c = false ';
        }
        // if (isPerfect != null) {
            // soql += ' and IsPerfect__c = '+ !isPerfect;
        // }
        //add by rentx 2021-05-07 end 1802
        return soql;
    }
    public void saveAndSearch() {
        if(save()) {
            searchOpp();
        }
    }
    public void saveAndSearchNext() {
        if(save()) {
            myComponentController.searchNext();
        }
    }
    public void saveAndsearchPrevious() {
        if(save()) {
            myComponentController.searchPrevious();
        }
    }
    public void saveAndsearchGoPage() {
        if(save()) {
            myComponentController.searchGoPage();
        }
    }

    public override void setViewList(List<sObject> queryList) {
        viewList = new List<WrapperInfo>();
        //Id lastTASId = null;
        // 自己的数据打勾，被检索出来的数据不打勾
        for (Integer i = 0; i < queryList.size(); i++) {
            TransferApplyDetail__c tad = (TransferApplyDetail__c) queryList[i];
            WrapperInfo wf= new WrapperInfo(tad, myComponentController);
            wf.check = false;
            wf.oldCheck = false;
            wf.canEdit = editMode && !tad.Arrival_in_wh__c && (tad.Arrival_wh_Flag_F__c || tad.Arrival_wh_Abandoned_Flag_F__c);
            wf.lineNo = i;
            viewList.add(wf);
        }
    }

    public List<SelectOption> getPickList(String objApi, String fieldApi) {
        Schema.DescribeFieldResult fieldResult = Schema.getGlobalDescribe().get(objApi).getDescribe().fields.getMap().get(fieldApi).getDescribe();
        List<SelectOption> pickListValuesList= new List<SelectOption>();
        List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
        pickListValuesList.add(new SelectOption('', ''));
        pickListValuesList.add(new SelectOption('--无--', '--无--'));
        for( Schema.PicklistEntry pickListVal : ple){
            pickListValuesList.add(new SelectOption(pickListVal.getValue(), pickListVal.getLabel()));
        }
        return pickListValuesList;
    }
    private static Map<String, String> FIELD_NAME_MAP = new Map<String, String> {
        // 明细字段->asset字段
        'Asset__c'                       => 'Id',
        'Fixture_Model_No__c'            => 'Fixture_Model_No_F__c',
        'SerialNumber__c'                => 'SerialNumber',
        'Internal_Asset_number_key__c'   => 'Internal_Asset_number_key__c',
        'Internal_asset_location__c'     => 'Internal_asset_location__c',
        'SalesBU__c'             => 'SalesBU__c',
        'SalesProvince__c'               => 'SalesProvince__c',
        'Equipment_Type__c'              => 'Equipment_Type__c',
        'EquipmentSet_Managment_Code__c' => 'EquipmentSet_Managment_Code__c',
        'Main_OneToOne__c'               => 'Main_OneToOne__c',
        'Manage_type__c'                 => 'Manage_type__c',
        'Loaner_accsessary__c'           => 'Loaner_accsessary__c',
        'You_Xiao_Ku_Cun__c'             => 'You_Xiao_Ku_Cun__c'
    };
        // CTOM增加BU省 hql 20250606
    private static String extractFirstNumber(String input) {
        if (String.isBlank(input)) {
            return '';
        }
        // 先尝试按 - 分割
        String[] firstSplit = input.split('-');
        String partToProcess = firstSplit[0];

        if (firstSplit.size() > 1) {
            // 如果有 -，取第二部分继续处理
            partToProcess = firstSplit[1];
        }

        // 再按 . 分割
        String[] secondSplit = partToProcess.split('\\.');
        if (secondSplit.size() > 0) {
            return secondSplit[0].trim();
        }
        return '';
    }
}