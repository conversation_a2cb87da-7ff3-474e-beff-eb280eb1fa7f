({
    readFile: function (component, helper, file) {
        if (!file) return;
        if (!file.name.match(/\.(csv||CSV)$/)) {
            return alert('only support csv files');
        } else {
            reader = new FileReader();
            reader.onerror = function errorHandler(evt) {
                switch (evt.target.error.code) {
                    case evt.target.error.NOT_FOUND_ERR:
                        alert('File Not Found!');
                        break;
                    case evt.target.error.NOT_READABLE_ERR:
                        alert('File is not readable');
                        break;
                    case evt.target.error.ABORT_ERR:
                        break; // noop
                    default:
                        alert('An error occurred reading this file.');
                };
            }
            reader.onabort = function (e) {
                alert('File read cancelled');
            };
            reader.onloadstart = function (e) {
                var output = '<ui type=\"disc\"><li><strong>' + file.name + '</strong> (' + file.type + ')- ' + file.size + 'bytes, last modified: ' + file.lastModifiedDate.toLocaleDateString() + '</li></ui>';
                component.set("v.filename", file.name);
                component.set("v.TargetFileName", output);
            };
            reader.onload = function (e) {
                var data = e.target.result;
                // data.replace(/\n/,"")
                var allTextLines11 = data.split(/\r/);
                var s = allTextLines11.toString();
                component.set("v.fileContentData", s);
                console.log("file dataqqq" + JSON.stringify(s));
                var allTextLines = data.split(/\r\n|\n/);
                var dataRows = allTextLines.length - 1;
                var headers = allTextLines[0].split(',');
                var numOfRows = component.get("v.NumOfRecords");
                if (dataRows > numOfRows + 1 || dataRows == 1 || dataRows == 0) {
                    alert("File Rows between 1 to " + numOfRows + " .");
                    component.set("v.showMain", true);
                }
                else {
                    var lines = [];
                    var filecontentdata;
                    var content = "<table class=\"table slds-table slds-table--bordered slds-table--cell-buffer\">";
                    content += "<thead><tr class=\"slds-text-title--caps\">";
                    content += '<th scope=\"col"\>' + '行号' + '</th>';
                    for (i = 0; i < headers.length; i++) {
                        content += '<th scope=\"col"\>' + headers[i] + '</th>';
                    }
                    content += "</tr></thead>";
                    for (var i = 1; i < allTextLines.length; i++) {
                        filecontentdata = allTextLines[i].split(',');
                        if (filecontentdata[0] != '') {
                            content += "<tr>";
                            content += "<td>" + i + "</td>";
                            for (var j = 0; j < filecontentdata.length; j++) {
                                content += '<td>' + filecontentdata[j] + '</td>';
                            }
                            content += "</tr>";
                        }
                    }
                    content += "</table>";
                    console.log(content);
                    component.set("v.TableContent", content);
                    component.set("v.showMain", false);
                }
            }
            reader.readAsText(file, 'gb2312');

        }
        var reader = new FileReader();
        reader.onloadend = function () {
        };
        reader.readAsDataURL(file);
    },
    isNumber: function(str) {
        return /^\d+$/.test(str);
    },
    saveRecords: function (component, event, helper) {
        component.set('v.login', true);
        var action = component.get("c.processData");
        var fieldsList = ['Name', 'Phone', 'AccountNumber']; //Please write your code dynamic fields
        action.setParams({
            fileData: component.get("v.fileContentData"),
            sobjectName: 'Account', //Any object
            fields: fieldsList
        });
        action.setCallback(this, function (response) {
            debugger
            var state = response.getState();
            if (state === "SUCCESS") {
                var res = response.getReturnValue();
                var res1 = '';
                if (res != 'success') {
                    console.log('res = ' + res)
                    var ress = res.split('=');
                    console.log('ress = ' + ress);
                    let errorDatas = [];
                    let errorDataRow = [];
                    debugger
                    for (var i = 0; i < ress.length; i++) {
                        res1 += ress[i].substr(7) + '\n';
                        if (ress[i] != '') {
                            let errorData = {}
                            if(!this.isNumber(ress[i].substr(7).substring(1, 2))){
                                continue
                            }
                            let index = errorDataRow.indexOf(ress[i].substr(7).substring(1, 2));
                            if (index == -1) {
                                errorData.errorRow = ress[i].substr(7).substring(1, 2);
                                errorDataRow.push(errorData.errorRow);
                                errorData.errorInfo = ress[i].substr(7).replace(/,/g, ":");
                                errorDatas.push(errorData);
                            } else {
                                errorDatas[index].errorInfo += '; ' + ress[i].substr(7).replace(/,/g, ":");
                            }
                        }
                    }
                    var fileContentData = component.get("v.fileContentData").split('\n');
                    let errorColumns = [
                        { label: '错误行', fieldName: 'errorRow', type: 'text', hideDefaultActions: true, initialWidth: 100 },
                        { label: '错误信息', fieldName: 'errorInfo', type: 'text', hideDefaultActions: true,initialWidth: 300,wrapText: true }
                    ];
                    for (var i = 0; i < fileContentData[0].split(',').length - 1; i++) {
                        errorColumns.push({
                            label: fileContentData[0].split(',')[i], 
                            fieldName: 'field' + i, 
                            type: 'text', 
                            hideDefaultActions: true,
                            wrapText: true,
                            initialWidth: 120
                        });
                    }
                    
                    for(var i = 0; i < errorDatas.length; i++){
                        let data = fileContentData[errorDatas[i].errorRow];
                        console.log('data = ' + JSON.stringify(data));
                        if(data){
                            for(var j = 0; j < data.split(',').length; j++){
                                let label = 'field' + j;
                                errorDatas[i][label] = data.split(',')[j];
                            }
                        }
                    }
                    console.log('errorDatas = ' + JSON.stringify(errorDatas))
                    
                    component.set('v.errorColumns', errorColumns);
                    component.set('v.errorData', errorDatas);
                    component.set("v.showErrorInfo", true);

                    console.log("==" + res1);
                    //this.error( res1 + "请修改后重新上传" );
                    component.set('v.errorMessage', res1 + "请修改后重新上传");
                    this.showErrorToast(component);
                    component.set('v.login', false);
                    component.find('save_con').set('v.disabled', true); 
                } else {
                    // this.success('导入成功');
                    component.set('v.successMessage', '导入成功');
                    this.showSuccessToast01(component);
                    component.set("v.showMain", true);
                    component.set('v.login', false);
                    component.find('save_con').set('v.disabled', false); 
                }
            }
            else if (state === "INCOMPLETE") {
                component.set('v.login', false);
            }
            else if (state === "ERROR") {
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        console.log("Error message: " + errors[0].message);
                        let errorDatas = [{
                            errorRow: '1',
                            errorInfo: errors[0].message.replace(/,/g, ":")
                        }];
                        let errorColumns = [
                            { label: '错误行', fieldName: 'errorRow', type: 'text', hideDefaultActions: true, initialWidth: 50 },
                            { label: '错误信息', fieldName: 'errorInfo', type: 'text', hideDefaultActions: true,initialWidth: 300,wrapText: true }
                        ];
                        component.set('v.errorData', errorDatas);
                        component.set('v.errorColumns', errorColumns);
                        component.set("v.showErrorInfo", true);
                        component.set('v.login', false);
                    }
                } else {
                    console.log("Unknown error");
                    component.set('v.login', false);
                }
            }
        });
        $A.enqueueAction(action);

    },
    //弹出 成功提示
    showSuccessToast01: function (component) {
        $A.util.removeClass(component.find('successDiv01'), 'slds-hide');
        window.setTimeout($A.getCallback(function () {
            $A.util.addClass(component.find('successDiv01'), 'slds-hide');
        }),
            3000
        );
    },
    //弹出 错误提示
    showErrorToast: function (component) {
        $A.util.removeClass(component.find('errorDiv'), 'slds-hide');
        // window.setTimeout($A.getCallback(function () {
        //     $A.util.addClass(component.find('errorDiv'), 'slds-hide');
        // }),
        //     7000
        // );
    },
    success: function (message) {
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams({
            "title": "成功!",
            "message": message,
            "duration": "5000"
        });
        toastEvent.fire();
    },

    warning: function (message) {
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams({
            "title": "警告!",
            "message": message,
            "duration": "10000"
        });
        toastEvent.fire();
    },

    error: function (message) {
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams({
            "title": "错误!",
            "message": message,
            "mode": "sticky"
        });
        toastEvent.fire();
    },
    //zhj 导出错误信息 2023-05-16 start
    exportErrorInfoHelper: function (component) {
        var errorData = component.get('v.errorData');
        var errorColumns = component.get('v.errorColumns');

        var datas = [];
        let errorColumnsArray = [];
        for(var i in errorColumns){
            errorColumnsArray.push(errorColumns[i].label);
        }
        datas.push(errorColumnsArray);
        //datas.push(errorColumns);

        for (var i in errorData) {
            let data = [];
            data.push(errorData[i].errorRow)
            data.push(errorData[i].errorInfo)
            for(var j = 0 ; j < Object.keys(errorData[i]).length - 2 ; j++){
                data.push(errorData[i]['field' + j])
            }
            datas.push(data);
        }

        console.log('datas = ' + JSON.stringify(datas));
        var csv = this.convertToCSV(component, datas);
        console.log('csv = ' + JSON.stringify(csv))
        this.downloadCSV(component, csv);
    },

    convertToCSV: function (component, data) {
        console.log('enter convertToCSV')
        console.log('data = ' + data);
        var csv = '';
        for (var i = 0; i < data.length; i++) {
            var row = data[i];
            console.log('row = ' + JSON.stringify(row));
            for (var j = 0; j < row.length; j++) {
                var val = '';
                val = row[j] === null ? '' : row[j].toString();
                console.log('val = ' + val);
                if (val.indexOf(',') !== -1 || val.indexOf('"') !== -1 || val.indexOf('\n') !== -1) {
                    //val = '"' + val.replace(/"/g,'""') + '"';
                }
                if (j > 0) {
                    csv += ',';
                }
                csv += val;
            }
            csv += '\n';
        }
        return csv;
    },

    downloadCSV: function (component, csv) {
        var link = document.createElement('a');
        // 设置字符集和文件头
        var bom = "\uFEFF";
        link.href = 'data:text/csv;charset=utf-8,' + encodeURIComponent(bom + csv);
        //link.href = URL.createObjectURL(csvFile);
        link.download = 'data.csv';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    },
    //zhj 导出错误信息 2023-05-16 end
})