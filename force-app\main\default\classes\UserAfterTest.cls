@isTest
private class UserAfterTest {
	static User setNewUser(String firstName, String lastName, String aName, String email,String sFDCPc,String pSIPc,String workLocation) {
		User user = new User(Test_staff__c = true);
        user.LastName = ' ' + lastName;
        user.Alias = aName;
        user.Email = email;
        user.Username = 'Olympus' + email;
        user.CommunityNickname = aName;
        user.IsActive = true;
        user.EmailEncodingKey = 'ISO-2022-JP';
        user.TimeZoneSidKey = 'Asia/Tokyo';
        user.LocaleSidKey = 'ja_JP';
        user.LanguageLocaleKey = 'ja';
        user.ProfileId = System.Label.ProfileId_SystemAdmin;
        user.Job_Category__c = '销售推广';
        user.Province__c = '上海市';
        user.Use_Start_Date__c = Date.today().addMonths(-6);
        user.SFDCPosition_C__c = SFDCPc;
        user.Product_specialist_incharge_product__c = PSIPc;
        user.Work_Location__c = workLocation;
        insert user;

        return user;
	}
    static testMethod void testMethod1() {
		User user1 = setNewUser('ztest01', 'User001', 'Zhang001', '<EMAIL>','销售推广','ET/ENG','北京');
		User user2 = setNewUser('ztest02', 'User002', 'Zhang002', '<EMAIL>','销售推广','ET/ENG','北京');
		User user3 = setNewUser('ztest03', 'User003', 'Zhang003', '<EMAIL>','营业助理','ET/ENG','北京');
		user3.SFDCPosition_C__c='服务';
		user2.SFDCPosition_C__c='服务';
		user1.IsActive=false;
		update user3;
		update user2;
		update user1;
    }
}