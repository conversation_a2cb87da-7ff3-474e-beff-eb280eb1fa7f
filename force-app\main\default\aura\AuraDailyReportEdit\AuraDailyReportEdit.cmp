<aura:component implements="lightning:isUrlAddressable,force:appHostable,flexipage:availableForAllPageTypes,lightning:actionOverride,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction" access="global" >
    <aura:html tag="style">
        .slds-template_default{
            padding:0rem;
        }
        .slds-pill{
            width:100%;
            height:32px;
            max-width:830px;
        }
        .slds-pill__label {
            max-width:530px;
        }
    </aura:html>
    <aura:attribute name="recordId" type="String" />
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <div style="background-color: #fff;width:100%;">
        <c:lwcDailyReport recordId="{!v.recordId}"/>
    </div>
</aura:component>