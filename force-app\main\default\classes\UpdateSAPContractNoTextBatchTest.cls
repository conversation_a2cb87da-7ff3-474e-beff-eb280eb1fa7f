@isTest
private class UpdateSAPContractNoTextBatchTest {

    static testMethod void testMethod1() {
        StaticParameter.ConsumableAssetHanderTrigger = true;

        Product2 prod01 = new Product2(Name='Test01',ProductCode='Test01',Asset_Model_No__c = 'Test01',SFDA_Status__c = '有効',JANCODE__c = '04953170200311',Dealer_special_Object__c = true);
        insert new Product2[] {prod01};
        Product2__c pro1 = new Product2__c(Name='Pro001',OT_CODE_Text__c='N20201020',Product2__c = prod01.Id);
        insert new Product2__c[] {pro1};

        //创建明细2
        Consumable_order_details2__c order1 = new Consumable_order_details2__c();
        order1.Name                         = 'TEST_01_10161144';
        order1.SerialLotNo__c               = '86K';
        order1.Consumable_Product__c        = pro1.Id;
        order1.RecordTypeId                 = System.Label.RT_ConOrderDetail2_Delivery;
        order1.Consumable_order_minor__c    = null;
        order1.TracingCode__c               = 'BXJRA';
        insert order1;
        List<Consumable_order_details2__c> coclist = [select id,Consumable_product__r.OT_CODE_Text__c from Consumable_order_details2__c];

        // System.debug('tessNo__c,,SerialLotNo__c:'+order1.SerialLotNo__c);
        // System.debug('tessNo__c,,TracingCode__c:'+order1.TracingCode__c);
        System.debug('pro1 OT_CODE_Text__c   ,'+pro1.OT_CODE_Text__c);
        System.debug('tessNo__c,'+order1.Consumable_product__r.OT_CODE_Text__c + ':' + order1.sNo__c);
        System.debug('tessNo__c,'+order1.Consumable_product__r.OT_CODE_Text__c + ':' + order1.SerialLotNo__c);


        // Consumable_order_details2__c order2 = new Consumable_order_details2__c();
        // order2.Name                      = 'TEST_02_10161144';
        // order2.SerialLotNo__c                = '86F';
        // order2.Consumable_Product__c         = pro2.Id;
        // order2.RecordTypeId              = System.Label.RT_ConOrderDetail2_Delivery;
        // order2.Bar_Code__c                   = '1234567880900000000097777675656454ZDH450RXREN';
        // order2.Consumable_order_minor__c     = null;
        // order2.TracingCode__c                = 'RXREN';
        // insert order2;

        Opportunity opp = new Opportunity();

        opp.Name='testOpp1';
        opp.StageName='引合';
        opp.CloseDate=Date.today();
        opp.Sales_Root__c = '販売店';
        opp.Competitor__c ='A';
        opp.Click_Close_Date__c = null;
        insert opp;

        List<Opportunity> opplist = [select id,Name from Opportunity];

        //创建注残
        Statu_Achievements__c sac = new Statu_Achievements__c();

        sac.name = 'zhucan_one';
        sac.Opportunity__c = opp.id;
        sac.DeliveryDate__c = Date.today();
        sac.ContractNO__c = 'ContractNO1';
        sac.ContractAmount__c = 100;
        insert sac;
        System.debug('sac,,oppid:'+sac.Opportunity__c);


        Account hospital = new Account();
        hospital.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'HP'].id;
        hospital.Name = 'test hospital';
        hospital.Is_Active__c = '有効';
        hospital.Attribute_Type__c = '卫生部';
        hospital.Speciality_Type__c = '综合医院';
        hospital.Grade__c = '一级';
        hospital.OCM_Category__c = 'SLTV';
        hospital.Is_Medical__c = '医疗机构';
        hospital.Town__c = '东京';
        insert hospital;

        Account[] strategicDep = [SELECT ID, Name FROM Account WHERE parentId = :hospital.Id AND recordType.DeveloperName = 'Department_Class_OTH'];

        Account dep = new Account();
        dep.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'Department_OTH'].id;
        dep.Name = 'test dep';
        dep.AgentCode_Ext__c = '9999998';
        dep.ParentId = strategicDep[0].Id;
        dep.Department_Class__c = strategicDep[0].Id;
        dep.Hospital__c = hospital.Id;
        insert dep;

        Product2 pro5 = new Product2(Name='name05',IsActive=true,Family='SP',
                                     Fixture_Model_No__c='n05',Serial_Lot_No__c='S/N tracing',
                                     Fixture_Model_No_T__c = 'n05',
                                     ProductCode_Ext__c='pc05',Manual_Entry__c=false,ProductCode = 'N20201020');


        insert pro5;

        //创建对应的保有设备
        Asset asset1                        = new Asset(Asset_Owner__c = 'Olympus');
        asset1.RecordTypeId                 = System.Label.Asset_RecordType;
        asset1.SerialNumber                 = '86K';
        asset1.Name                         = 'asset1';
        asset1.AccountId                    = dep.Id;
        asset1.Hospital__c                  = hospital.Id;
        asset1.Backorder__c                 = sac.Id;
        asset1.IsCompetitorProduct          = true;
        asset1.Product2Id                   = pro5.Id;
        asset1.Product_Serial_No__c         = '123:321:2321';
        insert asset1;

        List<Asset> alist = [select id,OT_CODE__c,TocodeAndSerialNumber__c from Asset];
        System.debug('TocodeAndSerialNumber__c::'+alist);
        for (Asset ass : alist ) {
            System.debug('12121:OT_CODE__c::'+ass.OT_CODE__c);
        }

        // List<Consumable_order_details2__c> listt = [SELECT Id FROM Consumable_order_details2__c where ContractNo__c = null limit 1];
        System.Test.startTest();
        List<String> IdList=new List<String>();
        IdList.add(String.valueOf(order1.Id));
        Database.executeBatch(new UpdateSAPContractNoTextBatch(IdList));
        Database.executeBatch(new UpdateSAPContractNoTextBatch());
        System.Test.stopTest();


    }
}