public without sharing class UpdateSLACompleteNumber {
    public UpdateSLACompleteNumber() {

    }
    
    /*
        2021-08-27 杨杰克 增加参数 isOverdue ：是否逾期，根据此参数判断是否增加实际发放次数
    */
    public static void saveSLADetails(Map<String,Event__c> eventCAndSLA , List<String> SLAIds,boolean isOverdue){
        List<SLAReportInfo__c> slaReports = new List<SLAReportInfo__c>();
        List<String> slaTaskIds = new List<String>();
        List<NewMaintenanceReport_Task__c> reportTasks = new List<NewMaintenanceReport_Task__c>();
        List<SLAReportInfo__c> updateSLAs = new List<SLAReportInfo__c>();
        if (SLAIds.size() > 0) {
            slaReports = [SELECT Id,Event__c,NUMFlag__c,Maintenance_Contract__c,Distribution_Person__c,SLATask__c FROM SLAReportInfo__c WHERE Id =: SLAIds];
        }
        if (slaReports.size() > 0) {
            for(SLAReportInfo__c sla : slaReports){
                if (eventCAndSLA.containsKey(sla.Id)) {
                    sla.Event__c = eventCAndSLA.get(sla.Id).Id;
                    if (!sla.NUMFlag__c) {
                        slaTaskIds.add(sla.SLATask__c);
                        updateSLAs.add(sla);
                    }
                }
            }
            update slaReports;
        }
        
        if (slaTaskIds.size() > 0) {
            reportTasks = [SELECT Id,ActualDistributionTimes_Quarter__c,NewMaintenance_Contract__c,Distribution_Start_Date__c,Distribution_End_Date__c,Distribution_Person__c,ActualDistributionTimesAll_Quarter__c FROM NewMaintenanceReport_Task__c WHERE Id =:slaTaskIds];
        }
        if (reportTasks.size()>0) {
            for (SLAReportInfo__c sla : updateSLAs) {
                for (NewMaintenanceReport_Task__c reportTask : reportTasks) {
                    if (sla.Maintenance_Contract__c == reportTask.NewMaintenance_Contract__c 
                        && eventCAndSLA.get(sla.Id).ActivityDate__c >= reportTask.Distribution_Start_Date__c
                        && eventCAndSLA.get(sla.Id).ActivityDate__c <= reportTask.Distribution_End_Date__c
                        && !sla.NUMFlag__c) {
                        
                        if(isOverdue){
                            if (reportTask.ActualDistributionTimes_Quarter__c != null) {
                                reportTask.ActualDistributionTimes_Quarter__c += 1;
                            }else{
                                reportTask.ActualDistributionTimes_Quarter__c = 1;
                            }
                        }

                        if (reportTask.ActualDistributionTimesAll_Quarter__c != null) {
                            reportTask.ActualDistributionTimesAll_Quarter__c += 1;
                        }else{
                            reportTask.ActualDistributionTimesAll_Quarter__c = 1;
                        }

                        if(reportTask.Distribution_Person__c == null || reportTask.Distribution_Person__c == ''){
                            reportTask.Distribution_Person__c = sla.Distribution_Person__c;
                        }
                        sla.NUMFlag__c = true;
                    }
                }
                
            }
            update reportTasks;
            update updateSLAs;
            
        }
    }
    public static void saveSLARecorded(Map<String,String> eventCAndMainC , List<String> mainCIds){
        List<Event__c> eventCs = [SELECT Id,ActivityDate__c,Related_Service1_ID__c,SLARecorded__c FROM Event__c WHERE Id =:eventCAndMainC.keySet()];
        List<NewMaintenanceReport_Task__c> reportTasks = [SELECT Id,UseReportNum__c,NewMaintenance_Contract__c,Distribution_Start_Date__c,Distribution_End_Date__c FROM NewMaintenanceReport_Task__c WHERE NewMaintenance_Contract__c = :mainCIds];
        for (Event__c e : eventCs) {
            for (NewMaintenanceReport_Task__c reportTask : reportTasks) {
                if (e.Related_Service1_ID__c == reportTask.NewMaintenance_Contract__c 
                && e.ActivityDate__c >= reportTask.Distribution_Start_Date__c
                && e.ActivityDate__c <= reportTask.Distribution_End_Date__c) {
                    if (reportTask.UseReportNum__c != null) {
                        reportTask.UseReportNum__c +=1;
                    }else{
                        reportTask.UseReportNum__c =1;
                    }
                    
                    e.SLARecorded__c = true;
                }
            }
        }
        update reportTasks;
        update eventCs;
    }
    
}