<aura:component implements="force:lightningQuickActionWithoutHeader,force:hasRecordId,lightning:isUrlAddressable" >
    <aura:attribute name = "recordId" type = "Id" default = ""/>
	<aura:handler name="init" value="{!this}" action="{!c.doInit}" />
    <c:NewAgencyContact>
        <aura:set attribute="isClone" value="true"/>
        <aura:set attribute="recordId" value="{!v.recordId}"/>
    </c:NewAgencyContact>
</aura:component>