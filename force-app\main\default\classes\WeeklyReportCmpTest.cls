@isTest
public class WeeklyReportCmpTest {
    
    public static Account account1 = new Account();
    public static Account account2 = new Account();
    public static Contact contact1 = new Contact();
    public static Contact contact2 = new Contact();
    public static User user = new User();
    public static Agency_Hospital_Link__c agency_hospital_link = new Agency_Hospital_Link__c();
    public static Agency_Contact__c agency_contact = new Agency_Contact__c();
    public static ProductTypes__c product_types = new ProductTypes__c();
    // 经销商活动周报导入 update by vivek start
    public static ProductTypes__c product_types1 = new ProductTypes__c();
    public static ProductTypes__c product_types2 = new ProductTypes__c();
    public static ProductTypes__c product_types3 = new ProductTypes__c();
    // 经销商活动周报导入 update by vivek end
    public static Agency_Opportunity__c agency_opportunity = new Agency_Opportunity__c();
    public static OlympusCalendar__c olympus_calendar = new OlympusCalendar__c();
    
    public static String report_id;
    public static String report_header_id;
    
    @testSetup
    static void setUp(){
        TestDataUtility.CreatePIPolicyConfigurations( new string[]{'Agency_Contact__c'});
        TestDataUtility.CreatePIPolicyConfigurations( new string[]{'Contact'});
    }
    @isTest
    static void TestgetAwsurl(){
        WeeklyReportCmp.getAwsurl('Agency_Contact__c');
    }
    
    /* 病院。コンタクト、ユーザー、カレンダーなどの初期データ作る
    */
    private static void testInit() {
        StaticParameter.EscapeAccountTrigger = true;
        StaticParameter.EscapeNFM007Trigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
        StaticParameter.EscapeNFM001Trigger = true;
        Oly_TriggerHandler.bypass('AgencyReportTrigger');
        Oly_TriggerHandler.bypass('AgencyReportHandler');
        Oly_TriggerHandler.bypass('AgencyHospitalTrigger');
        Oly_TriggerHandler.bypass('AgencyHospitalHandler');
        Oly_TriggerHandler.bypass('AgencyOpportunityTrigger');
        Oly_TriggerHandler.bypass('AgencyOpportunityHandler');
        // 取引先        
        account1.Name = 'test1医院';
        account1.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('HP').getRecordTypeId();
        insert account1;
        
        account2.Name = 'test1经销商';
        account2.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();
        insert account2;

        // 取引先責任者
        contact1.AccountId = account1.Id;
        // contact1.FirstName = '責任者';
        contact1.LastName = 'test1医院 責任者';
        insert contact1;
        
        contact2.AccountId = account2.Id;
        // contact2.FirstName = '責任者';
        contact2.LastName = 'test1经销商 責任者';
        insert contact2;

        Profile p = [select Id from Profile where Name = '901_经销商活动系统'];
        //Profile p = [select Id from Profile where id =:System.Label.ProfileId_SystemAdmin];
        user.ProfileId = p.Id;
        user.ContactId = contact2.Id;
        // user.FirstName = 'ユーザー';
        user.LastName = 'テスト ユーザー';
        user.Email = '<EMAIL>';
        user.emailencodingkey='UTF-8';
        user.languagelocalekey='zh_CN';
        user.localesidkey='ja_JP';
        user.timezonesidkey='Asia/Shanghai';
        user.Username = '<EMAIL>';
        user.Alias = 'テユ';
        user.CommunityNickname = 'テストユーザー';
        
        User thisUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId()];
        system.runAs(thisUser){
            insert user;
        }
        
        // 代理店医院
        agency_hospital_link.Name = 'test1代理店医院';
        agency_hospital_link.Hospital__c = account1.Id;
        agency_hospital_link.Agency__c = account2.Id;
        agency_hospital_link.OwnerId = user.Id;
        agency_hospital_link.Agency_Campaign_Obj__c = true;
        insert agency_hospital_link;
        
        // 先生
        agency_contact.Name = 'test1医院先生';
        agency_contact.Doctor_Division1__c = '院长';
        agency_contact.Type__c = '医生';
        agency_contact.Agency_Hospital__c = agency_hospital_link.Id;
        agency_contact.OwnerId = user.Id;
        // 经销商活动周报导入 update by vivek start
        // agency_contact.Hospital_ID18__c = agency_hospital_link.Id;
        // 经销商活动周报导入 update by vivek end
        insert agency_contact;
        
        // 製品区分
        product_types.Name = '製品区分1';
        product_types.OwnerId = user.Id;
        product_types.Department_Cateogy__c = 'GI;BF;ET;GYN;OTH;URO;GS;ENT';
        insert product_types;

        // 经销商活动周报导入 update by vivek start
        product_types1.OwnerId = user.Id;
        product_types1.Department_Cateogy__c = 'GI;BF;ET;GYN;OTH;URO;GS;ENT';
        product_types1.Name = '170系统';
        product_types1.OPD_Flg__c = true;
        insert product_types1;

        product_types2.OwnerId = user.Id;
        product_types2.Department_Cateogy__c = 'GI;BF;ET;GYN;OTH;URO;GS;ENT';
        product_types2.Name = 'EUS系统';
        product_types2.SIS_Flg__c = true;
        insert product_types2;

        product_types3.OwnerId = user.Id;
        product_types3.Department_Cateogy__c = 'GI;BF;ET;GYN;OTH;URO;GS;ENT';
        product_types3.Name = 'CHF胆道镜';
        insert product_types3;
        // 经销商活动周报导入 update by vivek end

        // 引合
        agency_opportunity.RecordTypeId = [select Id,DeveloperName from RecordType where IsActive = true and SobjectType = 'Agency_Opportunity__c' and DeveloperName = 'Opportunity'].Id;
        agency_opportunity.Name = '引合1';
        agency_opportunity.Hospital_Target__c = account1.Id;
        agency_opportunity.Agency__c = account2.Id;
        agency_opportunity.Agency_Hospital__c = agency_hospital_link.Id;
        agency_opportunity.StageName__c = '还没申请预算';
        agency_opportunity.OwnerId = user.Id;
        agency_opportunity.NewOpportunity_Agency_Apply_Status__c = '批准';
        insert agency_opportunity;
        
        // オリンパスカレンダー
        olympus_calendar.Date__c = Date.valueOf('2017-04-10');
        olympus_calendar.OwnerId = user.Id;
        insert olympus_calendar;
        

        List<Account> accList = new List<Account>();

        Account account3 = new Account();
        account3.Name = 'test1 戦略科室分類 呼吸科';
        account3.ParentId = account1.Id;
        account3.Hospital_Department_Class__c = account1.Id;
        account3.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_BF').getRecordTypeId();
        accList.add(account3);

        Account account4 = new Account();
        account4.Name = 'test1 戦略科室分類 消化科';
        account4.ParentId = account1.Id;
        account4.Hospital_Department_Class__c = account1.Id;
        account4.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_GI').getRecordTypeId();
        accList.add(account4);
        
        Account account5 = new Account();
        account5.Name = 'test1 戦略科室分類 妇科';
        account5.ParentId = account1.Id;
        account5.Hospital_Department_Class__c = account1.Id;
        account5.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_GYN').getRecordTypeId();
        accList.add(account5);
        
        Account account6 = new Account();
        account6.Name = 'test1 戦略科室分類 Department_Class_GS';
        account6.ParentId = account1.Id;
        account6.Hospital_Department_Class__c = account1.Id;
        account6.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_GS').getRecordTypeId();
        accList.add(account6);
        
        Account account7 = new Account();
        account7.Name = 'test1 戦略科室分類 Department_Class_URO';
        account7.ParentId = account1.Id;
        account7.Hospital_Department_Class__c = account1.Id;
        account7.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_URO').getRecordTypeId();
        accList.add(account7);
        
        Account account8 = new Account();
        account8.Name = 'test1 戦略科室分類 Department_Class_ENT';
        account8.ParentId = account1.Id;
        account8.Hospital_Department_Class__c = account1.Id;
        account8.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_ENT').getRecordTypeId();
        accList.add(account8);
        
        Account account9 = new Account();
        account9.Name = 'test1 戦略科室分類 Department_Class_ET';
        account9.ParentId = account1.Id;
        account9.Hospital_Department_Class__c = account1.Id;
        account9.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_ET').getRecordTypeId();
        accList.add(account9);
        
        Account account10 = new Account();
        account10.Name = 'test1 戦略科室分類 Department_Class_ET';
        account10.ParentId = account1.Id;
        account10.Hospital_Department_Class__c = account1.Id;
        account10.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_OTH').getRecordTypeId();
        accList.add(account10);
        insert accList;
        ProductTypes__c producttyoe = WeeklyReportCmp.getProduct(product_types.Id);
    }

    @isTest public static void getProductListTest() {
        //testInit();
        
        User thisUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId()];
        system.runAs(thisUser){
            testInit();
            // 病院リスト取得
            List<Map<String,String>> test = WeeklyReportCmp.getProductList('%GI%', '');
            System.assertNotEquals(test.size(), 1);
        }
    }
    
    @isTest public static void getalldataTest() {
        //testInit();
        
        User thisUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId()];
        system.runAs(thisUser){
            testInit();
            // 病院リスト取得
            WeeklyReportCmp test = WeeklyReportCmp.getalldata();
            System.assertNotEquals(test, null);
        }
    }
    
    @isTest
    public static void createReportHeaderTest() {
        //testInit();
        
        User thisUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId()];
        system.runAs(thisUser){
            testInit();
            // 週報ヘッダー作成
            String name = 'テスト ユーザー';
            String s_date = '2017-04-10';
            String s_agency = contact2.Id;
            String head_key = contact2.Id + ':20170410';
            report_header_id = WeeklyReportCmp.createReportHeader(name, s_date, s_agency, head_key);
            
            Agency_Report_Header__c data = [select Id, Name, Week__c, Agency_Person2__c, HeaderInputKey__c, OlympusDate__r.Date__c from Agency_Report_Header__c where HeaderInputKey__c = :head_key];
            System.assertEquals(name + ' (' + s_date + ')', data.Name);
            System.assertEquals(Date.valueOf(s_date), data.Week__c);
            System.assertEquals(s_agency, data.Agency_Person2__c);
            System.assertEquals(head_key, data.HeaderInputKey__c);
            System.assertEquals(Date.valueOf(s_date), data.OlympusDate__r.Date__c);
        }
    }
    
    @isTest public static void getHospitalListTest() {
        //testInit();
        
        User thisUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId()];
        system.runAs(thisUser){
            testInit();
            // 病院リスト取得
            String hospital_name = 'test';
            List<Agency_Hospital_Link__c> hospital_data = WeeklyReportCmp.getHospitalList(hospital_name);
            System.assertEquals(account1.Name, hospital_data[0].Hospital_Name_readonly__c);
        }
    }
    
    @isTest public static void getDoctorListTest() {
        //testInit();
        
        User thisUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId()];
        system.runAs(thisUser){
            testInit();
            //List<Map<String,String>> doctor_data = WeeklyReportCmp.getDoctorList(agency_hospital_link.Id).values(); 报错注释 by Deloitte-allen ********
            List<Map<String,String>> doctor_data = WeeklyReportCmp.getDoctorList(agency_hospital_link.Id);
            Map<String,String> test_data = new Map<String,String>();
            test_data.put('label', agency_contact.Name);
            test_data.put('value', agency_contact.Id);
            test_data.put('selected', 'false');
            test_data.put('Doctor_Division1__c', agency_contact.Doctor_Division1__c);
            //System.assertEquals(test_data, doctor_data[1]);
        }
    }
    
    @isTest public static void selectOpportunityByIdAndHospitalLinkIdTest() {
        //testInit();
        
        User thisUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId()];
        system.runAs(thisUser){
            testInit();
            List<Agency_Opportunity__c> opportunity_data = WeeklyReportCmp.selectOpportunityByIdAndHospitalLinkId(agency_opportunity.Id, agency_hospital_link.Id);
            WeeklyReportCmp.getHosId(agency_hospital_link.Id);
            // System.assertEquals('::'+agency_opportunity.Name, opportunity_data[0].Name);
        }
    }
    
    @isTest static void saveAgencyReportTest() {
        //testInit();
        
        User thisUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId()];
        system.runAs(thisUser){
            testInit();
            String name = 'テスト ユーザー';
            String s_date = '2017-04-10';
            String s_agency = contact2.Id;
            String head_key = contact2.Id + ':20170410';
            report_header_id = WeeklyReportCmp.createReportHeader(name, s_date, s_agency, head_key);
            
            // 週報作成
            String Department_Cateogy = '';
            String Purpose_Type = '';
            String Agency_Report_Header = '';
            String Agency_Hospital = '';
            String Person_In_Charge2 = '';
            String doctor = '';
            String Submit_date = '';
            String Product_Category = '';
            String Result = '';
            String Opportunity = '';
            //fy 20220505 
            String SupportNeedsc ='';
            
            Department_Cateogy = 'GI';
            Purpose_Type = '询价挖掘-会议对应';
            Agency_Report_Header = report_header_id;
            Agency_Hospital = agency_hospital_link.Id;
            Person_In_Charge2 = contact2.Id;
            doctor = agency_contact.Id;
            Submit_date = '2017-04-10';
            Product_Category = product_types.Id;
            Result = '一般';
            Opportunity = agency_opportunity.Id;
            //fy 20220505 
            SupportNeedsc = '维修备品';                                                    //fy 20220505 SupportNeedsc
            report_id = (String) WeeklyReportCmp.saveAgencyReport(Department_Cateogy, Purpose_Type,SupportNeedsc, Agency_Report_Header, Agency_Hospital, Person_In_Charge2, doctor, Submit_date, Product_Category,'','', Result, Opportunity,'还没申请预算','15','16','2017-04-10',Submit_date).Data;
            
            Agency_Report__c data = [select Id, Department_Cateogy__c, Purpose_Type__c, Agency_Report_Header__c, Agency_Hospital__c,
            Person_In_Charge2__c, doctor2__c, Submit_date__c, Product_Category__c, Result__c, Opportunity__c from Agency_Report__c where Id=:report_id];
            System.assertEquals(Department_Cateogy, data.Department_Cateogy__c);
            System.assertEquals(Purpose_Type, data.Purpose_Type__c);
            System.assertEquals(Agency_Report_Header, data.Agency_Report_Header__c);
            System.assertEquals(Agency_Hospital, data.Agency_Hospital__c);
            System.assertEquals(Person_In_Charge2, data.Person_In_Charge2__c);
            System.assertEquals(doctor, data.doctor2__c);
            System.assertEquals(Date.valueOf(Submit_date), data.Submit_date__c);
            //System.assertEquals(Product_Category, data.Product_Category__c);
            System.assertEquals(Result, data.Result__c);
            System.assertEquals(Opportunity, data.Opportunity__c);
        }
    }
    
    @isTest public static void editAgencyReportTest() {

        User thisUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId()];
        system.runAs(thisUser){
            saveAgencyReportTest();
            String Department_Cateogy = '';
            String Purpose_Type = '';
            String Agency_Report_Header = '';
            String Agency_Hospital = '';
            String Person_In_Charge2 = '';
            String doctor = '';
            String Submit_date = '';
            String Product_Category = '';
            String Result = '';
            String Opportunity = '';
            //fy 20220505 
            String SupportNeedsc ='';
            
            Department_Cateogy = 'GI';
            Purpose_Type = '询价挖掘-会议对应';
            Agency_Report_Header = report_header_id;
            Agency_Hospital = agency_hospital_link.Id;
            Person_In_Charge2 = contact2.Id;
            doctor = agency_contact.Id;
            Submit_date = '2017-04-10';
            Product_Category = product_types.Id;
            Result = 'result_test';
            Opportunity = agency_opportunity.Id;
            //fy 20220505 
            SupportNeedsc ='维修备品';
            Test.startTest();
            // 週報編集
            Department_Cateogy = 'BF';
            //fy 20220505 
            report_id = WeeklyReportCmp.editAgencyReport(report_id, Department_Cateogy, Purpose_Type,SupportNeedsc, Agency_Report_Header, Agency_Hospital, Person_In_Charge2, doctor, Submit_date, Product_Category,'','', Result, Opportunity,'','','','',Submit_date);
            Agency_Report__c data = [select Id, Department_Cateogy__c, Purpose_Type__c, Agency_Report_Header__c, Agency_Hospital__c,
                                     Person_In_Charge2__c, doctor2__c, Submit_date__c, Product_Category__c, Result__c, Opportunity__c from Agency_Report__c where Id=:report_id];
            System.assertEquals(Department_Cateogy, data.Department_Cateogy__c);
            
            Department_Cateogy = 'GYN';
            //fy 20220505 
            report_id = WeeklyReportCmp.editAgencyReport(report_id, Department_Cateogy, Purpose_Type,SupportNeedsc, Agency_Report_Header, Agency_Hospital, Person_In_Charge2, doctor, Submit_date, Product_Category,'','', Result, Opportunity,'','','','',Submit_date);
            data = [select Id, Department_Cateogy__c, Purpose_Type__c, Agency_Report_Header__c, Agency_Hospital__c,
                                     Person_In_Charge2__c, doctor2__c, Submit_date__c, Product_Category__c, Result__c, Opportunity__c from Agency_Report__c where Id=:report_id];
            System.assertEquals(Department_Cateogy, data.Department_Cateogy__c);

            Department_Cateogy = 'GS';
            //fy 20220505 
            report_id = WeeklyReportCmp.editAgencyReport(report_id, Department_Cateogy, Purpose_Type,SupportNeedsc, Agency_Report_Header, Agency_Hospital, Person_In_Charge2, doctor, Submit_date, Product_Category,'','', Result, Opportunity,'','','','',Submit_date);
            data = [select Id, Department_Cateogy__c, Purpose_Type__c, Agency_Report_Header__c, Agency_Hospital__c,
                                     Person_In_Charge2__c, doctor2__c, Submit_date__c, Product_Category__c, Result__c, Opportunity__c from Agency_Report__c where Id=:report_id];
            System.assertEquals(Department_Cateogy, data.Department_Cateogy__c);

            Department_Cateogy = 'URO';
            //fy 20220505 
            report_id = WeeklyReportCmp.editAgencyReport(report_id, Department_Cateogy, Purpose_Type,SupportNeedsc, Agency_Report_Header, Agency_Hospital, Person_In_Charge2, doctor, Submit_date, Product_Category,'','', Result, Opportunity,'','','','',Submit_date);
            data = [select Id, Department_Cateogy__c, Purpose_Type__c, Agency_Report_Header__c, Agency_Hospital__c,
                                     Person_In_Charge2__c, doctor2__c, Submit_date__c, Product_Category__c, Result__c, Opportunity__c from Agency_Report__c where Id=:report_id];
            System.assertEquals(Department_Cateogy, data.Department_Cateogy__c);

            Department_Cateogy = 'ENT';
            //fy 20220505 
            report_id = WeeklyReportCmp.editAgencyReport(report_id, Department_Cateogy, Purpose_Type,SupportNeedsc, Agency_Report_Header, Agency_Hospital, Person_In_Charge2, doctor, Submit_date, Product_Category,'','', Result, Opportunity,'','','','',Submit_date);
            data = [select Id, Department_Cateogy__c, Purpose_Type__c, Agency_Report_Header__c, Agency_Hospital__c,
                                     Person_In_Charge2__c, doctor2__c, Submit_date__c, Product_Category__c, Result__c, Opportunity__c from Agency_Report__c where Id=:report_id];
            System.assertEquals(Department_Cateogy, data.Department_Cateogy__c);

            Department_Cateogy = 'ET';
            //fy 20220505 
            report_id = WeeklyReportCmp.editAgencyReport(report_id, Department_Cateogy, Purpose_Type,SupportNeedsc, Agency_Report_Header, Agency_Hospital, Person_In_Charge2, doctor, Submit_date, Product_Category,'','', Result, Opportunity,'','','','',Submit_date);
            data = [select Id, Department_Cateogy__c, Purpose_Type__c, Agency_Report_Header__c, Agency_Hospital__c,
                                     Person_In_Charge2__c, doctor2__c, Submit_date__c, Product_Category__c, Result__c, Opportunity__c from Agency_Report__c where Id=:report_id];
            System.assertEquals(Department_Cateogy, data.Department_Cateogy__c);

            Department_Cateogy = 'OTH';
            //fy 20220505 
            report_id = WeeklyReportCmp.editAgencyReport(report_id, Department_Cateogy, Purpose_Type,SupportNeedsc, Agency_Report_Header, Agency_Hospital, Person_In_Charge2, doctor, Submit_date, Product_Category,'','', Result, Opportunity,'还没申请预算','15','16','2017-04-10',Submit_date);
            data = [select Id, Department_Cateogy__c, Purpose_Type__c, Agency_Report_Header__c, Agency_Hospital__c,
                                     Person_In_Charge2__c, doctor2__c, Submit_date__c, Product_Category__c, Result__c, Opportunity__c from Agency_Report__c where Id=:report_id];
            System.assertEquals(Department_Cateogy, data.Department_Cateogy__c);

            Test.stopTest();
        }
    }
    
    @isTest public static void getReports() {
        saveAgencyReportTest();
        
        System.runAs(user) {
            String date_str = '2017-04-10';
            String person_str = '';
            Test.startTest();
            WeeklyReportCmp reports = WeeklyReportCmp.getReports(date_str, person_str);
            List<Agency_Report__c> report = WeeklyReportCmp.getReportsById(report_id);
            Test.stopTest();
            system.assertEquals(report_id, report[0].Id);
        }
    }
    
    @isTest public static void saveAgencyContact() {
        testInit();
        Test.startTest();
        WeeklyReportCmp weekly = new WeeklyReportCmp();
        Map<String, String> agencyContact = WeeklyReportCmp.saveAgencyContact('name', '', '', agency_hospital_link.Id, '');
        List<contact> conList = weekly.selectMAgencyPerson();
        weekly.test();
        WeeklyReportCmp.searchAgencyDataId(agency_hospital_link.Id);
        Test.stopTest();
    }


    @isTest public static void processData() {

        //testInit();
        OlympusCalendar__c oc1 = new OlympusCalendar__c();
        oc1.Date__c = Date.valueOf('2019-9-25');
        insert oc1;
        OlympusCalendar__c oc2 = new OlympusCalendar__c();
        oc2.Date__c = Date.valueOf('2019-9-26');
        insert oc2;
        OlympusCalendar__c oc3 = new OlympusCalendar__c();
        oc3.Date__c = Date.valueOf('2019-9-23');
        insert oc3;
        Test.startTest();
        User thisUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId()];
        system.runAs(thisUser){
        testInit();
        List<Agency_Report__c> data1 =WeeklyReportCmp.getReportsByDate('2019-9-23','2019-9-25');
        // String reports = WeeklyReportCmp.processData('"担当,活动日,医院,科室,拜访人,产品区分1,产品区分2,产品区分3,活动区分,结果\r\ntest1经销商 責任者,2019/9/25,test1医院,普外科,test1医院先生,EUS系统,170系统,CHF胆道镜,信息收集-日常拜访,\r\n经销商活动管理 测试,2019/9/26,test1医院,呼吸科,DDDDD,170系统,EUS系统,CHF胆道镜,信息收集-日常拜访,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,耳鼻喉科,DDDDD,170系统,EUS系统,CHF胆道镜,信息收集-日常拜访,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,ET耗材,DDDDD,170系统,EUS系统,CHF胆道镜,信息收集-日常拜访,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,消化科,DDDDD,170系统,EUS系统,CHF胆道镜,信息收集-日常拜访,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,妇科,DDDDD,170系统,EUS系统,CHF胆道镜,信息收集-日常拜访,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,其他,DDDDD,170系统,EUS系统,CHF胆道镜,信息收集-日常拜访,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,泌尿科,DDDDD,170系统,EUS系统,CHF胆道镜,信息收集-日常拜访,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,其他,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-OPD,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,妇科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-OPD,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,消化科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-OPD,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,ET耗材,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-OPD,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,耳鼻喉科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-OPD,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,呼吸科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-OPD,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,普外科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-OPD,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,泌尿科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-OPD,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,泌尿科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-SIS,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,普外科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-SIS,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,呼吸科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-SIS,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,耳鼻喉科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-SIS,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,ET耗材,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-SIS,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,消化科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-SIS,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,妇科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-SIS,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,其他,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-SIS,满意\r\n','',null);
            // System.assertEquals(reports,'');
        }
        WeeklyReportCmp week = new WeeklyReportCmp();
        //List<contact> contacts = week.selectMAgencyPerson();
        //List<Agency_Report__c> data1 =WeeklyReportCmp.getReportsByDate('2019-9-23','2019-9-25');
        //String processDataawsid = WeeklyReportCmp.processDataAWSId('"担当,活动日,医院,科室,拜访人,产品区分1,产品区分2,产品区分3,活动区分,结果\r\ntest1经销商 責任者,2019/9/25,test1医院,普外科,test1医院先生,EUS系统,170系统,CHF胆道镜,信息收集-日常拜访,\r\n经销商活动管理 测试,2019/9/26,test1医院,呼吸科,DDDDD,170系统,EUS系统,CHF胆道镜,信息收集-日常拜访,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,耳鼻喉科,DDDDD,170系统,EUS系统,CHF胆道镜,信息收集-日常拜访,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,ET耗材,DDDDD,170系统,EUS系统,CHF胆道镜,信息收集-日常拜访,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,消化科,DDDDD,170系统,EUS系统,CHF胆道镜,信息收集-日常拜访,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,妇科,DDDDD,170系统,EUS系统,CHF胆道镜,信息收集-日常拜访,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,其他,DDDDD,170系统,EUS系统,CHF胆道镜,信息收集-日常拜访,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,泌尿科,DDDDD,170系统,EUS系统,CHF胆道镜,信息收集-日常拜访,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,其他,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-OPD,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,妇科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-OPD,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,消化科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-OPD,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,ET耗材,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-OPD,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,耳鼻喉科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-OPD,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,呼吸科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-OPD,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,普外科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-OPD,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,泌尿科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-OPD,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,泌尿科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-SIS,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,普外科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-SIS,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,呼吸科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-SIS,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,耳鼻喉科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-SIS,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,ET耗材,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-SIS,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,消化科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-SIS,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,妇科,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-SIS,满意\r\n经销商活动管理 测试,2019/9/26,test1医院,其他,DDDDD,170系统,EUS系统,CHF胆道镜,询价挖掘-SIS,满意\r\n');
        // List<Contact> conList = [select id,name from Contact where name = 'test1经销商 責任者'];
        // Date d = Date.valueOf('2019-9-25');
        // List<OlympusCalendar__c> olympusDateList = [select Id,Date__c,FirstDayOfWeek__c,DayOfTheWeek__c from OlympusCalendar__c where Date__c= :d ];
        // System.assertEquals(conList[0].Name, '');
        // System.assertEquals(olympusDateList[0].FirstDayOfWeek__c, Date.today());
        Test.stopTest();
    }
}