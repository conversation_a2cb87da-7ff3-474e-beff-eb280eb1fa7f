@isTest
private class UpdateActivatedDateContactBatchTest {
	@testSetup
    private static void setupTestData() {
    	Profile p = [select id from Profile where id = :System.Label.ProfileId_SystemAdmin];
        String loginId = UserInfo.getUserId();
    	User u1 = new User(Test_staff__c = true);
        u1.LastName = '123';
        u1.FirstName = '2';
        u1.Batch_User__c = true;
        u1.Alias = '2';
        u1.Email = '<EMAIL>';
        u1.Username = '<EMAIL>';
        u1.CommunityNickname = 'あ1';
        u1.IsActive = true;
        u1.EmailEncodingKey = 'ISO-2022-JP';
        u1.TimeZoneSidKey = 'Asia/Tokyo';
        u1.LocaleSidKey = 'ja_JP';
        u1.LanguageLocaleKey = 'ja';
        u1.ProfileId = p.id;
        u1.Job_Category__c = '销售服务';
        u1.Province__c = '東京';
        u1.Employee_No__c = '0001';//20220426 ljh add
        insert u1;

    }
    static testMethod void testMethod1() {
    	User u1 = [select id from User where LastName='123'];
    	// List<Opportunity> opps = new List<Opportunity>();
        Date dt = Date.today();
    	Opportunity opp1 = new Opportunity();
        opp1.Name = 'aaa1';
        opp1.StageName = 'contact';
        opp1.CloseDate = Date.today();
        opp1.OwnerId = u1.Id;
        opp1.Owner_System__c = u1.Id;
        //判断时间
        opp1.Autholization_Activated_Date__c = dt.addMonths(-3);
        //条件1
        // opp1.Bidding_Project_Name_Bid__c = null;
        //条件2
        // opp1.Assistant_Applied_Date__c = null;

        opp1.Authorized_DB_No__c='TYHD201701062';
        opp1.Authorized_Finish_Sales__c='江西西泰茂医疗科技有限公司';
        opp1.Authorized_Date__c=dt.addYears(-1);


        // opps.add(opp1);
        
        // Opportunity opp2 = new Opportunity();
        // opp2.Name = 'aaa2';
        // opp2.StageName = 'contact';
        // opp2.CloseDate = Date.today();
        // opp2.OwnerId = u1.Id;
        // opp2.Owner_System__c = u1.Id;
        // //判断时间
        // oo2.Autholization_Activated_Date__c = null;
        // //条件1
        // oo2.Bidding_Project_Name_Bid__c = '';
        // //条件2
        // oo2.Assistant_Applied_Date__c = null;

        // oo2.Authorized_DB_No__c='TYHD201701062';
        // oo2.Authorized_Finish_Sales__c='江西西泰茂医疗科技有限公司';
        // oo2.Authorized_Date__c=2017-01-24;
        
        insert opp1;


    	// Opportunity Op = [select Id, Autholization_Activated_Date__c,Authorized_DB_No__c,Authorized_Finish_Sales__c,Authorized_Date__c from Opportunity where Name='aaa1'];
        System.Test.startTest();
    	Database.executeBatch(new UpdateActivatedDateContactBatch(),10);
        System.Test.stopTest();
        
    }
}