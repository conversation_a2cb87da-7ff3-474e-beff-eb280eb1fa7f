<aura:component implements="flexipage:availableForAllPageTypes,force:lightningQuickActionWithoutHeader,force:hasRecordId,lightning:actionOverride,lightning:isUrlAddressable" access="global"> 
    <aura:attribute name="recordId" type="String" />
    <!-- <aura:handler name="render" value="{!this}"  action="{!c.closeModal}" /> -->
    <aura:attribute name="isDoneRendering" type="Boolean" default="false"/>
    <div class="exampleHolder">

        <c:lexUpdateTimeReply recordId="{!v.recordId}"  onclosem="{!c.closeModal}"/>
        
    </div>
</aura:component>