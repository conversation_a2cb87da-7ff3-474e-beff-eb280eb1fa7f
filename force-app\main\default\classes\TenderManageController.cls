/**
*  2021-05-19 mzy 
* 页面提醒当前用户招标信息
*/
public with sharing class TenderManageController {

    public TenderManageController() {
        
    }

    //获取当前用户招投标项目信息
    public static TenderInformation getCurrentTenderInformation(){
        //1.根据当前用户信息进行查找
        List<Tender_information__c> AllTender = goSelectByCurrentUser();
        //2.分组
           // 1) 待确认的招投标项目
           List<Tender_information__c> MyTBCTender = new List<Tender_information__c>();
           // 2) 待关联询价的招投标项目
           List<Tender_information__c> MyTBRTender = new List<Tender_information__c>();
           // 3) 待应标的招投标项目
           List<Tender_information__c> MyTBBTender = new List<Tender_information__c>();
           // 4) 分组
           if(AllTender.size()>0){
                for(Tender_information__c tempT : AllTender){
                    //待确认
                    if( tempT.ViewWaitConfirm__c && '01.待确认'.equals(tempT.status__c)){
                        MyTBCTender.add(tempT);
                    }
                    //DB202306372336 you 20230714 start
                    //待关联询价
                    //if(tempT.ViewRelatedOpp__c && '04.待关联询价'.equals(tempT.status__c)){
                    //  MyTBRTender.add(tempT);
                    //}
                    //20240517 lt 首页提醒优化 add: ViewRelatedOppTenAlert__c/1-4
                    if(tempT.ViewRelatedOpp__c && (tempT.ViewRelatedOppTenAlert__c || tempT.ViewRelatedOppTenAlert1__c || tempT.ViewRelatedOppTenAlert2__c || tempT.ViewRelatedOppTenAlert3__c || tempT.ViewRelatedOppTenAlert4__c)) {
                      MyTBRTender.add(tempT);
                    }

                    //DB202306372336 you 20230714 end 

                    //待应标 zys 20210709 应标确认 变为 待应标确认
                    //20210716  mzy 添加 ViewBidConfirm__c 判断 排除2s6-2s9的助理的提醒
                    if(tempT.ViewBidConfirm__c&&'03.是否应标'.equals(tempT.status__c)&&tempT.NotBidApprovalStatus__c != '申请中'){
                        MyTBBTender.add(tempT);
                    }
                }
           }

        //3.封装
        // zq 20231029 千里马任务修改 start
        // TenderInformation MyTenderInformation= new TenderInformation(MyTBCTender,MyTBRTender,MyTBBTender);
        TenderInformation MyTenderInformation= new TenderInformation(MyTBCTender,MyTBRTender,MyTBBTender,new List<Tender_information__c>(),new List<Integer>());
        // zq 千里马任务修改 end
        return MyTenderInformation;
    }
    //kk ******** DB202309519775 start
     public static TenderTansforSubmmit getCurrentTenderTransfInformation(){
         List<TenderTansforSubmmit__c> AllTenderTransf = goSelectTransfByCurrentUser();
         List<TenderTansforSubmmit__c> MyTDPTenderTransf = new List<TenderTansforSubmmit__c>();
         if(AllTenderTransf.size()>0){
             for( TenderTansforSubmmit__c tts : AllTenderTransf){
                 MyTDPTenderTransf.add(tts);
             }
         }
         TenderTansforSubmmit MyTenderTransfInformation= new TenderTansforSubmmit(MyTDPTenderTransf);
         return MyTenderTransfInformation;
            
     }
    //kk ******** DB202309519775 end  

    //lwt  ******** DB202311717846 start
    public static Integer goModifyOwnerAgreementInfomation(){
        return goModifyOwnerAgreementByCurrentUser();
    }
    //lwt  ******** DB202311717846 end 
    //获取当前用户招投标项目信息 2023-10-16 千里马六大区显示 zq start
    public static TenderInformation getOBSATenderInformation(){
        //1.根据当前用户信息进行查找
        List<Tender_information__c> AllMyOBSATender = goSelectByOBSA();

        //2.分组
           // 1) OBSA待确认的招投标项目
           List<Tender_information__c> MyOBSATBCTender = new List<Tender_information__c>();
            // 1) 千里马六大区 OBSA待确认的招投标项目
           List<Integer> MyOBSASixAreasTender = new List<Integer>();

           // 4) 分组
           System.debug('---AllMyOBSATender---');
           System.debug(AllMyOBSATender.size());
           if(AllMyOBSATender.size()>0){
                    Integer MyOBSAFirstAreaNumber = 0;
                    Integer MyOBSASecondAreaNumber = 0;
                    Integer MyOBSAThirdAreaNumber = 0;
                    Integer MyOBSAFourthAreaNumber = 0;
                    Integer MyOBSAFifthAreaNumber = 0;
                    Integer MyOBSASixthAreaNumber = 0;
                for(Tender_information__c tempT : AllMyOBSATender){
                    //待确认
                    if( tempT.ViewWaitConfirm__c && '01.待确认'.equals(tempT.status__c)){
                        System.debug('-------------------------');
                        MyOBSATBCTender.add(tempT);
                    }
                    if('1.华北'.equals(tempT.Salesdepartment_SAP__c)){
                        MyOBSAFirstAreaNumber = MyOBSAFirstAreaNumber + 1;
                    }
                    if('2.东北'.equals(tempT.Salesdepartment_SAP__c)){
                        MyOBSASecondAreaNumber = MyOBSASecondAreaNumber + 1;
                    }
                    if('3.西北'.equals(tempT.Salesdepartment_SAP__c)){
                        MyOBSAThirdAreaNumber = MyOBSAThirdAreaNumber + 1;
                    }
                    if('4.西南'.equals(tempT.Salesdepartment_SAP__c)){
                        MyOBSAFourthAreaNumber = MyOBSAFourthAreaNumber + 1;
                    }
                    if('5.华东'.equals(tempT.Salesdepartment_SAP__c)){
                        MyOBSAFifthAreaNumber = MyOBSAFifthAreaNumber + 1;
                    }
                    if('6.华南'.equals(tempT.Salesdepartment_SAP__c)){
                        MyOBSASixthAreaNumber = MyOBSASixthAreaNumber + 1;
                    }
                }
                // String OBSANum = MyOBSAFirstAreaNumber + ';' + MyOBSASecondAreaNumber +';'+ MyOBSAThirdAreaNumber +';'+ MyOBSAFourthAreaNumber +';'+MyOBSAFifthAreaNumber+';'+MyOBSASixthAreaNumber;
                MyOBSASixAreasTender.add(MyOBSAFirstAreaNumber);
                MyOBSASixAreasTender.add(MyOBSASecondAreaNumber);
                MyOBSASixAreasTender.add(MyOBSAThirdAreaNumber);
                MyOBSASixAreasTender.add(MyOBSAFourthAreaNumber);
                MyOBSASixAreasTender.add(MyOBSAFifthAreaNumber);
                MyOBSASixAreasTender.add(MyOBSASixthAreaNumber);
                // MyOBSASixAreasTender = [MyOBSAFirstAreaNumber,MyOBSASecondAreaNumber,MyOBSAThirdAreaNumber,MyOBSAFourthAreaNumber,MyOBSAFifthAreaNumber,MyOBSASixthAreaNumber];
           }

        //3.封装
        TenderInformation MyTenderInformation= new TenderInformation(new List<Tender_information__c>(),new List<Tender_information__c>(),new List<Tender_information__c>(),MyOBSATBCTender,MyOBSASixAreasTender);

        return MyTenderInformation;
    }
    //2023-10-16 千里马六大区显示 zq end

    //根据当前用户查找招标项目
    public static List<Tender_information__c> goSelectByCurrentUser(){
        //定义List封装查询结果
        List<Tender_information__c> AllTender = new List<Tender_information__c>();
        //查询
        //20240517 lt 首页提醒优化 add: ViewRelatedOppTenAlert__c/1-4
        String query = 'SELECT id,ViewRelatedOppAlert__c,status__c,NotBidApprovalStatus__c,ViewWaitConfirm__c,ViewRelatedOpp__c,ViewRelatedOppTenAlert__c,ViewRelatedOppTenAlert1__c,ViewRelatedOppTenAlert2__c,ViewRelatedOppTenAlert3__c,ViewRelatedOppTenAlert4__c,ViewBidConfirm__c ';
               query += 'FROM Tender_information__c ';
               //待确认 
               // ******** ljh SWAG-CK28WT update start
               // query += 'WHERE ( status__c = \'01.待确认\' AND  ViewWaitConfirm__c = true ) ';
               // query += 'WHERE (( status__c = \'01.待确认\' AND  ViewWaitConfirm__c = true ) ';
               //DB202306372336 you 20230714 start
               // 20231230 ssm 跳过众成补充数据，待确认数据通用改造，增加自定义标签补充soql start
               query += 'WHERE (( status__c = \'01.待确认\' AND  ViewWaitConfirm__c = true ';
               String more_query = System.Label.HomePage_NormalTen1_More;
               query += String.isNotBlank(more_query) && more_query != '无' ? more_query : '';
               query += ') ';
               // 20231230 ssm 跳过众成补充数据，待确认数据通用改造，增加自定义标签补充soql end
               //待关联询价 
               //query += 'OR ( status__c =\'04.待关联询价\' AND ViewRelatedOpp__c = true ) ';
               //20240517 lt 首页提醒优化 start
                //  query += 'OR ( ViewRelatedOpp__c = true ) ';
                 query += 'OR (ViewRelatedOpp__c = true AND (ViewRelatedOppTenAlert__c = true OR ViewRelatedOppTenAlert1__c = true OR ViewRelatedOppTenAlert2__c = true OR ViewRelatedOppTenAlert3__c = true OR ViewRelatedOppTenAlert4__c = true)  ) ';
               //20240517 lt 首页提醒优化 end
               //DB202306372336 you 20230714 end
               //待应标 zys 20210709 应标确认 变为 待应标确认
               // query += 'OR ( status__c = \'03.是否应标\' AND NotBidApprovalStatus__c != \'申请中\'  ) ';
               query += 'OR ( status__c = \'03.是否应标\' AND NotBidApprovalStatus__c != \'申请中\'  )) ';
                // ******** ljh SWAG-CK28WT update end

        // ******** ljh SWAG-CK28WT add start
        String proId = UserInfo.getProfileId();
        String p_2M4 = System.Label.ProfileId_2M4;
        if(proId.substring(0,15) == p_2M4.substring(0,15)){
               query += ' AND OwnerId =\''+UserInfo.getUserId()+'\'';
        }
        System.debug('query:'+query);
        // ******** ljh SWAG-CK28WT add end
        AllTender = Database.query(query);

        return AllTender;
    }

    // kk ******** DB202309519775 start  根据当前用户查找待审批的招标转科室
    //根据当前用户查找招标项目  得修改一下 只要oba7、8的
     public static List<TenderTansforSubmmit__c> goSelectTransfByCurrentUser(){
         //定义List封装查询结果
         List<TenderTansforSubmmit__c> AllTenderTransf = new List<TenderTansforSubmmit__c>();
         //只有当前用户简档为oba7、8才查询，
         //查询
        String proId = UserInfo.getProfileId();
        String oba78ProfileId = System.Label.oba7_8profileId;
        List<String> result = new List<String>();
        result = oba78ProfileId.split(',');
        String oba7ProfileId = result[0];
        String oba8ProfileId = result[1];
        // DB202410082163 chenjingwu 20241011 start
        if(proId.substring(0,15) == oba7ProfileId.substring(0,15) || proId.substring(0,15) == oba8ProfileId.substring(0,15)  || System.Label.TransfByCurrentUserProId.contains(proId.substring(0,15))){
        // DB202410082163 chenjingwu 20241011 end
        // DB202406382590 招标项目：不应标申请里增加涉及科室 fy start
            // AllTenderTransf = [SELECT id FROM TenderTansforSubmmit__c WHERE status__c='审批中'];
            AllTenderTransf = [SELECT id FROM TenderTansforSubmmit__c 
            WHERE status__c='审批中'
            AND  RecordType.Name ='转科室'
            ];
        // DB202406382590 招标项目：不应标申请里增加涉及科室 fy end
            
        }else{
            
        }
         

         return AllTenderTransf;
     }
    // kk ******** DB202309519775 end 

    // lwt  ******** DB202311717846 start  根据当前用户查找待审批的招标转科室
    //根据当前用户查找需要修改拥有者的契约
    public static Integer goModifyOwnerAgreementByCurrentUser(){
        String uId=Userinfo.getUserId();
        List<Account> aL=[SELECT id  from Account where Is_Active_Formula__c='有效' and Acc_Record_Type__c = '契約' and Business_Assistant__c!=null and Business_Assistant__c=:uId and  OwnerId =:uId and  (ENG_Dealer__c=true OR ET_SP_Dealer__c=true OR SP_DealerContact__c=true)];

        return aL.size();
    }
    // lwt  ******** DB202311717846 end 



    // lwt  合同生命周期 start
    public static Integer getCLCCKHB(){
        String ulist=System.Label.ckhbUser;
        List<String> uidlist=ulist.split(',');
        String uId=Userinfo.getUserId();
        Boolean flag=false;
        for(String sitem:uidlist){
            if(uId==sitem){
                flag=true;
                break;
            }
        }
        if(!flag){
            return 0;
        }
        List<ContractLifecycle__c> clcL=[SELECT id  from ContractLifecycle__c where ContractCheck__c='未检查' and SAContractStatus__c=true and Bid_NotNULL__c=true and Salesdepartment_Owner__c='1.华北'];

        return clcL.size();
    }
    public static Integer getCLCCKDB(){
        String ulist=System.Label.ckdbUser;
        List<String> uidlist=ulist.split(',');
        String uId=Userinfo.getUserId();
        Boolean flag=false;
        for(String sitem:uidlist){
            if(uId==sitem){
                flag=true;
                break;
            }
        }
        if(!flag){
            return 0;
        }
        List<ContractLifecycle__c> clcL=[SELECT id  from ContractLifecycle__c where ContractCheck__c='未检查' and SAContractStatus__c=true and Bid_NotNULL__c=true and Salesdepartment_Owner__c='2.东北'];

        return clcL.size();
    }
    public static Integer getCLCCKXB(){
        String ulist=System.Label.ckxbUser;
        List<String> uidlist=ulist.split(',');
        String uId=Userinfo.getUserId();
        Boolean flag=false;
        for(String sitem:uidlist){
            if(uId==sitem){
                flag=true;
                break;
            }
        }
        if(!flag){
            return 0;
        }
        List<ContractLifecycle__c> clcL=[SELECT id  from ContractLifecycle__c where ContractCheck__c='未检查' and SAContractStatus__c=true and Bid_NotNULL__c=true and Salesdepartment_Owner__c='3.西北'];

        return clcL.size();
    }
    public static Integer getCLCCKXN(){
        String ulist=System.Label.ckxnUser;
        List<String> uidlist=ulist.split(',');
        String uId=Userinfo.getUserId();
        Boolean flag=false;
        for(String sitem:uidlist){
            if(uId==sitem){
                flag=true;
                break;
            }
        }
        if(!flag){
            return 0;
        }
        List<ContractLifecycle__c> clcL=[SELECT id  from ContractLifecycle__c where ContractCheck__c='未检查' and SAContractStatus__c=true and Bid_NotNULL__c=true and Salesdepartment_Owner__c='4.西南'];

        return clcL.size();
    }
    public static Integer getCLCCKHD(){
        String ulist=System.Label.ckhdUser;
        List<String> uidlist=ulist.split(',');
        String uId=Userinfo.getUserId();
        Boolean flag=false;
        for(String sitem:uidlist){
            if(uId==sitem){
                flag=true;
                break;
            }
        }
        if(!flag){
            return 0;
        }
        List<ContractLifecycle__c> clcL=[SELECT id  from ContractLifecycle__c where ContractCheck__c='未检查' and SAContractStatus__c=true and Bid_NotNULL__c=true and Salesdepartment_Owner__c='5.华东'];

        return clcL.size();
    }
    public static Integer getCLCCKHN(){
        String ulist=System.Label.ckhnUser;
        List<String> uidlist=ulist.split(',');
        String uId=Userinfo.getUserId();
        Boolean flag=false;
        for(String sitem:uidlist){
            if(uId==sitem){
                flag=true;
                break;
            }
        }
        if(!flag){
            return 0;
        }
        List<ContractLifecycle__c> clcL=[SELECT id  from ContractLifecycle__c where ContractCheck__c='未检查' and SAContractStatus__c=true and Bid_NotNULL__c=true and Salesdepartment_Owner__c='6.华南'];

        return clcL.size();
    }
    public static Integer getCLCCKALL(){
        String ulist=System.Label.ckOBSAPUser;
        List<String> uidlist=ulist.split(',');
        String uId=Userinfo.getUserId();
        Boolean flag=false;
        for(String sitem:uidlist){
            if(uId==sitem){
                flag=true;
                break;
            }
        }
        if(!flag){
            return 0;
        }
        List<ContractLifecycle__c> clcL=[SELECT id  from ContractLifecycle__c where ContractCheck__c='未检查' and SAContractStatus__c=true and Bid_NotNULL__c=true ];

        return clcL.size();
    }


    public static Integer getCLCAKHN(){
        String ulist=System.Label.akhnUser;
        List<String> uidlist=ulist.split(',');
        String uId=Userinfo.getUserId();
        Boolean flag=false;
        for(String sitem:uidlist){
            if(uId==sitem){
                flag=true;
                break;
            }
        }
        if(!flag){
            return 0;
        }
        List<ContractLifecycle__c> clcL=[SELECT id  from ContractLifecycle__c where AuthorizationCheck__c='未检查' and CompletedBy__c!='SCM' and CompletedBy__c!='OBSAP'  and Salesdepartment_Owner__c='6.华南'  AND opp_Ten__c=true];

        return clcL.size();
    }
    public static Integer getCLCAKHD(){
        String ulist=System.Label.akhdUser;
        List<String> uidlist=ulist.split(',');
        String uId=Userinfo.getUserId();
        Boolean flag=false;
        for(String sitem:uidlist){
            if(uId==sitem){
                flag=true;
                break;
            }
        }
        if(!flag){
            return 0;
        }
        List<ContractLifecycle__c> clcL=[SELECT id  from ContractLifecycle__c where AuthorizationCheck__c='未检查' and CompletedBy__c!='SCM' and CompletedBy__c!='OBSAP'  and Salesdepartment_Owner__c='5.华东'  AND opp_Ten__c=true];

        return clcL.size();
    }
    public static Integer getCLCAKALL(){
        String ulist=System.Label.akALLUser;
        List<String> uidlist=ulist.split(',');
        String uId=Userinfo.getUserId();
        Boolean flag=false;
        for(String sitem:uidlist){
            if(uId==sitem){
                flag=true;
                break;
            }
        }
        if(!flag){
            return 0;
        }
        List<ContractLifecycle__c> clcL=[SELECT id  from ContractLifecycle__c where AuthorizationCheck__c='未检查' and CompletedBy__c!='SCM' and CompletedBy__c!='OBSAP'   AND opp_Ten__c=true];

        return clcL.size();
    }

    public static Integer getCLCCK(){
        String uId=Userinfo.getUserId();
        List<ContractLifecycle__c> clcL=[SELECT id  from ContractLifecycle__c where ContractCheck__c='不一致' and ContractCheckExplanan__c='' and Opportunity__r.ownerId=:uId];

        return clcL.size();
    }
    public static Integer getCLCAK(){
        String uId=Userinfo.getUserId();
        List<ContractLifecycle__c> clcL=[SELECT id  from ContractLifecycle__c where AuthorizationCheck__c='不一致' and AuthorizationCheckExplanan__c='' and CompletedBy__c!='SCM' and CompletedBy__c!='OBSAP'  and Opportunity__r.ownerId=:uId];

        return clcL.size();
    }
    // lwt  合同生命周期 end



    // lwt  20240611 报价委托意向合并 start
    public static Integer getOBSAP(){
        String uId=Userinfo.getUserId();
        User user=[SELECT id,RoleName_wave__c from user where id = :uId];
        //System.debug('ddddddddddddd==='+user.RoleName_wave__c);
        //System.debug(user);
         
        if(user.RoleName_wave__c!='OBSAP报价委托小组'){ return 0;}
        AggregateResult[] aL=[SELECT count(id) cnt from task where  Subject like '%报价委托：%'  and status='未着手' and OwnerId=:System.Label.obsap_group_opp and CreatedDate >2024-02-08T08:00:00.000+08:00];
        if (aL != null && aL.size() > 0) {
            return Integer.valueOf(aL[0].get('cnt'));
        }
        return 0;
    }
    public static Integer getOBSAPLead(){
        String uId=Userinfo.getUserId();
        User user=[SELECT id,RoleName_wave__c from user where id = :uId];
        //System.debug('ddddddddddddd==='+user.RoleName_wave__c);
        //System.debug(user);
         
        if(user.RoleName_wave__c!='OBSAP报价委托小组'){ return 0;}
        AggregateResult[] aL=[SELECT  count(id) cnt from Lead   where QuotationDelegationStatus__c='已经委托'];
        if (aL != null && aL.size() > 0) {
            return Integer.valueOf(aL[0].get('cnt'));
        }
        return 0;
    }
    // lwt  ******** 报价委托意向合并 end

    //根据OBSA查找招标项目  2023-10-16 千里马六大区显示 zq start
    public static List<Tender_information__c> goSelectByOBSA(){
        //定义List封装查询结果
        List<Tender_information__c> AllTender = new List<Tender_information__c>();
        //查询
        String query = 'SELECT id,ViewRelatedOppAlert__c,status__c,NotBidApprovalStatus__c,ViewWaitConfirm__c,ViewRelatedOpp__c,ViewBidConfirm__c,Salesdepartment_SAP__c ';
               query += 'FROM Tender_information__c ';
               //待确认 
               // ******** ljh SWAG-CK28WT update start
               // query += 'WHERE ( status__c = \'01.待确认\' AND  ViewWaitConfirm__c = true ) ';
                query += 'WHERE (( status__c = \'01.待确认\' AND  ViewWaitConfirm__c = true )) ';
        // 20231230 ssm 跳过众成补充数据，待确认数据通用改造，增加自定义标签补充soql start
        String more_query = System.Label.HomePage_NormalTen1_More;
        query += String.isNotBlank(more_query) && more_query != '无' ? more_query : '';
        // 20231230 ssm 跳过众成补充数据，待确认数据通用改造，增加自定义标签补充soql end
        // ******** ljh SWAG-CK28WT add start
        String proId = UserInfo.getProfileId();
        String p_2M4 = System.Label.ProfileId_2M4;
        if(proId.substring(0,15) == p_2M4.substring(0,15)){
               query += ' AND OwnerId =\''+UserInfo.getUserId()+'\'';
        }
        System.debug('query>>>>>>>>>'+query);
        // ******** ljh SWAG-CK28WT add end
        AllTender = Database.query(query);

        return AllTender;
    }
    // 2023-10-16 千里马六大区显示 zq end

    public class TenderInformation {
        //待确认的招投标项目 : GI / SP AND 01.待确认
        public List<Tender_information__c> MyTBCTender;

        //待关联询价的招投标项目 : GI / SP AND 04.确认应标
        public List<Tender_information__c> MyTBRTender;

        //待应标的招投标项目 : AccountOwner
        public List<Tender_information__c> MyTBBTender;
    
        // 2023-10-16千里马六大区显示 zq start
        //待确认的OBSA全国招投标项目 : GI / SP AND 01.待确认
        public List<Tender_information__c> MyOBSATender;
        //待确认的OBSA 千里马六大区 
        public List<Integer> MyOBSASixAreasTender;
        // 2023-10-16千里马六大区显示 zq end

        public TenderInformation(list<Tender_information__c> MyTBCTender, list<Tender_information__c> MyTBRTender, list<Tender_information__c> MyTBBTender, list<Tender_information__c> MyOBSATender,list<Integer> MyOBSASixAreasTender) {
            this.MyTBCTender = MyTBCTender;
            this.MyTBRTender = MyTBRTender;
            this.MyTBBTender = MyTBBTender;
            // 2023-10-16千里马六大区显示 zq start
            this.MyOBSATender = MyOBSATender;
            this.MyOBSASixAreasTender = MyOBSASixAreasTender;
            // 2023-10-16千里马六大区显示 zq end
        }
    }   
    // kk ******** DB202309519775 start 
     public class TenderTansforSubmmit {
         //待审批的招标转科室申请
         public List<TenderTansforSubmmit__c> MyTDPTenderTransf;

         public TenderTansforSubmmit(list<TenderTansforSubmmit__c> MyTDPTenderTransf) {
             this.MyTDPTenderTransf = MyTDPTenderTransf; 
         }
     }
    // kk ******** DB202309519775 end
    
   

}