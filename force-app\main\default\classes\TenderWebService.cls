global class TenderWebService {
	public TenderWebService() {

	}
	//招投标反逻辑删除
	WebService static String ContraryLogicalDel(String DTenId) {
		Tender_information__c DTenInfo = [Select Id, InfoId__c, Logical_delete__c, ProjectId__c, Retain_Tender__c From Tender_information__c Where id = : DTenId];
		// 更新删除招投标
		List<Tender_information__c> updateTenInfoList = new List<Tender_information__c>();
		// 更新保留招投标
		// List<Tender_information__c> updateBTenList = new List<Tender_information__c>();
		if (String.isNotBlank(DTenInfo.Retain_Tender__c)) {
			// System.debug('11111111' + TenInfo.Retain_Tender__c);
			//要保留的招投标
			Tender_information__c BTen =  [select Id, InfoId__c From Tender_information__c
			                               Where Id = : DTenInfo.Retain_Tender__c];

			// 删除招投标关联的询价
			// List<Tender_Opportunity_Link__c> DTenLinkList = [select Opportunity__c
			//         from Tender_Opportunity_Link__c
			//         where Tender_information__c = :DTenId and IsRelated__c = true];
			// System.debug('---------2---------' + DTenLinkList);
			// Set<Id> DTenLinkOppIdSet = new Set<Id>();
			// if (DTenLinkList.size() > 0) {
			// 	for (Tender_Opportunity_Link__c DTenlink : DTenLinkList) {
			// 		DTenLinkOppIdSet.add(DTenlink.Opportunity__c);
			// 	}
			// 	System.debug('---------3---------' + DTenLinkOppIdSet);
			// 	// 删除项目关联并且与保留项目关联的询价关联信息
			// 	List<Tender_Opportunity_Link__c> DelD_BTenLinkList = [select id, Opportunity__c, Tender_information__c
			// 	        from Tender_Opportunity_Link__c
			// 	        where Tender_information__c = :BTen.Id and Opportunity__c in : DTenLinkOppIdSet];
			// 	System.debug('---------1---------' + DelD_BTenLinkList);
			// 	if (DelD_BTenLinkList.size() > 0) {
			// 		Delete DelD_BTenLinkList;
			// 	}
			// }

			// 保留项目通过软删除逻辑关联来的询价
			List<Tender_Opportunity_Link__c> DelD_BTenLinkList = [select id, Opportunity__c, Tender_information__c
				        from Tender_Opportunity_Link__c
				        where Tender_information__c = :BTen.Id and IsRelated__c = true];
			// 判断link是否为空
			if (DelD_BTenLinkList != null && DelD_BTenLinkList.size() > 0) {
				// 逻辑有大坑 暂时只把打标记的删掉 不做回写的操作了
				// List<Tender_Opportunity_Link__c> add_list = new List<Tender_Opportunity_Link__c>();
				// for (Tender_Opportunity_Link__c link : DelD_BTenLinkList) {
				// 	Tender_Opportunity_Link__c add_link = new Tender_Opportunity_Link__c();
				// 	add_link.Tender_information__c = DTenInfo.Id;
				// 	add_link.Opportunity__c = link.Opportunity__c;
				// 	add_link.Tender_Opportunity_Uniq__c = DTenInfo.Id + '' + link.Opportunity__c;
				// 	add_link.IsRelated__c = false;
				// 	add_list.add(add_link);
				// }
				// 删掉保留项目上的关联询价
				delete DelD_BTenLinkList;
				// 删除项目上的关联加回来
				// if (add_list.size() > 0) {
				// 	insert add_list;
				// }
			}

			// 互换保留招投标与删除招投标的信息Id
			DTenInfo.Retain_Tender__c = BTen.Id;
			String BTenInfo = BTen.InfoId__c;
			BTen.InfoId__c = DTenInfo.InfoId__c;//保留招投标的信息Id赋给删除招投标的信息Id
			DTenInfo.InfoId__c = BTenInfo;//删除招投标的信息Id赋给保留招投标的信息Id
			// 点击保存后 删除招投标上的逻辑删除字段变为true
			DTenInfo.Logical_delete__c = false;
			// update TenInfo;
			// 一起更新就行了
			updateTenInfoList.add(DTenInfo);
			updateTenInfoList.add(BTen);
			update updateTenInfoList;

			// updateBTenList.add(BTen);
			// update updateBTenList;

		}
		return 'OK';
	}
}