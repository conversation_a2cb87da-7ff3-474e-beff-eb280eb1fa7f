@isTest
private class TenderDeleteControllerTest {
    static testMethod void testMethod1() {
        List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院' Limit 1];
        if (rectCo.size() == 0) {
            return;
        }
        List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 呼吸科' Limit 1];
        if (rectSct.size() == 0) {
            return;
        }
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 消化科' Limit 1];
        if (rectDpt.size() == 0) {
            return;
        }

        //List<Tender_information__c> TenInfo = [Select Id, InfoId__c, Logical_delete__c, ProjectId__c, Retain_Tender__c From Tender_information__c Where id = : 'a4J1m0000009Tv5'];
        Tender_information__c TenInfo = new Tender_information__c();
        Tender_information__c TenInfo1 = new Tender_information__c();
        TenInfo1.Name = 'QLMTEST08111057-02';
        insert TenInfo1;

        TenInfo.Name = '2345';
        TenInfo.InfoId__c = '1234';
        TenInfo.Retain_Tender__c = TenInfo1.Id;
        insert TenInfo;

        //List<Tender_Opportunity_Link__c> BlinksList = [select Opportunity__c from Tender_Opportunity_Link__c where Tender_information__c = :BTen.Id];
        Account company = new Account();
        company.RecordTypeId = rectCo[0].Id;
        company.Name         = 'NFM007TestCompany';
        upsert company;
        Account section = new Account();
        section.RecordTypeId = rectSct[0].Id;
        section.Name         = '*';
        section.Department_Class_Label__c = '消化科';
        section.ParentId                  = company.Id;
        section.Hospital_Department_Class__c = company.Id;
        upsert section;
        Account depart = new Account();
        depart.RecordTypeId = rectDpt[0].Id;
        depart.Name         = '*';
        depart.Department_Name__c  = 'NFM007TestDepart';
        depart.ParentId            = section.Id;
        depart.Department_Class__c = section.Id;
        depart.Hospital__c         = company.Id;
        upsert depart;

        Opportunity opp = new Opportunity();
        opp.AccountId           = depart.Id;
        opp.Department_Class__c = section.Id;
        opp.Hospital__c         = company.Id;
        opp.SAP_Send_OK__c      = false;
        opp.Name                = 'GZ-SP-NFM007_1';
        opp.Trade__c            = '内貿';
        opp.StageName           = '引合';
        opp.CloseDate           = date.newinstance(2025, 11, 30);
        opp.Stock_apply_status__c = '申请中';
        opp.Whether_Bidding__c = '否';
        insert opp;

        Tender_Opportunity_Link__c BlinksList = new Tender_Opportunity_Link__c();
        BlinksList.Opportunity__c = opp.Id;
        BlinksList.CurrencyIsoCode = 'CNY';
        BlinksList.Tender_information__c = TenInfo1.Id;
        //insert BlinksList;

        Tender_Opportunity_Link__c linksList = new Tender_Opportunity_Link__c();
        linksList.Opportunity__c = opp.Id;
        linksList.CurrencyIsoCode = 'CNY';
        linksList.Tender_information__c = TenInfo.Id;
        // insert linksList;

        Tender_Opportunity_Link__c addlinksList = new Tender_Opportunity_Link__c();
        addlinksList.Opportunity__c = opp.Id;
        addlinksList.CurrencyIsoCode = 'CNY';
        addlinksList.Tender_information__c = TenInfo1.Id;
        //insert addlinksList;
        //Tender_information__c BTen = [select Id, InfoId__c From Tender_information__c Where Id = : TenInfo.Retain_Tender__c];
        Tender_information__c BTen = new Tender_information__c();
        BTen.InfoId__c = '1122';

        TenInfo.Retain_Tender__c = BTen.Id;
        String BTenInfo = BTen.InfoId__c;
        BTen.InfoId__c = TenInfo.InfoId__c;
        TenInfo.InfoId__c = BTenInfo;
        TenInfo.Logical_delete__c = true;
        List<Tender_information__c> updateTenInfoList = new List<Tender_information__c>();
        updateTenInfoList.add(TenInfo);
        updateTenInfoList.add(BTen);

        //Apexpages.currentPage().getParameters().put('id', TenInfo.Id);
        PageReference peg = new PageReference('/apex/TenderDeletePage?id=' + TenInfo.Id);
        System.Test.setCurrentPage(peg);
        TenderDeleteController tenderDeleteController = new TenderDeleteController();
        Test.StartTest();
        tenderDeleteController.init();
        tenderDeleteController.returnFresh();
        tenderDeleteController.saveTenInfo();
        tenderDeleteController.test();
        Test.stopTest();


    }
}