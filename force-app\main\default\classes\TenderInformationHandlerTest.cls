@isTest
private class TenderInformationHandlerTest {

    static testMethod void testMethod1() {
        ControllerUtil.EscapeNFM001Trigger = true;
        StaticParameter.EscapeOtherUpdateTenOwner = false;
        
        Oly_TriggerHandler.bypass('UpdateContractAimAmountHandler');

        Profile p = [select id from Profile where id = :System.Label.ProfileId_SystemAdmin];
        String loginId = UserInfo.getUserId();
        User thisUser= [select Id from User where Id=:UserInfo.getUserId()];
System.runAs (thisUser){
        User sys = [select id from User where Id = :loginId];
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        User u1 = new User(Test_staff__c = true);
        u1.LastName = '123';
        // u1.FirstName = '2';
        u1.Batch_User__c = true;
        u1.Alias = '2';
        u1.Email = '<EMAIL>';
        u1.Username = '<EMAIL>';
        u1.CommunityNickname = 'あ1';
        u1.IsActive = true;
        u1.EmailEncodingKey = 'ISO-2022-JP';
        u1.TimeZoneSidKey = 'Asia/Tokyo';
        u1.LocaleSidKey = 'ja_JP';
        u1.LanguageLocaleKey = 'ja';
        u1.ProfileId = p.id;
        u1.Job_Category__c = '销售服务';
        u1.Province__c = '東京';
        u1.Employee_No__c = '0001';//******** ljh add
        insert u1;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        User u2 = new User(Test_staff__c = true);
        u2.LastName = '_サンブリッジ';
        // u2.FirstName = 'い';
        u2.Batch_User__c = true;
        u2.Alias = 'い';
        u2.Email = '<EMAIL>';
        u2.Username = '<EMAIL>';
        u2.CommunityNickname = 'い';
        u2.IsActive = true;
        u2.EmailEncodingKey = 'ISO-2022-JP';
        u2.TimeZoneSidKey = 'Asia/Tokyo';
        u2.LocaleSidKey = 'ja_JP';
        u2.LanguageLocaleKey = 'ja';
        u2.ProfileId = p.id;
        u2.Job_Category__c = '销售推广';
        u2.Province__c = '東京';
        u2.IsActive = true;
        u2.Employee_No__c = '0002';//******** ljh add
        insert u2;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        User u3 = new User(Test_staff__c = true);
        u3.LastName = '1222';
        // u3.FirstName = 'い';
        u3.Batch_User__c = true;
        u3.Alias = 'い';
        u3.Email = '<EMAIL>';
        u3.Username = '<EMAIL>';
        u3.CommunityNickname = '33';
        u3.IsActive = true;
        u3.EmailEncodingKey = 'ISO-2022-JP';
        u3.TimeZoneSidKey = 'Asia/Tokyo';
        u3.LocaleSidKey = 'ja_JP';
        u3.LanguageLocaleKey = 'ja';
        u3.ProfileId = p.id;
        u3.Job_Category__c = '销售推广';
        u3.Province__c = '東京';
        u3.IsActive = true;
        u3.Employee_No__c = '0003';//******** ljh add
        insert u3;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        User u4 = new User(Test_staff__c = true);
        u4.LastName = '244';
        // u4.FirstName = 'い';
        u4.Batch_User__c = true;
        u4.Alias = 'い';
        u4.Email = '<EMAIL>';
        u4.Username = '<EMAIL>';
        u4.CommunityNickname = '444';
        u4.IsActive = true;
        u4.EmailEncodingKey = 'ISO-2022-JP';
        u4.TimeZoneSidKey = 'Asia/Tokyo';
        u4.LocaleSidKey = 'ja_JP';
        u4.LanguageLocaleKey = 'ja';
        u4.ProfileId = p.id;
        u4.Job_Category__c = '销售推广';
        u4.Province__c = '東京';
        u4.IsActive = true;
        u4.Employee_No__c = '0004';//******** ljh add
        insert u4;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        // User u5 = new User(Test_staff__c = true);
        // u5.LastName = 'test00001';
        // u5.FirstName = 'qbcuy';
        // u5.Batch_User__c = true;
        // u5.Alias = '12cs';
        // u5.Email = '<EMAIL>';
        // u5.Username = '<EMAIL>';
        // u5.CommunityNickname = 'fewfa';
        // u5.IsActive = true;
        // u5.EmailEncodingKey = 'ISO-2022-JP';
        // u5.TimeZoneSidKey = 'Asia/Tokyo';
        // u5.LocaleSidKey = 'ja_JP';
        // u5.LanguageLocaleKey = 'ja';
        // u5.ProfileId = p.id;
        // u5.Job_Category__c = '销售推广';
        // u5.Province__c = '東京';
        // u5.IsActive = true;
        // u5.Employee_No__c = '0005'; //******** ljh add
        // insert u5;

        // StaticParameter.EscapeOpportunityBefUpdTrigger = true;
        OCM_Management_Province__c mp1 = new OCM_Management_Province__c();
        mp1.Name = '北京';
        mp1.GI_assistant__c = u1.Id;
        mp1.SP_assistant__c = u2.Id;
        mp1.Window2__c = u3.Id;
        mp1.Admin_assistant3__c = u4.Id;
        insert mp1;
        // RecordType rt1 = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        // Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('HP').getRecordTypeId();
        // RecordType rt2 = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 呼吸科'];
        // Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_BF').getRecordTypeId();
        // RecordType rt3 = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 呼吸科'];
        // Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_BF').getRecordTypeId();

        //创建一个医院
        // 省
        Address_Level__c al = new Address_Level__c();
        al.Name = '北京';
        al.Level1_Code__c = 'CN-99';
        al.Level1_Sys_No__c = '999999';
        insert al;
        // 市
        Address_Level2__c al2 = new Address_Level2__c();
        al2.Level1_Code__c = 'CN-99';
        al2.Level1_Sys_No__c = '999999';
        al2.Level1_Name__c = '北京';
        al2.Name = '海淀区';
        al2.Level2_Code__c = 'CN-9999';
        al2.Level2_Sys_No__c = '9999999';
        al2.Address_Level__c = al.id;
        insert al2;

        // Account company = new Account();
        // company.Name = 'aiueo病院';
        // company.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('HP').getRecordTypeId();
        // company.State_Master__c = al.Id;
        // company.City_Master__c = al2.Id;
        // company.GI_Main__c = u4.id;
        // company.BF_owner__c = u4.id;
        // company.SP_Main__c  = u4.id;
        // company.URO_owner_ID__c   = u4.id;
        // company.GYN_owner__c   = u4.id;
        // company.ENT_owner_ID__c   = u4.id;
        // insert company;

        Account company = new Account();
        company.recordtypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('HP').getRecordTypeId();
        company.Name = 'test hospital';
        company.Is_Active__c = '有効';
        company.Attribute_Type__c = '卫生部';
        company.Speciality_Type__c = '综合医院';
        company.Grade__c = '一级';
        company.OCM_Category__c = 'SLTV';
        company.Is_Medical__c = '医疗机构';
        company.State_Master__c = al.id;
        company.City_Master__c = al2.id;
        company.Town__c = '东京';
        // insert company;
        
        // Account sct = [Select Id, Name, Department_Class_Label__c, Sys_Dept_Name_Change_Chk__c from Account where Parent.Id = :company.Id and RecordTypeId = :Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_BF').getRecordTypeId() Limit 1];
        
        // Account depart = new Account();
        // depart.RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_BF').getRecordTypeId();
        // depart.Name = '*';
        // depart.Department_Name__c = 'aiueo診療科';
        // depart.ParentId = sct.Id;
        // depart.Department_Class__c = sct.Id;
        // // depart.Hospital__c = company.Id;
        // depart.Department_Class_Label__c ='呼吸科';
        // depart.ownerid = u4.id;

        // insert depart;
        StaticParameter.EscapeOpportunityBefUpdTrigger = true;
        StaticParameter.EscapeOppandStaTrigger = true;
        // Opportunity opp = new Opportunity(OwnerId = u1.Id , Name = 'aiueo商談', StageName = 'contact', CloseDate=Date.today(), AccountId = company.Id,Department_Class__c = depart.id,Hospital__c=company.id);
        // insert opp;

        // 病院を作る
        Account hospital = new Account();
        hospital.recordtypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('HP').getRecordTypeId();
        hospital.Name = 'test hospital';
        hospital.Is_Active__c = '有効';
        hospital.Attribute_Type__c = '卫生部';
        hospital.Speciality_Type__c = '综合医院';
        hospital.Grade__c = '一级';
        hospital.OCM_Category__c = 'SLTV';
        hospital.Is_Medical__c = '医疗机构';
        hospital.State_Master__c = al.id;
        hospital.City_Master__c = al2.id;
        hospital.Town__c = '东京';
        // hospital.GI_Main__c = u1.Id;
        // hospital.BF_owner__c = u2.Id;
        // hospital.ET_owner__c = u2.Id;
        // hospital.SP_Main__c = u3.Id;
        // hospital.URO_owner_ID__c = u4.Id;
        // hospital.GYN_owner__c = u1.Id;
        // hospital.ENT_owner_ID__c = u1.Id;
        hospital.Energy_LeaderStr__c = '2 123';
        insert hospital;

        Test.startTest();

        Tender_information__c info = new Tender_information__c();
        Tender_information__c info2 = new Tender_information__c();
        System.runAs(u1) {
            info.OwnerId = u1.Id;
            info.Name = 'TEST001';
            info.InfoTitle__c = 'TEST001';
            info.AreaProvince__c = '北京';
            info.Hospital__c = hospital.Id;
            info.Hospital1__c = hospital.Id;
            info.Hospital2__c = hospital.Id;
            info.Hospital3__c = hospital.Id;
            info.Hospital4__c = hospital.Id;
            info.department_selection__c = lexTransferRoomCompoentController.getApiIdByValue('消化科');
            info.department_selection1__c = '消化科;呼吸科';
            info.department_selection2__c = '消化科;普外科';
            info.department_selection3__c = '消化科;妇科';
            info.department_selection4__c = '耳鼻喉科;呼吸科';
            info.notBidsSelection__c = '消化科';
            info.notBidsSelection1__c = '消化科;呼吸科';
            info.notBidsSelection2__c = '消化科;普外科';
            info.notBidsSelection3__c = '消化科;妇科';
            info.notBidsSelection4__c = '耳鼻喉科;呼吸科';
            info.IsBid__c = '否';
            info.InfoType__c = '3：结果';
            info.OpportunityNum__c = 2;
            info.TenderAssistant2__c = u2.Id;
            info.Window2__c = u2.Id;
            info.TenderAssistant1__c = u2.Id;
            info.YingyeWindow__c = u2.Id;
            info.SP_assistant__c = u2.Id;
            info.GI_assistant__c = u2.Id;
            info.ResultDate__c=Date.today();
            info.subInfoType__c='1-1：意见征集';
            info.department_selection__c = '012C80000000NG5IAM';
            if(!NFMUtil.isSandbox()){
                info.RecordTypeId = '012C80000000NJFIA2';
            } else {
                info.RecordTypeId = '012C80000000NJFIA2';
            }
            
            insert info;

            info2.OwnerId = u1.Id;
            info2.Name = 'TEST002';
            info2.InfoTitle__c = 'TEST002';
            info2.AreaProvince__c = '北京';
            info2.Hospital__c = hospital.Id;
            info2.IsBid__c = '否';
            info2.InfoType__c = '3：结果';
            info2.OpportunityNum__c = 2;
            info2.TenderAssistant2__c = u2.Id;
            info2.Window2__c = u2.Id;
            info2.TenderAssistant1__c = u2.Id;
            info2.YingyeWindow__c = u2.Id;
            info2.SP_assistant__c = u2.Id;
            info2.GI_assistant__c = u2.Id;
            info2.ResultDate__c=Date.today();
            info2.subInfoType__c='1-1：意见征集';
            // insert info2;
            StaticParameter.EscapeTOLinkTrigger = true;

            // Tender_Opportunity_Link__c BlinksList = new Tender_Opportunity_Link__c();
            // BlinksList.Opportunity__c = opp.Id;
            // BlinksList.CurrencyIsoCode = 'CNY';
            // BlinksList.Tender_information__c = info.Id;
            // BlinksList.IsRelated__c = false;
            // insert BlinksList;

             // 中标任务
            Id winBiddingTask_TaskId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('winBiddingTask').getRecordTypeId();
            Task__c tempTask = new Task__c();
            tempTask.RecordTypeId = winBiddingTask_TaskId;
            tempTask.taskDifferent__c = '被动任务';
            tempTask.taskStatus__c = '02 接受';       //任务状态2
            // tempTask.assignee__c = opp.OwnerId;    //被分配者
            // tempTask.account__c = opp.AccountId;
            tempTask.Name = '中标结果确认：' ;//+ info.TenderManageCode__c;  //招标项目.SFDC编码
            // tempTask.OwnerId = opp.OwnerId;   //所有人
            // tempTask.OpportunityId__c = opp.Id;
            tempTask.Tender_information_Task__c = info.Id;       //招标项目
            tempTask.ConfirmDate__c = Date.today();
            insert tempTask;

            // Tender_Opportunity_Link__c BlinksList2 = new Tender_Opportunity_Link__c();
            // BlinksList2.Opportunity__c = opp.Id;
            // BlinksList2.CurrencyIsoCode = 'CNY';
            // // BlinksList2.Tender_information__c = info2.Id;
            // BlinksList2.IsRelated__c = false;
            // insert BlinksList2;

            List<Tender_information__c> clist = [select id, department_category__c from Tender_information__c ];
            info.InfoType__c = '2：公告';
            info.department_selection__c = '012C80000000NG7IAM';
            info.OpportunityNum__c =3;
            update info;
        }
        
        Test.stopTest();

    }
}
       
}