global class UpdateStatuAchievementsDailyBatch implements Database.Batchable<sObject> {
    
    String query;
    
    global UpdateStatuAchievementsDailyBatch() {
        
    }
    
    global Database.QueryLocator start(Database.BatchableContext BC) {
        return Database.getQueryLocator([select Id,Name,Status_1__c,Status_1_text__c from Statu_Achievements__c where IsEqStatus_Text__c =false]);
    }

    global void execute(Database.BatchableContext BC, List<sObject> saList) {
        List<Statu_Achievements__c> updateList = new List<Statu_Achievements__c>();

        for(sObject obj :saList){
            Statu_Achievements__c sa = (Statu_Achievements__c) obj;
            if(sa.Status_1__c != sa.Status_1_text__c){
                sa.Status_1_text__c = sa.Status_1__c;
                updateList.add(sa);
            }
        }
        if(updateList.size() >0){
            update updateList;
        }
    }
    
    global void finish(Database.BatchableContext BC) {
        
    }
    
}