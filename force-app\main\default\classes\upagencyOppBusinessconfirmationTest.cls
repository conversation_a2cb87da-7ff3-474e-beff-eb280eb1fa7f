@isTest
private class upagencyOppBusinessconfirmationTest {
    public static Account account1 = new Account();
    public static Account account2 = new Account();
    public static Account account3 = new Account();
    public static Opportunity opp = new Opportunity();
    public static Agency_Opportunity__c agency_Opp = new Agency_Opportunity__c();
    public static Agency_Hospital_Link__c aHosLink = new Agency_Hospital_Link__c();
    public static User user = new User();
    public static User user2 = new User();
    public static Contact contact2 = new Contact();
    public static Contact contact = new Contact();
    private static User getUser2() {
        String timenow = Datetime.now().format('yyyyMMddHHmmss');
        // システム管理者
        User user = new User(Test_staff__c = true);
        user.LastName = '_サンブリッジ';
        user.FirstName = 'う';
        user.Alias = 'う';
        user.Email = '<EMAIL>';
        user.Username = '<EMAIL>';
        user.CommunityNickname = 'う';
        user.IsActive = true;
        user.EmailEncodingKey = 'ISO-2022-JP';
        user.TimeZoneSidKey = 'Asia/Tokyo';
        user.LocaleSidKey = 'ja_JP';
        user.LanguageLocaleKey = 'ja';
        user.ProfileId = System.Label.ProfileId_SystemAdmin;
        user.Job_Category__c = '销售推广';
        user.Province__c = '上海市';
        user.Use_Start_Date__c = Date.today().addMonths(-6);
        user.SalesManager__c = UserInfo.getUserId();
        user.BuchangApprovalManagerSales__c = UserInfo.getUserId();
        user.JingliApprovalManager__c = UserInfo.getUserId();
        user.BuchangApprovalManager__c = UserInfo.getUserId();
        user.ZongjianApprovalManager__c = UserInfo.getUserId();
        user.Dept__c = '医疗华北营业本部';
        insert user;
        return user;
    }
    static testMethod void testMethod1() {
        
        // 取引先
        account1.Name = 'test1医院';
        account1.RecordTypeId = '01210000000QemG';
        insert account1;

        account2.Name = 'test1经销商';
        account2.RecordTypeId = '01210000000Qem1';
        insert account2;


        List<Account> accTestList = [SELECT Id, Name FROM Account order by Name];
        System.debug('GYFaccTestList='+accTestList);
        System.assertEquals(10, accTestList.size());
        System.assertEquals('test1医院', accTestList[0].Name);

        //经销商医院データを作る
        aHosLink.Name = 'test1经销商医院';
        aHosLink.Hospital__c = account1.Id;
        aHosLink.Agency__c = account2.Id;
        aHosLink.Agency_Campaign_Obj__c = true;

        insert aHosLink;
        //取引先責任者
        contact2.AccountId = account2.Id;
        contact2.FirstName = '責任者';
        contact2.LastName = 'test1经销商';
        insert contact2;

        //ユーザー
        Profile p = [SELECT Id FROM Profile where Name = '901_经销商周报'];
        user.ProfileId = p.Id;
        user.ContactId = contact2.Id;
        user.IsActive = true;
        user.FirstName = 'ユーザー';
        user.LastName = 'テスト';
        user.Email = '<EMAIL>';
        user.emailencodingkey='UTF-8';
        user.languagelocalekey='zh_CN';
        user.localesidkey='ja_JP';
        user.timezonesidkey='Asia/Shanghai';
        user.Username = '<EMAIL>';
        user.Alias = 'テユ';
        user.CommunityNickname = 'テストユーザー';
        insert user;

        Id rtId = [select Id,DeveloperName from RecordType where IsActive = true and SobjectType = 'Agency_Opportunity__c' and DeveloperName = 'Opportunity'].Id;
        agency_Opp.RecordTypeId = rtId;
        agency_Opp.Name = '引合1';
        agency_Opp.Hospital_Target__c = account1.Id;
        agency_Opp.Agency__c = account2.Id;
        agency_Opp.Agency_Hospital__c = aHosLink.Id;
        agency_Opp.StageName__c = '还没申请预算';
        agency_Opp.NewOpportunity_Agency_Apply_Status__c = '批准';

        agency_Opp.OwnerId = user.Id;
        insert agency_Opp;
        aHosLink.Agency_Campaign_Obj__c = false;
        string aaa =  upagencyOppBusinessconfirmation.Businessconfirmation(agency_Opp.id);
        System.assertEquals('', aaa);
        aHosLink.Agency_Campaign_Obj__c = true;
        update aHosLink;
        aaa =  upagencyOppBusinessconfirmation.Businessconfirmation(agency_Opp.id);
        System.assertEquals('询价已经确认，不需要重复确认。', aaa);
    }
}