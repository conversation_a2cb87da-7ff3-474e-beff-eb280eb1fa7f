public without sharing class UploadOBPMController {
    private String Id { get; set; }
    private String errorStr { get; set; }
    public Boolean hasError { get; set; }
    public List<OBPMInfo> OBPMInfoList { get; set; }
    public Integer lineNo { get; set; }
    public Boolean IF_Approved { get; set; }

    public UploadOBPMController() {
        Id = ApexPages.currentPage().getParameters().get('id');
    }

    public void init(){
        hasError = false;
        OBPMInfoList = new  List<OBPMInfo>();
        
        Campaign cam = [select id, Name, Shared_User__c, IF_Approved__c, Shared_Editing__c, Carbon_Copy__c from Campaign where id = :Id];
        if (cam == null || String.isBlank(cam.Id)) {
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR, '学会不存在。'));
            hasError = true;
            return;
        }

        IF_Approved = cam.IF_Approved__c;

        List<String> user_nos = new List<String>();
        if (String.isNotBlank(cam.Shared_Editing__c)) {
            user_nos.addAll(cam.Shared_Editing__c.split(','));
        }
        if (String.isNotBlank(cam.Carbon_Copy__c)) {
            user_nos.addAll(cam.Carbon_Copy__c.split(','));
        }
        List<User> users = new List<User>();
        if (user_nos.size() > 0) {
            users = [select Id, Employee_No__c from User where Employee_No__c in :user_nos];
        }

        Integer line = 0;
        if (users != null && users.size() > 0) {
            for(User u : users) {
                line += 1;
                Campaign t_cam = new Campaign();
                t_cam.Shared_User__c = u.Id;
                OBPMInfo temp = new OBPMInfo(line, t_cam);
                if (String.isNotBlank(cam.Shared_Editing__c) && cam.Shared_Editing__c.contains(u.Employee_No__c)) {
                    temp.Role = '共同编辑人';
                } else if (String.isNotBlank(cam.Carbon_Copy__c) && cam.Carbon_Copy__c.contains(u.Employee_No__c)) {
                    temp.Role = '抄送人';
                } 
                OBPMInfoList.add(temp);
            }
            if (users.size() < 5) {
                Integer line_add = 5 - users.size();
                for (Integer i = 0; i < line_add; i++) {
                    OBPMInfo temp = new OBPMInfo(line, new Campaign());
                    OBPMInfoList.add(temp);
                }
            }
        } else {
            for (Integer i = 0; i < 5; i++) {
                line += 1;
                OBPMInfo temp = new OBPMInfo(line, new Campaign());
                OBPMInfoList.add(temp);
            }
        }
    }

    public List<SelectOption> getItems() {
        List<SelectOption> options = new List<SelectOption>();
        options.add(new SelectOption('','--无--'));
        options.add(new SelectOption('共同编辑人','共同编辑人'));
        options.add(new SelectOption('抄送人','抄送人'));
        return options;
    }

    public PageReference addLine() {
        Integer nowLine = (OBPMInfoList == null ? 0 : OBPMInfoList.size());
        OBPMInfo newopi = new OBPMInfo(nowLine + 1, new Campaign());
        List<OBPMInfo> tempList = new List<OBPMInfo>();
        for (OBPMInfo ob : OBPMInfoList) {
            tempList.add(ob);
        }
        tempList.add(newopi);
        OBPMInfoList = tempList.clone();
        return null;
    }

    public PageReference deleteLine() {
        Integer rownum = 0;
        List<OBPMInfo> tempList = new List<OBPMInfo>();
        for (OBPMInfo ob : OBPMInfoList) {
            if (ob.line != lineNo) {
                rownum += 1;
                OBPMInfo tmp = new OBPMInfo(rownum, ob.cam, ob.Role);
                tempList.add(tmp);
            }
        }
        OBPMInfoList = tempList.clone();
        return null;
    }

    public void uploadOBPM() {
        List<String> userIdList = new List<String>();
        Map<String,String> userMap = new Map<String,String>();
        List<Campaign> camList = new List<Campaign>();
        Campaign cam = [select id, Name, Shared_User__c, Shared_Editing__c, Carbon_Copy__c from Campaign where id = :Id];
        for(OBPMInfo ob : OBPMInfoList){
            if(String.isNotBlank(ob.cam.Shared_User__c) && (ob.Role == '共同编辑人' || ob.Role =='抄送人')){
                userIdList.add(ob.cam.Shared_User__c);
            }
        }
        String SharedEditing = '';
        String CarbonCopy = '';
        if(userIdList.size() > 0){
            List<User> userList = [select id, Employee_No__c from User where id = :userIdList];
            for(User user : userList){
                userMap.put(user.id, user.Employee_No__c);
            }
            for(OBPMInfo ob : OBPMInfoList){
                if(String.isNotBlank(ob.cam.Shared_User__c)){
                    if(userMap.containsKey(ob.cam.Shared_User__c)){
                        if(ob.Role == '共同编辑人'){
                            SharedEditing += String.isNotBlank(userMap.get(ob.cam.Shared_User__c))? userMap.get(ob.cam.Shared_User__c) + ',' : '';
                        } else if(ob.Role == '抄送人'){
                            CarbonCopy += String.isNotBlank(userMap.get(ob.cam.Shared_User__c))? userMap.get(ob.cam.Shared_User__c) + ',' : '';
                        }
                    }
                }
            }
        }
        //截取掉null和最后的逗号
        SharedEditing = SharedEditing.length() > 0 ? SharedEditing.substring(0, SharedEditing.lastIndexOf(',')) : '';
        CarbonCopy = CarbonCopy.length() > 0 ? CarbonCopy.substring(0, CarbonCopy.lastIndexOf(',')) : '';
        cam.Shared_Editing__c = SharedEditing;
        cam.Carbon_Copy__c = CarbonCopy;
        cam.Is_Upload_To_OBPM_Success__c = true;
        camList.add(cam);
        errorStr = '';
        Savepoint sp = Database.setSavepoint();
        try {
            if(camList.size() > 0){
                update camList;
            }
            // 703接口触发
            List<String> cam_ids = new List<String>();
            cam_ids.add(cam.Id);
            NFM703Controller.callout(null,cam_ids);

        } catch (System.Exception e) {
            Database.rollback(sp);
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR, e.getMessage()));
            errorStr = e.getMessage();
            hasError = true;
        }
    }

    //lwc改造 zzm 20231026 start
    @AuraEnabled
    public static Campaign toUploadOBPM(String recordId) {
        Campaign cam = [select id, Name,IF_Approved__c from Campaign where id = :recordId];
        return cam;

    }
    //lwc改造 zzm 20231026 end

    public class OBPMInfo {
        public Integer line { get; set; }
        public Campaign cam { get; set; }
        public String Role { get; set; }
        public OBPMInfo(Integer lineInfo, Campaign camInfo){
            line = lineInfo;
            cam = camInfo;
            Role = '';
        }
        public OBPMInfo(Integer lineInfo, Campaign camInfo, String roleInfo){
            line = lineInfo;
            cam = camInfo;
            Role = roleInfo;
        }
    }
}