global class UpdateURFContactBatch implements Database.Batchable<sObject> {
    public String query;
    public String conId;

    global UpdateURFContactBatch() {
        this.query = query;
    }
    global UpdateURFContactBatch(String conId) {
        this.query = query;
        this.conId = conId;
    }

    global Database.QueryLocator start(Database.BatchableContext bc) {
        //URF限次合同2期 LY 20220908 start
        // query = 'select Id,Maintenance_Contract__c,Series_AllCount__c,URF_Series_F__c from Maintenance_Contract_Asset__c where ';
        // query += ' Maintenance_Contract__r.Status__c = \'契約\' and  Maintenance_Contract__r.URF_Contract__c = true ';
        // if (conId != null && conId != '') {
        //     query += ' and Maintenance_Contract__c = :conId ';
        // }
        query = 'select Id,URF_Contract__c,Status__c from Maintenance_Contract__c where ';
        query += ' Status__c = \'契約\' and  URF_Contract__c = true ';
        if (conId != null && conId != '') {
            query += ' and Id = :conId ';
        }
        //URF限次合同2期 LY 20220908 end
        return Database.getQueryLocator(query);
    }

    //global void execute(Database.BatchableContext BC, list<Maintenance_Contract_Asset__c> scope) {
    global void execute(Database.BatchableContext BC, list<Maintenance_Contract__c> scope) {
        Map<String,Maintenance_Contract__c> MCMap = new Map<String,Maintenance_Contract__c>();
        //URF限次合同2期 LY 20220908 start
        //限次合同内的所有限次产品的大修次数全部使用完时，合同状态变为契約満了
        System.debug('11111111111111112222224444'+scope.size());
        List<ID> mcIDs = new List<ID>();
        for (Maintenance_Contract__c  mcID: scope){
            if (!mcIDs.contains(mcID.Id)){
                mcIDs.add(mcID.Id);
            }
        }
        System.debug('11111111111111112222223333'+mcIDs.size());
        
        List<Maintenance_Contract_Asset__c> mcList=[select Id,Maintenance_Contract__c,Series_AllCount__c,URF_Series_F__c 
                        from Maintenance_Contract_Asset__c 
                        where Maintenance_Contract__c =: mcIDs order by Series_AllCount__c];
        //URF限次合同2期 LY 20220908 end
        System.debug('1111111111111111222222'+mcList.size());
            for (Maintenance_Contract_Asset__c mca : mcList) {
                if (MCMap.containsKey(mca.Maintenance_Contract__c) && mca.Series_AllCount__c) {
                    MCMap.remove(mca.Maintenance_Contract__c);
                }else if (!MCMap.containsKey(mca.Maintenance_Contract__c) && mca.Series_AllCount__c == false) {
                    Maintenance_Contract__c mc = new Maintenance_Contract__c();
                    mc.Id = mca.Maintenance_Contract__c;
                    mc.Status__c = '契約満了';
                    mc.URFContact_EndDate__c = Date.today();
                    MCMap.put(mca.Maintenance_Contract__c, mc);
                }
                
            }
        System.debug('维修合同1111'+MCMap.values());
        if (MCMap.size() > 0) {
            update MCMap.values();
        } 
            
    }

    global void finish(Database.BatchableContext BC) {

    }
}