<aura:component implements="lightning:actionOverride,lightning:isUrlAddressable,force:lightningQuickAction,force:hasRecordId"
    access="global">
    <aura:html tag="style">
        .slds-modal__container{
            max-width: 100% !important;
            width:100% !important;
        }
        .cuf-content {
            padding: 0 0rem !important;
        }
        .slds-p-around--medium {
            padding: 0rem !important;
        }
        .slds-modal__content{
            overflow-y:auto !important;
            height:unset !important;
            max-height:unset !important;
            min-height:8rem;
        }
        .slds-tile_board{
            padding:20px 40px;
        }
    </aura:html>
    <aura:handler name="init" value="{!this}" action="{!c.redirectToURL}"/>
    <c:lexSearchReportByNameLWC />
</aura:component>