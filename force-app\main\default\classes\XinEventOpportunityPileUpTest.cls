/**
 * This class contains unit tests for validating the behavior of Apex classes
 * and triggers.
 *
 * Unit tests are class methods that verify whether a particular piece
 * of code is working properly. Unit test methods take no arguments,
 * commit no data to the database, and are flagged with the testMethod
 * keyword in the method definition.
 *
 * All test methods in an organization are executed whenever Apex code is deployed
 * to a production organization to confirm correctness, ensure code
 * coverage, and prevent regressions. All Apex classes are
 * required to have at least 75% code coverage in order to be deployed
 * to a production organization. In addition, all triggers must have some code coverage.
 * 
 * The @isTest class annotation indicates this class only contains test
 * methods. Classes defined with the @isTest annotation do not count against
 * the organization size limit for all Apex scripts.
 *
 * See the Apex Language Reference for more information about Testing and Code Coverage.
 */
@isTest
private class XinEventOpportunityPileUpTest {

    static testMethod void myUnitTest() {
        Oly_TriggerHandler.bypass('PowerBIBaseHandler');
        Opportunity opp = new Opportunity();
        opp.Name = 'aiueo';
        opp.StageName = 'contact';
        opp.CloseDate = Date.today();
        insert opp;
        
        RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        RecordType rtDoc = [select id from RecordType where IsActive = true and DeveloperName =:'Doctor'];
        
        Account acc = new Account();
        acc.RecordTypeId = rectCo.Id;
        acc.Name = '病院テスト1';
        insert acc;
        
        Contact con = new Contact();
        con.Doctor_Division1__c = '院长';
        con.LastName = 'yuanzhang';
        con.RecordTypeId = rtDoc.id;
        con.AccountId = acc.Id;
        Contact con2 = new Contact();
        con2.Doctor_Division1__c = '副主任';
        con2.LastName = 'zhuren';
        con2.RecordTypeId = rtDoc.id;
        con2.AccountId = acc.Id;
        insert new Contact[] {con, con2};
        
        Date dtDate = System.today().addDays(-4);
        Datetime dt = DateTime.now().addDays(-4);
        Daily_Report__c dr = new Daily_Report__c(reported_date__c = dtDate, Status__c = '申請中', Reporter__c = Userinfo.getUserId());
        Daily_Report__c dr2 = new Daily_Report__c(reported_date__c = dtDate.addDays(2), Status__c = '申請中', Reporter__c = Userinfo.getUserId());
        Daily_Report__c dr3 = new Daily_Report__c(reported_date__c = dtDate.addDays(4), Status__c = '作成中', Reporter__c = Userinfo.getUserId());
        Daily_Report__c dr4 = new Daily_Report__c(reported_date__c = dtDate.addDays(3), Status__c = '未承認', Reporter__c = Userinfo.getUserId());
        insert new Daily_Report__c[] {dr, dr2, dr3, dr4};
        Event__c ec = new Event__c(
            Daily_Report__c = dr.Id,
            StartDateTime__c = System.now(),
            EndDateTime__c = System.now(),
            Subject__c = 'Subject',
            Location__c = 'Location',
            ActivityDate__c = dr.reported_date__c,
            Visitor1_ID__c = con.Id,
            Visitor2_ID__c = con2.Id
        );
        Event__c ec2 = new Event__c(
            Daily_Report__c = dr2.Id,
            StartDateTime__c = System.now(),
            EndDateTime__c = System.now(),
            Subject__c = 'Subject',
            Location__c = 'Location',
            ActivityDate__c = dr2.reported_date__c,
            Visitor1_ID__c = con.Id,
            Visitor2_ID__c = con2.Id
        );
        Event__c ec3 = new Event__c(
            Daily_Report__c = dr3.Id,
            StartDateTime__c = System.now(),
            EndDateTime__c = System.now(),
            Subject__c = 'Subject',
            Location__c = 'Location',
            ActivityDate__c = dr3.reported_date__c,
            Visitor1_ID__c = con.Id,
            Visitor2_ID__c = con2.Id
        );
        Event__c ec4 = new Event__c(
            Daily_Report__c = dr4.Id,
            StartDateTime__c = System.now(),
            EndDateTime__c = System.now(),
            Subject__c = 'Subject',
            Location__c = 'Location',
            ActivityDate__c = dr4.reported_date__c,
            Visitor1_ID__c = con.Id,
            Visitor2_ID__c = con2.Id
        );
        insert new Event__c[] {ec, ec2, ec3, ec4};
        
        List<Event_Oppotunity__c> eoList = new List<Event_Oppotunity__c>();
        
        Event_Oppotunity__c eo = new Event_Oppotunity__c();
        eo.Opportunity__c = opp.Id;
        eo.End_Time__c = dt;
        eo.Daily_Report__c = dr.Id;
        eo.EventC_ID__c = ec.Id;
        eoList.add(eo);
        Event_Oppotunity__c eo2 = new Event_Oppotunity__c();
        eo2.Opportunity__c = opp.Id;
        eo2.End_Time__c = dt.addDays(2);
        eo2.Daily_Report__c = dr2.Id;
        eo2.EventC_ID__c = ec2.Id;
        eoList.add(eo2);
        Event_Oppotunity__c eo3 = new Event_Oppotunity__c();
        eo3.Opportunity__c = opp.Id;
        eo3.End_Time__c = dt.addDays(4);
        eo3.Daily_Report__c = dr3.Id;
        eo3.EventC_ID__c = ec3.Id;
        eoList.add(eo3);
        Event_Oppotunity__c eo4 = new Event_Oppotunity__c();
        eo4.Opportunity__c = opp.Id;
        eo4.End_Time__c = dt.addDays(3);
        eo4.Daily_Report__c = dr4.Id;
        eo4.EventC_ID__c = ec4.Id;
        eoList.add(eo4);
        insert eoList;

        opp = [select Xin_Last_Follow_Up_Date_For_Report__c, Xin_Gross_Follow_Num__c, Visit_President_Count__c, Visit_Head_Doctor_Count__c from Opportunity where Id = :opp.Id];
        System.assertEquals(dt.addDays(3), opp.Xin_Last_Follow_Up_Date_For_Report__c);
        System.assertEquals(3, opp.Xin_Gross_Follow_Num__c);
        System.assertEquals(3, opp.Visit_President_Count__c);
        System.assertEquals(3, opp.Visit_Head_Doctor_Count__c);

        eo.End_Time__c = dt.addDays(5);
        update eo;
        opp = [select Xin_Last_Follow_Up_Date_For_Report__c, Xin_Gross_Follow_Num__c from Opportunity where Id = :opp.Id];
        System.assertEquals(dt.addDays(5), opp.Xin_Last_Follow_Up_Date_For_Report__c);

        delete eo;
        opp = [select Xin_Last_Follow_Up_Date_For_Report__c, Xin_Gross_Follow_Num__c, Visit_President_Count__c, Visit_Head_Doctor_Count__c from Opportunity where Id = :opp.Id];
        System.assertEquals(dt.addDays(3), opp.Xin_Last_Follow_Up_Date_For_Report__c);
        System.assertEquals(2, opp.Xin_Gross_Follow_Num__c);
        System.assertEquals(2, opp.Visit_President_Count__c);
        System.assertEquals(2, opp.Visit_Head_Doctor_Count__c);
        
        delete opp;
    }
}