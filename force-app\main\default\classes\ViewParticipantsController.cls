global class ViewParticipantsController {
    public String staticResourceContact {get; set;}
    public String viewContactId {get; set;}
    public ViewParticipantsController(ApexPages.StandardController controller) {
        staticResourceContact = JSON.serialize(PIHelper.getPIIntegrationInfo('Contact'));
        SObject obj = controller.getRecord();
        if (obj.id != null||Test.isRunningTest()) {
            List<CampaignMember__c> lcm = [select ViewContactId__c from CampaignMember__c where id=:obj.id];
            if (lcm.size() > 0) {
                viewContactId = lcm[0].ViewContactId__c;
            }
        }
    }
}