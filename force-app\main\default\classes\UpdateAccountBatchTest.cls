@isTest
private class UpdateAccountBatchTest {
    @isTest static void test_method_one() {
        List<RecordType> rectHp = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
		Account myAccount1 =
            new Account(name='testaccount001',
                        RecordTypeId = rectHp[0].Id,
                        OwnerId = userinfo.getUserId(),
                       FSE_Main__c = userinfo.getUserId());
        insert myAccount1;
        System.Test.startTest();
        Id execBTId = Database.executeBatch(new UpdateAccountBatch(), 1);
        System.Test.stopTest();
        
    }

}