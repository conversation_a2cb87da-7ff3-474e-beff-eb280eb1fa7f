@isTest
private class ZCTenderHandlerTest {
    @IsTest
    static void TestMethod01() {
        ZCData__c zc = new ZCData__c();
        zc.bid__c = 'c50fb28c85d1f620876caf047cf89c44';
        zc.sfdcCode__c = '20231010';
        zc.title__c = '纤维支气管镜';
        zc.contractObject__c = '宫腔检查镜';
        zc.projectNo__c = 'ZHJLQC-【2022】025';
        zc.originalUrl__c = 'https://www.hbncp.com.cn/#/resultDetail/61883';
        zc.tenderee__c = '蕲春县刘河镇中心卫生院';
        zc.province__c = '湖北省';
        zc.city__c = '黄冈市';
        zc.district__c = '蕲春县';
        zc.supplier__c = '湖北省圆润医疗器械有限公司';
        zc.projectTotalPrice__c = '972500.00';
        zc.publishTime__c = '2023-01-05 00:00:00';
        zc.modifyTime__c = '2053-01-05 00:00:00';
        zc.hostIf__c = '否';
        zc.fundingSource__c = 'test';
        zc.projectNo__c = 'TEST20231027';
        zc.url__c = 'https://www.mdbid.cn/beaconDetail?id=BE130F74826E56B1E3992C67B0304603A3C47799FCFFD75585883BBC7FC83F727BD8A91BD1E4AD35DC45346CFD46C93350C6165690C8E7D4AAA1EA0919AA7C61';

        insert zc;

	}

    // @IsTest
    // static void TestMethod03() {
        
	// }

}