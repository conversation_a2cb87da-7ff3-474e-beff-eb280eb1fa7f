/**
Database.executeBatch(new UpdateRentalApplyEquipmentSetBatch());
system.schedule('UpdateRentalApplyEquipmentSetSchedule01','0 20 1 * * ? 2015-2035', new UpdateRentalApplyEquipmentSetSchedule());
*/
global class UpdateRentalApplyEquipmentSetSchedule implements Schedulable {
    // 每个小时的5，20，35，50分执行，即15分钟执行一次
    global void execute(SchedulableContext SC) {
        // 10备品借出申请历史ずつ処理する
        Id execBTId = Database.executeBatch(new UpdateRentalApplyEquipmentSetBatch(), 10);
    }
}