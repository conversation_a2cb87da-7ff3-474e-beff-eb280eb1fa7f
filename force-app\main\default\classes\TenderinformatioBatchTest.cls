@isTest
private class TenderinformatioBatchTest {

    @isTest
    static void test_createTaskByOppConfirmationofAward(){
        StaticParameter.EscapeContactInsUpdUser = true;
        StaticParameter.EscapeContactToUser = true;
        StaticParameter.EscapeAccountTrigger = true;

        StaticParameter.EscapeOpportunityBefUpdTrigger = true;
        StaticParameter.EscapeSyncOpportunityTrigger = true;
        StaticParameter.EscapeNFM007Trigger = true;
        StaticParameter.EscapeOpportunityHpDeptUpdTrigger = true;
        StaticParameter.EscapeOpportunityownerUpdate = true;
        StaticParameter.EscapeTOLinkTrigger = true;
        StaticParameter.EscapeEventTrigger = true;

        User user = new User(Test_staff__c = true);
        user.LastName = '_サンブリッジ';
        // user.FirstName = 'う';
        user.Alias = 'う';
        user.Email = '<EMAIL>';
        user.Username = '<EMAIL>';
        user.CommunityNickname = 'う';
        user.IsActive = true;
        user.EmailEncodingKey = 'ISO-2022-JP';
        user.TimeZoneSidKey = 'Asia/Tokyo';
        user.LocaleSidKey = 'ja_JP';
        user.LanguageLocaleKey = 'ja';
        user.ProfileId = System.Label.ProfileId_SystemAdmin;
        user.Job_Category__c = '销售推广';
        user.Province__c = '上海市';
        user.Use_Start_Date__c = Date.today().addMonths(-6);
        user.SalesManager__c = UserInfo.getUserId();
        user.BuchangApprovalManagerSales__c = UserInfo.getUserId();
        user.JingliApprovalManager__c = UserInfo.getUserId();
        user.BuchangApprovalManager__c = UserInfo.getUserId();
        user.ZongjianApprovalManager__c = UserInfo.getUserId();

        Profile p = [select id from Profile where id =:System.Label.ProfileId_SystemAdmin];
        String loginId = UserInfo.getUserId();
        User sys = [select id from User where Id = :loginId];
        User u1 = new User(Test_staff__c = true);
        u1.LastName = '123';
        // u1.FirstName = '2';
        u1.Batch_User__c = true;
        u1.Alias = '2';
        u1.Email = '<EMAIL>';
        u1.Username = '<EMAIL>';
        u1.CommunityNickname = 'あ1';
        u1.IsActive = true;
        u1.EmailEncodingKey = 'ISO-2022-JP';
        u1.TimeZoneSidKey = 'Asia/Tokyo';
        u1.LocaleSidKey = 'ja_JP';
        u1.LanguageLocaleKey = 'ja';
        u1.ProfileId = p.id;
        u1.Job_Category__c = '销售服务';
        u1.Province__c = '東京';
        u1.IsActive = true;

        User u2 = new User(Test_staff__c = true);
        u2.LastName = '_サンブリッジ';
        // u2.FirstName = 'い';
        u2.Batch_User__c = true;
        u2.Alias = 'い';
        u2.Email = '<EMAIL>';
        u2.Username = '<EMAIL>.tenopplink1';
        u2.CommunityNickname = 'い';
        u2.IsActive = true;
        u2.EmailEncodingKey = 'ISO-2022-JP';
        u2.TimeZoneSidKey = 'Asia/Tokyo';
        u2.LocaleSidKey = 'ja_JP';
        u2.LanguageLocaleKey = 'ja';
        u2.ProfileId = p.id;
        u2.Job_Category__c = '销售推广';
        u2.Province__c = '東京';
        u2.IsActive = true;
        insert u2;

        List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        if (rectCo.size() == 0) {
            return;
        }
        List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 呼吸科'];
        if (rectSct.size() == 0) {
            return;
        }
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 消化科'];
        if (rectDpt.size() == 0) {
            return;
        }


        System.runAs(new User(Id = Userinfo.getUserId())) {
            insert user;
            insert u1;

            // テストデータ
            Account company = new Account();
            company.RecordTypeId = rectCo[0].Id;
            company.Name         = '测试医院';
            insert company;
            Account section = new Account();
            section.RecordTypeId = rectSct[0].Id;
            section.Name         = '测试医院 消化科';
            section.Department_Class_Label__c = '消化科';
            section.ParentId                  = company.Id;
            section.Hospital_Department_Class__c = company.Id;
            insert section;

            Account section1 = new Account();
            section1.RecordTypeId = rectSct[0].Id;
            section1.Name         = '测试医院 消化科 消化科';
            section1.Department_Class_Label__c = '消化科';
            section1.ParentId                  = company.Id;
            section1.Hospital_Department_Class__c = company.Id;
            insert section1;

            Account depart = new Account();
            depart.RecordTypeId = rectDpt[0].Id;
            depart.Name         = '*';
            depart.Department_Name__c  = 'NFM007TestDepart';
            depart.ParentId            = section.Id;
            depart.Department_Class__c = section.Id;
            depart.Hospital__c         = company.Id;
            insert depart;

            // 插入招标项目1
            Tender_information__c info1 = new Tender_information__c();
            info1.Name = 'TEST001';
            info1.AreaProvince__c = '北京';
            info1.InfoType__c= '3：结果';
            info1.subInfoType__c = '3-5：中标通知';
            info1.OpportunityNum__c = 0;
            info1.OwnerId = u1.Id;
            info1.InfoOwner__c = u2.Id;
            info1.relativeTime__c =System.now();
            info1.IsReactionOpp__c = false;
            info1.TerminateReason__c = '经销商原因';
            insert info1;

            Opportunity opp = new Opportunity();
            // opp.AccountId           = depart.Id;
            // opp.Department_Class__c = section.Id;
            // opp.Hospital__c         = company.Id;
            //opp.SAP_Send_OK__c      = false;
            opp.Name                = 'GZ-SP-NFM007_1';
            // opp.Trade__c            = '内貿';
            opp.StageName           = '引合';
            opp.ConfirmationofAward__c='OLY中标';
            opp.CloseDate           = date.newinstance(2025, 11, 30);
            opp.OwnerId = u1.id;
            //opp.Bidding_Project_Name_Bid__c =info1.id;
            insert opp;

            Tender_Opportunity_Link__c BlinksList = new Tender_Opportunity_Link__c();
            BlinksList.Opportunity__c = opp.Id;
            BlinksList.CurrencyIsoCode = 'CNY';
            BlinksList.IsRelated__c = false;
            BlinksList.Tender_information__c = info1.Id;
            insert BlinksList;

            info1.IsTerminate__c = '是';
            info1.TerminateApprovalStatus__c = '批准';
            update BlinksList;
                  
            List<String> TenderIds = new List<String>();
            TenderIds.add(info1.Id);

            DataBase.executeBatch(new TenderinformatioBatch(TenderIds),1);
        }
    }
}