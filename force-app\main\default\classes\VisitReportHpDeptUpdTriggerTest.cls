/**
 * This class contains unit tests for validating the behavior of Apex classes
 * and triggers.
 *
 * Unit tests are class methods that verify whether a particular piece
 * of code is working properly. Unit test methods take no arguments,
 * commit no data to the database, and are flagged with the testMethod
 * keyword in the method definition.
 *
 * All test methods in an organization are executed whenever Apex code is deployed
 * to a production organization to confirm correctness, ensure code
 * coverage, and prevent regressions. All Apex classes are
 * required to have at lerpt 75% code coverage in order to be deployed
 * to a production organization. In addition, all triggers must have some code coverage.
 * 
 * The @isTest class annotation indicates this class only contains test
 * methods. Classes defined with the @isTest annotation do not count against
 * the organization size limit for all Apex scripts.
 *
 * See the Apex Language Reference for more information about Testing and Code Coverage.
 */
@isTest
private class VisitReportHpDeptUpdTriggerTest {

    static testMethod void testUpdate() {
        // recode type を取得
        List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        if (rectCo.size() == 0) {
            return;
        }
        List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 呼吸科'];
        if (rectSct.size() == 0) {
            return;
        }
        List<RecordType> rectDpt = [select Id, Name from RecordType where IsActive = true and SobjectType = 'Account' and Name IN ('診療科 消化科', '診療科 呼吸科') order by Name desc];
        if (rectDpt.size() == 0) {
            return;
        }

        // insert
        Account company1 = new Account();
        Account company2 = new Account();
        company1.RecordTypeId = rectCo[0].Id;
        company1.Name = '病院テスト1';
        company2.RecordTypeId = rectCo[0].Id;
        company2.Name = '病院テスト2';
        List<Account> hps = new Account[] {company1, company2};
        insert hps;
  
        List<Account> dc1s = [Select Id, Name, Department_Class_Label__c, Sys_Dept_Name_Change_Chk__c from Account where Parent.Id = :company1.Id order by Department_Class_Label__c];
        List<Account> dc2s = [Select Id, Name, Department_Class_Label__c, Sys_Dept_Name_Change_Chk__c from Account where Parent.Id = :company2.Id order by Department_Class_Label__c];

        Account depart1 = new Account();
        depart1.RecordTypeId = rectDpt[0].Id;
        depart1.Name         = '*';
        depart1.Department_Name__c  = '診療科1';
        depart1.ParentId            = dc1s[0].Id;
        depart1.Department_Class__c = dc1s[0].Id;
        depart1.Hospital__c         = company1.Id;
        
        Account depart2 = new Account();
        depart2.RecordTypeId = rectDpt[1].Id;
        depart2.Name         = '*';
        depart2.Department_Name__c  = '診療科2';
        depart2.ParentId            = dc2s[1].Id;
        depart2.Department_Class__c = dc2s[1].Id;
        depart2.Hospital__c         = company2.Id;

        insert new Account[] {depart1, depart2};

        //まずひとつ追加
        Visit_Report__c rpt = new Visit_Report__c();
        rpt.Department__c = depart1.Id;

        Visit_Report__c rpt2 = new Visit_Report__c();
        rpt2.Department__c = depart2.Id;

        insert new Visit_Report__c[] {rpt, rpt2};
        //まずひとつ
        Visit_Report__c rptChanged = [Select Id, Department__c, Department_Class__c, Hospital_Reference__c from Visit_Report__c where Id = :rpt.Id ];
        System.assertEquals(rptChanged.Department_Class__c, dc1s[0].Id);
        System.assertEquals(rptChanged.Hospital_Reference__c , company1.Id);

        Visit_Report__c rptChanged2 = [Select Id, Name, Department_Class__c, Hospital_Reference__c from Visit_Report__c where Id = :rpt2.Id];
        System.assertEquals(rptChanged2.Department_Class__c, dc2s[1].Id);
        System.assertEquals(rptChanged2.Hospital_Reference__c , company2.Id);
        
        rpt.Department__c = depart2.Id;
        rpt2.Department__c = depart1.Id;
        update new Visit_Report__c[] {rpt, rpt2};
        
        //まずひとつ
        rptChanged = [Select Id, Department__c, Department_Class__c, Hospital_Reference__c from Visit_Report__c where Id = :rpt.Id ];
        System.assertEquals(rptChanged.Department_Class__c, dc2s[1].Id);
        System.assertEquals(rptChanged.Hospital_Reference__c , company2.Id);

        rptChanged2 = [Select Id, Name, Department_Class__c, Hospital_Reference__c from Visit_Report__c where Id = :rpt2.Id];
        System.assertEquals(rptChanged2.Department_Class__c, dc1s[0].Id);
        System.assertEquals(rptChanged2.Hospital_Reference__c , company1.Id);

    }
}