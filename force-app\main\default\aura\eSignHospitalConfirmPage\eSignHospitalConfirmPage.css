.THIS table thead tr th:first-child 
{
    
    position: fixed;
    /*top: 45px;*/
    z-index: 2;
    /*width: 50px;*/
    background-color: white;
    padding-top:11px;
 

}
.THIS table thead  tr th:nth-child(2)
{
    /*background-color: white;
    position: fixed;
    z-index: 1;
    top: 45px;
    padding-left: 52px;
    width: 161px;*/
    padding-left: 0px;
 
}
.THIS table thead  tr th:nth-child(3)
{
    padding-left: 0px;

}
.THIS table thead  tr th:nth-child(4)
{
    padding-left: 0px;

}
.THIS table thead  tr th:nth-child(5)
{
    padding-left: 0px;

}
.THIS table thead  tr th:nth-child(6)
{
    padding-left: 0px;

}

.THIS table tbody tr th:first-child 
{
    background-color: white;
    position: fixed;
    z-index: 2;
    padding-top:9px;
    /*width: 50px;
    padding-top: 9.5px;*/
    
     
    

}

.THIS table tbody  tr th:nth-child(2)
{
    /*background-color: white;
    position: fixed;
    z-index: 1;
    padding-left: 52px;
    padding-top: 9.5px;
    height:36px;
        width: 161px;*/
     
    padding-left: 0px;

}
.THIS table tbody  tr th:nth-child(3)
{
    padding-left: 0px;

}

.THIS table tbody  tr th:nth-child(4)
{
    padding-left: 0px;

}

.THIS table tbody  tr th:nth-child(5)
{
    padding-left: 0px;

}
.THIS .slds-button_icon-bare{
    display: none;
}

.THIS #imgDiv{
    width: 200px;
    height: 200px;
    border: 1px dashed #000;
    /* margin: 0 auto; */
    margin: 10px;
    position: relative;
    display: table-cell;
    vertical-align: middle;
    text-align: center;
}

.THIS #imgDiv>img {
    width: 100%;
    height: 100%;
}

/*wangweipeng 20210616    图片预览背景图*/
.THIS #outerdiv {
    position:fixed;
    top:0;
    left:0;
    background:rgba(0,0,0,0.7);
    z-index:2;
    width:100%;
    height:100%;
    display:none;
}
/*wangweipeng 20210616    图片预览样式*/
.THIS #bigImg {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
}
/*增加文件列表删除功能  精琢技术 thh 2021-08-25 start*/
/*删除图标的样式*/
.THIS .deleteicon {
    color: #0f218b;
    width: 25px;
    height: 25px;
    /*这样才能使相邻的a看起来就隔着一个边框*/
	margin: -1px 0 0 -1px;
	float:left;
	/*使得相邻排列*/
	position:relative;
	border-bottom-left-radius: 2px;
	white-space: nowrap;
    text-overflow: ellipsis;
}

.THIS .deleteicon:hover{
	border:1px solid  #0f218b;
	border-radius:2px;
	/*这样才能使鼠标放上去的时候显示全部的蓝色框,否则因为maring的缘故，边框的右半部分会被相邻的a的边框遮盖 */
	z-index: 10;
}
/*增加文件列表删除功能  精琢技术 thh 2021-08-25 end*/
/*loaded自己加了style，得重新设置样式 精琢技术 thh 2021-09-15 start*/
.THIS #loaded {
    width: 200px;
    height: 200px;
    position: relative;
    text-align: center;
}

.THIS #loaded>img {
    width: 100%;
    height: 100%;
}
/*loaded自己加了style，得重新设置样式  精琢技术 thh 2021-09-15 end*/