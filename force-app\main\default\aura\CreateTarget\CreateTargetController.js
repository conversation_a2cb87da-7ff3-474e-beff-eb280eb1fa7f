({
	doInit : function(component, event, helper) {
        helper.doinit(component, event, helper);
    },
	toggle_report : function(component, event, helper) {
        helper.toggle_report(component, event, helper);
    },
	createTarget : function (component, event, helper) {
	    helper.createTarget(component, event, helper);
	},
	hosChange : function(component, event, helper) {
		// var hospital_name = event.getParam("value");
		// if (hospital_name.match(/(\S+\s)+/)) {
		// 	helper.searchHos(component, event, helper);
		// } else {
		// 	helper.hideSearch(component, event, helper);
		// }
		var hosId = component.get("v.record.Agency_Hospital__c");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.selectHos(component, event, helper);
		}
		
    },
    productcategoryGIChange1 : function(component, event, helper) {
    	var hosId = component.find("select_GI1").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'GI', 1);
		}
    },
    productcategoryGIChange2 : function(component, event, helper) {
    	var hosId = component.find("select_GI2").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'GI', 2);
		}
    },
    productcategoryGIChange3 : function(component, event, helper) {
    	var hosId = component.find("select_GI3").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'GI', 3);
		}
    },
	aimpricGIChange4 : function(component, event, helper) {
    	var hosId = component.find("select_GI4_2").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'GI', 3);
		}
    },


    productcategoryGSChange1 : function(component, event, helper) {
    	var hosId = component.find("select_GS1").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'GS', 1);
		}
    },
    productcategoryGSChange2 : function(component, event, helper) {
    	var hosId = component.find("select_GS2").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'GS', 2);
		}
    },
    productcategoryGSChange3 : function(component, event, helper) {
    	var hosId = component.find("select_GS3").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'GS', 3);
		}
    },
	aimpricGSChange4 : function(component, event, helper) {
    	var hosId = component.find("select_GS4").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'GS', 3);
		}
    },


    productcategoryETChange1 : function(component, event, helper) {
    	var hosId = component.find("select_ET1").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'ET', 1);
		}
    },
    productcategoryETChange2 : function(component, event, helper) {
    	var hosId = component.find("select_ET2").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'ET', 2);
		}
    },
    productcategoryETChange3 : function(component, event, helper) {
    	var hosId = component.find("select_ET3").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'ET', 3);
		}
    },
	aimpricETChange4 : function(component, event, helper) {
    	var hosId = component.find("select_ET4").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'ET', 3);
		}
    },

    productcategoryBFChange1 : function(component, event, helper) {
    	var hosId = component.find("select_BF1").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'BF', 1);
		}
    },
    productcategoryBFChange2 : function(component, event, helper) {
    	var hosId = component.find("select_BF2").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'BF', 2);
		}
    },
    productcategoryBFChange3 : function(component, event, helper) {
    	var hosId = component.find("select_BF3").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'BF', 3);
		}
    },
	aimpricBFChange4 : function(component, event, helper) {
    	var hosId = component.find("select_BF4").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'BF', 3);
		}
    },


    productcategoryOTHChange1 : function(component, event, helper) {
    	var hosId = component.find("select_OTH1").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'OTH', 1);
		}
    },
    productcategoryOTHChange2 : function(component, event, helper) {
    	var hosId = component.find("select_OTH1").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'OTH', 2);
		}
    },
    productcategoryOTHChange3 : function(component, event, helper) {
    	var hosId = component.find("select_OTH3").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'OTH', 3);
		}
    },
	aimpricOTHChange4 : function(component, event, helper) {
    	var hosId = component.find("select_OTH4").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'OTH', 3);
		}
    },

    productcategoryENTChange1 : function(component, event, helper) {
    	var hosId = component.find("select_ENT1").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'ENT', 1);
		}
    },
    productcategoryENTChange2 : function(component, event, helper) {
    	var hosId = component.find("select_ENT2").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'ENT', 2);
		}
    },
    productcategoryENTChange3 : function(component, event, helper) {
    	var hosId = component.find("select_ENT3").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'ENT', 3);
		}
    },
	aimpricENTChange4 : function(component, event, helper) {
    	var hosId = component.find("select_ENT4").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'ENT', 3);
		}
    },

    productcategoryUROChange1 : function(component, event, helper) {
    	var hosId = component.find("select_URO1").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'URO', 1);
		}
    },
    productcategoryUROChange2 : function(component, event, helper) {
    	var hosId = component.find("select_URO2").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'URO', 2);
		}
    },
    productcategoryUROChange3 : function(component, event, helper) {
    	var hosId = component.find("select_URO3").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'URO', 3);
		}
    },
	aimpricUROChange4 : function(component, event, helper) {
    	var hosId = component.find("select_URO4").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'URO', 3);
		}
    },

    productcategoryGYNChange1 : function(component, event, helper) {
    	var hosId = component.find("select_GYN1").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'GYN', 1);
		}
    },
    productcategoryGYNChange2 : function(component, event, helper) {
    	var hosId = component.find("select_GYN2").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'GYN', 2);
		}
    },
    productcategoryGYNChange3 : function(component, event, helper) {
    	var hosId = component.find("select_GYN3").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'GYN', 3);
		}
    },
	aimpricGYNChange4 : function(component, event, helper) {
    	var hosId = component.find("select_GYN4").get("v.value");
		if (hosId != null && hosId != '' && hosId != 'MALFORMED_ID') {
			helper.productcategoryChange(component, event, helper, 'GYN', 3);
		}
    },

  //   selectHos : function(component, event, helper) {
		// helper.selectHos(component, event, helper);
  //   },
})