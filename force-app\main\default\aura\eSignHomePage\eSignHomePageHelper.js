({
    init: function(cmp, event) {
        debugger
        // 创建Base64对象
        var Base64 = {
            _keyStr: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",
            encode: function(e) {
                var t = "";
                var n, r, i, s, o, u, a;
                var f = 0;
                e = Base64._utf8_encode(e);
                while (f < e.length) {
                    n = e.charCodeAt(f++);
                    r = e.charCodeAt(f++);
                    i = e.charCodeAt(f++);
                    s = n >> 2;
                    o = (n & 3) << 4 | r >> 4;
                    u = (r & 15) << 2 | i >> 6;
                    a = i & 63;
                    if (isNaN(r)) { u = a = 64 } else if (isNaN(i)) { a = 64 }
                    t = t + this._keyStr.charAt(s) + this._keyStr.charAt(o) + this._keyStr.charAt(u) + this._keyStr.charAt(a)
                }
                return t
            },
            decode: function(e) {
                var t = "";
                var n, r, i;
                var s, o, u, a;
                var f = 0;
                e = e.replace(/[^A-Za-z0-9+/=]/g, "");
                while (f < e.length) {
                    s = this._keyStr.indexOf(e.charAt(f++));
                    o = this._keyStr.indexOf(e.charAt(f++));
                    u = this._keyStr.indexOf(e.charAt(f++));
                    a = this._keyStr.indexOf(e.charAt(f++));
                    n = s << 2 | o >> 4;
                    r = (o & 15) << 4 | u >> 2;
                    i = (u & 3) << 6 | a;
                    t = t + String.fromCharCode(n);
                    if (u != 64) { t = t + String.fromCharCode(r) }
                    if (a != 64) { t = t + String.fromCharCode(i) }
                }
                t = Base64._utf8_decode(t);
                return t
            },
            _utf8_encode: function(e) {
                e = e.replace(/rn/g, "n");
                var t = "";
                for (var n = 0; n < e.length; n++) {
                    var r = e.charCodeAt(n);
                    if (r < 128) { t += String.fromCharCode(r) } else if (r > 127 && r < 2048) {
                        t += String.fromCharCode(r >> 6 | 192);
                        t += String.fromCharCode(r & 63 | 128)
                    } else {
                        t += String.fromCharCode(r >> 12 | 224);
                        t += String.fromCharCode(r >> 6 & 63 | 128);
                        t += String.fromCharCode(r & 63 | 128)
                    }
                }
                return t
            },
            _utf8_decode: function(e) {
                var t = "";
                var n = 0;
                var r = c1 = c2 = 0;
                while (n < e.length) {
                    r = e.charCodeAt(n);
                    if (r < 128) {
                        t += String.fromCharCode(r);
                        n++
                    } else if (r > 191 && r < 224) {
                        c2 = e.charCodeAt(n + 1);
                        t += String.fromCharCode((r & 31) << 6 | c2 & 63);
                        n += 2
                    } else {
                        c2 = e.charCodeAt(n + 1);
                        c3 = e.charCodeAt(n + 2);
                        t += String.fromCharCode((r & 15) << 12 | (c2 & 63) << 6 | c3 & 63);
                        n += 3
                    }
                }
                return t
            }
        };

        //判断url是否有参数
        debugger
        var flag = this.getURLISTrueParameter();
        //参数对象map
        //******** 进口单证改造 fy start
        // var paramValue = this.getURLParameterValue();
        var paramValue = this.getURLParameterValue(cmp);
        //******** 进口单证改造 fy end
        //参数对象map的长度
        var length = Object.keys(paramValue).length;

        //①一个参数 AgencyID：  
        //②两个参数 注残id：  DN号  优先跳转医院首页。

        //参数 管理编码
        var code;
        //参数 DN号
        var DNName;

        //先判断是否有参数
        if (flag) {
            //判断
            if (paramValue.AgencyID) {
                code = paramValue.AgencyID;
                //BASE64转码
                code = window.atob(code);
            } else if (paramValue.DNName) {

                DNName = paramValue.DNName;
                //base64转码
                DNName = window.atob(DNName);
            } else {
                //隐藏检索页
                cmp.set("v.DNNameInput", false);
                //报错信息
                cmp.set("v.LoadError", true);
                return;
            }

        } else {
            //显示报错页面
            //隐藏检索页
            cmp.set("v.DNNameInput", false);
            //报错信息
            cmp.set("v.LoadError", true);
            return;
        }

        // if(!paramValue.DNName&&flag){
        // 	//隐藏检索页
        // 	cmp.set("v.DNNameInput", false);
        // 	//报错信息
        // 	cmp.set("v.LoadError", true);
        // 	return ;
        // }
        // var DNName =   paramValue.DNName;

        // else if(DNName){
        // 	//隐藏检索页
        // 	cmp.set("v.DNNameInput", false);
        // 	//显示首页
        // 	cmp.set('v.buttonGroup', true);
        // 	//隐藏返回按钮
        // 	cmp.set('v.isButtonShow', false);
        // 	cmp.set('v.DNName', DNName);

        // 	var action = cmp.get("c.getDNSignUpStatus");
        // 	action.setParams({
        // 		"DNName" : DNName
        // 	});
        // 	this.showSpinner(cmp);
        // 	action.setCallback(this, function(response) {
        // 		this.hideSpinner(cmp);
        // 		var state = response.getState();

        //            if (state === "SUCCESS") {
        //            	//res里是 签收单对象 和 签收单录入表对象
        // 			var res = response.getReturnValue();
        // 			console.log('res:'+res);
        // 			//如果签收单录入表有数据，先取录入表的否则就检索签收单信息
        // 			var result;
        // 			if(res){
        // 					if(res.eSignFormEntry != null){
        // 					result = res.eSignFormEntry;
        // 					cmp.set("v.agencyScanDayBack", result.agencyScanDayBack__c);
        // 					cmp.set("v.agencySignUpDateBack", result.agencySignUpDateBack__c);
        // 					cmp.set("v.agencyConfirmDateBack",result.agencyConfirmDateBack__c);

        // 					cmp.set("v.HPScanDay", result.HPScanDay__c);
        // 					cmp.set("v.HPSignUpDate", result.HPSignUpDate__c);
        // 					cmp.set("v.salesHPManageFBDate",result.salesHPManageFBDate__c);
        // 					cmp.set("v.salesHPManageConfirmDate", result.salesHPManageConfirmDate__c);


        // 				}else{
        // 					 result = res.eSignForm;
        // 					cmp.set("v.agencyScanDayBack", result.agencyScanDay__c);
        // 					cmp.set("v.agencySignUpDateBack", result.agencySignUpDate__c);
        // 					cmp.set("v.agencyConfirmDateBack",result.agencyConfirmDate__c);

        // 					cmp.set("v.HPScanDay", result.HPScanDay__c);
        // 					cmp.set("v.HPSignUpDate", result.HPSignUpDate__c);
        // 					cmp.set("v.salesHPManageFBDate",result.salesHPManageFBDate__c);
        // 					cmp.set("v.salesHPManageConfirmDate", result.salesHPManageConfirmDate__c);
        // 				}
        // 				cmp.set("v.IsAgencyShow", res.isAgencySubmit);
        // 				cmp.set("v.IsHPShow", res.isHPSubmit);
        // 				cmp.set("v.entryType",res.entryType)
        // 			}else{
        // 					 result = res;
        // 					 cmp.set("v.IsAgencyShow", false);
        // 					 cmp.set("v.IsHPShow", false);
        // 			}

        // 			//result 是最后检索的一条数据 用于判断状态
        // 			console.log('result:'+result);

        // 			if(!result || !result.Sales_Root_Formula__c){
        // 				cmp.set("v.errorMessage", 'DN不存在，请修改DN号后重试！');
        //                    this.showErrorToast(cmp);
        // 				return;
        // 			}

        // 			cmp.set('v.eSignForm', result);
        // 			cmp.set('v.DNNameInput', false);
        // 			cmp.set('v.buttonGroup', true);

        //            }else if (state === "ERROR") {
        //            	cmp.set("v.errorMessage", '网络异常，请重新输入！');
        //                this.showErrorToast(cmp);
        // 			return;
        //            }
        //        });
        //        $A.enqueueAction(action);
        // }

        //如果管理编码不为空 则显示DN清单页
        if (code) {
            //去0操作
            var reg = new RegExp("([0]*)([1-9]+[0-9]+)", "g");

            //解码后的管理编码
            cmp.set("v.decodeCode", code.replace(reg, "$2"));
            //******** 进口单证改造 fy start
            //隐藏检索页
            // cmp.set("v.DNNameInput", true);
            //******** 进口单证改造 fy start
            //显示首页
            cmp.set('v.buttonGroup', false);
            //******** 进口单证改造 fy start
            if(paramValue.pinit=='true'){
                cmp.set("v.DNNameInput", false);
                cmp.set("v.code", code.replace(reg, "$2"));
                cmp.set('v.allWidth', document.documentElement.clientWidth - 40);
                cmp.set('v.floatWidth', document.documentElement.clientWidth * 0.85);
                cmp.set('v.floatWidth1', document.documentElement.clientWidth);
                console.log('floatWidth:' + document.documentElement.clientWidth * 0.85);
                this.getAccountHandler(cmp);
            }else{
                cmp.set("v.DNNameInput", true);
            }
            //******** 进口单证改造 fy end

        }
        //判断DNName是否为空 不为空则隐藏检索页
        else if (DNName) {
            //隐藏检索页
            cmp.set("v.DNNameInput", false);
            // //显示首页
            // cmp.set('v.buttonGroup', true);
            //隐藏返回按钮
            // cmp.set('v.isButtonShow', false);
            //去0操作
            var reg = new RegExp("([0]*)([1-9]+[0-9]+)", "g");

            cmp.set('v.DNName', DNName.replace(reg, "$2"));

            var DNName = cmp.get('v.DNName');

            var action = cmp.get("c.getDNSignUpStatus");
            action.setParams({
                "DNName": DNName
            });
            this.showSpinner(cmp);
            action.setCallback(this, function(response) {
                this.hideSpinner(cmp);
                var state = response.getState();
                debugger;
                if (state === "SUCCESS") {
                    //res里是 签收单对象 和 签收单录入表对象
                    var res = response.getReturnValue();
                    console.log('res:' + res);
                    //如果签收单录入表有数据，先取录入表的否则就检索签收单信息
                    var result;
                    if (res) {
                        if (res.eSignFormEntry != null) {
                            result = res.eSignFormEntry;
                            cmp.set("v.agencyScanDayBack", result.agencyScanDayBack__c);
                            cmp.set("v.agencySignUpDateBack", result.agencySignUpDateBack__c);
                            cmp.set("v.agencyConfirmDateBack", result.agencyConfirmDateBack__c);

                            cmp.set("v.HPScanDay", result.HPScanDay__c);
                            cmp.set("v.HPSignUpDate", result.HPSignUpDate__c);
                            cmp.set("v.salesHPManageFBDate", result.salesHPManageFBDate__c);
                            cmp.set("v.salesHPManageConfirmDate", result.salesHPManageConfirmDate__c);


                        } else {
                            result = res.eSignForm;
                            cmp.set("v.agencyScanDayBack", result.agencyScanDay__c);
                            cmp.set("v.agencySignUpDateBack", result.agencySignUpDate__c);
                            cmp.set("v.agencyConfirmDateBack", result.agencyConfirmDate__c);

                            cmp.set("v.HPScanDay", result.HPScanDay__c);
                            cmp.set("v.HPSignUpDate", result.HPSignUpDate__c);
                            cmp.set("v.salesHPManageFBDate", result.salesHPManageFBDate__c);
                            cmp.set("v.salesHPManageConfirmDate", result.salesHPManageConfirmDate__c);
                        }
                        cmp.set("v.IsAgencyShow", res.isAgencySubmit);
                        cmp.set("v.IsHPShow", res.isHPSubmit);
                        cmp.set("v.entryType", res.entryType)
                    } else {
                        result = res;
                        cmp.set("v.IsAgencyShow", false);
                        cmp.set("v.IsHPShow", false);
                    }

                    //result 是最后检索的一条数据 用于判断状态
                    // console.log('result:'+result);

                    if (!result || !result.Sales_Root_Formula__c) {
                        cmp.set("v.errorMessage", 'DN不存在，请修改DN号后重试！');
                        this.showErrorToast(cmp);
                        return;
                    }
                    cmp.set('v.eSignForm', result);
                    // 阿西赛多签收单 精琢技术 fy 2021-09-08 start
                    if (result.skip_Hospital_Sign__c) {
                    // 阿西赛多签收单 精琢技术 fy 2021-09-08 end
                        //根据不同情况返回对应的提示信息  精琢技术 thh 2021-10-11 start
                        cmp.set("v.errorMessage", result.skip_Hospital_Sign_Message__c);
                        //根据不同情况返回对应的提示信息  精琢技术 thh 2021-10-11 end
                        this.showErrorToast(cmp);
                        return;
		    // 阿西赛多签收单 精琢技术 fy 2021-09-08 start
                    } else if (result.skip_Distribution_Sign__c) {
		    // 阿西赛多签收单 精琢技术 fy 2021-09-08 end
                        this.HPClick(cmp);

                        // cmp.set("v.HPPage", true);
                        // cmp.set("v.isDirectSales", true);
                        // cmp.set("v.IsHPShow", false);

                    } else if (result.Sales_Root_Formula__c == '販売店') {
                        this.HPhandleClick(cmp);
                        // cmp.set("v.HPPage", true);
                        // cmp.set("v.isDirectSales", false);
                        // cmp.set("v.IsHPShow", false);

                    }

                    cmp.set('v.allWidth', document.documentElement.clientWidth - 40);
                    cmp.set('v.floatWidth', document.documentElement.clientWidth * 0.9);
                    cmp.set('v.floatWidth1', document.documentElement.clientWidth);
                    // cmp.set('v.DNNameInput', false);
                    // cmp.set('v.buttonGroup', true);

                } else if (state === "ERROR") {
                    cmp.set("v.errorMessage", '网络异常，请重新输入！');
                    this.showErrorToast(cmp);
                    return;
                }
            });
            $A.enqueueAction(action);
        } else {
            //显示报错页面
            //隐藏检索页
            cmp.set("v.DNNameInput", false);
            //报错信息
            cmp.set("v.LoadError", true);
            return;
        }


    },
    getStatusHandler: function(cmp) {

        var DNName = cmp.get("v.DNName");
        var action = cmp.get("c.getDNSignUpStatus");
        action.setParams({
            "DNName": DNName
        });
        this.showSpinner(cmp);
        action.setCallback(this, function(response) {
            this.hideSpinner(cmp);
            var state = response.getState();

            if (state === "SUCCESS") {
                //res里是 签收单对象 和 签收单录入表对象
                var res = response.getReturnValue();
                console.log('res:' + res);
                //如果签收单录入表有数据，先取录入表的否则就检索签收单信息
                var result;
                if (res) {
                    if (res.eSignFormEntry != null) {
                        result = res.eSignFormEntry;
                        cmp.set("v.agencyScanDayBack", result.agencyScanDayBack__c);
                        cmp.set("v.agencySignUpDateBack", result.agencySignUpDateBack__c);
                        cmp.set("v.agencyConfirmDateBack", result.agencyConfirmDateBack__c);

                        cmp.set("v.HPScanDay", result.HPScanDay__c);
                        cmp.set("v.HPSignUpDate", result.HPSignUpDate__c);
                        cmp.set("v.salesHPManageFBDate", result.salesHPManageFBDate__c);
                        cmp.set("v.salesHPManageConfirmDate", result.salesHPManageConfirmDate__c);


                    } else {
                        result = res.eSignForm;
                        cmp.set("v.agencyScanDayBack", result.agencyScanDay__c);
                        cmp.set("v.agencySignUpDateBack", result.agencySignUpDate__c);
                        cmp.set("v.agencyConfirmDateBack", result.agencyConfirmDate__c);

                        cmp.set("v.HPScanDay", result.HPScanDay__c);
                        cmp.set("v.HPSignUpDate", result.HPSignUpDate__c);
                        cmp.set("v.salesHPManageFBDate", result.salesHPManageFBDate__c);
                        cmp.set("v.salesHPManageConfirmDate", result.salesHPManageConfirmDate__c);
                    }
                    cmp.set("v.IsAgencyShow", res.isAgencySubmit);
                    cmp.set("v.IsHPShow", res.isHPSubmit);
                    cmp.set("v.entryType", res.entryType)
                } else {
                    result = res;
                    cmp.set("v.IsAgencyShow", false);
                    cmp.set("v.IsHPShow", false);
                }
// result
                //result 是最后检索的一条数据 用于判断状态
                // console.log('result:'+result);

                if (!result || !result.Sales_Root_Formula__c) {
                    cmp.set("v.errorMessage", 'DN不存在，请修改DN号后重试！');
                    this.showErrorToast(cmp);
                    return;
                }
                cmp.set('v.eSignForm', result);
		// 阿西赛多签收单 精琢技术 fy 2021-09-08 start
                if (result.skip_Distribution_Sign__c) {
		// 阿西赛多签收单 精琢技术 fy 2021-09-08 end
                    this.HPClick(cmp);

                    // cmp.set("v.HPPage", true);
                    // cmp.set("v.isDirectSales", true);
                    // cmp.set("v.IsHPShow", false);

                } else if (result.Sales_Root_Formula__c == '販売店') {
                    this.HPhandleClick(cmp);
                    // cmp.set("v.HPPage", true);
                    // cmp.set("v.isDirectSales", false);
                    // cmp.set("v.IsHPShow", false);

                }

                cmp.set('v.allWidth', document.documentElement.clientWidth - 40);
                cmp.set('v.floatWidth', document.documentElement.clientWidth * 0.9);
                cmp.set('v.floatWidth1', document.documentElement.clientWidth);
                // cmp.set('v.DNNameInput', false);
                // cmp.set('v.buttonGroup', true);

            } else if (state === "ERROR") {
                cmp.set("v.errorMessage", '网络异常，请重新输入！');
                this.showErrorToast(cmp);
                return;
            }
        });
        $A.enqueueAction(action);
    },
    //获取客户id + 记录类型
    getAccountHandler: function(cmp) {

        var code = cmp.get("v.code");

        var decodeCode = cmp.get("v.decodeCode");

        //校验
        if (code == decodeCode) {
            //①调用后台方法 根据url上的管理编码返回客户id和记录类型
            var action = cmp.get("c.getAccount");
            action.setParams({
                "managementCode": code
            });
            action.setCallback(this, function(response) {
                var state = response.getState();

                if (state === "SUCCESS") {
                    var res = response.getReturnValue();
                    console.log('res:' + res);
                    //返回客户 id ,记录类型
                    if (res) {
                        if (res.accountId != null) {
                            cmp.set("v.accountId", res.accountId);
                        }
                        if (res.recordTypeName != null) {
                            cmp.set("v.recordTypeName", res.recordTypeName);
                        }
                        if (res.agencyName != null) {
                            cmp.set("v.agencyName", res.agencyName);
                        }
                        //隐藏校验页面
                        cmp.set('v.DNNameInput', false);
                        //显示清单页面
                        cmp.set('v.DNListPage', true);

                    } else {
                        cmp.set("v.accountId", '');
                        cmp.set("v.recordTypeName", '');
                        cmp.set("v.agencyName", '');
                        cmp.set("v.errorMessage", '您输入的电子签收ID有误,请再确认！');
                        this.showErrorToast(cmp);
                        return;
                    }
                    //调用一下 后台
                    this.getDNListHandler(cmp);
                } else if (state === "ERROR") {
                    console.log('There was a problem : ' + response.getError());
                    cmp.set("v.errorMessage", '网络异常，请重新输入！');
                    this.showErrorToast(cmp);
                    return;
                }
            });
            $A.enqueueAction(action);
        } else {
            cmp.set("v.errorMessage", '您输入的电子签收ID有误！');
            this.showErrorToast(cmp);
            return;
        }

    },
    //获取客户id + 记录类型
    getDNListHandler: function(cmp) {

        //②根据id和记录类型 去调用后台方法 返回DN签收list

        var id = cmp.get("v.accountId");
        var type = cmp.get("v.recordTypeName");
        if (id != '' && type != '') {
            var excute = cmp.get("c.getDNSignUpLists");
            excute.setParams({
                "id": id,
                "type": type
            });

            this.showSpinner(cmp);
            excute.setCallback(this, function(response) {
                this.hideSpinner(cmp);
                var state = response.getState();

                if (state === "SUCCESS") {
                    //res里是 签收单对象 和 签收单录入表对象
                    var res = response.getReturnValue();
                    console.log('res:' + res);
                    //如果签收单录入表有数据，先取录入表的否则就检索签收单信息
                    var result;
                    //存放录入表list
                    var eSignFormEntryList=[];
                    //存放签收单list
                    var eSignFormList=[];
                    
                    //fy 20220727 start
                    //存放附件名
                    var AttachmentMap = new Map();
                    //fy 20220727 end
                    if (res) {
                        if (res.eSignFormEntryList != undefined) {
                            eSignFormEntryList = res.eSignFormEntryList;
                        }
                        if (res.eSignFormList != undefined) {
                            eSignFormList = res.eSignFormList;
                        }
                        //fy 20220727 start
                        if (res.AttachmentStringList!=undefined) {
                            for(var i = 0; i < res.AttachmentStringList.length; i++){
                                var liststr=[];
                                liststr=res.AttachmentStringList[i].split('_');
                                if(liststr.length>0){
                                    AttachmentMap.set(liststr[0],liststr[1]);
                                }
                            }
                        }
                        //fy 20220727 end
                        var today = this.getNowFormatDate();
                        if (eSignFormEntryList) {
                            for (var i = 0; i < eSignFormList.length; i++) {
                                for (var j = 0; j < eSignFormEntryList.length; j++) {
                                    //如果未处理的录入表中有已经确认的 则原data移除该DN号
                                    if ((eSignFormList[i].DNNameNo0__c == eSignFormEntryList[j].DNNameNo0__c) && !eSignFormEntryList[j].IsHandled__c) {
                                        //重构一下状态
                                        eSignFormList[i].agencyDNSignUpStatus__c = eSignFormEntryList[j].agencyDNSignUpStatus__c;
                                        eSignFormList[i].HPDNSignUpStatus__c = eSignFormEntryList[j].HPDNSignUpStatus__c;
                                        eSignFormList[i].agencySignUpDate__c = eSignFormEntryList[j].agencySignUpDateBack__c;
                                        //即便eSignFormEntryList[j].endConfirmDate__c 日期为null ；日期<今天为false
                                        //也不移除，因为反正也要保留3天 15分钟后就会更上日期
                                        if ((eSignFormEntryList[j].isProcessed__c || eSignFormEntryList[j].DNSignUpStatus__c) && eSignFormEntryList[j].endConfirmDate__c < today) {
                                            //移除数组中的已确认的数据
                                            eSignFormList.splice(i, 1);
                                        }

                                    }
                                    //如果最终用户是契约，则医院状态赋值为N/A
				    // 阿西赛多签收单 精琢技术 fy 2021-09-08 start
                                    if (eSignFormList[i].skip_Hospital_Sign__c) {
				    // 阿西赛多签收单 精琢技术 fy 2021-09-08 end
                                        eSignFormList[i].HPDNSignUpStatus__c = 'N/A(不适用)x1';
                                    }

                                }
                            }
                            result = eSignFormList;
                        } else {
                            //如果最终用户是契约，则医院状态赋值为N/A
                            for (var i = 0; i < eSignFormList.length; i++) {
			    // 阿西赛多签收单 精琢技术 fy 2021-09-08 start
                                if (eSignFormList[i].skip_Hospital_Sign__c) {
			    // 阿西赛多签收单 精琢技术 fy 2021-09-08 end
                                    eSignFormList[i].HPDNSignUpStatus__c = 'N/A(不适用)x2';
                                }
                            }
                            result = eSignFormList;
                        }

                    } else {
                        result = res;
                    }

                    var pageSize = cmp.get('v.pageSize');
                    //fy 20220727 start
                    debugger
                    if(res){
                        if(AttachmentMap.size>0){
                            for(var i = 0; i < result.length; i++){
                                if(AttachmentMap.has(result[i].Id)){
                                    result[i].AttachmentName=AttachmentMap.get(result[i].Id);
                                }
                            }
                        }
                    }
                    //fy 20220727 end
                    cmp.set("v.data", result);
                    var data = result;
                    cmp.set("v.lineItemSize", data.length);
                    //计算所有页
                    var allPage = this.pageCount(data.length, pageSize);
                    var tempData = this.getTempData(data, 1, pageSize);
                    cmp.set("v.isLastPage", this.isLastPage(data, tempData));
                    cmp.set("v.allPage", allPage);
                    cmp.set("v.dataSize", tempData.length);
                    cmp.set("v.currentData", tempData);
                    cmp.set('v.DNNameInput', false);
                    cmp.set('v.buttonGroup', false);
                    cmp.set('v.DNListPage', true);

                } else if (state === "ERROR") {
                    cmp.set("v.errorMessage", '网络异常，请重新输入！');
                    this.showErrorToast(cmp);
                    return;
                }
            });
            $A.enqueueAction(excute);

        }
    },
    HPhandleClick: function(cmp) {
debugger
        var eSignForm = cmp.get('v.eSignForm');
        var IsShow = cmp.get('v.IsHPShow');
        var IsAgencyShow = cmp.get('v.IsAgencyShow');
        var entryType = cmp.get('v.entryType');
        var HPStatus;
        if (eSignForm.HPSignUpStatus__c) {
            HPStatus = eSignForm.HPSignUpStatus__c;
        } else {
            HPStatus = '';
        }
        if (HPStatus == '不批准') {
            cmp.set('v.IsHPShow', false);
        }
        IsShow = cmp.get('v.IsHPShow');
        //用于判断经销商已签收或者已提交后 医院二维码则可以扫入
        var date;
        if (eSignForm.agencySignUpDate__c) {
            date = eSignForm.agencySignUpDate__c;
        } else if (eSignForm.agencySignUpDateBack__c) {
            date = eSignForm.agencySignUpDateBack__c;

        } else {
            date = '';
        }
	// 阿西赛多签收单 精琢技术 fy 2021-09-08 start
        if (eSignForm.skip_Hospital_Sign__c) {
	// 阿西赛多签收单 精琢技术 fy 2021-09-08 end
            //根据不同情况返回对应的提示信息  精琢技术 thh 2021-10-11 start
            cmp.set("v.errorMessage", eSignForm.skip_Hospital_Sign_Message__c);
            //根据不同情况返回对应的提示信息  精琢技术 thh 2021-10-11 end
            this.showErrorToast(cmp);
        }
        //①先区分直销还是分销
        else if (eSignForm.Sales_Root_Formula__c == '販売店' && !date && HPStatus == ''&&!eSignForm.skip_Distribution_Sign__c) {
            cmp.set("v.errorMessage", '请等待经销商发货!');
            this.showErrorToast(cmp);
            //②判断是否已经审批完毕
        } else if (HPStatus == '申请中') {
            cmp.set("v.errorMessage", '医院签收结果审批中，请等待！');
            this.showErrorToast(cmp);
            // cmp.set("IsShow", true);
        } else if (IsShow && (HPStatus == '' || HPStatus == '申请中' || HPStatus == '草案中')) {
            cmp.set("v.errorMessage", '医院签收数据已提交，请等待！');
            this.showErrorToast(cmp);
	    // 阿西赛多签收单 精琢技术 fy 2021-09-08 start
        } else if (eSignForm.salesHPManageConfirmDate__c && !eSignForm.skip_Distribution_Sign__c) {
	// 阿西赛多签收单 精琢技术 fy 2021-09-08 end
            cmp.set("v.errorMessage", '医院签收结果已确认x3！');
            this.showErrorToast(cmp);
            cmp.set('v.eSignHospitalConfirmReadOnlyPage', true);
            cmp.set('v.buttonGroup', false);
	    // 阿西赛多签收单 精琢技术 fy 2021-09-08 start
        } else if (eSignForm.HPConfirmDate__c && eSignForm.skip_Distribution_Sign__c) {
	// 阿西赛多签收单 精琢技术 fy 2021-09-08 end
            cmp.set("v.errorMessage", '医院签收结果已确认x4！');
            this.showErrorToast(cmp);
            cmp.set('v.eSignHospitalConfirmReadOnlyPage', true);
            cmp.set('v.DirectFlag', true);
            cmp.set('v.buttonGroup', false);
        } else {
            //验证结果 为空 或者 (验证结果不为空&& 审批状态为草案中)
            // && IsAgencyShow
            if (!eSignForm.HPAcceptResult__c || (eSignForm.HPAcceptResult__c &&
                    (!HPStatus || HPStatus == '草案中' || (HPStatus == '不批准')))) {
                //判断直销还是分销
		// 阿西赛多签收单 精琢技术 fy 2021-09-08 start
                if (eSignForm.skip_Distribution_Sign__c) {
		// 阿西赛多签收单 精琢技术 fy 2021-09-08 end
                    cmp.set('v.isDirectSales', true);
                }
                cmp.set('v.HPPage', true);
                cmp.set('v.buttonGroup', false);
            } else if ((eSignForm.HPAcceptResult__c == '部分验收' ||
                    eSignForm.HPAcceptResult__c == '整单非完好') &&
                !eSignForm.salesHPManageConfirmDate__c
            ) {
                cmp.set("v.errorMessage", '营业管理课处理中！');
                this.showErrorToast(cmp);
                return;
            } else if (eSignForm.HPAcceptResult__c == '部分验收' ||
                eSignForm.HPAcceptResult__c == '整单非完好'
            ) {
                cmp.set('v.HPPConfirmage', true);
                cmp.set('v.buttonGroup', false);

            } else {
                cmp.set("v.errorMessage", '网络异常，请返回重新检索DN号！');
                this.showErrorToast(cmp);
                return;
            }
        }

        // cmp.set('v.buttonGroup', false);
        // cmp.set('v.HPPage',true);
    },
    agencyhandleClick: function(cmp) {
        var eSignForm = cmp.get('v.eSignForm');
        var IsShow = cmp.get('v.IsAgencyShow');
        var entryType = cmp.get('v.entryType');
        var status;
        if (eSignForm.agencyAutoSignUpStatus__c) {
            status = eSignForm.agencyAutoSignUpStatus__c;
        } else {
            status = '';
        }

        //①先区分直销还是分销
	// 阿西赛多签收单 精琢技术 fy 2021-09-08 start
        if (eSignForm.skip_Distribution_Sign__c) {
	// 阿西赛多签收单 精琢技术 fy 2021-09-08 end
            //根据不同情况返回对应的提示信息  精琢技术 thh 2021-10-09 start
            cmp.set("v.errorMessage", eSignForm.skip_Distribution_Sign_Message__c);
            //根据不同情况返回对应的提示信息  精琢技术 thh 2021-10-09 start
            this.showErrorToast(cmp);
            //②判断是否已经审批完毕
        } else if (status == '申请中') {
            cmp.set("v.errorMessage", '经销商签收结果审批中，请等待！');
            this.showErrorToast(cmp);
        } else if (IsShow && (status == '' || status == '申请中' || status == '草案中')) {
            cmp.set("v.errorMessage", '经销商签收数据已提交，请等待！');
            this.showErrorToast(cmp);
        } else if (eSignForm.agencyConfirmDate__c != null) {
            cmp.set("v.errorMessage", '经销商签收结果已确认！');
            this.showErrorToast(cmp);
            cmp.set('v.agencyConfirmReadOnlyPage', true);
            cmp.set('v.buttonGroup', false);
        } else {
            //验证结果 为空 或者 (验证结果不为空&& 审批状态为草案中)
            if (!eSignForm.agencyAcceptResult__c || (eSignForm.agencyAcceptResult__c &&
                    (!status || status == '草案中' || status == '不批准'))) {
                cmp.set('v.agencyPage', true);
                cmp.set('v.buttonGroup', false);
            } else if (eSignForm.agencyAcceptResult__c == '全部验收' && eSignForm.agencyConfirmDate__c) {
                cmp.set("v.errorMessage", '经销商已经验收完毕！');
                this.showErrorToast(cmp);
                cmp.set('v.agencyConfirmReadOnlyPage', true);
                cmp.set('v.buttonGroup', false);
            } else if ((eSignForm.agencyAcceptResult__c == '部分验收' ||
                    eSignForm.agencyAcceptResult__c == '整单非完好') &&
                !eSignForm.salesManageConfirmDate__c
            ) {
                cmp.set("v.errorMessage", '营业管理课处理中！');
                this.showErrorToast(cmp);
                return;
            } else if (eSignForm.agencyAcceptResult__c == '部分验收' ||
                eSignForm.agencyAcceptResult__c == '整单非完好'
            ) {
                cmp.set('v.agencyConfirmPage', true);
                cmp.set('v.buttonGroup', false);

            } else {
                cmp.set("v.errorMessage", '网络异常，请返回重新检索DN号！');
                this.showErrorToast(cmp);
                return;
            }
        }


    },
    // 明细页 table 首页功能实现
    handleHome: function(cmp) {
        var pageSize = cmp.get('v.pageSize');
        var pageNumber = cmp.get('v.pageNumber');
        // var data = cmp.get('v.data');
        var currentData = cmp.get('v.currentData');

        //获取输入的value值
        var key = document.getElementById('searchInput').value;
        var data = cmp.get('v.data');
        var tempDataList = [];
        var pageSize = cmp.get('v.pageSize');

        if (key) {
            //模糊检索
            tempDataList = this.filterByName(data, key);
            //设置分页
            var tempData = this.getTempData(tempDataList, 1, pageSize);

            cmp.set("v.lineItemSize", tempDataList.length);
            //计算所有页
            var allPage = this.pageCount(tempDataList.length, pageSize);
            cmp.set("v.allPage", allPage);
            cmp.set('v.pageNumber', 1);
            console.log('currentData1:' + currentData);
            cmp.set('v.currentData', tempData);
            cmp.set('v.data', data);
            cmp.set("v.isLastPage", this.isLastPage(tempDataList, tempData));
            cmp.set("v.searchSize", tempDataList.length);


        } else {
            //显示全部箱
            var tempData = this.getTempData(data, 1, pageSize);
            cmp.set('v.pageNumber', 1);
            console.log('currentData1:' + currentData);
            cmp.set('v.currentData', tempData);
            cmp.set('v.data', data);
            cmp.set("v.isLastPage", this.isLastPage(data, tempData));
            cmp.set('v.searchSize', null);

        }



    },
    // 明细页 table 尾页功能实现
    handleLast: function(cmp) {
        var pageSize = cmp.get('v.pageSize');
        var pageNumber = cmp.get('v.pageNumber');
        var data = cmp.get('v.data');
        //获取数据长度
        var size = data.length;
        //获取尾页页码
        var pages = size % pageSize == 0 ? (size / pageSize) : (Math.floor(size / pageSize) + 1);

        var currentData = cmp.get('v.currentData');

        //获取输入的value值
        var key = document.getElementById('searchInput').value;
        var tempDataList = [];

        if (key) {
            //模糊检索
            tempDataList = this.filterByName(data, key);
            //设置分页
            var tempData = this.getTempData(tempDataList, pages, pageSize);
            cmp.set("v.lineItemSize", tempDataList.length);
            //计算所有页
            var allPage = this.pageCount(tempDataList.length, pageSize);
            cmp.set("v.allPage", allPage);
            cmp.set('v.pageNumber', pages);
            console.log('currentData1:' + currentData);
            cmp.set('v.currentData', tempData);
            cmp.set('v.data', data);
            cmp.set("v.isLastPage", this.isLastPage(tempDataList, tempData));
            cmp.set("v.searchSize", tempDataList.length);


        } else {
            //显示全部箱
            var tempData = this.getTempData(data, pages, pageSize);
            cmp.set('v.pageNumber', pages);
            console.log('currentData1:' + currentData);
            cmp.set('v.currentData', tempData);
            cmp.set('v.data', data);
            cmp.set("v.isLastPage", this.isLastPage(data, tempData));
            cmp.set('v.searchSize', null);

        }





    },
    // 明细页 table 下一页功能实现
    handleNext: function(cmp) {
        var pageSize = cmp.get('v.pageSize');
        var pageNumber = cmp.get('v.pageNumber');
        var data = cmp.get('v.data');
        var currentData = cmp.get('v.currentData');

        //获取输入的value值
        var key = document.getElementById('searchInput').value;
        var tempDataList = [];

        if (key) {
            //模糊检索
            tempDataList = this.filterByName(data, key);
            //设置分页
            var tempData = this.getTempData(tempDataList, pageNumber + 1, pageSize);
            cmp.set('v.pageNumber', pageNumber + 1);
            cmp.set("v.lineItemSize", tempDataList.length);
            //计算所有页
            var allPage = this.pageCount(tempDataList.length, pageSize);
            cmp.set("v.allPage", allPage);
            console.log('currentData1:' + currentData);
            cmp.set('v.currentData', tempData);
            cmp.set('v.data', data);
            cmp.set("v.isLastPage", this.isLastPage(tempDataList, tempData));
            cmp.set("v.searchSize", tempDataList.length);


        } else {
            //显示全部箱
            var tempData = this.getTempData(data, pageNumber + 1, pageSize);
            cmp.set('v.pageNumber', pageNumber + 1);
            console.log('currentData1:' + currentData);
            cmp.set('v.currentData', tempData);
            cmp.set('v.data', data);
            cmp.set("v.isLastPage", this.isLastPage(data, tempData));
            cmp.set('v.searchSize', null);

        }




    },
    // 明细页 table 上一页功能实现
    handlePrev: function(cmp) {
        var pageSize = cmp.get('v.pageSize');
        var pageNumber = cmp.get('v.pageNumber');
        var data = cmp.get('v.data');
        var currentData = cmp.get('v.currentData');
        //获取输入的value值
        var key = document.getElementById('searchInput').value;
        var tempDataList = [];

        if (key) {
            //模糊检索
            tempDataList = this.filterByName(data, key);
            //设置分页
            cmp.set("v.lineItemSize", tempDataList.length);
            //计算所有页
            var allPage = this.pageCount(tempDataList.length, pageSize);
            cmp.set("v.allPage", allPage);
            var tempData = this.getTempData(tempDataList, pageNumber - 1, pageSize);
            console.log('currentData2:' + currentData);
            cmp.set('v.pageNumber', pageNumber - 1);
            cmp.set('v.currentData', tempData);
            cmp.set('v.data', data);
            cmp.set("v.isLastPage", this.isLastPage(tempDataList, tempData));
            cmp.set("v.searchSize", tempDataList.length);


        } else {
            //显示全部箱
            var tempData = this.getTempData(data, pageNumber - 1, pageSize);
            console.log('currentData2:' + currentData);
            cmp.set('v.pageNumber', pageNumber - 1);
            cmp.set('v.currentData', tempData);
            cmp.set('v.data', data);
            cmp.set("v.isLastPage", this.isLastPage(data, tempData));
            cmp.set('v.searchSize', null);

        }


    },
    // 计算并返回当前页码的数据
    getTempData: function(data, pageNumber, pageSize) {
        var tempData = data.slice((pageNumber - 1) * pageSize, pageNumber * pageSize);
        return tempData;
    },
    //计算所有页
    pageCount: function(totalnum, limit) {
        return totalnum > 0 ? ((totalnum < limit) ? 1 : ((totalnum % limit) ? (parseInt(totalnum / limit) + 1) : (totalnum / limit))) : 0;
    },
    // 判断当前页是否是最后一页
    isLastPage: function(data, tempData) {
        if (tempData.length == 0 ||
            tempData[tempData.length - 1].Id == data[data.length - 1].Id) {
            return true;
        } else {
            return false;
        }
    },
    //返回按钮
    backButtonHandler: function(cmp) {
        cmp.set('v.DNNameInput', true);
        cmp.set('v.buttonGroup', false);

    },
    //弹出 错误提示toast 方法
    showErrorToast: function(cmp) {
        const width = document.documentElement.clientWidth;
        const contentDOM1 = document.getElementById('errorSonDiv1');
        contentDOM1.style.width = width * 0.5 + 'px';
        const contentDOM2 = document.getElementById('errorSonDiv2');
        contentDOM2.style.width = width * 0.38 + 'px';
        contentDOM2.style.height = '4rem';
        const contentDOM3 = document.getElementById('errorSonDiv3');
        contentDOM3.style.width = width - 150 + 'px';

        $A.util.removeClass(cmp.find('errorDiv'), 'slds-hide');
        window.setTimeout($A.getCallback(function() {
                $A.util.addClass(cmp.find('errorDiv'), 'slds-hide');
            }),
            3000
        );
    },
    // function automatic called by aura:waiting event
    showSpinner: function(cmp) {
        // remove slds-hide class from mySpinner
        var spinner = cmp.find("mySpinner");
        $A.util.removeClass(spinner, "slds-hide");
    },
    // function automatic called by aura:doneWaiting event
    hideSpinner: function(cmp) {
        // add slds-hide class from mySpinner
        var spinner = cmp.find("mySpinner");
        $A.util.addClass(spinner, "slds-hide");
    }, //******** 进口单证改造 fy start
    getURLParameterValue: function(cmp) {
    // getURLParameterValue: function() {
       //******** 进口单证改造 fy end

        var querystring = window.location.search.substr(1);
        var paramValue = {};
        querystring.split("&").forEach(function(part) {
            var param = part.split("=");
            paramValue[param[0]] = decodeURIComponent(param[1]);
            //******** 进口单证改造 fy start
            if(param[0]=='AgencyID'){
                cmp.set("v.AgencyIDM", param[1]);
            }
            //******** 进口单证改造 fy end
        });

        //console.log('paramValue-' + paramValue);
        return paramValue;
    },
    getURLISTrueParameter: function() {

        var url = window.location.search;
        var flag;
        if (url.indexOf("?") != -1) {
            flag = true;
        } else {
            flag = false;
        }
        //console.log('paramValue-' + paramValue);
        return flag;
    },
    handlerMessageEvt: function(cmp, event) {
        var DNName = event.getParam("DNName");
        var ShowDNNameQR = event.getParam("ShowDNNameQR");

        //经销商签收状态
        var agencyDNSignUpStatus = event.getParam("agencyDNSignUpStatus");
        //医院签收状态
        var HPDNSignUpStatus = event.getParam("HPDNSignUpStatus");
        //dn号备用
        var DNNameSpare = event.getParam("DNNameSpare");
        //经销商签收日期
        var agencySubmit = event.getParam("agencySubmit");

        if (DNName) {
            this.getStatusHandler(cmp);
            cmp.set('v.ShowDNNameQR', ShowDNNameQR);
        } else {

            //遍历前台data 用于更新最新状态
            var data = cmp.get('v.data');

            for (var i = 0; i < data.length; i++) {

                if (data[i].DNNameNo0__c == DNNameSpare) {
                    //重构一下状态
                    if (agencyDNSignUpStatus) {
                        data[i].agencyDNSignUpStatus__c = agencyDNSignUpStatus;
                    }
                    if (HPDNSignUpStatus) {
                        data[i].HPDNSignUpStatus__c = HPDNSignUpStatus;
                    }
                    if (agencySubmit) {
                        data[i].agencySignUpDate__c = agencySubmit;
                    }





                }


            }
            cmp.set('v.data', data);
            cmp.set('v.agencyPage', false);
            cmp.set('v.agencyConfirmPage', false);
            cmp.set('v.agencyConfirmReadOnlyPage', false);
            cmp.set('v.HPPage', false);
            cmp.set('v.isDirectSales', false);
            cmp.set('v.HPPConfirmage', false);
            cmp.set('v.eSignHospitalConfirmReadOnlyPage', false);
            cmp.set('v.buttonGroup', false);
            cmp.set('v.DNListPage', true);
            cmp.set('v.ShowDNNameQR', ShowDNNameQR);
        }


        var IsAgencyShow = event.getParam("IsAgencyShow");
        cmp.set('v.IsAgencyShow', IsAgencyShow);
    },
    searchByDNName: function(cmp) {
        //获取输入的value值
        var key = document.getElementById('searchInput').value;
        var data = cmp.get('v.data');
        //定义一个空数组 用于存放模糊检索的data
        var tempDataList = [];
        var pageSize = cmp.get('v.pageSize');
        //判断搜索框内是否有值
        if (key) {
            cmp.set('v.searchFlag', true);
            //模糊检索
            tempDataList = this.filterByName(data, key);
            //设置分页
            cmp.set("v.lineItemSize", tempDataList.length);
            //计算所有页
            var allPage = this.pageCount(tempDataList.length, pageSize);
            cmp.set("v.allPage", allPage);
            //设置分页
            var tempData = this.getTempData(tempDataList, 1, pageSize);
            cmp.set("v.isLastPage", this.isLastPage(tempDataList, tempData));
            cmp.set("v.dataSize", tempData.length);
            cmp.set("v.currentData", tempData);
            cmp.set('v.pageNumber', 1);
            cmp.set("v.searchSize", tempDataList.length);

        } else {
            cmp.set('v.searchFlag', false);
            //显示全部箱
            var data = cmp.get('v.data');
            var tempData = this.getTempData(data, 1, pageSize);
            var allPage = this.pageCount(data.length, pageSize);
            cmp.set("v.allPage", allPage);
            cmp.set("v.isLastPage", this.isLastPage(data, tempData));
            cmp.set("v.dataSize", tempData.length);
            cmp.set("v.currentData", tempData);
            cmp.set('v.pageNumber', 1);
            cmp.set('v.searchSize', null);

        }
    },
    //js 模糊检索
    filterByName: function(aim, name) {
        return aim.filter(item => item.DNNameNo0__c.match(name)||item.ContractNO__c.match(name));
    },
    openESignPage: function(cmp, event) {
        //获取按钮中属性name  ==  获取参数 箱号
        var caseNumber = event.target.name;
        //知道点击的是哪个DN号 然后从data中筛选出 esignForm
        //赋值
        cmp.set("v.DNName", caseNumber);

        var data = cmp.get("v.data");

        var esign;
        for (var i = 0; i < data.length; i++) {
            if (data[i].DNNameNo0__c == caseNumber) {
                esign = data[i];
            }
        }
        if (esign) {
            cmp.set("v.eSignForm", esign);
        }
        //根据记录类型判断跳转
        var recordType = cmp.get("v.recordTypeName");
        if (recordType == 'Agency' || recordType == 'AgencyContract') {

            this.anencyClick(cmp);
        } else if (recordType == 'HP') {
            debugger
            this.HPClick(cmp);

        }
        console.log("我走到这里了！");
        // cmp.set("v.DNListPage",false);
        // cmp.set("v.buttonGroup",true);
    },
    anencyClick: function(cmp) {
        var eSignForm = cmp.get('v.eSignForm');
        var IsShow = cmp.get('v.IsAgencyShow');
        var entryType = cmp.get('v.entryType');
        var status;
        if (eSignForm.agencyAutoSignUpStatus__c) {
            status = eSignForm.agencyAutoSignUpStatus__c;
        } else {
            status = '';
        }

        //①先区分直销还是分销
	// 阿西赛多签收单 精琢技术 fy 2021-09-08 start
        if (eSignForm.skip_Distribution_Sign__c) {
	// 阿西赛多签收单 精琢技术 fy 2021-09-08 end
            //根据不同情况返回对应的提示信息  精琢技术 thh 2021-10-09 start
            cmp.set("v.errorMessage", eSignForm.skip_Distribution_Sign_Message__c);
            //根据不同情况返回对应的提示信息  精琢技术 thh 2021-10-09 start
            this.showErrorToast(cmp);
            //②判断是否已经审批完毕
        } else if (status == '申请中') {
            cmp.set("v.errorMessage", '经销商签收结果审批中，请等待！');
            this.showErrorToast(cmp);
        } else if (IsShow && (status == '' || status == '申请中' || status == '草案中')) {
            cmp.set("v.errorMessage", '经销商签收数据已提交，请等待！');
            this.showErrorToast(cmp);
        } else if (eSignForm.agencyConfirmDate__c != null) {
            cmp.set("v.DNListPage", false);
            cmp.set("v.errorMessage", '经销商签收结果已确认！');
            this.showErrorToast(cmp);
            cmp.set('v.agencyConfirmReadOnlyPage', true);
            cmp.set('v.buttonGroup', false);
        } else {
            //验证结果 为空 或者 (验证结果不为空&& 审批状态为草案中)
            if (!eSignForm.agencyAcceptResult__c || (eSignForm.agencyAcceptResult__c &&
                    (!status || status == '草案中' || status == '不批准'))) {
                cmp.set("v.DNListPage", false);
                cmp.set('v.agencyPage', true);
                cmp.set('v.buttonGroup', false);
            } else if (eSignForm.agencyAcceptResult__c == '全部验收' && eSignForm.agencyConfirmDate__c) {
                cmp.set("v.DNListPage", false);
                cmp.set("v.errorMessage", '经销商已经验收完毕！');
                this.showErrorToast(cmp);
                cmp.set('v.agencyConfirmReadOnlyPage', true);
                cmp.set('v.buttonGroup', false);
            } else if ((eSignForm.agencyAcceptResult__c == '部分验收' ||
                    eSignForm.agencyAcceptResult__c == '整单非完好') &&
                !eSignForm.salesManageConfirmDate__c
            ) {
                cmp.set("v.errorMessage", '营业管理课处理中！');
                this.showErrorToast(cmp);
                return;
            } else if (eSignForm.agencyAcceptResult__c == '部分验收' ||
                eSignForm.agencyAcceptResult__c == '整单非完好'
            ) {
                cmp.set("v.DNListPage", false);
                cmp.set('v.agencyConfirmPage', true);
                cmp.set('v.buttonGroup', false);

            } else {
                cmp.set("v.errorMessage", '网络异常，请返回重新检索DN号！');
                this.showErrorToast(cmp);
                return;
            }
        }
    },
    HPClick: function(cmp) {
debugger
        var eSignForm = cmp.get('v.eSignForm');
        var IsShow = cmp.get('v.IsHPShow');
        var IsAgencyShow = cmp.get('v.IsAgencyShow');
        var entryType = cmp.get('v.entryType');
        var HPStatus;
        if (eSignForm.HPSignUpStatus__c) {
            HPStatus = eSignForm.HPSignUpStatus__c;
        } else {
            HPStatus = '';
        }
        if (HPStatus == '不批准') {
            cmp.set('v.IsHPShow', false);
        }
        IsShow = cmp.get('v.IsHPShow');
        //用于判断经销商已签收或者已提交后 医院二维码则可以扫入
        var date;
        if (eSignForm.agencySignUpDate__c) {
            date = eSignForm.agencySignUpDate__c;
        } else if (eSignForm.agencySignUpDateBack__c) {
            date = eSignForm.agencySignUpDateBack__c;

        } else {
            date = '';
        }
        // if(eSignForm.ACC_Text__c == 'Y'){
        // 	cmp.set("v.errorMessage", 'acc标识的发货DN不可进行医院签收!');
        // 	this.showErrorToast(cmp);
        // }
        // else 
	// 阿西赛多签收单 精琢技术 fy 2021-09-08 start
        if (eSignForm.skip_Hospital_Sign__c) {
	// 阿西赛多签收单 精琢技术 fy 2021-09-08 end
            //根据不同情况返回对应的提示信息  精琢技术 thh 2021-10-11 start
            cmp.set("v.errorMessage", eSignForm.skip_Hospital_Sign_Message__c);
            //根据不同情况返回对应的提示信息  精琢技术 thh 2021-10-11 end
            this.showErrorToast(cmp);
        }
        //①先区分直销还是分销
        else if (eSignForm.Sales_Root_Formula__c == '販売店' && !date && HPStatus == ''&&!eSignForm.skip_Distribution_Sign__c) {
            cmp.set("v.errorMessage", '请等待经销商发货!');
            this.showErrorToast(cmp);
            //②判断是否已经审批完毕
        } else if (HPStatus == '申请中') {
            cmp.set("v.errorMessage", '医院签收结果审批中，请等待！');
            this.showErrorToast(cmp);
            // cmp.set("IsShow", true);
        } else if (IsShow && (HPStatus == '' || HPStatus == '申请中' || HPStatus == '草案中')) {
            cmp.set("v.errorMessage", '医院签收数据已提交，请等待！');
            this.showErrorToast(cmp);
	    // 阿西赛多签收单 精琢技术 fy 2021-09-08 start
        } else if (eSignForm.salesHPManageConfirmDate__c && !eSignForm.skip_Distribution_Sign__c) {
	// 阿西赛多签收单 精琢技术 fy 2021-09-08 end
            cmp.set("v.DNListPage", false);
            cmp.set("v.errorMessage", '医院签收结果已确认x1！');
            this.showErrorToast(cmp);
            cmp.set('v.eSignHospitalConfirmReadOnlyPage', true);
            cmp.set('v.buttonGroup', false);
	    // 阿西赛多签收单 精琢技术 fy 2021-09-08 start
        } else if (eSignForm.HPConfirmDate__c && eSignForm.skip_Distribution_Sign__c) {
	// 阿西赛多签收单 精琢技术 fy 2021-09-08 end
            cmp.set("v.DNListPage", false);
            cmp.set("v.errorMessage", '医院签收结果已确认x2！');
            this.showErrorToast(cmp);
            cmp.set('v.eSignHospitalConfirmReadOnlyPage', true);
            cmp.set('v.DirectFlag', true);
            cmp.set('v.buttonGroup', false);
        } else {
            //验证结果 为空 或者 (验证结果不为空&& 审批状态为草案中)
            //&& IsAgencyShow
            if (!eSignForm.HPAcceptResult__c || (eSignForm.HPAcceptResult__c &&
                    (!HPStatus || HPStatus == '草案中' || (HPStatus == '不批准')))) {
                //判断直销还是分销
		// 阿西赛多签收单 精琢技术 fy 2021-09-08 start
                if (eSignForm.skip_Distribution_Sign__c) {
		// 阿西赛多签收单 精琢技术 fy 2021-09-08 end
                    cmp.set('v.isDirectSales', true);
                }
                cmp.set("v.DNListPage", false);
                cmp.set('v.HPPage', true);
                cmp.set('v.buttonGroup', false);
            } else if ((eSignForm.HPAcceptResult__c == '部分验收' ||
                    eSignForm.HPAcceptResult__c == '整单非完好') &&
                !eSignForm.salesHPManageConfirmDate__c
            ) {
                cmp.set("v.errorMessage", '营业管理课处理中！');
                this.showErrorToast(cmp);
                return;
            } else if (eSignForm.HPAcceptResult__c == '部分验收' ||
                eSignForm.HPAcceptResult__c == '整单非完好' || (!eSignForm.HPConfirmDate__c && eSignForm.HPAcceptResult__c == '全部验收')
            ) {
                cmp.set("v.DNListPage", false);
                cmp.set('v.HPPConfirmage', true);
                cmp.set('v.buttonGroup', false);

            } else {
                cmp.set("v.errorMessage", '网络异常，请返回重新检索DN号！');
                this.showErrorToast(cmp);
                return;
            }
        }

        // cmp.set('v.buttonGroup', false);
        // cmp.set('v.HPPage',true);
    },
    getNowFormatDate: function() {
        var date = new Date();
        var seperator1 = "-";
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = year + seperator1 + month + seperator1 + strDate;
        return currentdate;
    },
    showESignQR: function(cmp, event) {
        var DNName = event.target.name;
        var agencySignUpDate__c = event.target.value;
        var eSignForm = cmp.get('v.eSignForm');
        var ShowDNNameQR = cmp.get('v.ShowDNNameQR');
	// 阿西赛多签收单 精琢技术 fy 2021-09-08 start
        var skip_Hospital_Sign__c = event.target.id;
	// 阿西赛多签收单 精琢技术 fy 2021-09-08 end
        //最终用户

        //如果经销商的签收日为空的话 说明未签收完成
        // if(eSignForm.ACC_Text__c == 'Y'){
        // 	alert('acc标识的发货DN不能生成二维码！')
        // }else 
	// 阿西赛多签收单 精琢技术 fy 2021-09-08 start
        if (skip_Hospital_Sign__c == 'true') {
	// 阿西赛多签收单 精琢技术 fy 2021-09-08 end
            alert('经销商协议不能生成医院二维码');
        } else if (!agencySignUpDate__c && ShowDNNameQR != DNName.toString()) {
            alert('经销商未签收，不能生成二维码！')
        } else {
            cmp.set("v.DNName", DNName);
            //显示二维码
            cmp.set('v.showQR', true);
            //隐藏dn页
            cmp.set('v.DNListPage', false);

        }

    },
    testIn: function(cmp, event) {
        var id =$A.get("$Label.c.eSignHome");//niwu add  - 01t10000000ThMe  =eSignHome 
        var ids = new Array;
        ids.push(id);
        //①调用后台方法 根据url上的管理编码返回客户id和记录类型
        var action = cmp.get("c.getProduct");
        action.setParams({
            "ids": ids
        });
        action.setCallback(this, function(response) {
            var state = response.getState();

            if (state === "SUCCESS") {
                var res = response.getReturnValue();

                alert('resId:' + res[0].Name);
            } else if (state === "ERROR") {
                console.log('There was a problem : ' + response.getError());
                cmp.set("v.errorMessage", '网络异常，请重新输入！');
                this.showErrorToast(cmp);
                return;
            }
        });
        $A.enqueueAction(action);

    },
    // 进口单证 fy start
    showAttachmentDisplay: function(cmp, event) {
        debugger
        var DNId = event.target.name;
        cmp.set('v.DNId', DNId);
        cmp.set('v.AttachmentDisplayflag', true);
        cmp.set('v.DNListPage', false);
    },
    rebackDNListPage: function(cmp, event) {
        debugger
        cmp.set('v.AttachmentDisplayflag', false);
        cmp.set('v.DNListPage', true);
    },
    showAttachmentDisplay2: function(cmp, event) {
        debugger
        var DNId = event.target.name;
        //******** 进口单证改造 fy start
        var AgencyIDM = cmp.get('v.AgencyIDM');
        window.open('/eSignSystem/s/AttachmentDisplay?DNId=' + DNId+'&AgencyIDM='+AgencyIDM, '_blank');
        //******** 进口单证改造 fy end
    },
    // 进口单证 fy end
    // OTS物流信息 WYL start
    rebackDNListPage2: function(cmp, event) {
        debugger
        // cmp.set('v.LogisticsDisplayflag', false);
        // cmp.set('v.DNListPage', true);
        var AgencyIDM = cmp.get('v.AgencyIDM');;
        window.open('/eSignSystem/s/EsignDataEntry?AgencyID='+AgencyIDM+'&pinit=true', '_self');
    },
    showlogisticsDisplay: function(cmp, event) {
        // debugger
        // var DNId = event.target.name;
        // var AgencyIDM = cmp.get('v.AgencyIDM');
        // window.open('/eSignSystem/s/LogisticsDisplay?DNId=' + DNId+'&AgencyIDM='+AgencyIDM, '_blank');
        debugger
        var DNId = event.target.name;
        cmp.set('v.DNId', DNId);
        cmp.set('v.LogisticsDisplayflag', true);
        cmp.set('v.DNListPage', false);
    },
    // OTS物流信息 WYL end

})