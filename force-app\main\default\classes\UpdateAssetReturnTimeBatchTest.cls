@isTest
private class UpdateAssetReturnTimeBatchTest {

	static testMethod void testMethod1() {
		// 省
		Address_Level__c al = new Address_Level__c();
		al.Name = '東京';
		al.Level1_Code__c = 'CN-99';
		al.Level1_Sys_No__c = '999999';
		insert al;
		// 市
		Address_Level2__c al2 = new Address_Level2__c();
		al2.Level1_Code__c = 'CN-99';
		al2.Level1_Sys_No__c = '999999';
		al2.Level1_Name__c = '東京';
		al2.Name = '渋谷区';
		al2.Level2_Code__c = 'CN-9999';
		al2.Level2_Sys_No__c = '9999999';
		al2.Address_Level__c = al.id;
		insert al2;

		// 病院を作る
		Account hospital = new Account();
		hospital.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'HP'].id;
		hospital.Name = 'test hospital';
		hospital.Is_Active__c = '有効';
		hospital.Attribute_Type__c = '卫生部';
		hospital.Speciality_Type__c = '综合医院';
		hospital.Grade__c = '一级';
		hospital.OCM_Category__c = 'SLTV';
		hospital.Is_Medical__c = '医疗机构';
		hospital.State_Master__c = al.id;
		hospital.City_Master__c = al2.id;
		hospital.Town__c = '东京';
		insert hospital;

		// 戦略科室を得る
		Account[] strategicDep = [SELECT ID, Name FROM Account WHERE parentId = :hospital.Id AND recordType.DeveloperName = 'Department_Class_OTH'];
		// 診療科を作る
		Account dep = new Account();
		dep.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'Department_OTH'].id;
		dep.Name = 'test dep';
		dep.AgentCode_Ext__c = '9999998';
		dep.ParentId = strategicDep[0].Id;
		dep.Department_Class__c = strategicDep[0].Id;
		dep.Hospital__c = hospital.Id;
		insert dep;

		Contact contact2 = new Contact();
		contact2.AccountId = dep.Id;
		//contact2.FirstName = '責任者';
		contact2.LastName = 'test1经销商';
		insert contact2;

		//备品借出申请
		Rental_Apply__c ra1 = new Rental_Apply__c();
		// ra1.OPDPlan__c = oPDPlan.id;
		ra1.Name = 'testra';
		ra1.Product_category__c = 'GI';
		ra1.Demo_purpose1__c = '产品试用';
		ra1.demo_purpose2__c = FixtureUtil.raDemo_purpose2MAP.get('shiyongwuxunjia');
		ra1.direct_send__c = '医疗机构';
		ra1.Loaner_received_staff__c = '王五';
		ra1.Loaner_received_staff_phone__c = '110';
		ra1.direct_shippment_address__c = '北京市';
		ra1.Hospital__c = hospital.Id;
		ra1.Strategic_dept__c = strategicDep[0].Id;
		ra1.Account__c = dep.Id;
		ra1.Request_shipping_day__c = Date.toDay();
		ra1.Hope_Lonaer_date_Num__c = 16;
		ra1.Phone_number__c = '**********';
		ra1.Loaner_medical_Staff__c = contact2.Id;
		insert ra1;

		// 借出备品配套一览
		Rental_Apply_Equipment_Set__c raes1 = new Rental_Apply_Equipment_Set__c();
		raes1.Rental_Apply__c = ra1.Id;
		raes1.Received_Confirm__c = 'OK';
        raes1.Asset_return_time__c=Date.toDay();
		raes1.Cancel_Select__c = false;
		raes1.Shipment_request_time__c = Date.toDay();
		raes1.Rental_Start_Date__c = Date.toDay();
		raes1.Rental_End_Date__c = Date.toDay();
		raes1.IndexFromUniqueKey__c = 1;
		insert raes1;

		List<String> IdList=new List<String>();
		IdList.add(ra1.Id);
        Database.executeBatch(new UpdateAssetReturnTimeBatch(IdList));
		Database.executeBatch(new UpdateAssetReturnTimeBatch());

	}
}