<aura:component implements="force:appHostable,flexipage:availableForAllPageTypes" 
access="global"  controller="eSignHospitalPageController">
<!-- eSignHospitalPageController -->
        <aura:attribute name="DNName" type="String" 
                    description="用来从父对象接收传递过来的DN号"
                    default=""/>
        <aura:attribute name="allWidth" type="String"
                    description="用来从父对象接收传递过来的屏幕宽度"
                    default=""/>
        <aura:attribute name="floatWidth" type="String"
                    description="用来从父对象接收传递过来的屏幕宽度"
                    default=""/>
        <aura:attribute name="SANumber" type="String" 
                    description="用来从父对象接收传递过来的注残号"
                    default=""/>
        <aura:attribute name="IsSubmit" type="boolean" default="false"
                     description="判断是否录入表需要提交"/>
        <aura:attribute name="IsWhole" type="boolean" default="false"
                     description="判断明细中是否有不完好的"/>
        <aura:attribute name="IsHaveFile" type="boolean" default="false"
                     description="判断是否上传附件"/>
        <aura:attribute name="IsUpLoad" type="boolean" default="false"
                     description="判断是否上传附件"/>
        <aura:attribute name="AgencyClick" type="boolean" default="false"
                     description="判断是否已提交过一次"/>
        <aura:attribute name="IsAgencyShow" type="Boolean" default=""/>
        <aura:attribute name="IsHPShow" type="Boolean" default=""/>

        <aura:attribute name="hideCheckboxColumn" type="boolean" default="true"
                     description="因为点上一页的时候不知道为什么置成false了所以用变量来设置"/>
        <aura:attribute name="recordId" type="String" default=""
                        description="在明细页点击下一步会上传录入表，
                                     然后返回的录入表ID存在这里，用于上传文件使用"/>
        <!-- 经销商扫描日 -->
        <aura:attribute name="agencyScanDayBack" type="Date" default=""/>
        <!-- 经销商签收日 -->
        <aura:attribute name="agencySignUpDateBack" type="Date" default=""/>
        <!-- 经销商确认日 -->
        <aura:attribute name="agencyConfirmDateBack" type="Date" default=""/>
        <!-- 医院验收结果 -->
        <aura:attribute name="HospitalAcceptanceResults" type="string" default="全部验收"/>
        <!-- 合同编号 -->
        <aura:attribute name="Contract_No" type="string" default="123455"/>
        <!-- 发货DN -->
        <aura:attribute name="DNSignUp" type="string" default="123456"/>
        <!-- 医院名称 -->
        <aura:attribute name="HospitalName" type="string" default="TEST BJ 医院"/>
        <!-- 科室 -->
        <aura:attribute name="DepartmentName" type="string" default="TEST BJ 医院 消化科"/>
        <!-- 箱数 -->
        <aura:attribute name="NumberOfCases" type="string" default="2箱"/>
        <aura:attribute name="HomePageDisplay" type="Boolean" default="true"/>
        <aura:attribute name="NextPageDisplay" type="Boolean" default="false"/>
        <!-- checkbox -->
        <aura:attribute name="chk" type="string" default=""/>
        <aura:attribute name="check" type="Boolean" default="false"/>
        <!-- 用于判断是直销还是分销 -->
        <aura:attribute name="isAgencyOrHp" type="Boolean" default=""/>
        <aura:attribute name="eSignForm" type="Object"
                    description="首页信息"/>
        <aura:attribute name="uploadFilePage" type="Boolean" default="false"
                    description="判断是否是文件上传页"/>
        <aura:attribute name="data" type="Object[]"
                        description="所有明细的信息"/>
        <aura:attribute name="currentData" type="Object[]"
                        description="明细页当前显示的明细的信息"/>
        <aura:attribute name="dataSize" type="Integer" default="0"
                        description="明细页当前显示的明细的数量"/> 
        <aura:attribute name="columns" type="List"
                         description="明细页表格的每列构造的设定"/>
        <aura:attribute name="pageNumber" type="Integer" default="1"
                        description="明细页表格的第几页"/>
        <aura:attribute name="pageSize" type="Integer" default="5"
                        description="明细页表格的每页明细容量"/>
        <aura:attribute name="isLastPage" type="Boolean" default="false"
                         description="是否最后一页，如果是最后一页，那么表格分页的下一页不可用"/>
        <aura:attribute name="draftValues" type="Object" default="[]"
                        description="明细页表格的修改内容，保存后从这里读值存到当前表格里面"
                        />
        <aura:attribute name="successMessage" type="String"
                        description="成功消息"/>
        <aura:attribute name="errorMessage" type="String"
                        description="失败消息"/>
        <!-- 适用+检索  start-->
        <aura:attribute name="searchSize" type="Integer" default=""
                    description="检索数据的长度"/>
        <aura:attribute name="searchCase" type="Integer" default=""
                        description="存放检索值，保留缓存"/>
        <aura:attribute name="searchCaseKey" type="Integer" default=""
                        description="存放检索值，赋值给searchCase"/>
        <aura:attribute name="searchFlag" type="boolean" default=""
        description="搜索框是否有值"/>
        <aura:attribute name="lineItemSize" type="Integer" default=""
                        description="存放一共有多少条明细size"/>
        <!-- 医院电子邮件 -->
        <aura:attribute name="HPWorkflowEmailBack" type="String"
                        description="医院邮箱"/>
        <aura:attribute name="applyButton" type="String" default=""
                        description="显示适用按钮"/>
        <aura:attribute name="allApplyButton" type="String" default=""
                        description="显示全部按钮"/>
        <!-- 文件名展示列 -->
        <aura:attribute name="activeSections" type="List" default="['A']" />
        <aura:attribute name="activeSectionsMessage" type="String" default="" />
        <aura:attribute name="flName" type="String" default=""
                        description="显示文件名"/>
        <aura:attribute name="nameList" type="Object[]"
                        description="显示所有文件名"/>    
        <aura:attribute name="fileSize" type="Integer" default=""
                        description="记录一共有几个文件"/> 
        <aura:attribute name="flUpLoadName" type="String" default=""
                    description="显示上传文件名"/>
        <aura:attribute name="fileUpLoadSize" type="Integer" default=""
                        description="显示上传文件数"/>
        <aura:attribute name="nameUpLoadList" type="Object[]"
                        description="显示所有文件名"/>
        <aura:attribute name="uploadFlag" type="Boolean" default="false" 
                        description="点击上传文件标识"/>
        <aura:attribute name="base64List" type="Object[]"
                        description="显示所有文件名"/>  
        <aura:attribute name="messageFlag" type="boolean" default="false" description="判断上传文件是否提示"  />
        <aura:attribute name="submitAfterShow" type="Boolean" default="false" 
                    description="提交后显示退回首页按钮"/>
        <aura:attribute name="errorFlag" type="boolean" default="false" description="网络不好的情况置成true，由于恢复网络上传文件！"  />

        <!-- CHAN-BWCBL8 精琢技术 wql 20201225 start -->
        <!-- 返回首页 end -->
        <aura:attribute name="ISDenyReason" type="boolean" default="false"  description="是否医院驳回" />
        <!-- CHAN-BWCBL8 精琢技术 wql 20201225 end -->
        <!-- 增加扫描标识，batch取消驳回勾选 2020/12/30 wql start -->
        <aura:attribute name="IsHPScan" type="boolean" default="false"  description="医院是否扫描" /> 
        <!-- 增加扫描标识，batch取消驳回勾选 2020/12/30 wql end -->
        <!-- 文件名展示列 -->
        <!-- 文件上传-->
        <aura:attribute name="parentId" type="Id" default="" />
     <!-- 'showLoadingSpinner' attribute for show/hide the uploading image and msg in aura:if-->
        <aura:attribute name="showLoadingSpinner" type="boolean" default="false" />
     <!-- 'fileName' attribute for display the selected file name -->  
        <aura:attribute name="fileName" type="String" default="No File Selected.." />
     <!-- Lightning Input with file type and on file change call the 'handleFilesChange' controller -->   <aura:attribute name="getchunk" type="String" default="" />

        <aura:attribute name="baseFile" type="String" default="" />               
        <!-- 文件名展示列 -->
        <!-- 适用+检索 end -->
        <aura:handler name="init" action="{!c.doInit}" value="{!this}"
                  description="初始化方法"/>


        <!-- 复选框组 -->
        <aura:attribute name="options" type="List" default="[
        {'label': '上述内容已阅读（默认勾选）点“下一步”到明细页', 'value': 'option1'}
        
        ]"/>
        <aura:attribute name="value" type="List" default="option1"/>
        
        <!-- 上传附件 图片预览 -->
        <aura:attribute name="imageSrc" type="String" default="" />
        <aura:attribute name="imgList" type="String" default="" />
        <!--wangweipeng  20210616   选择好上传图片以后预览-->
        <aura:attribute name="imageBigSrc" type="String" default="" />
        <!-- 增加删除功能  精琢技术 thh 2021-09-26 start -->
        <aura:attribute name="FontSrc" type="Map" default="{}" description="关联文件名和文件" />
        <aura:attribute name="FileSrc" type="Map" default="{}" description="关联压缩后的文件名和文件" />
        <aura:attribute name="attachmentID" type="Map" default="{}" description="关联附件ID和附件名" />
        <aura:attribute name="loaded" type="Boolean" default="true" description="根据传入文件类型决定是否显示图片"/>
        <aura:attribute name="isUploadName" type="Map" default="{}" description="关联文件名和该文件是否已经上传" />
        <aura:attribute name="pdfSrc" type="Map" default="{}" description="关联pdf文件名和空白src" />
        <!-- 增加删除功能  精琢技术 thh 2021-09-26 end -->

        <div>
            <lightning:spinner aura:id="mySpinner" size="large"
                               description="加载中组件，如果不隐藏整个页面就在加载中"/>
        </div>

        <!-- 成功toast 组件的实现-->
        <div class="slds-modal slds-fade-in-open slds-hide" aura:id="successDiv">
            <div class="demo-only" style="height: 8rem;width:40%">
                <div class="slds-notify_container slds-is-relative">
                    <div class="slds-notify slds-notify_toast slds-theme_success" role="alert">
                        <div class=" slds-m-left_xx-large">
                            <div class="slds-notify__content slds-m-left_small slds-align_absolute-center slds-m-left_xx-large">
                                <lightning:icon alternativeText="Success" iconName="utility:success" size="small"
                                                class="buttonColor slds-m-right_small" />
                                <h2 class="slds-text-heading_small ">{!v.successMessage}</h2>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 失败toast 组件的实现-->
        <div class="slds-modal slds-fade-in-open slds-hide" aura:id="errorDiv">
            <div class="demo-only" style="height: 8rem;" id="errorSonDiv1">
                <div class="slds-notify_container slds-is-relative">
                    <div class="slds-notify slds-notify_toast slds-theme_error" role="status" id="errorSonDiv2" style="">
                        <div class="">
                            <div class="slds-notify__content slds-m-left_small slds-align_absolute-center">
                                <lightning:icon alternativeText="error" iconName="utility:error" size="small"
                                                class="buttonColor slds-m-right_small" />
                                <h2 class="slds-text-heading_small ">
                                    <div id="errorSonDiv3" style=" word-break: break-all;overflow: auto;">
                                    {!v.errorMessage}
                                    </div>
                                </h2>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <aura:registerEvent name="componentEvent" type="c:BackToHomePage"/>
        <!-- 首页 -->
        <aura:if isTrue="{!v.HomePageDisplay}">
        <p style="font-size: 17px;">
        首页
        </p>
        <div  id="qs"  style="   margin-bottom: 5%;" class="slds-card" >
         
            <!-- 首页，由于与医院确认页公用相同主页，所以使用这个组件,然后可以公用 -->
            <c:eSignHospitalHomePage
                                   HospitalAcceptanceResults = "{!v.HospitalAcceptanceResults}"
                                   eSignForm = "{!v.eSignForm}"
                                   />

         
        <!-- 高度 -->
       <!--  <div style="height: 20px;"></div> -->
       
        <!-- 注意事项： -->
        <div  id="attention"  style=" position:relative;margin-right: 4px;  margin-right: 4px; " class="" >
            <lightning:layout horizontalAlign="left" class="slds-m-top_large">
                    <!-- 未设置layoutItem的size大小 不涉及并列这种情况 目前自适应 -->
                         <lightning:layoutItem padding="around-small" >
                            <div class="header-column">
                               <p class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;">注意事项 ： </p>
                               <p class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;"> 1、收到货物请在24小时内确认 </p>
                               <p class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;"> 2、货物外观破损请当面与送货人签字确认 </p>
                               <p class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;"> 3、书面通知供货者破损情况并描述破损状态 </p>
                               <p class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;"> 4、必要时请拍照记录 </p>
                               <!-- 须知 -->
                               <!-- CHAN-BWCBL8 精琢技术 wql 20201225 start -->
                                <aura:if isTrue="{!v.ISDenyReason}">
                                <p class="field-title" title="Field 3" style="font-weight:bold;font-size: 20px;color: #0f218b;"></p>
                                <p class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;color: red;"> 驳回原因：{!v.eSignForm.HPDenyReason__c} </p>
                                </aura:if>
                                <!-- CHAN-BWCBL8 精琢技术 wql 20201225 end -->
                            </div>
                        </lightning:layoutItem>
                </lightning:layout>

            <!-- 须知 -->
                <lightning:layout horizontalAlign="left" class="slds-m-top_small">
                <!-- 未设置layoutItem的size大小 不涉及并列这种情况 目前自适应 -->
                        <lightning:layoutItem padding="around-small">
                            <!-- <lightning:checkboxGroup name="checkbox"
                                 label=""
                                 options="{! v.options }"
                                 value="{! v.value }"
                                 onchange="{! c.handleChange }"/> -->
                                 <lightning:input type="checkbox" label="上述内容已阅读，点“下一步”到明细页" id="chk" name="chk" checked="{!v.check}" />
                        </lightning:layoutItem>
                </lightning:layout>
            
        </div>  


        <!-- 从首页到检索页的上一步按钮 -->
        <!-- <div class="slds-float_left">
            <lightning:button variant="brand"
                              label="上一步" title="上一步" onclick="{! c.BackToHomePage }" />
        </div> -->
        <div class="slds-float_right">            
                <lightning:button variant="brand" 
                                  label="下一步" title="下一步" onclick="{! c.hospitalDetailsPageNextClick }" />
        </div>
</div>
   <!--  </lightning:datatable> -->
    </aura:if> 
    <aura:renderIf isTrue="{!v.NextPageDisplay}">
        <!-- 加上滚动条 -->

        <div id="header" style="{!'width:'+v.floatWidth+'px;'}">
            <tr>
            <td class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;" align="left">（向右滑动查看更多信息） 
            
            </td>
            </tr>
               
        </div>
        <!-- 检索 -->
        <div id="searchDiv" class="slds-page-header" role="banner"  style="{!'width:'+v.floatWidth+'px;'}">
            <div class="slds-float_center" style="text-align:center">
            <tr>
            <td align="right" style="{!'padding-left:'+v.floatWidth*0.15+'px;'}">箱号:
            <input id="searchInput" lightning-input_input ="" type="text" class="slds-input" value="{!v.searchCase}" name=""  onchange="{!c.searchByCaseNumber}" style="{!'width:'+v.floatWidth*0.2+'px;'}"/>
            </td>

            <td style="padding-left: 5px;">
            <aura:if isTrue="{!v.searchFlag}">
                    该箱共有{!v.searchSize}条数据。
                <aura:set attribute="else">
                    该单共有{!v.lineItemSize}条数据。
                </aura:set>
            </aura:if>
            </td>
            </tr>
            </div>
        </div>       
        <div id="allDiv" style="{!'width:'+v.allWidth+'px;overflow-x:scroll;'}">
        <div  id="in" style=" width:799px;margin-right: 4px; ">
            <!-- 显示在明细页的当前明细的表格 -->
            <div id="inTable" >
                <!-- <lightning:card> -->
                    <aura:set attribute="title">
                        货物签收单明细
                    </aura:set>
                
                    <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-hint-parent slds-no-row-hover " style="table-layout: fixed;">
                        <thead>
                            <tr class="slds-line-height_reset slds-text-title_caps">
                                <!-- 第一列 产品型号 -->
                                <th  class="slds-is-resizable" tabindex="-1" data-label="产品型号"  scope="row">
                                            
                                    <span class="slds-grid slds-grid_align-spread" style="
                                            width: 110px;text-align: left;display:block;">
                                            <div class="slds-truncate">
                                                <span title="产品型号" class="slds-truncate" style="padding-bottom: 3px;">产品型号</span>    </div>
                                    </span>
                                                
                                            
                                    
                                </th>
                                <!-- 第二列 CODE -->
                                <!-- <th  class="slds-is-resizable" tabindex="-1" data-label="CODE"  scope="row" style="width: 137px;height: 35px; outline: none;">
                                          
                                    <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                        <div class="slds-truncate">
                                                <span title="CODE" class="slds-truncate" style="padding-bottom: 3px;">CODE</span>    
                                        </div>          
                                                      
                                    </span>
                                                    
                                    
                                </th> -->
                                
                                <!-- 第三列 产品名称-->
                                <th  class="slds-is-resizable" tabindex="-1" data-label="产品名称"  scope="row" style="width: 361px;height: 35px;">
                                            
                                    <span class="slds-grid slds-grid_align-spread" style="text-align: left;display:block;">
                                        <div class="slds-truncate">
                                                <span title="产品名称" class="slds-truncate" style="padding-bottom: 3px;">产品名称</span>   
                                                
                                            
                                        </div>
                                    </span>
                                        
                                    
                                </th>

                                <!-- 第四列 数量-->
                                <th  class="slds-is-resizable" tabindex="-1" data-label="数量"  scope="row" style="width: 50px;height: 35px;">
                                            
                                    <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                        <div class="slds-truncate">
                                                <span title="数量" class="slds-truncate" style="padding-bottom: 3px;">数量</span>   
                                                
                                            
                                        </div>
                                    </span>
                                                
                                        
                                    
                                </th>
                                
                                <!-- 第五列 箱号-->
                                <th  class="slds-is-resizable" tabindex="-1" data-label="箱号"  scope="row" style="width: 50px;height: 35px;">
                                            
                                    <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                        <div class="slds-truncate">
                                                <span title="箱号" class="slds-truncate" style="padding-bottom: 3px;">箱号</span>   
                                                
                                            
                                        </div>
                                    </span>
                                                
                                        
                                    
                                </th>
                                <!-- 第六列  货物情况-->
                                <th  class="slds-is-resizable" tabindex="-1" data-label="货物情况"  scope="row" style="width: 110px;height: 35px;">
                                            
                                    <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                        <div class="slds-truncate">
                                                <span title="货物情况" class="slds-truncate" style="padding-bottom: 3px;">货物情况</span>   
                                                
                                            
                                        </div>
                                    </span>
                                                
                                        
                                    
                                </th>
                                <!-- 第七列 按钮列-->

                                <th  class="slds-is-resizable" tabindex="-1" data-label="按钮列"  scope="row" style="width: 86px;height: 35px;">
                                            
                                    <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                        <div class="slds-truncate">
                                                <span title="按钮列" class="slds-truncate" style="padding-bottom: 3px;">按箱适用</span>   
                                                
                                            
                                        </div>
                                    </span>
                                                
                                        
                                    
                                </th>
                                
                                
                            </tr>
                        </thead>
                        <aura:if isTrue="{!v.currentData.length > 0}">
                            <tbody>
                                <aura:iteration items="{!v.currentData}" var="curr" indexVar="idx">
                            <tr class="slds-hint-parent">
                            <!-- 行项目也用th 因为用td标签点击列的高光 在苹果设备上 有痕迹 -->
                            <!-- 行项目 第一列 产品型号 -->
                            <th  class="slds-is-resizable" tabindex="-1" data-label="产品型号"  scope="row">
                                
                                        <span class="slds-grid slds-grid_align-spread" style="
                                            width: 110px;
                                            padding-top: 6px;height:31px;
                                        ">
                                            <div class="slds-truncate">
                                                    <lightning:formattedText id="{!curr.Id+':'+idx+':Asset_Model_No__c'}" value="{!curr.Asset_Model_No__c}" />                        
                                            </div>
                                        </span>
                                    
                                
                        
                            </th>
                                        
                            <!-- 行项目 第二列  CODE-->
                            <!-- <th  class="slds-is-resizable" tabindex="-1" data-label="CODE"  scope="row">
                              
                                        <span class="slds-grid slds-grid_align-spread">
                                            <div class="slds-truncate">
                                                    <lightning:formattedText value="{!curr.OTCode__c}" />
                                                    
                                                    
                                                
                                            </div>
                                        </span>
                                    
                                
                        
                            </th> -->
                            <!-- 行项目 第三列  产品名称-->          
                            <th  class="slds-is-resizable" tabindex="-1" data-label="产品名称"  scope="row" style="white-space:nowrap;
                            overflow:hidden;
                            text-overflow: ellipsis;">
                                
                                        <span class="slds-grid slds-grid_align-spread">
                                            <div class="slds-truncate">
                                                    <lightning:formattedText id="{!curr.Id+':'+idx+':productName__c'}" value="{!curr.productName__c}" />
                                                    
                                                
                                            </div>
                                        </span>
                                    
                            
                        
                            </th>
                            <!-- 行项目 第四列  数量-->           
                            <th  class="slds-is-resizable" tabindex="-1" data-label="数量"  scope="row" >
                              
                                        <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                            <div class="slds-truncate">
                                                    <lightning:formattedText id="{!curr.Id+':'+idx+':Count_Text__c'}" value="{!curr.Count_Text__c}" />
                                                    
                                                
                                            </div>
                                        </span>
                                    
                                
                        
                            </th>
                            <!-- 行项目 第五列  箱号-->          
                            <th  class="slds-is-resizable" tabindex="-1" data-label="箱号"  scope="row">
                             
                                        <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                            <div class="slds-truncate">
                                                    <lightning:formattedNumber id="{!curr.Id+':'+idx+':CaseNumber__c'}" value="{!curr.CaseNumber__c}"/>
                                                    
                                                
                                            </div>
                                        </span>
                                    
                                
                        
                            </th>

                            <!-- 行项目 第六列  货物情况-->          
                            <th  class="slds-is-resizable" tabindex="-1" data-label="货物情况"  scope="row">
                               
                                        <input id="{!curr.Id+':'+idx+':'+curr.CaseNumber__c}" lightning-input_input ="" type="text" class="slds-input" value="{!curr.HPGoodStatus__c}"   onchange="{!c.GoodsChanged}" />
                                       
                                        
                                        <button id="{!curr.Id+idx}" tabindex="-1"   class="slds-button slds-button_icon slds-cell-edit__button slds-m-left_x-small" style="display: none;" >
                                            <lightning:icon  iconName="utility:success" alternativeText="Success!" variant="Success"
                                            title="success variant xx-small" size="xx-small"/>
                                        </button>
                                        
                            </th>
                            <!-- 行项目 第七列 按钮列 -->
                            <!-- <aura:if isTrue="{!v.applyButton}"> -->
                            <th  class="slds-is-resizable" tabindex="-1" data-label=""  scope="row">
                                        
                                        <button id="{!curr.Id+':'+idx+':'}" tabindex="-1" class="slds-button ReturnButton" name="{!curr.CaseNumber__c}" style="{!'display:'+curr.isBlockButton__c+';width: 40px;'}" onclick="{!c.BatchUpdateByCase}">适用</button>
                                        
                                        
                            </th>
                 
                            </tr>
                        </aura:iteration>
                    </tbody>
                    
                </aura:if>
            </table>
                    
                    <!-- <div class="slds-box">
                        
                        <div align="center" style="float: left;padding-left: 370px;">
                            <lightning:button variant="neutral"
                                                              label="cancel" title="cancel" onclick=""  />
                        </div>
                        
                        <div align="center" style="padding-right: 370px;">
                            <lightning:button variant="brand"
                                                      label="save" title="save" onclick=""/>
                        </div>      
                    </div> -->
                    <aura:if isTrue="{!empty(v.currentData)}">
                        <div class="slds-align_absolute-center">
                            No records found
                        </div>
                    </aura:if>
        <!-- </lightning:card> -->



            </div>
            </div>
            </div>
            <!-- 分页功能的上一页和下一页， 还是最左侧的显示当前页数的逻辑-->
            <div id="floatDiv" style="{!'width:'+v.floatWidth+'px;'}">
            <div class="slds-clearfix">
                <div class="slds-page-header" role="banner">
                    <div class="slds-float_center" style="text-align:center">
                        <!-- iconName="utility:chevronleft" iconPosition="left" -->
                        <lightning:button  iconName="utility:left"
                                          onclick="{!c.handleHome}" disabled="{! v.pageNumber == 1}"/>
                        <lightning:button  iconName="utility:chevronleft" iconPosition="left"
                                          onclick="{!c.handlePrev}" disabled="{! v.pageNumber == 1}"/>
                        <span title="当前页">&nbsp;&nbsp;&nbsp;{!v.pageNumber}&nbsp;&nbsp;&nbsp;
                            </span>
                        <lightning:button  iconName="utility:chevronright" iconPosition="right"
                                          disabled="{! v.isLastPage}" onclick="{!c.handleNext}"/>
                        <!-- iconName="utility:chevronright" iconPosition="right" -->
                        <lightning:button  iconName="utility:right" 
                                          disabled="{! v.isLastPage}" onclick="{!c.handleLast}"/>

                    </div>
                    
                </div>
            </div>

            <!-- 明细页跳转至文件上传页的按钮 -->
             <!-- 明细页跳转至文件上传页的按钮 -->

            <div class="slds-float_right">
                <!-- <aura:if isTrue="{!v.isAgencyOrHp}"> -->
                      <div  id="attention"  style=" position:relative;margin-right: 4px;    margin-right: 4px; " class="" >
                      <!-- 确认提交 -->
                        <!-- <lightning:layout horizontalAlign="left" class="slds-m-top_small">
                            <lightning:layoutItem padding="around-small">
                                <lightning:input type="checkbox"
                                                 label="是否提交填写内容(默认勾选),点“下一步”到图片上传页"
                                                 id="IsSubmit" name="IsSubmit"
                                                 checked="{!v.IsSubmit}" />
                            </lightning:layoutItem>
                        </lightning:layout> -->
                      </div>
                <!-- </aura:if> -->
                <lightning:button variant="brand"
                                  label="下一步" title="下一步" onclick="{! c.handleDetailsPageNextClick }" />
            </div>
            <div class="slds-float_left">
                <div  id="attention"  style=" position:relative;margin-right: 4px;  margin-right: 4px; " class="" >
                    <!-- 确认提交 -->
                    <lightning:button variant="brand"
                                  label="上一步" title="上一步" onclick="{! c.handleShowPageNextClick }" />
                </div>
            </div>
        </div>
            
        
    </aura:renderIf>
    <aura:if isTrue="{!v.uploadFilePage}">
        <!-- 文件上传组件 -->
        <div aura:id="uploadFile" id="uploadFileDiv">
            <div class="slds-box">
                <lightning:input aura:id="fileId" onchange="{!c.handleFilesChange}" type="file" label="1.选择附件" multiple="false" />
                <lightning:notificationsLibrary aura:id="notifLibPlanA" />
                <div style="display: inline-block; vertical-align: top;"> 
                    <div id="imgDiv">
                        <div id="font">预览区域</div>
                        <!-- 当上传的附件为pdf时，隐藏预览区域  精琢技术 thh 2021-09-15 -->
                        <div id = "loaded" style="display: none;">
                            <img id="image" class="imgeBigCss" src="{!v.imageSrc}" style="display: none;" onclick="{!c.imgeBig}" />
                            <!--wangweipeng  20210616    图片选好以后，客户可能需要预览放大查看-->
                            <div id="outerdiv" onclick="{!c.imgeSmall}">
                                <img id="bigImg" src="{!v.imageBigSrc}" style="border:1px solid #fff;" />
                            </div>
                        </div>
                        <div id = "fontpdf" style="display: none;">PDF文件暂不支持预览</div>
                    </div>
                </div>
                <!-- 增加文件列表删除功能  精琢技术 thh 2021-08-25 start -->
                <!-- inline-block 元素保持行内排列且可以设置宽高 -->
                <div style="display: inline-block; vertical-align: top;">
                    <!-- <lightning:buttonIcon iconName="utility:close" variant="bare" onclick="{!c.deleteClick}" alternativeText="Close modal" title="删除" iconClass="deleteicon"/> -->
                    <lightning:button label="删除" onclick="{!c.deleteClick}" title="删除" />
                </div>
                <!-- 增加文件列表删除功能  精琢技术 thh 2021-08-25 end -->
                <!-- 标识预览图片名字  精琢技术 thh 2021-09-09 start -->
                <div>
                    <div id = "filenow" class="field-title" title="Field 3" style="display: inline-block; margin-left:10px; word-break: break-all; word-wrap: break-word; font-weight:bold; font-size: 15px; padding-top: 5px; color:#0f218b;"></div>
                    <!-- 文件上传后当前文件名后面出现绿色小勾  精琢技术 thh 2021-09-15 start-->
                    <div id = "uploadicon" style="display: none;">
                        <div style="margin-left: 5px;">
                            <lightning:icon iconName="utility:success" alternativeText="Success!" variant="Success" title="success variant x-small" size="x-small" />
                        </div>
                    </div>
                    <!-- 文件上传后当前文件名后面出现绿色小勾  精琢技术 thh 2021-09-15 end-->
                </div>
                <!-- 标识预览图片名字  精琢技术 thh 2021-09-09 end -->
                <p class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;padding-top: 7px;color:red;">
                    （随货同行单盖章后拍照上传）
                </p>
                <p class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;padding-top: 7px;color:red;">
                    （附件上传成功后请点击提交）
                </p>
                <aura:if isTrue="{!v.showLoadingSpinner}">
                    <div class="slds-text-body_small slds-text-color_error">上传中...
                        <img src="/auraFW/resources/aura/images/spinner.gif" class="spinner-img" alt="Loading" />
                    </div>
                </aura:if>
                <br/>
                <button class="slds-button slds-button_brand" onclick="{!c.doSave}">2.上传附件</button>
                <lightning:accordion allowMultipleSectionsOpen="true" onsectiontoggle="{! c.handleSectionToggle }" activeSectionName="{! v.activeSections }">
                    <lightning:accordionSection name="A" label="文件列表">
                        <!-- 增加小色块  精琢技术 thh 2021-09-26 start -->
                        <div>
                            <div style="display: inline-block; background-color: Grey; width:10px; height:10px;" />
                            <div style="margin-left: 5px; display: inline-block; font-weight:bold;font-size: 10px;padding-top: 7px;color:Grey;">   未上传  </div>
                            <div style="margin-left: 40px; display: inline-block; background-color: Green; width:10px; height:10px;" />
                            <div style="margin-left: 5px; display: inline-block; font-weight:bold;font-size: 10px;padding-top: 7px;color:Green;">   已上传  </div>
                        </div>
                        <!-- 增加小色块  精琢技术 thh 2021-09-26 end -->
                        <!-- overflow: scroll; 自动隐藏滚动条 -->
                        <div id="fileNameDiv" style="height:50px;width:250px; overflow: scroll;">
                            <!-- 增加点击文件列表切换查看图片附件  精琢技术 thh 2021-08-24 start -->
                            <p id='file00' onclick="{!c.buttonClicked}"></p>
                            <!-- 增加点击文件列表切换查看图片附件  精琢技术 thh 2021-08-24 end -->
                        </div>
                    </lightning:accordionSection>
                </lightning:accordion>
                <div id="emailDiv" style="height: 90px;padding-top: 30px;">
                    <lightning:input aura:id="inputCmp" name="fullName" type="email" value="" label="如有需要，请填写您的邮箱:" messageWhenTypeMismatch="请您输入有效的电子邮箱。" />
                </div>
            </div>
        </div>
        <!-- 明细页跳转至文件上传页的按钮 -->
            <div class="slds-float_left">
                <lightning:button variant="brand" 
                                  label="上一步" title="上一步" onclick="{! c.handleShowFielePageNextClick }" />
            </div>
            <!-- <div style="position: fixed;margin-left:36%;">
                <lightning:button variant="brand" 
                                  label="提交" title="提交" onclick="{! c.submitClick }" />
            </div>
            <div class="slds-float_right">
                <lightning:button variant="brand" 
                                  label="退回首页" title="退回首页" onclick="{! c.BackToHomePage }" />
            </div> -->
            <div id="submitAgency" style="text-align:right;flex: 1;padding-top: 1px;">
                        <lightning:button  variant="brand" 
                                      label="3.提交" title="3.提交" onclick="{! c.submitClick }" />
                </div>
                
            <aura:if isTrue="{!v.submitAfterShow}">
                <div id="backHome" style="text-align:right;flex: 1;padding-top: 1px;">
                    <lightning:button  variant="brand" 
                                      label="退回首页" title="退回首页" onclick="{! c.BackToHomePage }" />
                </div>
            </aura:if>
    </aura:if>
</aura:component>