@isTest
private class UpdateAgencyProductTargetScheduleTest {
    
    static testMethod void testExecute() {
        // This test runs a scheduled job at midnight Sept. 3rd. 2022
        String CRON_EXP = '0 0 0 3 9 ? 2024';

        System.Test.startTest();
        // Schedule the test job
        String jobId = system.schedule('UpdateAgencyProductTargetScheduleTest', CRON_EXP, new UpdateAgencyProductTargetSchedule());
        // Get the information from the CronTrigger API object
        CronTrigger ct = [SELECT Id, CronExpression, TimesTriggered, NextFireTime FROM CronTrigger WHERE id = :jobId];
        // Verify the expressions are the same
        System.assertEquals(CRON_EXP, ct.CronExpression);
        // Verify the job has not run
        System.assertEquals(0, ct.TimesTriggered);
        // Verify the next time the job will run
        System.assertEquals('2024-09-03 00:00:00', String.valueOf(ct.NextFireTime));

        UpdateAgencyProductTargetSchedule.assignOneMinute();
        System.Test.StopTest();
    }
    
}