({ 
    helperMethod : function() {
    
    },
    // 设置table 各栏属性
    getColumnAndAction : function(cmp) {
        var actions = [
            {label: 'Edit', name: 'edit'},
            {label: 'Delete', name: 'delete'},
            {label: 'View', name: 'view'}
        ];
        cmp.set('v.columns', [
            // {label: 'CODE', fieldName: 'OTCode__c', type: 'text', wrapText:false 
            // , hideDefaultActions: true ,fixedWidth: 110 },
            {label: '产品型号', fieldName: 'Asset_Model_No__c', type: 'text' , wrapText:false
            , hideDefaultActions: true,fixedWidth: 110  },
            {label: 'CODE', fieldName: 'OTCode__c', type: 'text', wrapText:false 
            , hideDefaultActions: true ,fixedWidth: 137 },
            {label: '产品名称', fieldName: 'productName__c', type: 'text', wrapText:false 
             , hideDefaultActions: true ,fixedWidth: 330  },
            {label: '数量', fieldName: 'Count_Text__c', type: 'text', wrapText:false 
            , hideDefaultActions: true ,fixedWidth: 50  },
            {label: '箱号', fieldName: 'CaseNumber__c', type: 'text', wrapText:false 
            , hideDefaultActions: true ,fixedWidth: 100  },
            {label: '货物情况', fieldName: 'HPGoodStatus__c', type: 'text', wrapText:false 
            , hideDefaultActions: true ,fixedWidth: 110  },
            {label: '处理意见', fieldName: 'handleOpinionHP__c', type: 'text', wrapText:false 
            , hideDefaultActions: true ,fixedWidth: 170   },
            {label: '医院确认结果', fieldName: 'HPConfirmResult__c', type: 'text', wrapText:false 
            , hideDefaultActions: true , fixedWidth: 140 },
        ]);
    },
    // 获取数据
    geteSign : function(cmp) {
        // this.showSpinner(cmp);
        var action = cmp.get("c.geteSigns");
        var DNName = cmp.get("v.DNName").toString();
        action.setParams({
            'DNName' : DNName,
        });
        console.log("已经进到这里了");
        action.setCallback(this,function(response) {
            var state = response.getState();
        console.log("状态："+state);
            if (state === "SUCCESS") {
                var resultData = response.getReturnValue();
                if(!!resultData && !!resultData.eSignFormLineItems
                    && !!resultData.eSignForm){
                    var data = resultData.eSignFormLineItems;
                    cmp.set('v.lineItemSize', data.length);
                    console.log('数据：'+data);
                    var pageSize = cmp.get('v.pageSize');
                    cmp.set("v.data", data);
                    cmp.set("v.eSignForm", resultData.eSignForm);
                    var tempData = this.getTempData(data, 1,pageSize);
                    cmp.set("v.isLastPage", this.isLastPage(data,tempData));
                    cmp.set("v.dataSize", tempData.length);
                    cmp.set("v.currentData", tempData);
                    this.hideSpinner(cmp);
                }else{
                    cmp.set("v.errorMessage", '加载失败，请重新打开此页面！');
                    this.showErrorToast(cmp);
                    this.hideSpinner(cmp);

                }
                
            }else{
                cmp.set("v.errorMessage", '加载失败，请重新打开此页面！');
                this.showErrorToast(cmp);
                this.hideSpinner(cmp);

            }
           
        });
        $A.enqueueAction(action);
    },
    // 明细页 table 首页功能实现
    handleHome : function(cmp) {
        var pageSize = cmp.get('v.pageSize');
        var pageNumber =  cmp.get('v.pageNumber');
        // var data = cmp.get('v.data');
        var currentData = cmp.get('v.currentData');

        //获取输入的value值
        var key = document.getElementById('searchInput').value;
        var data = cmp.get('v.data');
        var tempDataList = [];
        var pageSize = cmp.get('v.pageSize');
        
        if(key){
            for(var i = 0; i<data.length; i++){
                //获取所有name为箱号的input 批量更新
                if(data[i].CaseNumber__c == key){
                    tempDataList.push(data[i]);
                }
            
            }
            //设置分页
            var tempData = this.getTempData(tempDataList,1,pageSize );
            cmp.set('v.pageNumber', 1 );
            
            cmp.set('v.currentData',tempData);
            cmp.set('v.data',data);
            cmp.set("v.isLastPage", this.isLastPage(tempDataList,tempData));
            cmp.set("v.searchSize",tempDataList.length);


        }else{
            //显示全部箱
            var tempData = this.getTempData(data,1,pageSize );
            cmp.set('v.pageNumber', 1 );
            
            cmp.set('v.currentData',tempData);
            cmp.set('v.data',data);
            cmp.set("v.isLastPage", this.isLastPage(data,tempData));
            cmp.set('v.searchSize', null);

        }



    },
    // 明细页 table 尾页功能实现
    handleLast : function(cmp) {
        var pageSize = cmp.get('v.pageSize');
        var pageNumber =  cmp.get('v.pageNumber');
        var data = cmp.get('v.data');
        //获取数据长度
        var size = data.length;
        //获取尾页页码
        var pages=size%pageSize==0?(size/pageSize):(Math.floor(size/pageSize)+1);

        var currentData = cmp.get('v.currentData');

        //获取输入的value值
        var key = document.getElementById('searchInput').value;
        var tempDataList = [];

        if(key){
            for(var i = 0; i<data.length; i++){
                //获取所有name为箱号的input 批量更新
                if(data[i].CaseNumber__c == key){
                    tempDataList.push(data[i]);
                }
            
            }
            //设置分页
            var tempData = this.getTempData(tempDataList,pages,pageSize );
            cmp.set('v.pageNumber', pages );
            console.log('currentData1:'+currentData);
            cmp.set('v.currentData',tempData);
            cmp.set('v.data',data);
            cmp.set("v.isLastPage", this.isLastPage(tempDataList,tempData));
            cmp.set("v.searchSize",tempDataList.length);


        }else{
            //显示全部箱
            var tempData = this.getTempData(data,pages,pageSize );
            cmp.set('v.pageNumber', pages );
            console.log('currentData1:'+currentData);
            cmp.set('v.currentData',tempData);
            cmp.set('v.data',data);
            cmp.set("v.isLastPage", this.isLastPage(data,tempData));
            cmp.set('v.searchSize', null);

        }


    },
    // 明细页 table 下一页功能实现
    handleNext : function(cmp) {
        var pageSize = cmp.get('v.pageSize');
        var pageNumber =  cmp.get('v.pageNumber');
        var data = cmp.get('v.data');
        var currentData = cmp.get('v.currentData');

        //获取输入的value值
        var key = document.getElementById('searchInput').value;
        var tempDataList = [];

        if(key){
            for(var i = 0; i<data.length; i++){
                //获取所有name为箱号的input 批量更新
                if(data[i].CaseNumber__c == key){
                    tempDataList.push(data[i]);
                }
            
            }
            //设置分页
            var tempData = this.getTempData(tempDataList,pageNumber + 1,pageSize );
            cmp.set('v.pageNumber', pageNumber+1 );
            console.log('currentData1:'+currentData);
            cmp.set('v.currentData',tempData);
            cmp.set('v.data',data);
            cmp.set("v.isLastPage", this.isLastPage(tempDataList,tempData));
            cmp.set("v.searchSize",tempDataList.length);


        }else{
            //显示全部箱
            var tempData = this.getTempData(data,pageNumber + 1,pageSize );
            cmp.set('v.pageNumber', pageNumber+1 );
            console.log('currentData1:'+currentData);
            cmp.set('v.currentData',tempData);
            cmp.set('v.data',data);
            cmp.set("v.isLastPage", this.isLastPage(data,tempData));
            cmp.set('v.searchSize', null);

        }
        


    },
    // 明细页 table 上一页功能实现
    handlePrev : function(cmp) {
        var pageSize = cmp.get('v.pageSize');
        var pageNumber =  cmp.get('v.pageNumber');
        var data = cmp.get('v.data');
        var currentData = cmp.get('v.currentData');

        //获取输入的value值
        var key = document.getElementById('searchInput').value;
        var tempDataList = [];

        if(key){
            for(var i = 0; i<data.length; i++){
                //获取所有name为箱号的input 批量更新
                if(data[i].CaseNumber__c == key){
                    tempDataList.push(data[i]);
                }
            
            }
            //设置分页
            var tempData = this.getTempData(tempDataList,pageNumber - 1,pageSize );
            
            cmp.set('v.pageNumber', pageNumber - 1 );
            cmp.set('v.currentData',tempData);
            cmp.set('v.data',data);
            cmp.set("v.isLastPage", this.isLastPage(tempDataList,tempData));
            cmp.set("v.searchSize",tempDataList.length);


        }else{
            //显示全部箱
            var tempData = this.getTempData(data,pageNumber - 1,pageSize );
            
            cmp.set('v.pageNumber', pageNumber - 1 );
            cmp.set('v.currentData',tempData);
            cmp.set('v.data',data);
            cmp.set("v.isLastPage", this.isLastPage(data,tempData));
            cmp.set('v.searchSize', null);

        }
        
    },
    // 计算并返回当前页码的数据
    getTempData: function(data, pageNumber,pageSize){
        var tempData = data.slice((pageNumber-1) * pageSize,pageNumber * pageSize );
        return tempData;
    },
    // 判断当前页是否是最后一页
    isLastPage : function(data,tempData){
        if(tempData.length == 0 ||
            tempData[tempData.length-1].Id == data[data.length-1].Id ){
            return true;
        } else{
                return false;
        }
    },
    // 保存更改内容到当前明细内容
    saveEdition: function (cmp, draftValues) {
        if(!!draftValues && draftValues.length > 0){
            var currentData = cmp.get('v.currentData');
            for(var i = 0; i<currentData.length; i++){
                for(var j = 0; j<draftValues.length; j++){
                    if(currentData[i].Id == draftValues[j].Id){
                        currentData[i].HPConfirmResult__c = draftValues[j].HPConfirmResult__c;
                        break;
                    }
                }
            }
            cmp.set('v.currentData',currentData);
        }
        cmp.set('v.draftValues', []);
    },
    // 明细页点击下一步保存录入表数据到数据库，
    // 如果保存成功，跳转文件上传页
    // 如果保存失败，弹出错误toast，保存在当页
    saveeSign : function (cmp){
        var action = cmp.get("c.saveeSignFormEntry");
        var data = cmp.get('v.data');
        var eSignForm = cmp.get('v.eSignForm');
        var IsSubmit = cmp.get('v.IsSubmit');
        action.setParams({
            'eSignFormLineItems' : data,
            'eSignForm' : eSignForm,
            'entryType' : '医院确认',
            'IsSubmit' : IsSubmit
        });
        this.showSpinner(cmp);
        action.setCallback(this,function(response) {
            this.hideSpinner(cmp);
            var state = response.getState();
            if (state === "SUCCESS") {
                var resultData = response.getReturnValue();
                if( resultData.isSuccess == true ){
                    cmp.set('v.recordId',resultData.result);
                    // cmp.set('v.NextPageDisplay',false);
                    // cmp.set('v.uploadFilePage',true);
                }
                else{
                    cmp.set("v.errorMessage", resultData.result);
                    this.showErrorToast(cmp);
                }
            }else{
                cmp.set("v.errorMessage", '保存失败，请重新加载！');
                this.showErrorToast(cmp);
            }
            
        });
        $A.enqueueAction(action);

    },
    hospitalDetailsPageNextClick : function(cmp) {
        //存一下检索框里的值 不然会被清掉 
        cmp.set('v.searchCase',cmp.get('v.searchCaseKey'));  
        console.log('searchCaseKey:'+cmp.get('v.searchCaseKey'));
    	var check = cmp.get("v.check");
    	// if(check){
	    // 		if(confirm('请确认签收单信息是否准确？')){
	    //         cmp.set('v.HomePageDisplay',false);
	    //         cmp.set('v.NextPageDisplay',true);
	    //     }
    	// }else{
    	// 	 alert('请您确认无误后，请您先勾选，再点击下一步！');
    	// 	 console.log('1:'+check);
    	// 	 console.warn('请您确认无误后，请您先勾选，再点击下一步！');
    	// }
        //隐藏列
        cmp.set('v.hideCheckboxColumn',true);
        cmp.set('v.HomePageDisplay',false);
        cmp.set('v.NextPageDisplay',true);
        
    },
    submitClick : function(cmp){
        //勾选
        cmp.set('v.IsSubmit', true);
        //设置一个flag用于避免提交两次
        cmp.set('v.AgencyClick', true);
        if(confirm('请确认是否保存确认结果？')){
                this.saveeSign(cmp);
            }
        
        alert('已经保存好了！请您点击录入照片或者关闭此页面。');
    },
    // 明细也跳转至文件上传页逻辑，先提示是否填写完毕
    handleDetailsPageNextClick : function(cmp,event) {
        var IsHPShow = cmp.get('v.IsAgencyOrHPShow');
        var  entryType = cmp.get('v.entryType');
        var  AgencyClick = cmp.get('v.AgencyClick');
        // if(IsHPShow&&entryType == '医院确认'){
        //     alert('已经提交医院确认信息，您只能查看,不能提交！');
        // }else{
        //     var draftValues = event.getParam('draftValues');
        //     console.log('draftValues:');
        //     console.log(draftValues);
        //     if(confirm('请确认是否保存确认结果？')){
        //         this.saveeSign(cmp);
        //     }
        // }
        if(AgencyClick){
                    //直接跳转明细页
                    cmp.set('v.NextPageDisplay',false);
                    cmp.set('v.uploadFilePage',true);
            }else{
                var draftValues = event.getParam('draftValues');
                console.log('draftValues:');
                console.log(draftValues);
                if(confirm('请确认是否保存确认结果？')){
                    this.saveeSign(cmp);
                }
                cmp.set('v.NextPageDisplay',false);
                cmp.set('v.uploadFilePage',true);
            }
        
    },
    //返回明细页
    handleShowFielePageNextClick : function(cmp) {
                cmp.set('v.NextPageDisplay',true);
                cmp.set('v.uploadFilePage',false);
    },
    //弹出 成功提示toast 方法
    showSuccessToast : function(cmp) {
        $A.util.removeClass(cmp.find('successDiv'), 'slds-hide');
        window.setTimeout($A.getCallback(function() {
            $A.util.addClass(cmp.find('successDiv'), 'slds-hide');
            }),
            5000
        );
    },
    //弹出 错误提示toast 方法
    showErrorToast : function(cmp) {
        const width = document.documentElement.clientWidth;
        const contentDOM1 = document.getElementById('errorSonDiv1');
        contentDOM1.style.width = width*0.6 + 'px';
        const contentDOM2 = document.getElementById('errorSonDiv2');
        contentDOM2.style.width = width*0.38 + 'px';
        contentDOM2.style.height = '4rem';
        const contentDOM3 =document.getElementById('errorSonDiv3');
        contentDOM3.style.width = width-150 + 'px';
        $A.util.removeClass(cmp.find('errorDiv'), 'slds-hide');
        window.setTimeout($A.getCallback(function() {
            $A.util.addClass(cmp.find('errorDiv'), 'slds-hide');
            }),
            5000
        );
    },
    // 展示 等待框
    showSpinner: function(cmp) {
        // remove slds-hide class from mySpinner
        var spinner = cmp.find("mySpinner");
        $A.util.removeClass(spinner, "slds-hide");
    },
    // 隐藏 等待框
    hideSpinner : function(cmp){
        // add slds-hide class from mySpinner
        var spinner = cmp.find("mySpinner");
        $A.util.addClass(spinner, "slds-hide");
    },
    //返回明细页
    handleShowFielePageNextClick : function(cmp) {

        cmp.set('v.NextPageDisplay',true);
        cmp.set('v.uploadFilePage',false);
        //隐藏列
        cmp.set('v.hideCheckboxColumn',true);
    },
    //返回首页
    handleShowPageNextClick : function(cmp) {
        //存一下检索框里的值 不然会被清掉
        var key = document.getElementById('searchInput').value;  
        cmp.set('v.searchCaseKey',key);  
        cmp.set('v.HomePageDisplay',true);
        cmp.set('v.NextPageDisplay',false);
    },
    //页面上检索功能
    searchByCaseNumber :function(cmp,event){
        //获取输入的value值
        var key = document.getElementById('searchInput').value;
        var data = cmp.get('v.data');
        //定义一个空数组 用于存放按箱分类后的data
        var tempDataList = [];

        var pageSize = cmp.get('v.pageSize');
        //判断搜索框内是否有值
        if(key){
            cmp.set('v.searchFlag',true);
            for(var i = 0; i<data.length; i++){
                //获取所有name为箱号的input 批量更新
                if(data[i].CaseNumber__c == key){
                    tempDataList.push(data[i]);
                }
            
            }
            //设置分页
            var tempData = this.getTempData(tempDataList, 1,pageSize);
            cmp.set("v.isLastPage", this.isLastPage(tempDataList,tempData));
            cmp.set("v.dataSize", tempData.length);
            cmp.set("v.currentData", tempData);
            cmp.set('v.pageNumber', 1);
            cmp.set("v.searchSize",tempDataList.length);

        }else{
            cmp.set('v.searchFlag',false);
            //显示全部箱
            var data = cmp.get('v.data');
            var tempData = this.getTempData(data, 1,pageSize);
            cmp.set("v.isLastPage", this.isLastPage(data,tempData));
            cmp.set("v.dataSize", tempData.length);
            cmp.set("v.currentData", tempData);
            cmp.set('v.pageNumber', 1);
            cmp.set('v.searchSize', null);

        }

            
    },
     OpinionsTODetailsPage : function(cmp, event, helper){
        //存一下检索框里的值 不然会被清掉
        var key = document.getElementById('searchInput').value;  
        cmp.set('v.searchCaseKey',key);  
        cmp.set('v.handlingOpinionsPage',true);
        cmp.set('v.NextPageDisplay',false);
    }, 
    NextOpinions : function(cmp, event, helper){
        cmp.set('v.handlingOpinionsPage',false);
        cmp.set('v.NextPageDisplay',true);
    },
})