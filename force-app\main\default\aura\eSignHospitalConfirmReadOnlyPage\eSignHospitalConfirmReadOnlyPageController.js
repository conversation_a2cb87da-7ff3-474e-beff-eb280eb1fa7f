({
    myAction : function(component, event, helper) {
    
    },
    // 初始化函数
    doInit : function(cmp, event, helper) {
        // 设置明细页表格的构造
        helper.getColumnAndAction(cmp);
        // 从数据库里面读取数据
        helper.geteSign(cmp);
    },
    // 明细页表格的翻页下一页 
    handleNext : function(cmp, event, helper) { 
        helper.handleNext(cmp);
    },
     // 明细页表格的翻页上一页 
    handlePrev : function(cmp, event, helper) {
        helper.handlePrev(cmp);
    },
    // 明细页表格的翻页首页
    handleHome : function(cmp, event, helper) {
        helper.handleHome(cmp);
    },
     // 明细页表格的翻页尾页
    handleLast : function(cmp, event, helper) {
        helper.handleLast(cmp);
    },
    // 表格确认更改
    handleSaveEdition: function (cmp, event, helper) {
        //获取更改内容
        var draftValues = event.getParam('draftValues');
        helper.saveEdition(cmp, draftValues);
    },
    // 文件上传结果后的处理
    handleUploadFinished: function (cmp, event) {
        // 获取上传成功文件的信息
        var uploadedFiles = event.getParam("files");
        if(uploadedFiles.length > 0){
            // 之所以在这里写这个弹出框，因为写在helper里面不知道为啥弹不出来；
            alert("上传完毕，如果不需要继续上传请关闭此页面！");
        }
        // 打印上传成功文件的名字
        uploadedFiles.forEach(file => console.log(file.name));
    },
    hospitalDetailsPageNextClick : function (cmp, event, helper) {
        helper.hospitalDetailsPageNextClick(cmp);
    },
    handleChange :  function (cmp, event, helper) {
        console.log('点击复选框!');
        var s = cmp.get("v.check");
        console.log('q:'+s);
    },
    // 明细页跳转至文件上传页
    handleDetailsPageNextClick : function (cmp, event, helper) {
        helper.handleDetailsPageNextClick(cmp, event);
    },
    //保存提交按钮(考虑此时要不要插入一条数据)
    submitClick : function(cmp, event, helper){
        helper.submitClick(cmp);
    },
    handleShowPageNextClick :function (cmp, event, helper) {
        helper.handleShowPageNextClick(cmp);
    },
    handleShowFielePageNextClick:function (cmp, event, helper) {
        helper.handleShowFielePageNextClick(cmp);
    },
    //主页跳首页
    BackToHomePage : function(cmp, event, helper){
        if(confirm('确认回到首页吗？')){
                
                cmp.set("v.HomePage", false);
                cmp.set("v.uploadFilePage", false);
                cmp.set("v.HomePageDisplay", true);
            	cmp.set("v.handlingOpinionsPage",false);
            }
        

        // helper.BackToHomePage(cmp);
    },
    handleShowFielePageNextClick : function(cmp, event, helper){
        helper.handleShowFielePageNextClick(cmp);
    },
    handleShowPageNextClick : function(cmp, event, helper){
        helper.handleShowPageNextClick(cmp);
    },
    searchByCaseNumber : function(cmp, event, helper){
        helper.searchByCaseNumber(cmp,event);
    },
     OpinionsTODetailsPage : function(cmp, event, helper){
        helper.OpinionsTODetailsPage(cmp);
    },
    NextOpinions : function(cmp, event, helper){
        helper.NextOpinions(cmp);
    },
})