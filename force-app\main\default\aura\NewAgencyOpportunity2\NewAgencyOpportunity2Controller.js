({

    doInit: function (component, event, helper) {
        let rid = component.get('v.recordId');
        let pid = null;
        if (!rid) {
            //pid = window.location.href.replace("https://","").split("/")[4];
            pid = 'Agency_Opportunity__c';
        }

        let record_type_id = '';
        let pageref = component.get("v.pageReference")
        console.log('pageref = ' + JSON.stringify(pageref));
        if (!record_type_id && pageref) {
            record_type_id = pageref.state.recordTypeId ? pageref.state.recordTypeId : '';
        }

        let isClone = component.get('v.isClone');
        if (!rid || isClone) {
            component.set('v.title', $A.get("$Label.c.New") + '经销商询价');
            component.set("v.recordTypeId", record_type_id);
        } else {
            console.log('编辑');
            helper.CallBackAction(component,'getApproveStatus',{recordId:rid},function(data){
                var result = data.getReturnValue();
                console.log('result-----：'+JSON.stringify(result));
                if(result == true){
                    if(window.location.href.indexOf('lightning') == -1){
                        $A.get("e.force:closeQuickAction").fire();
                    }else{
                        helper.lightningJump(component, event, helper);
                    }
                    window.alert('数据审批中，不能编辑');
                    // return;
                }
            });
            component.set('v.title', $A.get("$Label.c.Edit") + '经销商询价');
        }
        console.log('rid: ' + rid);
        console.log('pid: ' + pid);
        console.log('record_type_id: ' + record_type_id);
        component.set("v.showSpinner", true);
        let that = this;
        debugger
        helper.CallBackAction(component, 'Init', {
            rid: rid,
            pid: pid,
            //rid : component.get('v.recordId'),
            record_type_id: record_type_id
        }, function (data) {
            console.log('data: ' + data);
            var rv = data.getReturnValue();
            console.log('rv:' + rv);
            console.log('rv.Data:' + rv.Data);
            console.log('rv.IsSuccess:' + rv.IsSuccess);
            if (data.getState() == "SUCCESS") {
                if (rv.IsSuccess) {

                    //deloitte-zhj 20231027 区分内部用户和社区用户
                    let isStandard = rv.Data.isStandard;
                    console.log('isStandard = ' + isStandard);
                    component.set('v.isStandard', isStandard);

                    let layout = JSON.parse(rv.Data.layout);
                    console.log('before layout  = ' + JSON.stringify(layout));
                    //deloitte-zhj 20231214 标准用户跳转到标准页面 start
                    console.log('12');
                    if(isStandard && !rid){
                        var urlStr = '/lightning/o/Agency_Opportunity__c/new?count=1&nooverride=1&useRecordTypeCheck=1&navigationLocation=LIST_VIEW&uid=170254592908064189&backgroundContext=%2Flightning%2Fo%2FAgency_Opportunity__c%2Flist%3FfilterName%3DRecent&recordTypeId=' + rv.Data.recordTypeId;
                        window.open(urlStr,'_self');
                        return
                    }
                    if(isStandard && rid){
                        var urlStr = '/lightning/r/Agency_Opportunity__c/' + rid + '/edit?count=1&nooverride=1&backgroundContext=%2Flightning%2Fr%2FAgency_Opportunity__c%2F' + rid + '%2Fview';
                        window.open(urlStr,'_self');
                        return
                    }
                    //deloitte-zhj 20231214 标准用户跳转到标准页面 end
                    let fields = rv.Data.fields;
                    // let staticResource = JSON.parse(rv.Data.staticResource)
                    let section_names = layout.map(s => s.label);
                    for(let i = 0; i < section_names.length;i++){
                        if(section_names[i] == 'Information'){
                            section_names[i] = '经销商询价信息';
                        }
                        if(section_names[i] == 'System Information'){
                            section_names[i] = '系统信息';
                        }
                    }
                                        // section_names = layout.map(s=>s.label);
                    component.set('v.section_names', section_names);
                    component.set('v.layout', layout);
                    component.set('v.fields', fields);
                    // component.set('v.staticResource', staticResource);
                    
                    let m = {};
                    // for (let f of staticResource.PIDetails) {
                        //     m[f.SF_Field_API_Name__c] = f;
                    // }
                    // component.set('v.pi_fields_map', m);
                    
                    var agencyHospitalLinkId = component.get("v.agencyHospitalLinkId");
                    console.log('agencyHospitalLinkId = ' + agencyHospitalLinkId);
                    var departmentName = component.get("v.departmentName");
                    console.log('departmentName = ' + departmentName);
                    if (rv.Data && rv.Data.data) {
                        
                        console.log('rv.Data.data = ' + rv.Data.data);

                        for (let s of layout) {
                            console.log('s.label = ' + s.label);
                            if(s.label == 'Information'){
                                s.label = '经销商询价信息';
                            }
                            if(s.label == 'System Information'){
                                s.label = '系统信息';
                            }
                            for (let c of s.layoutColumns) {
                                for (let item of c.layoutItems) {
                                    if (rv.Data.data.hasOwnProperty(item.field)) {
                                        item.value = rv.Data.data[item.field];
                                    }
                                    if (fields.hasOwnProperty(item.field)) {
                                        item.fi = fields[item.field];
                                    }

                                    //deloitte-zhj 20231104 
                                    if(item.field == 'Agency_Contact__c' && item.value){
                                        component.set('v.iconName', 'utility:close');
                                    }
                                    if(item.field == 'Agency_Contact__c' && !item.value){
                                        component.set('v.iconName', 'utility:search');
                                    }
                                    if(item.field == 'Agency_Hospital__c'){
                                        item.value = agencyHospitalLinkId;
                                    }
                                    if(item.field == 'Department_Cateogy__c'){
                                        item.value = departmentName;
                                    }
                                }
                            }
                        }
                        // component.set('v.layout',layout);
                        console.log('layout = ' + JSON.stringify(layout));
                        component.set('v.record_data', rv.Data.data);
                        if (rv.Data.data.Agency_Contact__r) {
                            component.set('v.ac_name', rv.Data.data.Agency_Contact__r.Name);
                        }
                    } else {
                        
                        console.log('rv.Data.data = ' + rv.Data.data);
                        console.log('layout = ' + JSON.stringify(layout));
                        for (let s of layout) {
                            console.log('s.label = ' + s.label);
                            if(s.label == 'Information'){
                                s.label = '经销商询价信息';
                            }
                            if(s.label == 'System Information'){
                                s.label = '系统信息';
                            }
                            for (let c of s.layoutColumns) {
                                for (let item of c.layoutItems) {
                                    if (rv.Data && fields.hasOwnProperty(item.field) && fields[item.field].References && fields[item.field].References.map(m => m.value).indexOf(rv.Data.pidType) > -1) {
                                        item.value = pid;
                                    }
                                    //deloitte-zhj 20231104 
                                    if(item.field == 'Agency_Contact__c' && item.value){
                                        component.set('v.iconName', 'utility:close');
                                    }
                                    if(item.field == 'Agency_Contact__c' && !item.value){
                                        component.set('v.iconName', 'utility:search');
                                    }
                                    if(item.field == 'Agency_Hospital__c'){
                                        item.value = agencyHospitalLinkId;
                                    }
                                    if(item.field == 'Department_Cateogy__c'){
                                        item.value = departmentName;
                                    }

                                }
                            }
                        }
                        
                        // component.set('v.layout',layout);
                    }
                    // component.set('v.fields',fields);
                    // component.set('v.staticResource',staticResource);
                    //component.set("v.showSpinner", false);
                } else {
                    component.set("v.showSpinner", false);
                    helper.ShowToast({
                        "message": rv.Message,
                        "type": "error"
                    });
                }

            } else {
                component.set("v.showSpinner", false);
                helper.ShowToast({
                    "message": "Init error",
                    "type": "error"
                });

            }
        });
    },
    handleLoad: function (component, event, helper) {
        console.log('recordEditForm handleSuccess')
        component.set("v.showSpinner", false);
    },
    saveClick: function (component, event, helper) {

        // let staticResource = component.get('v.staticResource');
        let record_id = component.get('v.recordId');
        let record_type_id = component.get('v.recordTypeId');

        let isClone = component.get('v.isClone');
        // let payloadPi = {};
                let layout = component.get('v.layout');

        // let pi_fields_map = component.get('v.pi_fields_map');

        for (let s of layout) {
            for (let c of s.layoutColumns) {
                for (let item of c.layoutItems) {
                                        if (item.behavior == "Required" && !item.value) {
                        let fs = component.find("field");
                        for (let fi in fs) {
                            let f = fs[fi];
                            if (!f.get('v.value')) {
                                f.reset();
                            }
                        }
                        return;
                    }
                }
            }
        }

        component.set("v.showSpinner", true);
        debugger
        let data = {};
        if (record_id && !isClone) {
            data.Id = record_id;
        }
        for (let s of layout) {
            for (let c of s.layoutColumns) {
                for (let item of c.layoutItems) {
                    if (item.field && item.behavior != "Readonly") {
                        console.log(item.field + ' value:' + item.value)
                        data[item.field] = item.value;

                        if(item.field == 'Agency_Person__c' && item.value && item.value.length == 0){
                            data[item.field] = null;
                        }
                        //deloitte-zhj 20231104
                        if(item.field == 'Change_To_Opportunity__c' && item.value && item.value.length == 0){
                            data[item.field] = null;
                        }
                    }
                }
            }
        }
        let x = 1;
        helper.CallBackAction(component, 'Save', {
            data: data,
            transId: '1',
            recordTypeId: record_type_id
        }, function (data) {
            component.set("v.showSpinner", false);
            if (data.getState() == "SUCCESS") {
                var rv = data.getReturnValue();
                console.log(rv);
                var sfId = null;
                if (rv.IsSuccess) {
                    sfId = rv.Data.recordId;
                    helper.ShowToast({
                        "message": "保存成功",
                        "type": "success"
                    });
                    component.set('v.Id', sfId);
                    helper.closeAgencyOpportunity(component);
                } else {
                    helper.ShowToast({
                        "message": rv.Message,
                        "type": "error"
                    });
                }
            } else {
                helper.ShowToast({
                    "message": "Init error",
                    "type": "error"
                });
            }
        })
    },
    cancelClick: function (component, event, helper) {
        debugger
        helper.closeAgencyOpportunity(component);
    },
    dataChange: function (component, event, helper)  {
        let fieldName = event.getSource().get("v.fieldName");
        if (fieldName == 'Agency_Hospital__c' && !event.getParam("value")[0]) {
            let layout = component.get('v.layout');
            for(let s of layout){
                for(let c of s.layoutColumns){                
                    for(let item of c.layoutItems){
                        if(item.field == 'Agency_Contact__c'){
                            item.value = '';
                            component.set('v.ac_name','');
                            component.set('v.searchKeyWord', '');
                        }
                    }
                }
            }
            component.set('v.iconName','utility:search');
        }
    },
    searchAgencyContact: function (component, event, helper) {
        component.find('button').set('v.disabled', true);
        helper.resetTable();
        helper.preparePayloadForSearchAgencyContact(component, event, helper);
    },
    // initContactTable: function (component, event, helper) {
        //     let cols = ['Name', 'AgencyHospital', 'DoctorDivision1', 'Type'];
        //     let contactInfoList = [];
        //     let data = component.get('v.result');
        //     if (data.object) {
            //         for (var i = 0; i < data.object.length; i++) {
                //             if (data.object[i].dataId) {
                    //                 let contactInfo = new Object();
                    //                 //需要修改
                    //                 contactInfo.Name = data.object[i].name;
                    //                 contactInfo.DoctorDivision1 = data.object[i].doctorDivision1;
                    //                 contactInfo.Type = data.object[i].type;
                    //                 contactInfo.pi = true;
                    //                 contactInfo.sfRecordId = '';
                    //                 contactInfoList.push(contactInfo);
                //             }
    //         }
    //     }
    //     let AWSIdToSFIdMapValue = {};
    // },
    openModel: function (component, event, helper) {
        let layout = component.get('v.layout');
        for (let s of layout) {
            for (let c of s.layoutColumns) {
                for (let item of c.layoutItems) {
                    if (item.field == 'Agency_Hospital__c' && !item.value) {
                        console.log('item.value = ' + item.value)
                        helper.ShowToast({
                            "message": "请先选择经销商医院",
                            "type": "warning"
                        });
                        return
                    } else if (item.field == 'Agency_Hospital__c' && item.value) {
                        component.set('v.agencyHospitalId', item.value)
                        console.log('v.agencyHospitalId item.value = ' + item.value)
                    }
                }
            }
        }
        // Set isModalOpen true
        component.set("v.isModalOpen", true);
        component.find('button').set('v.disabled', true);
        helper.preparePayloadForSearchAgencyContact(component, event, helper);
    },

    closeModel: function (component, event, helper) {
        // Set isModalOpen false
        component.set("v.isModalOpen", false);
    },

    saveDetails: function (component, event, helper) {
        //do something 
        component.set("v.isModalOpen", false);
    },

    showNewAgency: function (component, event, helper) {
        //do something 
        component.set("v.showNewAgencyContact", true);
    },
    closeAgencyContact: function (component, event, helper) {
        var message = event.getParam("showNewAgencyContact");
        component.set("v.showNewAgencyContact", message);
        component.find('button').set('v.disabled', true);
        helper.resetTable();
        helper.preparePayloadForSearchAgencyContact(component, event, helper);
    },
    //deloitte-zhj 20231104 清空客户人员 start
    clearName: function (component, event, helper){
        console.log("enter clearName");
        if (component.get('v.ac_name')) {
            component.set('v.ac_name','');
            let layout = component.get('v.layout');
            for (let s of layout) {
                for (let c of s.layoutColumns) {
                    for (let item of c.layoutItems) {
                        if(item.field == 'Agency_Contact__c'){
                            item.value = '';
                        }
                    }
                }
            }
        }
        component.set('v.iconName','utility:search');
    },
    //deloitte-zhj 20231104 清空客户人员 end
})