public with sharing class TransferEquipmentSetSRListController {
    public List<TransferApplySummary__c> resList { get; set; }
    public String sr_open { get; set; }
    public String sr_apply_no { get; set; }
    public String sr_id { get; set; }
    public String sr_type { get; set; }
    public List<SelectOption> typeList {get; set;}
    public String selectedType {get; set;}
    private String Id;
    public Boolean canSelect {get;set;}
    public Boolean showDown {get;set;}
    public Boolean showUp {get;set;}
    public String sr_FromLocation {get;set;}
    public String sr_ToLocation {get;set;}
    public Boolean qcdJudge {get;set;}// QCD WYL 20250314 add
    public Integer getResListSize() {
        return resList.size();
    }
    private static final Map<String, String> STATUS_TYPE_MAP = new Map<String, String>{
        '待下架' => '下架',
        '已下架' => '发货检测',
        '待发货' => '发货',
        '出库前已检测' => '发货',
        '已回寄' => '欠品',
        '已出库' => '欠品',
        '申请者已收货' => '欠品',
        '欠品中' => '入库检测',
        '已回收' => '入库检测',
        '已收货' => '入库检测'
    };
    public TransferEquipmentSetSRListController() {
        Id = ApexPages.currentPage().getParameters().get('id');
        sr_type = ApexPages.currentPage().getParameters().get('type');
        sr_open = ApexPages.currentPage().getParameters().get('open');
    }

    public void init() {
        resList = new List<TransferApplySummary__c>();
        User user1 = [SELECT OCM_man_province_Rental__c, Work_Location__c, UserRole.Name, Profile.Name FROM User WHERE Id=:UserInfo.getUserId()];
        canSelect = false;
        
        if (String.isNotBlank(Id)) {
            resList = [select TransferApply__r.Name
                            , TransferApply__r.RecordType.DeveloperName
                            , TransferApply__r.Destination_location__c
                            , TransferApply__r.Asset_return_time__c
                            , TransferApply__r.Yi_loaner_arranged__c
                            , TransferApply__r.From_Location__c
                            , First_TAD__r.Asset__r.Fixture_Model_No_F__c
                            , First_TAD__r.Asset__r.SerialNumber
                            , First_TAD__r.Asset__r.Fixture_QRCode__c
                            , OnStock_By_Cancel_Cnt__c
                            , TAS_Status__c
                            , Wei_Arrival_in_wh__c
                            , Yi_loaner_arranged__c
                            , Asset_return_time__c
                            , Inspection_before_ng_cnt__c
                            , Wei_Return__c
                            , TransferDetail_Cnt__c
                    from TransferApplySummary__c
                    where TransferApply__r.Name = :id
                        OR (First_TAD__r.Asset__r.Fixture_QRCode__c = :id AND TransferApply__r.ShelfUp_ng_num__c > 0)
                    order by Name
                ];
                TransferApply__c tr = [select id,name,TA_Status__c from TransferApply__c where name = :id];
                if (resList.size() > 0) {
                sr_apply_no = resList[0].TransferApply__r.Name;
                sr_id = resList[0].TransferApply__r.Name;
                sr_FromLocation = resList[0].TransferApply__r.From_Location__c;
                sr_ToLocation = resList[0].TransferApply__r.Destination_location__c;
                String recordTypeName = resList[0].TransferApply__r.RecordType.DeveloperName;
                Boolean isReturned = false;
                for(TransferApplySummary__c res:resList) {
                    // 下架后有取消
                    isReturned = isReturned || res.OnStock_By_Cancel_Cnt__c > 0;
                    // 办事处回寄
                    isReturned = isReturned || res.TransferDetail_Cnt__c - res.Wei_Return__c > 0;
                    // 下架前检测NG
                    isReturned = isReturned || res.Inspection_before_ng_cnt__c > 0;
                    // 如果不是来自同一调拨单，全清空
                    if(sr_apply_no != res.TransferApply__r.Name) {
                        sr_apply_no = '';
                        sr_id = '';
                        sr_FromLocation = '';
                        sr_ToLocation = '';
                        recordTypeName = '';
                        isReturned = false;
                    }
                }
                canSelect = initTypeList(recordTypeName, user1, sr_FromLocation, sr_ToLocation, isReturned, tr.TA_Status__c);
            }
            else {
                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '备品或调拨单不存在'));
            }
        }
    }
    /**
    @description 根据申请单类型，用户工作地，调拨至地区显示下拉框
    @param recordTypeName 记录类型developername
    @param u 用户
    @param destination 调拨至地区
    @param isReturned 是否需要在备品中心重新上架
    */
    private Boolean initTypeList(String recordTypeName, User u, String fromlocation, String destination, Boolean isReturned, String taStatus){
        typeList = new List<SelectOption>();
        typeList.add(new SelectOption('无','无'));
        showUp = false;
        showDown = false;
        qcdJudge = false;// QCD WYL 20250314 add
        switch on recordTypeName {
            when 'AgencyToCenter' {
                showDown = false;
                showUp = isMatch(u, destination);
            }
            when 'CenterToAgency', 'CenterToOther','ConsumablesTransferTube' {
                showDown = isMatch(u, fromlocation);
                showUp = isMatch(u, fromlocation) && isReturned;
            }
            when 'CenterToCenter' {
                showDown = isMatch(u, fromlocation);
                showUp = isMatch(u, destination) || (showDown && isReturned);
                qcdJudge = true;// QCD WYL 20250314 add
            }
            when 'InsideCenter' {
                showDown = false;
                showUp = false;
            }
        }
        // 全部显示，通过showDown和showUp控制是否允许跳转
        typeList.add(new SelectOption('下架', '下架'));
        typeList.add(new SelectOption('发货检测', '发货检测'));
        typeList.add(new SelectOption('发货','发货'));
        typeList.add(new SelectOption('欠品','欠品'));
        typeList.add(new SelectOption('入库检测','入库检测'));
        typeList.add(new SelectOption('上架', '上架'));

        // System_UserSetting__c config = System_UserSetting__c.getInstance(UserInfo.getUserId());
        // selectedType = config.TAS_Action_Type__c;
        // if (String.isBlank(selectedType)){
        //     selectedType = '无';
        // }
        // 判断taStatus如果是待下架 selectedType 显示 下架 如果是待发货 selectedType 显示 发货
        selectedType = STATUS_TYPE_MAP.containsKey(taStatus) ? STATUS_TYPE_MAP.get(taStatus) : '无';
        return showUp || showDown;
    }
    /**
    @description 用户角色与地区地否匹配
    @param u User
    @param center 备品中心
    */
    private Boolean isMatch(User u, String center) {
        if(u.Profile.Name == '2B3_备品中心管理者(照片)'){
            return true;
        }
        //20210519 you WLIG-C2J9AA
        if(u.UserRole.Name == '备品运营部'){
            return true;
        }
        switch on center {
            when '北京 备品中心' {
                return u.UserRole.Name == '备品中心北方管理成员';
            }
            when '上海 备品中心' {
                return u.UserRole.Name == '备品中心华东管理成员';
            }
            when '广州 备品中心' {
                return u.UserRole.Name == '备品中心南方管理成员';
            }
        }
        return false;
    }
    // 记忆用户的选择
    public void setType(){
        if (selectedType != '无' && String.isNotBlank(selectedType)) {
            System_UserSetting__c config = System_UserSetting__c.getInstance(UserInfo.getUserId());
            config.TAS_Action_Type__c = selectedType;
            FixtureUtil.withoutUpsertObjects(new System_UserSetting__c[]{config});
        }
    }
    /**
     * 转换办事处到省
     * @param str 存放地
     * @return OCSM管理省名字
     */
    private String getOcmMgtProvStr(String str) {
        Map<String, String> ocmMgtProvMap = AssetWebService.getOcmMgtProvMap();
        String res = '';
        if (ocmMgtProvMap.containsKey(str)) {
            res = ocmMgtProvMap.get(str);
        }
        return res;
    }
    public static void testMock1(){
        Integer i = 0;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
    }
}