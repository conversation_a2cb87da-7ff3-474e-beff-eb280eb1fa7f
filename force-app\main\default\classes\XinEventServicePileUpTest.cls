/**
 * This class contains unit tests for validating the behavior of Apex classes
 * and triggers.
 *
 * Unit tests are class methods that verify whether a particular piece
 * of code is working properly. Unit test methods take no arguments,
 * commit no data to the database, and are flagged with the testMethod
 * keyword in the method definition.
 *
 * All test methods in an organization are executed whenever Apex code is deployed
 * to a production organization to confirm correctness, ensure code
 * coverage, and prevent regressions. All Apex classes are
 * required to have at least 75% code coverage in order to be deployed
 * to a production organization. In addition, all triggers must have some code coverage.
 * 
 * The @isTest class annotation indicates this class only contains test
 * methods. Classes defined with the @isTest annotation do not count against
 * the organization size limit for all Apex scripts.
 *
 * See the Apex Language Reference for more information about Testing and Code Coverage.
 */
@isTest
private class XinEventServicePileUpTest {

    static testMethod void myUnitTest() {
        List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        if (rectCo.size() == 0) {
            return;
        }
        List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 呼吸科'];
        if (rectSct.size() == 0) {
            return;
        }
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 消化科'];
        if (rectDpt.size() == 0) {
            return;
        }
        
        Account company = new Account();
        company.RecordTypeId = rectCo[0].Id;
        company.Name = 'Katsu テスト';
        insert company;
        
        Account section = new Account();
        section.RecordTypeId = rectSct[0].Id;
        section.Name         = '*';
        section.Department_Class_Label__c = '消化科';
        section.ParentId                  = company.Id;
        section.Hospital_Department_Class__c = company.Id;
        insert section;
        
        Account depart = new Account();
        depart.RecordTypeId = rectDpt[0].Id;
        depart.Name         = '*';
        depart.Department_Name__c  = 'NFM001TestDepart';
        depart.ParentId            = section.Id;
        depart.Department_Class__c = section.Id;
        depart.Hospital__c         = company.Id;
        insert depart;
        
        Maintenance_Contract__c mc = new Maintenance_Contract__c();
        mc.Name = 'aiueo';
        mc.Department__c = depart.Id;
        insert mc;
        
        Date dtDate = System.today().addDays(-4);
        Daily_Report__c dr = new Daily_Report__c(reported_date__c = dtDate, Status__c = '申請中', Reporter__c = Userinfo.getUserId());
        Daily_Report__c dr2 = new Daily_Report__c(reported_date__c = dtDate.addDays(2), Status__c = '申請中', Reporter__c = Userinfo.getUserId());
        Daily_Report__c dr3 = new Daily_Report__c(reported_date__c = dtDate.addDays(4), Status__c = '作成中', Reporter__c = Userinfo.getUserId());
        Daily_Report__c dr4 = new Daily_Report__c(reported_date__c = dtDate.addDays(3), Status__c = '未承認', Reporter__c = Userinfo.getUserId());
        insert new Daily_Report__c[] {dr, dr2, dr3, dr4};
        Event__c ec = new Event__c(Daily_Report__c=dr.Id, StartDateTime__c=System.now(), EndDateTime__c=System.now(), Subject__c='Subject', Location__c='Location', ActivityDate__c=dr.reported_date__c);
        Event__c ec2 = new Event__c(Daily_Report__c=dr2.Id, StartDateTime__c=System.now(), EndDateTime__c=System.now(), Subject__c='Subject', Location__c='Location', ActivityDate__c=dr2.reported_date__c);
        Event__c ec3 = new Event__c(Daily_Report__c=dr3.Id, StartDateTime__c=System.now(), EndDateTime__c=System.now(), Subject__c='Subject', Location__c='Location', ActivityDate__c=dr3.reported_date__c);
        Event__c ec4 = new Event__c(Daily_Report__c=dr4.Id, StartDateTime__c=System.now(), EndDateTime__c=System.now(), Subject__c='Subject', Location__c='Location', ActivityDate__c=dr4.reported_date__c);
        insert new Event__c[] {ec, ec2, ec3, ec4};
        
        List<Event_Service__c> eoList = new List<Event_Service__c>();
        Date dt = Date.today();
        
        Event_Service__c es = new Event_Service__c();
        es.Service__c = mc.Id;
        es.Date__c = dt;
        es.Daily_Report__c = dr.Id;
        es.EventC_ID__c = ec.Id;
        eoList.add(es);
        Event_Service__c es2 = new Event_Service__c();
        es2.Service__c = mc.Id;
        es2.Date__c = dt.addDays(2);
        es2.Daily_Report__c = dr2.Id;
        es2.EventC_ID__c = ec2.Id;
        eoList.add(es2);
        Event_Service__c es3 = new Event_Service__c();
        es3.Service__c = mc.Id;
        es3.Date__c = dt.addDays(2);
        es3.Daily_Report__c = dr3.Id;
        es3.EventC_ID__c = ec3.Id;
        eoList.add(es3);
        Event_Service__c es4 = new Event_Service__c();
        es4.Service__c = mc.Id;
        es4.Date__c = dt.addDays(3);
        es4.Daily_Report__c = dr4.Id;
        es4.EventC_ID__c = ec4.Id;
        eoList.add(es4);
        insert eoList;
        
        mc = [select Xin_Last_Follow_Day__c from Maintenance_Contract__c where Id = :mc.Id];
        System.assertEquals(dt.addDays(3), mc.Xin_Last_Follow_Day__c);
        
        es.Date__c = dt.addDays(5);
        update es;
        mc = [select Xin_Last_Follow_Day__c from Maintenance_Contract__c where Id = :mc.Id];
        System.assertEquals(dt.addDays(5), mc.Xin_Last_Follow_Day__c);

        delete es;
        mc = [select Xin_Last_Follow_Day__c from Maintenance_Contract__c where Id = :mc.Id];
        System.assertEquals(dt.addDays(3), mc.Xin_Last_Follow_Day__c);
        
        delete mc;
        List<Event_Service__c> assertEoList = [select Id from Event_Service__c where id in :eoList];
        System.assertEquals(0, assertEoList.size());
    }
}