@isTest
private class TenderManageControllerTest {


    static testMethod void testMethod1() {

        // レコードタイプ取得
        RecordType hospitalRec = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        RecordType sectionRec = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 消化科'];
        RecordType departmentRec = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 消化科'];
        
        // 病院作成
        Account hospital = new Account();
        hospital.RecordTypeId = hospitalRec.Id;
        hospital.Name = 'TestHospital';
        insert hospital;
        
        // 病院を作ると戦略科室は、トリガーによって作られている
        // Account section = [select Management_Code__c, Management_Code_Auto__c, Name, Id from Account where Parent.Id = :hospital.Id and RecordTypeId = :sectionRec.Id limit 1];
        Account section = [select Management_Code__c, Name, Id from Account where Parent.Id = :hospital.Id and RecordTypeId = :sectionRec.Id limit 1];
        
        // 診療科1を作成
        Account depart1 = new Account();
        depart1.RecordTypeId = departmentRec.Id;
        depart1.Name = '*';
        depart1.Department_Name__c  = 'TestDepart';
        depart1.ParentId = section.Id;
        depart1.Department_Class__c = section.Id;
        depart1.Hospital__c = hospital.Id;
        depart1.AgentCode_Ext__c = '9999997';
        insert depart1;

      
        User loginUser = new User();
        
        Profile p = [select Id from Profile where id = :System.Label.ProfileId_2S6];
        /*
        // ******** ljh  SWAG-CK28WT 注释
        Profile p = [select Id from Profile where Name = '2S6_销售本部窗口&营业助理'];
        loginUser.ProfileId  = p.Id;
        loginUser.FirstName = 'ユーザー';
        loginUser.LastName = 'テスト';
        loginUser.Email = '<EMAIL>';
        loginUser.emailencodingkey='UTF-8';
        loginUser.languagelocalekey='zh_CN';
        loginUser.localesidkey='ja_JP';
        loginUser.timezonesidkey='Asia/Shanghai';
        loginUser.Username = '<EMAIL>' + System.now().millisecond();
        loginUser.Alias = 'テユ';
        loginUser.CommunityNickname = 'tu1' + System.now().millisecond();
        insert loginUser;*/
        loginUser = [select id from user where IsActive = true and ProfileId =:p.Id limit 1];
        // System.runAs(loginUser){ // ******** ljh  SWAG-CK28WT 注释
            

        //待确认招投标项目 : GI助理/SP助理 and 01.待确认
        Tender_information__c info = new Tender_information__c();
      info.Name = 'TEST001';
        info.OpportunityStatus__c = '';
        info.IsBid__c = null;
        info.IsRelateProject__c = null;
        // ******** ljh  SWAG-CK28WT start 
        info.subInfoType__c='1-1：意见征集'; 
        info.ownerId = loginUser.Id;
        insert info;

        info.GI_assistant__c = String.valueOf(loginUser.Id).subString(0,15);
        update info;

        info = [select status__c,Is_GIORSP__c,GI_assistant__c from Tender_information__c  where id = :info.Id];

        //System.assertEquals('00510000005sEEMAA2',info.GI_assistant__c); //18 00510000005sEEMAA2
        //System.assertEquals(null,loginUser.Id); //18 00510000005sEEMAA2
        // System.assertEquals(true,info.Is_GIORSP__c);
        System.assertEquals('01.待确认',info.status__c);
        // System.assertEquals(true,info.Is_GIORSP__c);


        //待关联询价的招投标项目 : GI助理/SP助理 and 04.确认应标
      Tender_information__c info2 = new Tender_information__c();
      info2.Name = 'TEST002';
        info2.OpportunityStatus__c = '';        
        info2.IsBid__c = '是';
        info2.OpportunityNum__c = 0;
        // ******** ljh  SWAG-CK28WT start 
        info2.subInfoType__c='1-1：意见征集';  
        insert info2;

        info2.SP_assistant__c = String.valueOf(loginUser.Id).subString(0,15);
        update info2;

        info2 = [select status__c,Is_GIORSP__c from Tender_information__c  where id = :info2.Id];

        // System.assertEquals(true,info2.Is_GIORSP__c);
        // System.assertEquals('04.确认应标',info2.status__c);
        System.runAs(loginUser){ // ******** ljh  SWAG-CK28WT add
         TenderManageController.getCurrentTenderInformation();  
         TenderManageController.getOBSATenderInformation();
         //lwt  20231212 DB202311717846 start
         TenderManageController.goModifyOwnerAgreementInfomation();
         TenderManageController.goModifyOwnerAgreementByCurrentUser();
         TenderManageController tc=new TenderManageController();
         //lwt  20231212 DB202311717846 end
         // DB202406382590 招标项目：不应标申请里增加涉及科室 start
         TenderManageController.getCurrentTenderTransfInformation();
         // DB202406382590 招标项目：不应标申请里增加涉及科室 end
        }



    }
}