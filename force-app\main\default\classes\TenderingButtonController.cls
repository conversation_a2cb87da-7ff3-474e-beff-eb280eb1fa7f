/*
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-31 13:36:53
 * @LastEditors: zhangchunxu
 * @LastEditTime: 2023-09-12 13:35:22
 * 
 */
public class TenderingButtonController {
    @AuraEnabled
    public static InitData initTenderingController(String recordId) {
        InitData res = new initData();
        try{
            Tender_information__c report = [SELECT 	OpportunityNum__c,OwnerId,Id,status__c,Name,IsRelateProject__c,TerminateApprovalStatus__c,Logical_delete__c,Hospital__c FROM Tender_information__c WHERE Id = :recordId LIMIT 1];
            List<AggregateResult> AccQuery = [SELECT count(id) total_cnt, sum(Opp_StageName_Check__c) stage_cnt, sum(Opp_StatusF_Check__c) statusf_cnt FROM Tender_Opportunity_Link__c WHERE Tender_information__c = :report.Id];
            res.AccSize = AccQuery;
            res.OwnerId = report.OwnerId;
            res.Id = report.Id;
            res.status = report.status__c;
            res.name = report.Name;
            // res.opportunityNum = String.valueOf(report.OpportunityNum__c);
            res.opportunityNum = Integer.valueOf(report.OpportunityNum__c);
            res.isRelateProject = report.IsRelateProject__c;
            res.profileId = UserInfo.getProfileId();
            res.systemProfileId = getProfileIdByName(LightingButtonConstant.SYSTEM_PROFILE_NAME);
            res.TwoS1_Sales_Hospital = System.Label.TwoS1_Sales_Hospital;
            res.TwoS1_Sales_Hospital_Non_Singleton = System.Label.TwoS1_Sales_Hospital_Non_Singleton;
            res.TwoS2_Sales_Product = getProfileIdByName(LightingButtonConstant.TwoS2_Sales_Product);
            res.TwoS4_Sales_Manager = getProfileIdByName(LightingButtonConstant.TwoS4_Sales_Manager);
            res.TwoS4_Sales_Manager_Non_Singleton  = getProfileIdByName(LightingButtonConstant.TwoS4_Sales_Manager_Non_Singleton);
            res.TwoS4_Sales_Manager_Majordomo  = getProfileIdByName(LightingButtonConstant.TwoS4_Sales_Manager_Majordomo);
            res.TwoS6_Trade_Assistant = System.Label.TwoS6_Trade_Assistant;
            res.TwoS6_Sales_Headquarters = System.Label.TwoS6_Sales_Headquarters;
            res.TwoS7_Sales_Headquarters = System.Label.TwoS7_Sales_Headquarters;
            res.TwoS8_Sales_Administrative_Assistant = System.Label.TwoS8_Sales_Administrative_Assistant;
            res.TwoS8_Sales_Administrative_Assistant_Plan = System.Label.TwoS8_Sales_Administrative_Assistant_Plan;
            res.OBA8_Hospital_construction_tender = System.Label.OBA8_Hospital_construction_tender;
            res.OBA9_psi = System.Label.OBA9_PSI;
            res.TwoS9_Spare = System.Label.TwoS9_Spare;
            res.OBA1_Tenderee = System.Label.OBA1_Tenderee;
            res.OBA7_Enquiry_Tenderee = System.Label.OBA7_Enquiry_Tenderee;
            res.TwoM4_Marketplace_Manager = getProfileIdByName(LightingButtonConstant.TwoM4_Marketplace_Manager);
            res.TerminateApprovalStatus = report.TerminateApprovalStatus__c;
            res.Environment_Url = System.Label.Environment_Url;
            res.Standard = Schema.SObjectType.Lead.getRecordTypeInfosByDeveloperName().get('Standard').getRecordTypeId();
            res.logicaldelete = report.Logical_delete__c;
            res.hospital = report.Hospital__c;
            System.debug(LoggingLevel.INFO, '*** res: ' + res);
        }catch(Exception e){
            System.debug(LoggingLevel.INFO, '*** error: ' + e);
        }
        return res;
    }

    @AuraEnabled
    public static string getProfileIdByName(String name){
        Profile profile = null;
        try {
            profile = [select Id from Profile where Name =:name];
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
        return profile.Id;
    }
    // var sql = "select id from Tender_Opportunity_Link__c where Tender_information__c='" + '{!Tender_information__c.Id}'+ "'";
    // var sqlResult = sforce.connection.query(sql);
    @AuraEnabled
    public static List<Tender_Opportunity_Link__c> sqlResult (String id) {
        try { 
            List<Tender_Opportunity_Link__c> TenderOpportunityLink = [SELECT id FROM Tender_Opportunity_Link__c WHERE Tender_information__c = :id];
            System.debug(LoggingLevel.INFO, '*** xu1: ' + TenderOpportunityLink);
            return TenderOpportunityLink;
        } catch (exception e) {
            System.debug(LoggingLevel.INFO, '*** xu1111111: ' + e);
            throw new AuraHandledException(e.getMessage());
        }
    }

    //招标项目 项目重启
    @AuraEnabled
    public static String projectRestart(String recordId) {
        String messageText = '';
        try {
        //lt 20240118 DB202401275115 紧急：招标项目终止状态的逻辑调整 sql里add  ,TerminateReason__c,TerminateExtraContent__c 
        Tender_information__c tender = [SELECT 	Id,IsTerminate__c,TerminateApprovalStatus__c,TerminateApprovalTime__c,ProjectRestartFLG__c,TerminateReason__c,TerminateExtraContent__c FROM Tender_information__c WHERE Id = :recordId LIMIT 1];
        // var tender = new sforce.SObject("Tender_information__c");
        tender.Id = tender.Id;
        tender.IsTerminate__c= null;
        tender.TerminateApprovalStatus__c= null;
        tender.TerminateApprovalTime__c= null;
        //lt 20240118 DB202401275115 紧急：招标项目终止状态的逻辑调整 start 
        tender.TerminateReason__c= null;
        tender.TerminateExtraContent__c= null;
        //lt 20240118 DB202401275115 紧急：招标项目终止状态的逻辑调整 end 
        tender.ProjectRestartFLG__c= true;
        messageText = '1';
        StaticParameter.EscapeTenderInformationUpdate = false;  //lt 重启后首页提醒
        System.debug('20240515---跳：'+StaticParameter.EscapeTenderInformationUpdate);
        update tender;
        StaticParameter.EscapeTenderInformationUpdate = true;
        return messageText;
        } catch (Exception e) {
        // System.debug(LoggingLevel.INFO, '*** cancelXu: ' + ex);
        // messageText = ex.getMessage();
        // return messageText;
        if(e.getMessage().contains(':')){
            String eMessage =e.getMessage();
            Integer left = eMessage.indexOf(',')+1;
            Integer right= eMessage.lastIndexOf('。')+1;
            if(right>eMessage.length()||right<=left){
                right=eMessage.length();
            }
            String mes=eMessage.substring(left,right);
            messageText = mes;
            return messageText;
        }else{
            messageText=e.getMessage();
            return messageText;
        }
        }
    }

    public class InitData{
        @AuraEnabled
        public String Id;
        @AuraEnabled
        public String OwnerId;
        @AuraEnabled
        public String status;
        @AuraEnabled
        public String name;
        @AuraEnabled
        public Integer opportunityNum;
        @AuraEnabled
        public String isRelateProject;
        @AuraEnabled
        public String profileId;
        @AuraEnabled
        public String systemProfileId;
        @AuraEnabled
        public String TwoS1_Sales_Hospital;
        @AuraEnabled
        public String TwoS1_Sales_Hospital_Non_Singleton;
        @AuraEnabled
        public String TwoS2_Sales_Product;
        @AuraEnabled
        public String TwoS4_Sales_Manager;
        @AuraEnabled
        public String TwoS4_Sales_Manager_Non_Singleton;
        @AuraEnabled
        public String TwoS4_Sales_Manager_Majordomo;
        @AuraEnabled
        public String TwoS6_Trade_Assistant;
        @AuraEnabled
        public String TwoS6_Sales_Headquarters;
        @AuraEnabled
        public String TwoS7_Sales_Headquarters;
        @AuraEnabled
        public String TwoS8_Sales_Administrative_Assistant;
        @AuraEnabled
        public String TwoS8_Sales_Administrative_Assistant_Plan;
        @AuraEnabled
        public String TwoS9_Spare;
        @AuraEnabled
        public String OBA1_Tenderee;
        @AuraEnabled
        public String OBA7_Enquiry_Tenderee;
        @AuraEnabled
        public String TwoM4_Marketplace_Manager;
        @AuraEnabled
        public String SalesMarketplaceId;
        @AuraEnabled
        public String Environment_Url;
        @AuraEnabled
        public String Standard;
        @AuraEnabled
        public String TerminateApprovalStatus;
        @AuraEnabled
        public String OBA8_Hospital_construction_tender;
        @AuraEnabled
        public String OBA9_psi;
        @AuraEnabled
        public String hospital;
        @AuraEnabled
        public List<AggregateResult> AccSize;
        @AuraEnabled
        public Boolean logicaldelete;
    }
}