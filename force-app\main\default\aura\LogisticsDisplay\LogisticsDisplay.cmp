<aura:component implements="flexipage:availableForAllPageTypes,forceCommunity:availableForAllPageTypes,lightning:isUrlAddressable" access="global" controller="LogisticsDisplayController">
	<aura:attribute name="DNId" type="String"
					description="用来从父对象接收传递过来的签收单id"
					default=""/>
	<aura:handler name="init" action="{!c.doInit}" value="{!this}"
					description="初始化方法"/>
	<aura:attribute name="currentData" type="Object[]" description="明细页当前显示的明细的信息" />
	<aura:attribute name="waybillData" type="Object" description="明细页当前显示的明细的信息" />
	<div>
		<lightning:spinner aura:id="mySpinner" alternativeText='请等待' size="large"  class="slds-spinner_container" style="height: 1000px;"/>
	</div>
	<div id="allDivs" >
		<div id="ins" style=" width:350px;margin-right: 2px; ">
            <div id="inTables">
                <aura:set attribute="title">
					OTS物流信息
				</aura:set>
				<aura:if isTrue="{!v.waybillData != null}">
					<table class="slds-table slds-table_cell-buffer slds-table_bordered slds-hint-parent slds-no-row-hover " style="border-collapse: collapse;border-color:#D7D7D7;margin-top:15px;" border="1px " >
							<thead>
								<tr class="slds-line-height_reset slds-text-title_caps">
									<th class="slds-is-resizable" tabindex="-1" data-label="发货日期" scope="row" style="width: 100px;position:static">
										<span class="slds-grid slds-grid_align-spread" style="text-align: left;">
											<div class="slds-truncate" style="margin: auto;">
												<span title="发货日期" class="slds-truncate" style="padding-bottom: 3px;">发货日期</span>
											</div>
										</span>
									</th>
									<th class="slds-is-resizable" tabindex="-1" data-label="发货日期" style="width: 146px;height:36px;position:static"  colspan="2"> 
										<span class="slds-grid slds-grid_align-spread" style="margin-left: 20px;">
											<div class="slds-truncate" style="margin: auto;">
												<lightning:formattedText value="{!v.waybillData.shipping_time}" style="padding-bottom: 3px;"/>
											</div>
										</span>
									</th>
								</tr>
								<tr class="slds-line-height_reset slds-text-title_caps">
									<th class="slds-is-resizable" tabindex="-1" data-label="预计送达日" scope="row" style="width: 240px;height: 35px;position:static">
										<span class="slds-grid slds-grid_align-spread" style="text-align: left;">
											<div class="slds-truncate" style="margin: auto;">
												<span title="预计送达日" class="slds-truncate" style="padding-bottom: 3px;">预计送达日</span>
											</div>
										</span>
									</th>
									<th  class="slds-is-resizable" tabindex="-1" data-label="预计送达日" style="width: 146px;height:36px;position:static" colspan="2">
										<span class="slds-grid slds-grid_align-spread" style="margin-left: 20px;">
											<div class="slds-truncate" style="margin: auto;">
												<lightning:formattedText value="{!v.waybillData.request_time}" style="padding-bottom: 3px;"/>
											</div>
										</span>
									</th>
								</tr>
							<!-- </thead>
							<thead> -->
								<tr class="slds-line-height_reset slds-text-title_caps">
									<th class="slds-is-resizable" tabindex="-1" data-label="运输状态" scope="row" style="width: 146px;position:static">
										<span class="slds-grid slds-grid_align-spread" style="text-align: left;">
											<div class="slds-truncate" style="margin: auto;">
												<span title="运输状态" class="slds-truncate" style="padding-bottom: 3px;">运输状态</span>
											</div>
										</span>
									</th>
									<th  class="slds-is-resizable" tabindex="-1" data-label="运输状态" style="width: 146px;height:36px;position:static"  colspan="2">
										<span class="slds-grid slds-grid_align-spread" style="margin-left: 20px;">
											<div class="slds-truncate" style="margin: auto;">
												<lightning:formattedText value="{!v.waybillData.waybill_status}" style="padding-bottom: 3px;"/>
											</div>
										</span>	
									</th>
								</tr>
								<tr>
									<th class="slds-is-resizable" tabindex="-1" data-label="签收日期" scope="row" style="width: 146px;position:static">
										<span class="slds-grid slds-grid_align-spread" style="text-align: left;">
											<div class="slds-truncate" style="margin: auto;">
												<span title="签收日期" class="slds-truncate" style="padding-bottom: 3px;">签收日期</span>
											</div>
										</span>
									</th>
									<th class="slds-is-resizable" tabindex="-1" data-label="签收日期" style="width: 146px;height:36px;position:static" colspan="2">
										<span class="slds-grid slds-grid_align-spread" style="margin-left: 20px;">
											<div class="slds-truncate" style="margin: auto;">
												<lightning:formattedText value="{!v.waybillData.receive_time}" style="padding-bottom: 3px;"/>
											</div>
										</span>
									</th>
								</tr>
							</thead>
							<aura:if isTrue="{!v.waybillData.delay_reason != null}">
								<thead>
									<tr class="slds-line-height_reset slds-text-title_caps">
										<th class="slds-is-resizable" tabindex="-1" data-label="延误原因" scope="row" style="width: 146px;position:static">
											<span class="slds-grid slds-grid_align-spread" style="text-align: auto;">
												<div class="slds-truncate" style="margin: auto;">
													<span title="延误原因" class="slds-truncate" style="padding-bottom: 3px;">延误原因</span>
												</div>
											</span>
										</th>
										<th class="slds-is-resizable" tabindex="-1" data-label="延误原因" style="width: 130px;height:36px;position:static" colspan="2">
											<span class="slds-grid slds-grid_align-spread" style="text-align: left;">
												<div style="margin-left: 20px; width:200px;">
													<lightning:formattedText value="{!v.waybillData.delay_reason}" style="padding-bottom: 3px;word-wrap: break-word;
													white-space: pre-wrap;"/>
												</div>
											</span>
										</th>
									</tr>
								</thead>
							</aura:if>
					</table>
					<div data-mohe-type="kuaidi_new" class="g-mohe " id="mohe-kuaidi_new">
						<div id="mohe-kuaidi_new_nucom">
							<div class="mohe-wrap mh-wrap">
								<div class="mh-cont mh-list-wrap mh-unfold" style="position: relative; left:-30px">
									<div class="mh-list">
										<ul>
											<aura:iteration items="{!v.currentData}" var="curr" indexVar="index">
											
											<li class="{!index==0?'first':''}" >
												<p><lightning:formattedText value="{!curr.op_time}" style="padding-bottom: 3px;"/>&nbsp;&nbsp;<lightning:formattedText value="{!curr.op_place}" style="padding-bottom: 3px;"/></p>
												<p><lightning:formattedText value="{!curr.cmemo}" style="padding-bottom: 3px;"/></p>
												<span class="before"></span><span class="after"></span>
												<aura:if isTrue="{!index == 0}">
														<i class="mh-icon mh-icon-new"></i>
												</aura:if>
											</li>
											</aura:iteration>
										</ul>
									</div>
								</div>
							</div>
						</div>
					</div>
				</aura:if>
                <aura:if isTrue="{!empty(v.waybillData)}">
					<div class="slds-align_absolute-center" style="float: left;margin-top:15px">
						暂未收到物流信息
					</div>
				</aura:if>
            </div>
		</div>
	</div>
</aura:component>