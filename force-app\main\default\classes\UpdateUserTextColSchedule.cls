global class UpdateUserTextColSchedule implements Schedulable {
    global final String testUserId;

    /**
     * コンスタント、パラメータを受け取る
     */
    global UpdateUserTextColSchedule() {
        this.testUserId = null;
    }

    /**
     * コンスタント、パラメータを受け取る
     */
    global UpdateUserTextColSchedule(String testUserId) {
        this.testUserId = testUserId;
    }

    global void execute(SchedulableContext SC) {
        // バッチ実行
        Id execBTId = Database.executeBatch(new UpdateUserTextColBatch(testUserId), 20);
    }
}