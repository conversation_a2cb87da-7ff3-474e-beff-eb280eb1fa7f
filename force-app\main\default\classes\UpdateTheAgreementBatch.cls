global class UpdateTheAgreementBatch implements Database.Batchable<SObject> , Database.AllowsCallouts,Database.Stateful{
    private final String accContractNo = null;
    private  Account accNew = null;
    private  Account accOld = null;
    private  String accId = null;
    public List<Account> accList = new List<Account>();
    private BatchIF_Log__c log = new BatchIF_Log__c();
    global UpdateTheAgreementBatch(List<Account> accList){
        this.accList = accList;
     }
    global UpdateTheAgreementBatch(String accId){
       this.accId = accId;
    }
    global Database.QueryLocator start(Database.BatchableContext BC){
        log.Type__c = 'UpdateTheAgreementBatch';
        log.Log__c = 'start\n';
        List<String> accIds = new List<String>();
        for(Account acc : accList){
            accIds.add(acc.Id);
        }
        String soql = 'Select Id,Term_Contract_No__c '
                    + 'From Account ';
        if(accId != null){
            soql += 'Where Id =:accId ';
        }
        else{
            soql += 'Where Id IN:accIds ';
        }
        soql += 'And Term_Contract_No__c Like \'%更%\'';
        System.debug('********---soql:'+soql);
        return Database.getQueryLocator(soql);
    }
    global void execute(Database.BatchableContext BC, List<Account> accList){
        String accNo = '%' + accList[0].Term_Contract_No__c.substring(0,accList[0].Term_Contract_No__c.indexOf('更'))+ '%';
        System.debug('********---accNo---'+accNo);
        List<Account> accs= [select Id,Name,SpecialDealerName__c from Account where Term_Contract_No__c like :accNo order by CreatedDate desc limit 2];
        if(accs.size() == 2){
            accNew = accs[0];
            accOld = accs[1];
        }else{
            return;
        }
        List<Opportunity> oppList = [select Id,SpecialAgency_Shipments__c,SpecialAgency_Shipments_Text__c from Opportunity where SpecialAgency_Shipments__c = :accOld.Id];
        System.debug('********---execute---');
        System.debug('********---oppList---'+oppList);
        if(oppList.Size() > 0 ){
            for(Opportunity opp : oppList){
                opp.SpecialAgency_Shipments__c = accNew.Id;
                opp.SpecialAgency_Shipments_Text__c = accNew.SpecialDealerName__c;
            }
            Database.SaveResult[] saveOppResults = Database.update(oppList);
            for(Integer i = 0;i<saveOppResults.size();i++) {
                if(!saveOppResults.get(i).isSuccess() ){ 
                    log.Log__c += 'Opportunity Id:'+oppList[i].Id+' update failed reason:'+ saveOppResults.get(i).getErrors()[0] + '\n';
                }
            }
        }
    }
    global void finish(Database.BatchableContext BC){
        log.Log__c += 'end';
        insert log;
    }
}