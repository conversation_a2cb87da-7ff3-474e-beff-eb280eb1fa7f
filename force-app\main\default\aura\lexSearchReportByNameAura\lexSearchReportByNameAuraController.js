({
    redirectToURL : function(component, event, helper) {
        // var urlEvent = $A.get("e.force:navigateToURL");

        // urlEvent.setParams({
        //     "url": "/lightning/n/SearchReport"
        // });
        // urlEvent.fire();
        // window.location.href = "/lightning/n/SearchReport";
        var maximizeButton = document.querySelector('[title="'+$A.get("$Label.c.SearchMax")+'"]');

        if (maximizeButton) {
            console.log('success' + $A.get("$Label.c.SearchMax"));
            maximizeButton.click();
        }
    }
})