@isTest
private class TransferShippmentReceived2ControllerTest {
    static TransferTestDataFactory Factory;
    static void setupTestData1() { 
        Factory = new TransferTestDataFactory();
        Factory.CreateTa('CenterToCenter');
        TransferApply__c raObj1 = [SELECT Id,Name,OwnerId,Status__c,Status_Text__c,Request_time__c,Request_approval_time__c  FROM TransferApply__c WHERE RecordType.DeveloperName = 'CenterToCenter' limit 1];       
        raObj1.Status__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
        raObj1.Status_Text__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
        raObj1.Request_time__c = System.now();
        raObj1.Request_approval_time__c = System.now();
        update raObj1;
        List<TransferApplyDetail__c> erList;
        erList = [select Check_lost_Item_F__c, CDS_complete_time__c
                    from TransferApplyDetail__c];
        for(TransferApplyDetail__c tad:erList){
            tad.Shipment_request_time2__c = Date.toDay();
            tad.StockDown__c = true;
            tad.StockDown_time__c = Date.toDay();
            tad.StockDown_staff__c = Userinfo.getUserId();
            tad.Shipment_Status_Text__c = FixtureUtil.raesdStatusMap.get(FixtureUtil.HistoryStatus.Yi_Xia_Jia.ordinal());
        }
        update erList;
    }
	static testMethod void test_init_error() {
        Test.startTest();
        setupTestData1();
        List<TransferApply__c> taList = [SELECT Id,OwnerId,Status__c FROM TransferApply__c]; 
        List<TransferApplySummary__c> esList;
        esList = [select TransferApply__r.Loaner_centre_mail_address__c, TransferApply__r.Name, TransferApply__c, Id, Name
                        from TransferApplySummary__c WHERE TransferApply__c = :taList[0].Id ];
        //&type=Shippment&open=web
        PageReference ref1 = new PageReference('/apex/TransferShippmentReceived2?id=' + '1234');
        Test.setCurrentPage(ref1);
        TransferShippmentReceived2Controller controller = new TransferShippmentReceived2Controller();
        controller.init();
        PageReference ref2 = new PageReference('/apex/TransferShippmentReceived2?id=' + esList[0].id+'&type=Received&open=web');
        Test.setCurrentPage(ref2);
        TransferShippmentReceived2Controller controller2 = new TransferShippmentReceived2Controller();
        controller2.init();
        Test.stopTest();
        System.assertEquals(0, controller.esdList.size());
    }
	static testMethod  void test_save() {
        
        setupTestData1();

        Test.startTest();
        List<TransferApply__c> taList = [SELECT Id,OwnerId,Status__c FROM TransferApply__c]; 
        List<TransferApplySummary__c> esList;
        esList = [select TransferApply__r.Loaner_centre_mail_address__c, TransferApply__r.Name, TransferApply__c, Id, Name
                        from TransferApplySummary__c WHERE TransferApply__c = :taList[0].Id ];
        PageReference ref = new PageReference('/apex/TransferShippmentReceived2?id=' + esList[0].id);
        Test.setCurrentPage(ref);
        TransferShippmentReceived2Controller controller = new TransferShippmentReceived2Controller();
        controller.init();

        controller.esdList[0].editable = true; 
        controller.esdList[0].rec.Inspection_result__c = 'OK';
        controller.esdList[0].rec.Inspection_result_ng__c = null;
        controller.esdList[0].rec.Pre_Inspection_Comment__c = '11';

        controller.esdList[1].editable = true;
        controller.esdList[1].rec.Inspection_result__c = 'OK';
        controller.esdList[1].rec.Inspection_result_ng__c = null;
        controller.esdList[1].rec.Pre_Inspection_Comment__c = '11';

        // controller.esdList[2].editable = true;
        // controller.esdList[2].rec.Inspection_result__c = null;
        // controller.esdList[2].rec.Inspection_result_ng__c = null;
        // controller.esdList[2].rec.Pre_Inspection_Comment__c = '11';
        controller.save();
        controller.Shippment1();
        controller.Shippment2();
        Test.stopTest();
        List<TransferApplyDetail__c> erList;
        erList = [select Inspection_result__c, Inspection_result_ng__c,Pre_Inspection_Comment__c
                        from TransferApplyDetail__c];
        System.assertEquals('OK', erList[0].Inspection_result__c);
        System.assertNotEquals(null, erList[0].Pre_Inspection_Comment__c);
        System.assertEquals('11', erList[0].Pre_Inspection_Comment__c);
    }
}