.THIS .slds-button
{
    display:initial;
}
.THIS .InheritedButton
{
	font-size:22px;
	width:90%;
	height:100%;
}
.THIS .InheritedDIV
{
	text-align: center;
}
.THIS .ReturnButton
{
	/*font-size:22px;
	width:90%;
	height:100%;*/
	color: #0f218b;
    border-color: #0f218b;
}
.THIS .slds-button_brand
{	
	/*奥林巴斯专属蓝色*/
	background-color: #0f218b;
}
.THIS table thead tr th:first-child 
{
    background-color: white;
    position: fixed;
    z-index: 2;
    padding-top:11px;
    /*top: 45px;
    width: 50px;*/
    
 

}
.THIS table thead  tr th:nth-child(2)
{
    /*background-color: white;
    position: fixed;
    z-index: 1;
    top: 45px;
    padding-left: 50px;
    width: 161px;*/
    padding-left: 0px;
 
}
.THIS table thead  tr th:nth-child(3)
{
    padding-left: 0px;

}
.THIS table thead  tr th:nth-child(4)
{
    padding-left: 0px;

}
.THIS table thead  tr th:nth-child(5)
{
    padding-left: 0px;

}
.THIS table thead  tr th:nth-child(6)
{
    padding-left: 0px;

}

.THIS table tbody tr th:first-child 
{
    background-color: white;
    position: fixed;
    z-index: 2;
    padding-top: 9px;
    /*width: 50px;
    padding-top: 9.0px;*/
    
     
    

}

.THIS table tbody  tr th:nth-child(2)
{
    /*background-color: white;
    position: fixed;
    z-index: 1;
    padding-left: 50px;
    padding-top: 9.0px;
    width: 161px;*/
    padding-left: 0px;
     
    

}
.THIS table tbody  tr th:nth-child(3)
{
    padding-left: 0px;

}

.THIS table tbody  tr th:nth-child(4)
{
    padding-left: 0px;

}

.THIS table tbody  tr th:nth-child(5)
{
    padding-left: 0px;

}