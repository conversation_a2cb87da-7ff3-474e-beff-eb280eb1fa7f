@isTest
private class TransferAutoGiveupBatchScheduleTest {
    static testMethod void myUnitTest() {
        Opportunity opp1 = new Opportunity();
        opp1.Name = 'aiueo';
        opp1.StageName = '引合';
        opp1.Trade__c = '内貿';
        opp1.SAP_Send_OK__c = false;
        opp1.Wholesale_Price__c = 101;
        opp1.Tax_Intra_F_Copy__c = 1;
        opp1.Opportunity_Category__c = 'GI';
        opp1.OCM_man_province_cus_txt__c = '北京';
        opp1.CloseDate = Date.today();
        insert opp1;

        BatchIF_Log__c iflog = new BatchIF_Log__c();
            iflog.Type__c = 'NFM215';
            iflog.Log__c  = 'callout start\n';
            insert iflog;

        Opportunity2__c o2pp1 = new Opportunity2__c();
        o2pp1.Opportunity__c = opp1.Id;
        insert o2pp1;
        List<String> oppList = new List<String>();
        oppList.add(opp1.Id);
        System.runAs(new User(Id = Userinfo.getUserId())) {
            // opp1.NeedGet_SpecialAgency_Time_OpportunityEd__c = Datetime.now();
            // UPDATE opp1;
            o2pp1.SalesAlloweDate_AssistantApply__c = Datetime.now();
            UPDATE o2pp1;
        }
        // This test runs a scheduled job at midnight Sept. 3rd. 2022
        String CRON_EXP = '0 0 0 3 9 ? 2032';

        System.Test.startTest();
        // Schedule the test job
        String jobId = system.schedule('TransferAutoGiveupBatchSchedule', CRON_EXP, new TransferAutoGiveupBatchSchedule());
        System.assertNotEquals(null, jobId);
        System.Test.stopTest();
    }
}