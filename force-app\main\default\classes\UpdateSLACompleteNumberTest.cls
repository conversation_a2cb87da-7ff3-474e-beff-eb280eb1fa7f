@isTest
private class UpdateSLACompleteNumberTest {
    static testMethod void testMethod1() {

    	// レコードタイプ取得
        RecordType hospitalRec = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        RecordType sectionRec = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 消化科'];
        RecordType departmentRec = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 消化科'];
        
        // 病院作成
        Account hospital = new Account();
        hospital.RecordTypeId = hospitalRec.Id;
        hospital.Name = 'TestHospital';
        insert hospital;
        
        // 病院を作ると戦略科室は、トリガーによって作られている
        // Account section = [select Management_Code__c, Management_Code_Auto__c, Name, Id from Account where Parent.Id = :hospital.Id and RecordTypeId = :sectionRec.Id limit 1];
        Account section = [select Management_Code__c, Name, Id from Account where Parent.Id = :hospital.Id and RecordTypeId = :sectionRec.Id limit 1];
        
        // 診療科1を作成
        Account depart1 = new Account();
        depart1.RecordTypeId = departmentRec.Id;
        depart1.Name = '*';
        depart1.Department_Name__c  = 'TestDepart';
        depart1.ParentId = section.Id;
        depart1.Department_Class__c = section.Id;
        depart1.Hospital__c = hospital.Id;
        depart1.AgentCode_Ext__c = '9999997';
        insert depart1;


    	//服务合同        
        Maintenance_Contract__c contract = new Maintenance_Contract__c();
        contract.Name = 'test contract';
        contract.Hospital__c = hospital.Id;
        contract.Department_Class__c = section.Id;
        contract.Department__c = depart1.Id;
        contract.RecordTypeId = '01210000000gTYqAAM';
        insert contract;

        //这个季度的生成服务合同报告书
        NewMaintenanceReport_Task__c tempNMCT = new NewMaintenanceReport_Task__c();
        tempNMCT.Name = 'test MT';
        tempNMCT.Distribution_Start_Date__c = Date.today().addDays(-10);
        tempNMCT.Distribution_End_Date__c = Date.today().addDays(79);
        tempNMCT.NewMaintenance_Contract__c = contract.Id;
        insert tempNMCT;

        Account account2 = new Account();
    	account2.Name = 'test1经销商';
        account2.RecordTypeId = '01210000000Qem1';
        insert account2;

    	Contact visitor1 = new Contact();
    	visitor1.AccountId = account2.Id;
        visitor1.FirstName = '責任者';
        visitor1.LastName = 'test1经销商';
        insert visitor1;

    	Contact visitor2 = new Contact();
    	visitor2.AccountId = account2.Id;
        visitor2.FirstName = '消费者';
        visitor2.LastName = 'test2经销商';
        insert visitor2;


    	SLAReportInfo__c slaInfo1 = new SLAReportInfo__c();
    	slaInfo1.Name = 'testSLAeventCAndMainCReportInfo1';
    	slaInfo1.Visitor1__c = visitor1.id;
    	slaInfo1.Visitor2__c = visitor2.id;
    	slaInfo1.SLATask__c  = tempNMCT.id;
    	slaInfo1.Maintenance_Contract__c = contract.Id;
    	slaInfo1.Distribution_Person__c = 'FSE';
    	slaInfo1.Distribution_Method__c = '线上（微信、邮件）';
    	insert slaInfo1;

    	List<String> slaInfoId = new List<String>();
    	slaInfoId.add(slaInfo1.id);

    	Daily_Report__c dr3 = new Daily_Report__c();
        dr3.Reporter__c = UserInfo.getUserId();
        dr3.Reported_Date__c = Date.today().addDays(4);
        dr3.Status__c = '申請中';
        insert dr3;

        Event__c event = new Event__c();
        event.ActivityDate__c = Date.today().addDays(-2);
        event.Daily_Report__c = dr3.Id;
        event.StartDateTime__c = Datetime.now();
        event.EndDateTime__c = Datetime.now().addDays(1);
        event.Related_Service1_ID__c = contract.Id;
        insert event;

        Map<String,Event__c> eventCAndSLA =  new Map<String,Event__c>();
        eventCAndSLA.put(slaInfo1.Id,event);
 

    	UpdateSLACompleteNumber.saveSLADetails(eventCAndSLA,slaInfoId,dr3.Submit_DateTime_New__c <= dr3.Report_Deadline__c );

    	Map<String,String> eventCAndMainC = new Map<String,String>();
    	eventCAndMainC.put(event.Id,contract.Id);
    	List<String> mainCIds = new List<String>();
    	mainCIds.add(contract.Id);

    	UpdateSLACompleteNumber.saveSLARecorded(eventCAndMainC,mainCIds);

        
    }
}