.THIS {
}

.THIS .name{
    margin-right:0.5cm;
}

.THIS .wh{
    width: 670px;
    height: 500px;
}

.THIS .slds-modal__container{
    max-width:70rem !important;
    width:90% !important;
}

.THIS .modal-body{
    height : 500px !important;
    max-height: 500px !important;
}

.THIS table{
    border-collapse: collapse;
    border-spacing: 0;
    width: 98%;
    margin-left: 5px;
    /* border: 1px solid #000000; */
    text-align: center;
}
.THIS tr{
    /* border: 1px solid #000000; */
    text-align: center;
}
.THIS td{
    /* border: 1px solid #000000; */
    text-align: center;
}

.THIS th{
    /* border: 1px solid #000000; */
    text-align: center;
}

.THIS.backgroundInverse {
    position: relative;
    background-color: #16325c;
    height: 80px;
}

.THIS.exampleHolder{
    position: relative;
    display: inline-block;
    margin-left: 15px;
    width: 55px;
    vertical-align: middle;
    white-space: nowrap;
}

.THIS .searchButton{
    margin-top:20px;
    margin-left:20px;
}

.THIS .slds-spinner_container{
    position: revert;
}

.THIS .slds-backdrop2 {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6); /* 遮罩层的半透明背景色 */
}
.THIS .slds-backdrop_open2 {
    z-index: 9003; /* 遮罩层的层级，需要确保比模态框的层级高 */
}