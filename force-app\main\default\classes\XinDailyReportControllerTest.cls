/**
 * This class contains unit tests for validating the behavior of Apex classes
 * and triggers.
 *
 * Unit tests are class methods that verify whether a particular piece
 * of code is working properly. Unit test methods take no arguments,
 * commit no data to the database, and are flagged with the testMethod
 * keyword in the method definition.
 *
 * All test methods in an organization are executed whenever Apex code is deployed
 * to a production organization to confirm correctness, ensure code
 * coverage, and prevent regressions. All Apex classes are
 * required to have at least 75% code coverage in order to be deployed
 * to a production organization. In addition, all triggers must have some code coverage.
 *
 * The @isTest class annotation indicates this class only contains test
 * methods. Classes defined with the @isTest annotation do not count against
 * the organization size limit for all Apex scripts.
 *
 * See the Apex Language Reference for more information about Testing and Code Coverage.
 */
@isTest
private class XinDailyReportControllerTest {

    private static String loginId {get; set;}
    private static User u1 {get; set;}
    private static User u2 {get; set;}
    private static User u3 {get; set;}
    private static Account accHP {get; set;}
    private static Account accDepClass {get; set;}
    private static Account accDep {get; set;}
    private static Contact con1 {get; set;}
    private static Contact con2 {get; set;}
    private static Contact con3 {get; set;}
    private static Contact con4 {get; set;}
    private static Contact con5 {get; set;}
    private static Opportunity opp1 {get; set;}
    private static Opportunity opp2 {get; set;}
    private static Opportunity opp3 {get; set;}
    private static Opportunity opp4 {get; set;}
    private static Opportunity opp5 {get; set;}
    private static Maintenance_Contract__c mc1 {get; set;}
    private static Maintenance_Contract__c mc2 {get; set;}
    private static String accDepId {get; set;}
    private static String accDepName {get; set;}

    @testSetup
    static void setUp(){
        
        TestDataUtility.CreatePIPolicyConfigurations( new string[]{'Contact'});
    }
    
    /**
     * 初期処理.
     */
    private static void init() {
        loginId = UserInfo.getUserId();
        id pid = UserInfo.getProfileId();
        //Profile p = [select id from Profile where id = :System.Label.ProfileId_SystemAdmin];
        System.Test.startTest();
        u3 = new User(Test_staff__c = true);
        u3.LastName = '_サンブリッジ';
        u3.Batch_User__c = true;
        u3.FirstName = 'う';
        u3.Alias = 'う';
        u3.Email = '<EMAIL>';
        u3.Username = '<EMAIL>';
        u3.CommunityNickname = 'う';
        u3.IsActive = true;
        u3.EmailEncodingKey = 'ISO-2022-JP';
        u3.TimeZoneSidKey = 'Asia/Tokyo';
        u3.LocaleSidKey = 'ja_JP';
        u3.LanguageLocaleKey = 'ja';
        u3.ProfileId = pid;
        u3.Job_Category__c = '支援';
        u3.Province__c = '東京';


        u1 = new User(Test_staff__c = true);
        u1.LastName = '_サンブリッジ';
        u1.FirstName = 'あ';
        u1.Batch_User__c = true;
        u1.Alias = 'あ';
        u1.Email = '<EMAIL>';
        u1.Username = '<EMAIL>';
        u1.CommunityNickname = 'あ1';
        u1.IsActive = true;
        u1.EmailEncodingKey = 'ISO-2022-JP';
        u1.TimeZoneSidKey = 'Asia/Tokyo';
        u1.LocaleSidKey = 'ja_JP';
        u1.LanguageLocaleKey = 'ja';
        u1.ProfileId = pid;
        u1.Job_Category__c = '销售服务';
        u1.Province__c = '東京';
        u1.ManagerId = u3.id;


        u2 = new User(Test_staff__c = true);
        u2.LastName = '_サンブリッジ';
        u2.FirstName = 'い';
        u2.Batch_User__c = true;
        u2.Alias = 'い';
        u2.Email = '<EMAIL>';
        u2.Username = '<EMAIL>';
        u2.CommunityNickname = 'い';
        u2.IsActive = true;
        u2.EmailEncodingKey = 'ISO-2022-JP';
        u2.TimeZoneSidKey = 'Asia/Tokyo';
        u2.LocaleSidKey = 'ja_JP';
        u2.LanguageLocaleKey = 'ja';
        u2.ProfileId = pid;
        u2.Job_Category__c = '销售推广';
        u2.Province__c = '東京';
        insert New List<user>{u1,u2,u3};


        //RecordType rtDepClass = [select id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName =:'Department_Class_ENT'];           // 耳鼻喉科

        //Address_Level__c al = [select id from Address_Level__c where Level1_Code__c =:'CN-01'];
        //Address_Level2__c al2 = [select id from Address_Level2__c where Name =:'延庆县'];
        system.runAs(u2) {
            Address_Level__c al = new Address_Level__c();
            al.Name = '東京';
            al.Level1_Code__c = 'CN-99';
            al.Level1_Sys_No__c = '999999';
            insert al;

            Address_Level2__c al2 = new Address_Level2__c();
            al2.Level1_Code__c = 'CN-99';
            al2.Level1_Sys_No__c = '999999';
            al2.Level1_Name__c = '東京';
            al2.Name = '渋谷区';
            al2.Level2_Code__c = 'CN-9999';
            al2.Level2_Sys_No__c = '9999999';
            al2.Address_Level__c = al.id;
            insert al2;

            accHP = new Account();
            accHP.Name = '病院1';
            accHP.Grade__c = '一般';
            accHP.OCM_Category__c = '一般';
            accHP.Attribute_Type__c = '保険省';
            accHP.Speciality_Type__c = '総合病院';
            accHP.Is_Active__c = '有効';
            accHP.State_Master__c = al.id;
            accHP.City_Master__c = al2.id;
            accHP.RecordTypeId = '01210000000QemG';
            accHP.Valid_To__c = Date.today() + 2;
            accHP.FSE_GI_Main_Leader__c = UserInfo.getUserId();
            accHP.FSE_SP_Main_Leader__c = UserInfo.getUserId();
            insert accHP;
            /*
            accDepClass = new Account();
            accDepClass.Name = '戦略科室分類1';
            accDepClass.Department_Class_Label__c = '耳鼻喉科';
            accDepClass.Hospital_Department_Class__c = accHP.id;
            accDepClass.ParentId = accHP.id;
            accDepClass.RecordTypeId = rtDepClass.id;
            insert accDepClass;
            */
            accDepClass = [select Id from Account where ParentId = :accHP.Id and Department_Class_Label__c = '耳鼻喉科'];

            accDep = new Account();
            accDep.Name = '診療科1';
            accDep.Department_Class_Label__c = '診療科1';
            accDep.Hospital__c = accHP.id;
            accDep.ParentId = accDepClass.id;
            accDep.Department_Class__c = accDepClass.id;
            accDep.Department_Name__c = '診療科1';
            accDep.CurrencyIsoCode = 'CNY';
            accDep.RecordTypeId = '01210000000Qfm2';
            accDep.FSE_GI_Main_Leader__c = UserInfo.getUserId();
            accDep.FSE_SP_Main_Leader__c = UserInfo.getUserId();

            insert accDep;
            accDepId = accDep.Id;
            accDep = [select Name from Account where Id = :accDep.Id];
            accDepName = accDep.Name;
        // 取引先責任者の住所を数式項目に変更の為、当該項目を削除
        // 省の参照先は病院の住所になったため、カバレッジテストを行うには
        // 所属する病院の賞を変更して実施してください。by 宗像(真)
            List<Contact> conList = new List<Contact>();
            con1 = new Contact();
            con1.Firstname = 'ZZ1';
            con1.LastName = '取引先責任者1';
            con1.RecordTypeId = '01210000000QfWd';
            con1.AccountId = accDep.Id;
        //        con1.State__c = '北京市';
            con2 = new Contact();
            con2.Firstname = 'ZZ2';
            con2.LastName = '取引先責任者2';
            con2.RecordTypeId = '01210000000QfWd';
            con2.AccountId = accDep.Id;
        //        con2.State__c = '北京市';
            con3 = new Contact();
            con3.Firstname = 'ZZ3';
            con3.LastName = '取引先責任者3';
            con3.RecordTypeId = '01210000000QfWd';
            con3.AccountId = accDep.Id;
        //        con3.State__c = '北京市';
            con4 = new Contact();
            con4.Firstname = 'ZZ4';
            con4.LastName = '取引先責任者4';
            con4.RecordTypeId = '01210000000QfWd';
            con4.AccountId = accDep.Id;
        //        con4.State__c = '北京市';
            con5 = new Contact();
            con5.Firstname = 'ZZ5';
            con5.LastName = '取引先責任者5';
            con5.RecordTypeId = '01210000000QfWd';
            con5.AccountId = accDep.Id;
        //        con5.State__c = '北京市';
            conList.add(con1);
            conList.add(con2);
            conList.add(con3);
            conList.add(con4);
            conList.add(con5);
            insert conList;
            System.Test.stopTest();
            List<Opportunity> oppList = new List<Opportunity>();
            opp1 = new Opportunity();
            opp1.Name = '引合1';
            opp1.AccountId = accDep.Id;
            opp1.Opportunity_Category__c = 'ENT';
            opp1.Trade__c = '外貿';
            opp1.StageName = '引合';
            opp1.CurrencyIsoCode = 'CNY';
            opp1.Close_Forecasted_Date__c = date.today().addMonths(1);
            opp1.CloseDate = date.today().addMonths(1);
            opp1.Opportunity_stage__c = '确定了对手参数';
            opp1.Competitor__c = 'E';
            opp1.Sales_Root__c = 'OCM直接販売';
            opp1.Hospital__c = accHP.id;
            opp1.Department_Class__c = accDepClass.id;
            opp1.StageName = '引合';
            opp2 = new Opportunity();
            opp2.Name = '引合2';
            opp2.AccountId = accDep.Id;
            opp2.Opportunity_Category__c = 'ENT';
            opp2.Trade__c = '外貿';
            opp2.StageName = '引合';
            opp2.Opportunity_stage__c = '确定了对手参数';
            opp2.CurrencyIsoCode = 'CNY';
            opp2.Close_Forecasted_Date__c = date.today().addMonths(1);
            opp2.CloseDate = date.today().addMonths(1);
            opp2.Competitor__c = 'E';
            opp2.Sales_Root__c = 'OCM直接販売';
            opp2.Hospital__c = accHP.id;
            opp2.Department_Class__c = accDepClass.id;
            opp2.StageName = '引合';
            opp3 = new Opportunity();
            opp3.Name = '引合3';
            opp3.AccountId = accDep.Id;
            opp3.Opportunity_Category__c = 'ENT';
            opp3.Trade__c = '外貿';
            opp3.StageName = '引合';
            opp3.Opportunity_stage__c = '确定了对手参数';
            opp3.CurrencyIsoCode = 'CNY';
            opp3.Close_Forecasted_Date__c = date.today().addMonths(1);
            opp3.CloseDate = date.today().addMonths(1);
            opp3.Competitor__c = 'E';
            opp3.Sales_Root__c = 'OCM直接販売';
            opp3.Hospital__c = accHP.id;
            opp3.Department_Class__c = accDepClass.id;
            opp3.StageName = '引合';
            opp4 = new Opportunity();
            opp4.Name = '引合4';
            opp4.AccountId = accDep.Id;
            opp4.Opportunity_Category__c = 'ENT';
            opp4.Trade__c = '外貿';
            opp4.StageName = '引合';
            opp4.CurrencyIsoCode = 'CNY';
            opp4.Opportunity_stage__c = '确定了对手参数';
            opp4.Close_Forecasted_Date__c = date.today().addMonths(1);
            opp4.CloseDate = date.today().addMonths(1);
            opp4.Competitor__c = 'E';
            opp4.Sales_Root__c = 'OCM直接販売';
            opp4.Hospital__c = accHP.id;
            opp4.Department_Class__c = accDepClass.id;
            opp4.StageName = '引合';
            opp5 = new Opportunity();
            opp5.Name = '引合5';
            opp5.AccountId = accDep.Id;
            opp5.Opportunity_Category__c = 'ENT';
            opp5.Trade__c = '外貿';
            opp5.StageName = '引合';
            opp5.CurrencyIsoCode = 'CNY';
            opp5.Close_Forecasted_Date__c = date.today().addMonths(1);
            opp5.CloseDate = date.today().addMonths(1);
            opp5.Competitor__c = 'E';
            opp5.Sales_Root__c = 'OCM直接販売';
            opp5.Opportunity_stage__c = '确定了对手参数';
            opp5.Hospital__c = accHP.id;
            opp5.Department_Class__c = accDepClass.id;
            opp5.StageName = '引合';
            oppList.add(opp1);
            oppList.add(opp2);
            oppList.add(opp3);
            oppList.add(opp4);
            oppList.add(opp5);
            insert oppList;
            System.debug('opp1.Name:' + opp1.Name);

            accompanying_report__c ar = new accompanying_report__c();
            ar.Name = 'システム管理者';
            ar.Profile_ID_1__c = pid;
            //Database.insert(ar, false);
            insert ar;

            report_report__c rr = new report_report__c();
            rr.Name = 'システム管理者';
            rr.Profile_ID_1__c = pid;
            //Database.insert(rr, false);
            insert rr;

            Daily_Report__c dr = new Daily_Report__c();
            dr.Reported_Date__c = date.today();
            dr.Reporter__c = u1.id;
        }
    }

    private static void init2() {
        List<Maintenance_Contract__c> mcList = new List<Maintenance_Contract__c>();
        mc1 = new Maintenance_Contract__c();
        mc1.Name = 'サービス契約1';
        mc1.Status__c = '引合中';
        mc1.Hospital__c = accHP.id;
        mc1.Department_Class__c = accDepClass.id;
        mc1.Department__c = accDep.Id;
        mc1.CurrencyIsoCode = 'CNY';
        mc2 = new Maintenance_Contract__c();
        mc2.Name = 'サービス契約2';
        mc2.Status__c = '引合中';
        mc2.Hospital__c = accHP.id;
        mc2.Department_Class__c = accDepClass.id;
        mc2.Department__c = accDep.Id;
        mc2.CurrencyIsoCode = 'CNY';
        mcList.add(mc1);
        mcList.add(mc2);
        insert mcList;
    }

    @isTest
    static void test01_01() {
        // ControllerUtil.EscapeNFM001Trigger = true;
        // init();

        // System.runAs(u1) {
        //     //System.Test.startTest();
        //     init2();
        //     //System.Test.stopTest();
        //     XinDailyReportController t = new XinDailyReportController();
        //     t.init();

        //     t.report_search.Reported_Date__c = date.today();
        //     t.getDailyReport();
        //     //t.getEvent();
        //     t.report.Reporter__c = u1.id;
        //     PageReference ref = t.getDailyReport();
        //     Test.setCurrentPage(ref);
        //     t = new XinDailyReportController();
        //     t.init();
        //     t.report.Reported_Date__c = date.today();
        //     t.repoStartHourText = '';
        //     t.repoStartMinuteText = '';
        //     t.repoEndHourText = '';
        //     t.repoEndMinuteText = '';
        //     t.report.Status__c = null;
        //     t.activities.get(0).act.Companion__c = 'a';
        //     t.activities.get(0).act.nextPlanDate__c = date.today();
        //     t.activities.get(0).actStartHourText = '9';
        //     t.activities.get(0).planStartHourText = '9';
        //     t.upsertActIndex = null;
        //     t.save();

        //     t.report.Reported_Date__c = date.today();
        //     t.repoStartHourText = '24';
        //     t.repoStartMinuteText = '00';
        //     t.repoEndHourText = '25';
        //     t.repoEndMinuteText = '00';
        //     t.activities.get(0).actStartHourText = '24';
        //     t.activities.get(0).actStartMinuteText = '00';
        //     t.activities.get(0).actEndHourText = '25';
        //     t.activities.get(0).actEndMinuteText = '00';
        //     t.activities.get(0).act.nextPlanDate__c = date.today();
        //     t.activities.get(0).planStartHourText = '24';
        //     t.activities.get(0).planStartMinuteText = '00';
        //     t.activities.get(0).planEndHourText = '25';
        //     t.activities.get(0).planEndMinuteText = '00';
        //     t.save();

        //     t.report.Reported_Date__c = date.today();
        //     t.repoStartHourText = '9';
        //     t.repoStartMinuteText = '00';
        //     t.repoEndHourText = '8';
        //     t.repoEndMinuteText = '00';
        //     t.activities.get(0).actStartHourText = '9';
        //     t.activities.get(0).actStartMinuteText = '00';
        //     t.activities.get(0).actEndHourText = '8';
        //     t.activities.get(0).actEndMinuteText = '00';
        //     t.activities.get(0).act.nextPlanDate__c = null;
        //     t.activities.get(0).planStartHourText = '9';
        //     t.activities.get(0).planStartMinuteText = '00';
        //     t.activities.get(0).planEndHourText = '8';
        //     t.activities.get(0).planEndMinuteText = '00';
        //     t.save();

        //     t.report.Reported_Date__c = date.today();
        //     t.repoStartHourText = 'a';
        //     t.repoStartMinuteText = '00';
        //     t.repoEndHourText = '8';
        //     t.repoEndMinuteText = '00';
        //     t.activities.get(0).actStartHourText = 'a';
        //     t.activities.get(0).actStartMinuteText = '00';
        //     t.activities.get(0).actEndHourText = '8';
        //     t.activities.get(0).actEndMinuteText = '00';
        //     t.activities.get(0).act.nextPlanDate__c = null;
        //     t.activities.get(0).planStartHourText = 'a';
        //     t.activities.get(0).planStartMinuteText = '00';
        //     t.activities.get(0).planEndHourText = '8';
        //     t.activities.get(0).planEndMinuteText = '00';
        //     t.save();
        //     t.cancelRequest();

        //     //2021-07-14   mzy  add
        //     Address_Level__c al = new Address_Level__c();
        //     al.Name = '東京';
        //     al.Level1_Code__c = 'CN-99';
        //     al.Level1_Sys_No__c = '999999';
        //     insert al;

        //     Address_Level2__c al2 = new Address_Level2__c();
        //     al2.Level1_Code__c = 'CN-99';
        //     al2.Level1_Sys_No__c = '999999';
        //     al2.Level1_Name__c = '東京';
        //     al2.Name = '渋谷区';
        //     al2.Level2_Code__c = 'CN-9999';
        //     al2.Level2_Sys_No__c = '9999999';
        //     al2.Address_Level__c = al.id;
        //     insert al2;
        //     Account acHP = new Account();
        //     acHP.Name = '病院1';
        //     acHP.Grade__c = '一般';
        //     acHP.OCM_Category__c = '一般';
        //     acHP.Attribute_Type__c = '保険省';
        //     acHP.Speciality_Type__c = '総合病院';
        //     acHP.Is_Active__c = '有効';
        //     acHP.State_Master__c = al.id;
        //     acHP.City_Master__c = al2.id;
        //     acHP.RecordTypeId = '01210000000QemG';
        //     acHP.Valid_To__c = Date.today() + 2;
        //     acHP.FSE_GI_Main_Leader__c = UserInfo.getUserId();
        //     acHP.FSE_SP_Main_Leader__c = UserInfo.getUserId();
        //     insert acHP;
        //     XinDailyReportController.testI();
        //     XinDailyReportController.testY();
        //     Xin_Maintenance_Contract blgmc = new Xin_Maintenance_Contract();
        //     PageReference pageRef6 = Page.Xin_SearchMaintenanceContract;
        //     pageRef6.getParameters().put('q', '契約');
        //     pageRef6.getParameters().put('r', '');
        //     Test.setCurrentPage(pageRef6);
        //     blgmc.search();
        //     //2021-07-14   mzy  add
        // }
        XinDailyReportController.testI();
        XinDailyReportController.testY();
        

    }
        
    static void test01_02() {
        init();

        System.runAs(u1) {
            XinDailyReportController t = new XinDailyReportController();
            t.init();

            t.report_search.Reported_Date__c = date.today();
            PageReference ref = t.getDailyReport();
            Test.setCurrentPage(ref);
            t = new XinDailyReportController();
            t.init();
            //          t.getEvent();
            //          t.report.Reporter__c = u1.id;

            t.report.Reported_Date__c = date.today();
            t.repoStartHourText = '9';
            t.repoStartMinuteText = '00';
            t.repoEndHourText = '18';
            t.repoEndMinuteText = '00';
            t.report.Status__c = '申請中';
            t.activities.get(0).actStartHourText = '9';
            t.activities.get(0).actStartMinuteText = '00';
            t.activities.get(0).actEndHourText = '18';
            t.activities.get(0).actEndMinuteText = '00';
            t.activities.get(0).act.nextPlanDate__c = date.today();
            t.activities.get(0).act.Activity_Type2__c = '病院';
            t.activities.get(0).act.Location__c = '戦略科室分類1 診療科1test';
            t.activities.get(0).act.Purpose__c = '目的(計画)';
            t.activities.get(0).act.Description__c = '結果';
            t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
            t.activities.get(0).act.Visitor1__c = '取引先責任者1';
            t.activities.get(0).act.Visitor1_ID__c = null;
            t.activities.get(0).act.Visitor2__c = '取引先責任者2';
            t.activities.get(0).act.Visitor2_ID__c = null;
            t.activities.get(0).act.Visitor3__c = '取引先責任者3';
            t.activities.get(0).act.Visitor3_ID__c = null;
            t.activities.get(0).act.Visitor4__c = '取引先責任者4';
            t.activities.get(0).act.Visitor4_ID__c = null;
            t.activities.get(0).act.Visitor5__c = '取引先責任者5';
            t.activities.get(0).act.Visitor5_ID__c = null;
            t.activities.get(0).act.Related_Opportunity1__c = opp1.Name + 'test';
            t.activities.get(0).act.Related_Opportunity2__c = opp2.Name + 'test';
            t.activities.get(0).act.Related_Opportunity3__c = opp3.Name + 'test';
            t.activities.get(0).act.Related_Opportunity4__c = opp4.Name + 'test';
            t.activities.get(0).act.Related_Opportunity5__c = opp5.Name + 'test';
            t.activities.get(0).act.Related_Service1__c = mc1.Name + 'test';
            t.activities.get(0).act.Related_Service2__c = mc2.Name + 'test';
            t.activities.get(0).planStartHourText = '9';
            t.activities.get(0).planStartMinuteText = '00';
            t.activities.get(0).planEndHourText = '18';
            t.activities.get(0).planEndMinuteText = '00';
            t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';
            t.save();

            t.activities.get(0).act.Location__c = accDepName;
            t.activities.get(0).act.whatid__c = accDep.Id;
            t.activities.get(0).act.Visitor1__c = '取引先責任者1';
            t.activities.get(0).act.Visitor1_ID__c = con1.id;
            t.activities.get(0).act.Visitor2__c = '取引先責任者2';
            t.activities.get(0).act.Visitor2_ID__c = con2.id;
            t.activities.get(0).act.Visitor3__c = '取引先責任者3';
            t.activities.get(0).act.Visitor3_ID__c = con3.id;
            t.activities.get(0).act.Visitor4__c = '取引先責任者4';
            t.activities.get(0).act.Visitor4_ID__c = con4.id;
            t.activities.get(0).act.Visitor5__c = '取引先責任者5';
            t.activities.get(0).act.Visitor5_ID__c = con5.id;
            t.activities.get(0).act.Related_Opportunity1__c = '::引合1';
            t.activities.get(0).act.Related_Opportunity2__c = '::引合2';
            t.activities.get(0).act.Related_Opportunity3__c = '::引合3';
            t.activities.get(0).act.Related_Opportunity4__c = '::引合4';
            t.activities.get(0).act.Related_Opportunity5__c = '::引合5';
            t.activities.get(0).act.Related_Opportunity1_ID__c = opp1.Id;
            t.activities.get(0).act.Related_Opportunity2_ID__c = opp2.Id;
            t.activities.get(0).act.Related_Opportunity3_ID__c = opp3.Id;
            t.activities.get(0).act.Related_Opportunity4_ID__c = opp4.Id;
            t.activities.get(0).act.Related_Opportunity5_ID__c = opp5.Id;
            t.activities.get(0).act.Related_Service1__c = mc1.Name;
            t.activities.get(0).act.Related_Service2__c = mc2.Name;
            t.activities.get(0).act.Related_Service1_ID__c = mc1.Id;
            t.activities.get(0).act.Related_Service2_ID__c = mc2.Id;
            t.upsertActIndex = '0';
            t.save();

            Date d = date.today();
            String strDate = d.year() + '/' + d.month() + '/' + d.day();

            Datetime dt = datetime.now();
            String strDatetime = '2000/01/01 10:10';
            System.debug('strDatetime' + strDatetime);

            System.Test.startTest();
            System.debug('t.activities.get(0).act.id:::::' + t.activities.get(0).act.id);
            String pdId = Add_Report.addReportPr(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Product_Explanation', '戦略科室分類1 診療科1test', con1.id, con2.id, con3.id, con4.id, con5.id, '結果', strDate);
            if (pdId != 'false') t.activities.get(0).act.Product_Description_Id__c = pdId;
            t.save();
            System.Test.stopTest();
        }
    }


//     static void test01_03() {
//         init();

//         System.runAs(u1) {
//             XinDailyReportController t = new XinDailyReportController();
//             t.init();

//             t.report_search.Reported_Date__c = date.today();
//             PageReference ref = t.getDailyReport();
//             Test.setCurrentPage(ref);
//             t = new XinDailyReportController();
//             t.init();


//             //          t.getEvent();
//             t.report.Reporter__c = u1.id;

//             t.report.Reported_Date__c = date.today();
//             t.repoStartHourText = '9';
//             t.repoStartMinuteText = '00';
//             t.repoEndHourText = '18';
//             t.repoEndMinuteText = '00';
//             t.report.Status__c = '申請中';
//             t.activities.get(0).actStartHourText = '9';
//             t.activities.get(0).actStartMinuteText = '00';
//             t.activities.get(0).actEndHourText = '18';
//             t.activities.get(0).actEndMinuteText = '00';
//             t.activities.get(0).act.nextPlanDate__c = date.today();
//             t.activities.get(0).act.Activity_Type2__c = '病院';
//             t.activities.get(0).act.Location__c = '戦略科室分類1 診療科1test';
//             t.activities.get(0).act.Purpose__c = '目的(計画)';
//             t.activities.get(0).act.Description__c = '結果';
//             t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = null;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = null;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = null;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = null;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = null;
//             t.activities.get(0).act.Related_Opportunity1__c = opp1.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity2__c = opp2.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity3__c = opp3.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity4__c = opp4.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity5__c = opp5.Name + 'test';
//             t.activities.get(0).act.Related_Service1__c = mc1.Name + 'test';
//             t.activities.get(0).act.Related_Service2__c = mc2.Name + 'test';
//             t.activities.get(0).planStartHourText = '9';
//             t.activities.get(0).planStartMinuteText = '00';
//             t.activities.get(0).planEndHourText = '18';
//             t.activities.get(0).planEndMinuteText = '00';
//             t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';
//             t.save();

//             t.activities.get(0).act.Location__c = accDepName;
//             t.activities.get(0).act.whatid__c = accDep.Id;
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = con1.id;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = con2.id;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = con3.id;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = con4.id;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = con5.id;
//             t.activities.get(0).act.Related_Opportunity1__c = '::引合1';
//             t.activities.get(0).act.Related_Opportunity2__c = '::引合2';
//             t.activities.get(0).act.Related_Opportunity3__c = '::引合3';
//             t.activities.get(0).act.Related_Opportunity4__c = '::引合4';
//             t.activities.get(0).act.Related_Opportunity5__c = '::引合5';
//             t.activities.get(0).act.Related_Opportunity1_ID__c = opp1.Id;
//             t.activities.get(0).act.Related_Opportunity2_ID__c = opp2.Id;
//             t.activities.get(0).act.Related_Opportunity3_ID__c = opp3.Id;
//             t.activities.get(0).act.Related_Opportunity4_ID__c = opp4.Id;
//             t.activities.get(0).act.Related_Opportunity5_ID__c = opp5.Id;
//             t.activities.get(0).act.Related_Service1__c = mc1.Name;
//             t.activities.get(0).act.Related_Service2__c = mc2.Name;
//             t.activities.get(0).act.Related_Service1_ID__c = mc1.Id;
//             t.activities.get(0).act.Related_Service2_ID__c = mc2.Id;
//             t.upsertActIndex = '0';
//             t.save();

//             Date d = date.today();
//             String strDate = d.year() + '/' + d.month() + '/' + d.day();

//             Datetime dt = datetime.now();
//             String strDatetime = '2000/01/01 10:10';
//             System.debug('strDatetime' + strDatetime);

//             System.Test.startTest();
//             System.debug('t.activities.get(0).act.id:::::' + t.activities.get(0).act.id);
//             String pdId = Add_Report.addReportPr(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Product_Explanation', accDep.Id, con1.id, con2.id, con3.id, con4.id, con5.id, '結果', strDate);
//             if (pdId != 'false') t.activities.get(0).act.Product_Description_Id__c = pdId;
//             t.save();
//             System.Test.stopTest();
//         }
//     }

//     static void test01_04() {
//         init();

//         System.runAs(u1) {
//             XinDailyReportController t = new XinDailyReportController();
//             t.init();

//             t.report_search.Reported_Date__c = date.today();
//             PageReference ref = t.getDailyReport();
//             Test.setCurrentPage(ref);
//             t = new XinDailyReportController();
//             t.init();
//             //          t.getEvent();
//             //          t.report.Reporter__c = u1.id;

//             t.report.Reported_Date__c = date.today();
//             t.repoStartHourText = '9';
//             t.repoStartMinuteText = '00';
//             t.repoEndHourText = '18';
//             t.repoEndMinuteText = '00';
//             t.report.Status__c = '申請中';
//             t.activities.get(0).actStartHourText = '9';
//             t.activities.get(0).actStartMinuteText = '00';
//             t.activities.get(0).actEndHourText = '18';
//             t.activities.get(0).actEndMinuteText = '00';
//             t.activities.get(0).act.nextPlanDate__c = date.today();
//             t.activities.get(0).act.Activity_Type2__c = '病院';
//             t.activities.get(0).act.Location__c = '戦略科室分類1 診療科1test';
//             t.activities.get(0).act.Purpose__c = '目的(計画)';
//             t.activities.get(0).act.Description__c = '結果';
//             t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = null;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = null;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = null;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = null;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = null;
//             t.activities.get(0).act.Related_Opportunity1__c = opp1.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity2__c = opp2.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity3__c = opp3.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity4__c = opp4.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity5__c = opp5.Name + 'test';
//             t.activities.get(0).act.Related_Service1__c = mc1.Name + 'test';
//             t.activities.get(0).act.Related_Service2__c = mc2.Name + 'test';
//             t.activities.get(0).planStartHourText = '9';
//             t.activities.get(0).planStartMinuteText = '00';
//             t.activities.get(0).planEndHourText = '18';
//             t.activities.get(0).planEndMinuteText = '00';
//             t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';
//             t.save();

//             t.activities.get(0).act.Location__c = accDepName;
//             t.activities.get(0).act.whatid__c = accDep.Id;
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = con1.id;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = con2.id;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = con3.id;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = con4.id;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = con5.id;
//             t.activities.get(0).act.Related_Opportunity1__c = '::引合1';
//             t.activities.get(0).act.Related_Opportunity2__c = '::引合2';
//             t.activities.get(0).act.Related_Opportunity3__c = '::引合3';
//             t.activities.get(0).act.Related_Opportunity4__c = '::引合4';
//             t.activities.get(0).act.Related_Opportunity5__c = '::引合5';
//             t.activities.get(0).act.Related_Opportunity1_ID__c = opp1.Id;
//             t.activities.get(0).act.Related_Opportunity2_ID__c = opp2.Id;
//             t.activities.get(0).act.Related_Opportunity3_ID__c = opp3.Id;
//             t.activities.get(0).act.Related_Opportunity4_ID__c = opp4.Id;
//             t.activities.get(0).act.Related_Opportunity5_ID__c = opp5.Id;
//             t.activities.get(0).act.Related_Service1__c = mc1.Name;
//             t.activities.get(0).act.Related_Service2__c = mc2.Name;
//             t.activities.get(0).act.Related_Service1_ID__c = mc1.Id;
//             t.activities.get(0).act.Related_Service2_ID__c = mc2.Id;
//             t.upsertActIndex = '0';
//             t.save();

//             Date d = date.today();
//             String strDate = d.year() + '/' + d.month() + '/' + d.day();

//             Datetime dt = datetime.now();
//             String strDatetime = '2000/01/01 10:10';
//             System.debug('strDatetime' + strDatetime);

//             System.Test.startTest();
//             System.debug('t.activities.get(0).act.id:::::' + t.activities.get(0).act.id);
//             String pdId = Add_Report.addReportPr(u1.id, t.activities.get(0).act.Product_Description_Id__c, t.report.id, t.activities.get(0).act.id, 'Product_Explanation', accDep.Id, con1.id, con2.id, con3.id, con4.id, con5.id, '結果', strDate);
//             if (pdId != 'false') t.activities.get(0).act.Product_Description_Id__c = pdId;
//             t.save();
//             System.Test.stopTest();
//         }
//     }

//     static void test01_05() {
//         init();

//         System.runAs(u1) {
//             XinDailyReportController t = new XinDailyReportController();
//             t.init();

//             t.report_search.Reported_Date__c = date.today();
//             PageReference ref = t.getDailyReport();
//             Test.setCurrentPage(ref);
//             t = new XinDailyReportController();
//             t.init();
//             //          t.getEvent();
//             //          t.report.Reporter__c = u1.id;

//             t.report.Reported_Date__c = date.today();
//             t.repoStartHourText = '9';
//             t.repoStartMinuteText = '00';
//             t.repoEndHourText = '18';
//             t.repoEndMinuteText = '00';
//             t.report.Status__c = '申請中';
//             t.activities.get(0).actStartHourText = '9';
//             t.activities.get(0).actStartMinuteText = '00';
//             t.activities.get(0).actEndHourText = '18';
//             t.activities.get(0).actEndMinuteText = '00';
//             t.activities.get(0).act.nextPlanDate__c = date.today();
//             t.activities.get(0).act.Activity_Type2__c = '病院';
//             t.activities.get(0).act.Location__c = '戦略科室分類1 診療科1test';
//             t.activities.get(0).act.Purpose__c = '目的(計画)';
//             t.activities.get(0).act.Description__c = '結果';
//             t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = null;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = null;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = null;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = null;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = null;
//             t.activities.get(0).act.Related_Opportunity1__c = opp1.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity2__c = opp2.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity3__c = opp3.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity4__c = opp4.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity5__c = opp5.Name + 'test';
//             t.activities.get(0).act.Related_Service1__c = mc1.Name + 'test';
//             t.activities.get(0).act.Related_Service2__c = mc2.Name + 'test';
//             t.activities.get(0).planStartHourText = '9';
//             t.activities.get(0).planStartMinuteText = '00';
//             t.activities.get(0).planEndHourText = '18';
//             t.activities.get(0).planEndMinuteText = '00';
//             t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';
//             t.save();

//             t.activities.get(0).act.Location__c = accDepName;
//             t.activities.get(0).act.whatid__c = accDep.Id;
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = con1.id;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = con2.id;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = con3.id;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = con4.id;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = con5.id;
//             t.activities.get(0).act.Related_Opportunity1__c = '::引合1';
//             t.activities.get(0).act.Related_Opportunity2__c = '::引合2';
//             t.activities.get(0).act.Related_Opportunity3__c = '::引合3';
//             t.activities.get(0).act.Related_Opportunity4__c = '::引合4';
//             t.activities.get(0).act.Related_Opportunity5__c = '::引合5';
//             t.activities.get(0).act.Related_Opportunity1_ID__c = opp1.Id;
//             t.activities.get(0).act.Related_Opportunity2_ID__c = opp2.Id;
//             t.activities.get(0).act.Related_Opportunity3_ID__c = opp3.Id;
//             t.activities.get(0).act.Related_Opportunity4_ID__c = opp4.Id;
//             t.activities.get(0).act.Related_Opportunity5_ID__c = opp5.Id;
//             t.activities.get(0).act.Related_Service1__c = mc1.Name;
//             t.activities.get(0).act.Related_Service2__c = mc2.Name;
//             t.activities.get(0).act.Related_Service1_ID__c = mc1.Id;
//             t.activities.get(0).act.Related_Service2_ID__c = mc2.Id;
//             t.upsertActIndex = '0';
//             t.save();

//             Date d = date.today();
//             String strDate = d.year() + '/' + d.month() + '/' + d.day();

//             Datetime dt = datetime.now();
//             String strDatetime = '2000/01/01 10:10';
//             System.debug('strDatetime' + strDatetime);

//             System.Test.startTest();
//             String ntId = Add_Report.addReportNT(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'NTC', '戦略科室分類1 診療科1test', con1.id, con2.id, con3.id, con4.id, con5.id, strDatetime, strDatetime, '内視鏡室');
//             if (ntId != 'false') t.activities.get(0).act.NTC_ID__c = ntId;
//             t.save();
//             System.Test.stopTest();
//         }
//     }

//     static void test01_06() {
//         init();

//         System.runAs(u1) {
//             XinDailyReportController t = new XinDailyReportController();
//             t.init();

//             t.report_search.Reported_Date__c = date.today();
//             PageReference ref = t.getDailyReport();
//             Test.setCurrentPage(ref);
//             t = new XinDailyReportController();
//             t.init();
//             //          t.getEvent();
//             //          t.report.Reporter__c = u1.id;

//             t.report.Reported_Date__c = date.today();
//             t.repoStartHourText = '9';
//             t.repoStartMinuteText = '00';
//             t.repoEndHourText = '18';
//             t.repoEndMinuteText = '00';
//             t.report.Status__c = '申請中';
//             t.activities.get(0).actStartHourText = '9';
//             t.activities.get(0).actStartMinuteText = '00';
//             t.activities.get(0).actEndHourText = '18';
//             t.activities.get(0).actEndMinuteText = '00';
//             t.activities.get(0).act.nextPlanDate__c = date.today();
//             t.activities.get(0).act.Activity_Type2__c = '病院';
//             t.activities.get(0).act.Location__c = '戦略科室分類1 診療科1test';
//             t.activities.get(0).act.Purpose__c = '目的(計画)';
//             t.activities.get(0).act.Description__c = '結果';
//             t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = null;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = null;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = null;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = null;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = null;
//             t.activities.get(0).act.Related_Opportunity1__c = opp1.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity2__c = opp2.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity3__c = opp3.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity4__c = opp4.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity5__c = opp5.Name + 'test';
//             t.activities.get(0).act.Related_Service1__c = mc1.Name + 'test';
//             t.activities.get(0).act.Related_Service2__c = mc2.Name + 'test';
//             t.activities.get(0).planStartHourText = '9';
//             t.activities.get(0).planStartMinuteText = '00';
//             t.activities.get(0).planEndHourText = '18';
//             t.activities.get(0).planEndMinuteText = '00';
//             t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';
//             t.save();

//             t.activities.get(0).act.Location__c = accDepName;
//             t.activities.get(0).act.whatid__c = accDep.Id;
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = con1.id;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = con2.id;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = con3.id;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = con4.id;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = con5.id;
//             t.activities.get(0).act.Related_Opportunity1__c = '::引合1';
//             t.activities.get(0).act.Related_Opportunity2__c = '::引合2';
//             t.activities.get(0).act.Related_Opportunity3__c = '::引合3';
//             t.activities.get(0).act.Related_Opportunity4__c = '::引合4';
//             t.activities.get(0).act.Related_Opportunity5__c = '::引合5';
//             t.activities.get(0).act.Related_Opportunity1_ID__c = opp1.Id;
//             t.activities.get(0).act.Related_Opportunity2_ID__c = opp2.Id;
//             t.activities.get(0).act.Related_Opportunity3_ID__c = opp3.Id;
//             t.activities.get(0).act.Related_Opportunity4_ID__c = opp4.Id;
//             t.activities.get(0).act.Related_Opportunity5_ID__c = opp5.Id;
//             t.activities.get(0).act.Related_Service1__c = mc1.Name;
//             t.activities.get(0).act.Related_Service2__c = mc2.Name;
//             t.activities.get(0).act.Related_Service1_ID__c = mc1.Id;
//             t.activities.get(0).act.Related_Service2_ID__c = mc2.Id;
//             t.upsertActIndex = '0';
//             t.save();

//             Date d = date.today();
//             String strDate = d.year() + '/' + d.month() + '/' + d.day();

//             Datetime dt = datetime.now();
//             String strDatetime = '2000/01/01 10:10';
//             System.debug('strDatetime' + strDatetime);

//             System.Test.startTest();
//             String ntId = Add_Report.addReportNT(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'NTC', accDep.Id, con1.id, con2.id, con3.id, con4.id, con5.id, strDatetime, strDatetime, '内視鏡室');
//             if (ntId != 'false') t.activities.get(0).act.NTC_ID__c = ntId;
//             t.save();
//             System.Test.stopTest();
//         }
//     }

//     static void test01_07() {
//         init();

//         System.runAs(u1) {
//             XinDailyReportController t = new XinDailyReportController();
//             t.init();

//             t.report_search.Reported_Date__c = date.today();
//             PageReference ref = t.getDailyReport();
//             Test.setCurrentPage(ref);
//             t = new XinDailyReportController();
//             t.init();
//             //          t.getEvent();
//             //          t.report.Reporter__c = u1.id;

//             t.report.Reported_Date__c = date.today();
//             t.repoStartHourText = '9';
//             t.repoStartMinuteText = '00';
//             t.repoEndHourText = '18';
//             t.repoEndMinuteText = '00';
//             t.report.Status__c = '申請中';
//             t.activities.get(0).actStartHourText = '9';
//             t.activities.get(0).actStartMinuteText = '00';
//             t.activities.get(0).actEndHourText = '18';
//             t.activities.get(0).actEndMinuteText = '00';
//             t.activities.get(0).act.nextPlanDate__c = date.today();
//             t.activities.get(0).act.Activity_Type2__c = '病院';
//             t.activities.get(0).act.Location__c = '戦略科室分類1 診療科1test';
//             t.activities.get(0).act.Purpose__c = '目的(計画)';
//             t.activities.get(0).act.Description__c = '結果';
//             t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = null;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = null;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = null;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = null;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = null;
//             t.activities.get(0).act.Related_Opportunity1__c = opp1.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity2__c = opp2.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity3__c = opp3.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity4__c = opp4.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity5__c = opp5.Name + 'test';
//             t.activities.get(0).act.Related_Service1__c = mc1.Name + 'test';
//             t.activities.get(0).act.Related_Service2__c = mc2.Name + 'test';
//             t.activities.get(0).planStartHourText = '9';
//             t.activities.get(0).planStartMinuteText = '00';
//             t.activities.get(0).planEndHourText = '18';
//             t.activities.get(0).planEndMinuteText = '00';
//             t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';
//             t.save();

//             t.activities.get(0).act.Location__c = accDepName;
//             t.activities.get(0).act.whatid__c = accDep.Id;
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = con1.id;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = con2.id;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = con3.id;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = con4.id;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = con5.id;
//             t.activities.get(0).act.Related_Opportunity1__c = '::引合1';
//             t.activities.get(0).act.Related_Opportunity2__c = '::引合2';
//             t.activities.get(0).act.Related_Opportunity3__c = '::引合3';
//             t.activities.get(0).act.Related_Opportunity4__c = '::引合4';
//             t.activities.get(0).act.Related_Opportunity5__c = '::引合5';
//             t.activities.get(0).act.Related_Opportunity1_ID__c = opp1.Id;
//             t.activities.get(0).act.Related_Opportunity2_ID__c = opp2.Id;
//             t.activities.get(0).act.Related_Opportunity3_ID__c = opp3.Id;
//             t.activities.get(0).act.Related_Opportunity4_ID__c = opp4.Id;
//             t.activities.get(0).act.Related_Opportunity5_ID__c = opp5.Id;
//             t.activities.get(0).act.Related_Service1__c = mc1.Name;
//             t.activities.get(0).act.Related_Service2__c = mc2.Name;
//             t.activities.get(0).act.Related_Service1_ID__c = mc1.Id;
//             t.activities.get(0).act.Related_Service2_ID__c = mc2.Id;
//             t.upsertActIndex = '0';
//             t.save();

//             Date d = date.today();
//             String strDate = d.year() + '/' + d.month() + '/' + d.day();

//             Datetime dt = datetime.now();
//             String strDatetime = '2000/01/01 10:10';
//             System.debug('strDatetime' + strDatetime);

//             System.Test.startTest();
//             String ntId = Add_Report.addReportNT(u1.id, t.activities.get(0).act.NTC_ID__c, t.report.id, t.activities.get(0).act.id, 'NTC', accDep.Id, con1.id, con2.id, con3.id, con4.id, con5.id, strDatetime, strDatetime, '内視鏡室');
//             t.save();
//             System.Test.stopTest();
//         }
//     }

//     static void test01_08() {
//         init();

//         System.runAs(u1) {
//             XinDailyReportController t = new XinDailyReportController();
//             t.init();

//             t.report_search.Reported_Date__c = date.today();
//             PageReference ref = t.getDailyReport();
//             Test.setCurrentPage(ref);
//             t = new XinDailyReportController();
//             t.init();
//             //          t.getEvent();
//             //          t.report.Reporter__c = u1.id;

//             t.report.Reported_Date__c = date.today();
//             t.repoStartHourText = '9';
//             t.repoStartMinuteText = '00';
//             t.repoEndHourText = '18';
//             t.repoEndMinuteText = '00';
//             t.report.Status__c = '申請中';
//             t.activities.get(0).actStartHourText = '9';
//             t.activities.get(0).actStartMinuteText = '00';
//             t.activities.get(0).actEndHourText = '18';
//             t.activities.get(0).actEndMinuteText = '00';
//             t.activities.get(0).act.nextPlanDate__c = date.today();
//             t.activities.get(0).act.Activity_Type2__c = '病院';
//             t.activities.get(0).act.Location__c = '戦略科室分類1 診療科1test';
//             t.activities.get(0).act.Purpose__c = '目的(計画)';
//             t.activities.get(0).act.Description__c = '結果';
//             t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = null;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = null;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = null;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = null;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = null;
//             t.activities.get(0).act.Related_Opportunity1__c = opp1.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity2__c = opp2.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity3__c = opp3.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity4__c = opp4.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity5__c = opp5.Name + 'test';
//             t.activities.get(0).act.Related_Service1__c = mc1.Name + 'test';
//             t.activities.get(0).act.Related_Service2__c = mc2.Name + 'test';
//             t.activities.get(0).planStartHourText = '9';
//             t.activities.get(0).planStartMinuteText = '00';
//             t.activities.get(0).planEndHourText = '18';
//             t.activities.get(0).planEndMinuteText = '00';
//             t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';
//             t.save();

//             t.activities.get(0).act.Location__c = accDepName;
//             t.activities.get(0).act.whatid__c = accDep.Id;
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = con1.id;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = con2.id;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = con3.id;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = con4.id;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = con5.id;
//             t.activities.get(0).act.Related_Opportunity1__c = '::引合1';
//             t.activities.get(0).act.Related_Opportunity2__c = '::引合2';
//             t.activities.get(0).act.Related_Opportunity3__c = '::引合3';
//             t.activities.get(0).act.Related_Opportunity4__c = '::引合4';
//             t.activities.get(0).act.Related_Opportunity5__c = '::引合5';
//             t.activities.get(0).act.Related_Opportunity1_ID__c = opp1.Id;
//             t.activities.get(0).act.Related_Opportunity2_ID__c = opp2.Id;
//             t.activities.get(0).act.Related_Opportunity3_ID__c = opp3.Id;
//             t.activities.get(0).act.Related_Opportunity4_ID__c = opp4.Id;
//             t.activities.get(0).act.Related_Opportunity5_ID__c = opp5.Id;
//             t.activities.get(0).act.Related_Service1__c = mc1.Name;
//             t.activities.get(0).act.Related_Service2__c = mc2.Name;
//             t.activities.get(0).act.Related_Service1_ID__c = mc1.Id;
//             t.activities.get(0).act.Related_Service2_ID__c = mc2.Id;
//             t.upsertActIndex = '0';
//             t.save();

//             Date d = date.today();
//             String strDate = d.year() + '/' + d.month() + '/' + d.day();

//             Datetime dt = datetime.now();
//             String strDatetime = '2000/01/01 10:10';
//             System.debug('strDatetime' + strDatetime);

//             System.Test.startTest();
//             System.debug('t.activities.get(0).act.id:::::' + t.activities.get(0).act.id);
//             String opId = Add_Report.addReportOP(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'OPD', '戦略科室分類1 診療科1test', con1.id, con2.id, con3.id, con4.id, con5.id, opp1.Id, opp2.Id, opp3.Id, opp4.Id, opp5.Id, strDate);
//             if (opId != 'false') t.activities.get(0).act.OPD_ID__c = opId;
//             t.save();
//             System.Test.stopTest();
//         }
//     }

//     static void test01_09() {
//         init();

//         System.runAs(u1) {
//             XinDailyReportController t = new XinDailyReportController();
//             t.init();

//             t.report_search.Reported_Date__c = date.today();
//             PageReference ref = t.getDailyReport();
//             Test.setCurrentPage(ref);
//             t = new XinDailyReportController();
//             t.init();
//             //          t.getEvent();
//             //          t.report.Reporter__c = u1.id;

//             t.report.Reported_Date__c = date.today();
//             t.repoStartHourText = '9';
//             t.repoStartMinuteText = '00';
//             t.repoEndHourText = '18';
//             t.repoEndMinuteText = '00';
//             t.report.Status__c = '申請中';
//             t.activities.get(0).actStartHourText = '9';
//             t.activities.get(0).actStartMinuteText = '00';
//             t.activities.get(0).actEndHourText = '18';
//             t.activities.get(0).actEndMinuteText = '00';
//             t.activities.get(0).act.nextPlanDate__c = date.today();
//             t.activities.get(0).act.Activity_Type2__c = '病院';
//             t.activities.get(0).act.Location__c = '戦略科室分類1 診療科1test';
//             t.activities.get(0).act.Purpose__c = '目的(計画)';
//             t.activities.get(0).act.Description__c = '結果';
//             t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = null;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = null;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = null;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = null;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = null;
//             t.activities.get(0).act.Related_Opportunity1__c = opp1.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity2__c = opp2.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity3__c = opp3.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity4__c = opp4.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity5__c = opp5.Name + 'test';
//             t.activities.get(0).act.Related_Service1__c = mc1.Name + 'test';
//             t.activities.get(0).act.Related_Service2__c = mc2.Name + 'test';
//             t.activities.get(0).planStartHourText = '9';
//             t.activities.get(0).planStartMinuteText = '00';
//             t.activities.get(0).planEndHourText = '18';
//             t.activities.get(0).planEndMinuteText = '00';
//             t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';
//             t.save();

//             t.activities.get(0).act.Location__c = accDepName;
//             t.activities.get(0).act.whatid__c = accDep.Id;
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = con1.id;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = con2.id;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = con3.id;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = con4.id;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = con5.id;
//             t.activities.get(0).act.Related_Opportunity1__c = '::引合1';
//             t.activities.get(0).act.Related_Opportunity2__c = '::引合2';
//             t.activities.get(0).act.Related_Opportunity3__c = '::引合3';
//             t.activities.get(0).act.Related_Opportunity4__c = '::引合4';
//             t.activities.get(0).act.Related_Opportunity5__c = '::引合5';
//             t.activities.get(0).act.Related_Opportunity1_ID__c = opp1.Id;
//             t.activities.get(0).act.Related_Opportunity2_ID__c = opp2.Id;
//             t.activities.get(0).act.Related_Opportunity3_ID__c = opp3.Id;
//             t.activities.get(0).act.Related_Opportunity4_ID__c = opp4.Id;
//             t.activities.get(0).act.Related_Opportunity5_ID__c = opp5.Id;
//             t.activities.get(0).act.Related_Service1__c = mc1.Name;
//             t.activities.get(0).act.Related_Service2__c = mc2.Name;
//             t.activities.get(0).act.Related_Service1_ID__c = mc1.Id;
//             t.activities.get(0).act.Related_Service2_ID__c = mc2.Id;
//             t.upsertActIndex = '0';
//             t.save();

//             Date d = date.today();
//             String strDate = d.year() + '/' + d.month() + '/' + d.day();

//             Datetime dt = datetime.now();
//             String strDatetime = '2000/01/01 10:10';
//             System.debug('strDatetime' + strDatetime);

//             System.Test.startTest();
//             System.debug('t.activities.get(0).act.id:::::' + t.activities.get(0).act.id);
//             String opId = Add_Report.addReportOP(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'OPD', accDep.Id, con1.id, con2.id, con3.id, con4.id, con5.id, opp1.Id, opp2.Id, opp3.Id, opp1.Id, opp5.Id, strDate);
//             if (opId != 'false') t.activities.get(0).act.OPD_ID__c = opId;
//             t.save();
//             System.Test.stopTest();
//         }
//     }

//     static void test01_10() {
//         init();

//         System.runAs(u1) {
//             XinDailyReportController t = new XinDailyReportController();
//             t.init();

//             t.report_search.Reported_Date__c = date.today();
//             PageReference ref = t.getDailyReport();
//             Test.setCurrentPage(ref);
//             t = new XinDailyReportController();
//             t.init();
//             //          t.getEvent();
//             //          t.report.Reporter__c = u1.id;

//             t.report.Reported_Date__c = date.today();
//             t.repoStartHourText = '9';
//             t.repoStartMinuteText = '00';
//             t.repoEndHourText = '18';
//             t.repoEndMinuteText = '00';
//             t.report.Status__c = '申請中';
//             t.activities.get(0).actStartHourText = '9';
//             t.activities.get(0).actStartMinuteText = '00';
//             t.activities.get(0).actEndHourText = '18';
//             t.activities.get(0).actEndMinuteText = '00';
//             t.activities.get(0).act.nextPlanDate__c = date.today();
//             t.activities.get(0).act.Activity_Type2__c = '病院';
//             t.activities.get(0).act.Location__c = '戦略科室分類1 診療科1test';
//             t.activities.get(0).act.Purpose__c = '目的(計画)';
//             t.activities.get(0).act.Description__c = '結果';
//             t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = null;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = null;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = null;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = null;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = null;
//             t.activities.get(0).act.Related_Opportunity1__c = opp1.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity2__c = opp2.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity3__c = opp3.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity4__c = opp4.Name + 'test';
//             t.activities.get(0).act.Related_Opportunity5__c = opp5.Name + 'test';
//             t.activities.get(0).act.Related_Service1__c = mc1.Name + 'test';
//             t.activities.get(0).act.Related_Service2__c = mc2.Name + 'test';
//             t.activities.get(0).planStartHourText = '9';
//             t.activities.get(0).planStartMinuteText = '00';
//             t.activities.get(0).planEndHourText = '18';
//             t.activities.get(0).planEndMinuteText = '00';
//             t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';
//             t.save();

//             t.activities.get(0).act.Location__c = accDepName;
//             t.activities.get(0).act.whatid__c = accDep.Id;
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = con1.id;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = con2.id;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = con3.id;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = con4.id;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = con5.id;
//             t.activities.get(0).act.Related_Opportunity1__c = '::引合1';
//             t.activities.get(0).act.Related_Opportunity2__c = '::引合2';
//             t.activities.get(0).act.Related_Opportunity3__c = '::引合3';
//             t.activities.get(0).act.Related_Opportunity4__c = '::引合4';
//             t.activities.get(0).act.Related_Opportunity5__c = '::引合5';
//             t.activities.get(0).act.Related_Opportunity1_ID__c = opp1.Id;
//             t.activities.get(0).act.Related_Opportunity2_ID__c = opp2.Id;
//             t.activities.get(0).act.Related_Opportunity3_ID__c = opp3.Id;
//             t.activities.get(0).act.Related_Opportunity4_ID__c = opp4.Id;
//             t.activities.get(0).act.Related_Opportunity5_ID__c = opp5.Id;
//             t.activities.get(0).act.Related_Service1__c = mc1.Name;
//             t.activities.get(0).act.Related_Service2__c = mc2.Name;
//             t.activities.get(0).act.Related_Service1_ID__c = mc1.Id;
//             t.activities.get(0).act.Related_Service2_ID__c = mc2.Id;
//             t.upsertActIndex = '0';
//             t.save();

//             Date d = date.today();
//             String strDate = d.year() + '/' + d.month() + '/' + d.day();

//             Datetime dt = datetime.now();
//             String strDatetime = '2000/01/01 10:10';
//             System.debug('strDatetime' + strDatetime);

//             System.Test.startTest();
//             System.debug('t.activities.get(0).act.id:::::' + t.activities.get(0).act.id);
//             String opId = Add_Report.addReportOP(u1.id, t.activities.get(0).act.OPD_ID__c, t.report.id, t.activities.get(0).act.id, 'OPD', accDep.Id, con1.id, con2.id, con3.id, con4.id, con5.id, opp1.Id, opp2.Id, opp3.Id, opp1.Id, opp5.Id, strDate);
//             if (opId != 'false') t.activities.get(0).act.OPD_ID__c = opId;
//             t.save();
//             System.Test.stopTest();
//         }
//     }

//     static void test02_01() {
//         init();

//         System.runAs(u1) {
//             XinDailyReportController t = new XinDailyReportController();
//             t.init();

//             t.report_search.Reported_Date__c = date.today();
//             PageReference ref = t.getDailyReport();
//             Test.setCurrentPage(ref);
//             t = new XinDailyReportController();
//             t.init();
//             //          t.getEvent();

//             t.report.Status__c = '申請中';
//             t.report.Reported_Date__c = date.today();
//             t.repoStartHourText = '9';
//             t.repoStartMinuteText = '00';
//             t.repoEndHourText = '18';
//             t.repoEndMinuteText = '00';
//             t.activities.get(0).actStartHourText = '9';
//             t.activities.get(0).actStartMinuteText = '00';
//             t.activities.get(0).actEndHourText = '18';
//             t.activities.get(0).actEndMinuteText = '00';
//             t.activities.get(0).act.Activity_Type2__c = '病院';
//             t.activities.get(0).act.Purpose__c = '目的(計画)';
//             t.activities.get(0).act.Description__c = '結果';
//             t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
//             t.activities.get(0).act.Location__c = accDepName;
//             t.activities.get(0).act.whatid__c = accDep.Id;
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = con1.id;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = con2.id;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = con3.id;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = con4.id;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = con5.id;
//             t.activities.get(0).act.Related_Opportunity1__c = '::引合1';
//             t.activities.get(0).act.Related_Opportunity2__c = '::引合2';
//             t.activities.get(0).act.Related_Opportunity3__c = '::引合3';
//             t.activities.get(0).act.Related_Opportunity4__c = '::引合4';
//             t.activities.get(0).act.Related_Opportunity5__c = '::引合5';
//             t.activities.get(0).act.Related_Opportunity1_ID__c = opp1.Id;
//             t.activities.get(0).act.Related_Opportunity2_ID__c = opp2.Id;
//             t.activities.get(0).act.Related_Opportunity3_ID__c = opp3.Id;
//             t.activities.get(0).act.Related_Opportunity4_ID__c = opp4.Id;
//             t.activities.get(0).act.Related_Opportunity5_ID__c = opp5.Id;
//             t.activities.get(0).act.Related_Service1__c = mc1.Name;
//             t.activities.get(0).act.Related_Service2__c = mc2.Name;
//             t.activities.get(0).act.Related_Service1_ID__c = mc1.Id;
//             t.activities.get(0).act.Related_Service2_ID__c = mc2.Id;
//             t.activities.get(0).planStartHourText = '9';
//             t.activities.get(0).planStartMinuteText = '00';
//             t.activities.get(0).planEndHourText = '18';
//             t.activities.get(0).planEndMinuteText = '00';
//             t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';
//             t.upsertActIndex = '0';
//             t.save();

//             Date d = date.today();
//             String strDate = d.year() + '/' + d.month() + '/' + d.day();

//             Datetime dt = datetime.now();
//             String strDatetime = '2000/01/01 10:10';

//             System.Test.startTest();

//             //t.activities.get(0).act.OnCall_ID__c = Add_Report.addReportOn(t.report.id, t.report.id, t.activities.get(0).act.id, 'On_Call', '戦略科室分類1 診療科1test', strDate);
//             //t.activities.get(0).act.OnCall_ID__c = Add_Report.addReportOn(t.report.id, t.report.id, t.activities.get(0).act.id, 'On_Call', '戦略科室分類1 診療科1', strDate);
//             //t.activities.get(0).act.OnCall_ID__c = Add_Report.addReportOn(t.activities.get(0).act.OnCall_ID__c, t.report.id, t.activities.get(0).act.id, 'On_Call', '戦略科室分類1 診療科1', strDate);

//             String raId = Add_Report.addReportAc(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Visit_With', '戦略科室分類1 診療科1test', strDate, '結果');
//             raId = Add_Report.addReportAc(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Visit_With', accDep.Id, strDate, '結果');
//             raId = Add_Report.addReportAc(u1.id, t.activities.get(0).act.ReportAccompanied_ID__c, t.report.id, t.activities.get(0).act.id, 'Visit_With', accDep.Id, strDate, '結果');
//             if (raId != 'false') t.activities.get(0).act.ReportAccompanied_ID__c = raId;
//             //            t.activities.get(0).act.ReportAccompanied_ID__c = Add_Report.addReportAc(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Visit_With', '戦略科室分類1 診療科1test', strDate, '結果');
//             //            t.activities.get(0).act.ReportAccompanied_ID__c = Add_Report.addReportAc(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Visit_With', '戦略科室分類1 診療科1', strDate, '結果');
//             //            t.activities.get(0).act.ReportAccompanied_ID__c = Add_Report.addReportAc(u1.id, t.activities.get(0).act.ReportAccompanied_ID__c, t.report.id, t.activities.get(0).act.id, 'Visit_With', '戦略科室分類1 診療科1', strDate, '結果');

//             String csId = Add_Report.addReportEx(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'MarketCondition', '戦略科室分類1 診療科1test', con1.id, strDate, '結果');
//             csId = Add_Report.addReportEx(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'MarketCondition', accDep.Id, con1.id, strDate, '結果');
//             csId = Add_Report.addReportEx(u1.id, t.activities.get(0).act.CityStatus_ID__c, t.report.id, t.activities.get(0).act.id, 'MarketCondition', accDep.Id, con1.id, strDate, '結果');
//             if (csId != 'false') t.activities.get(0).act.CityStatus_ID__c = csId;
//             //            t.activities.get(0).act.CityStatus_ID__c = Add_Report.addReportEx(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'MarketCondition', '戦略科室分類1 診療科1test', strDate, '結果');
//             //            t.activities.get(0).act.CityStatus_ID__c = Add_Report.addReportEx(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'MarketCondition', '戦略科室分類1 診療科1', strDate, '結果');
//             //            t.activities.get(0).act.CityStatus_ID__c = Add_Report.addReportEx(u1.id, t.activities.get(0).act.CityStatus_ID__c, t.report.id, t.activities.get(0).act.id, 'MarketCondition', '戦略科室分類1 診療科1', strDate, '結果');

//             System.Test.stopTest();
//         }
//     }
//     static void test02_02() {
//         init();

//         System.runAs(u1) {
//             XinDailyReportController t = new XinDailyReportController();
//             t.init();

//             t.report_search.Reported_Date__c = date.today();
//             PageReference ref = t.getDailyReport();
//             Test.setCurrentPage(ref);
//             t = new XinDailyReportController();
//             t.init();
//             //          t.getEvent();

//             t.report.Status__c = '申請中';
//             t.report.Reported_Date__c = date.today();
//             t.repoStartHourText = '9';
//             t.repoStartMinuteText = '00';
//             t.repoEndHourText = '18';
//             t.repoEndMinuteText = '00';
//             t.activities.get(0).actStartHourText = '9';
//             t.activities.get(0).actStartMinuteText = '00';
//             t.activities.get(0).actEndHourText = '18';
//             t.activities.get(0).actEndMinuteText = '00';
//             t.activities.get(0).act.Activity_Type2__c = '病院';
//             t.activities.get(0).act.Purpose__c = '目的(計画)';
//             t.activities.get(0).act.Description__c = '結果';
//             t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
//             t.activities.get(0).act.Location__c = accDepName;
//             t.activities.get(0).act.whatid__c = accDep.Id;
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = con1.id;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = con2.id;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = con3.id;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = con4.id;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = con5.id;
//             t.activities.get(0).act.Related_Opportunity1__c = '::引合1';
//             t.activities.get(0).act.Related_Opportunity2__c = '::引合2';
//             t.activities.get(0).act.Related_Opportunity3__c = '::引合3';
//             t.activities.get(0).act.Related_Opportunity4__c = '::引合4';
//             t.activities.get(0).act.Related_Opportunity5__c = '::引合5';
//             t.activities.get(0).act.Related_Opportunity1_ID__c = opp1.Id;
//             t.activities.get(0).act.Related_Opportunity2_ID__c = opp2.Id;
//             t.activities.get(0).act.Related_Opportunity3_ID__c = opp3.Id;
//             t.activities.get(0).act.Related_Opportunity4_ID__c = opp4.Id;
//             t.activities.get(0).act.Related_Opportunity5_ID__c = opp5.Id;
//             t.activities.get(0).act.Related_Service1__c = mc1.Name;
//             t.activities.get(0).act.Related_Service2__c = mc2.Name;
//             t.activities.get(0).act.Related_Service1_ID__c = mc1.Id;
//             t.activities.get(0).act.Related_Service2_ID__c = mc2.Id;
//             t.activities.get(0).planStartHourText = '9';
//             t.activities.get(0).planStartMinuteText = '00';
//             t.activities.get(0).planEndHourText = '18';
//             t.activities.get(0).planEndMinuteText = '00';
//             t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';
//             t.upsertActIndex = '0';
//             t.save();

//             Date d = date.today();
//             String strDate = d.year() + '/' + d.month() + '/' + d.day();

//             Datetime dt = datetime.now();
//             String strDatetime = '2000/01/01 10:10';

//             System.Test.startTest();

//             String cId = Add_Report.addReportEx(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Competitor', '戦略科室分類1 診療科1test', con1.id, strDate, '結果');
//             cId = Add_Report.addReportEx(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Competitor', accDep.Id, con1.id, strDate, '結果');
//             cId = Add_Report.addReportEx(u1.id, t.activities.get(0).act.Conflict_ID__c, t.report.id, t.activities.get(0).act.id, 'Competitor', accDep.Id, con1.id, strDate, '結果');
//             if (cId != 'false') t.activities.get(0).act.Conflict_ID__c = cId;
//         //            t.activities.get(0).act.Conflict_ID__c = Add_Report.addReportEx(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Competitor', '戦略科室分類1 診療科1test', strDate, '結果');
//         //            t.activities.get(0).act.Conflict_ID__c = Add_Report.addReportEx(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Competitor', '戦略科室分類1 診療科1', strDate, '結果');
//         //            t.activities.get(0).act.Conflict_ID__c = Add_Report.addReportEx(u1.id, t.activities.get(0).act.Conflict_ID__c, t.report.id, t.activities.get(0).act.id, 'Competitor', '戦略科室分類1 診療科1', strDate, '結果');

//             t.save();
//             System.Test.stopTest();
//         }
//     }


//     static void test02_03() {
//         init();

//         System.runAs(u1) {
//             XinDailyReportController t = new XinDailyReportController();
//             t.init();

//             t.report_search.Reported_Date__c = date.today();
//             PageReference ref = t.getDailyReport();
//             Test.setCurrentPage(ref);
//             t = new XinDailyReportController();
//             t.init();
//         //          t.getEvent();

//             t.report.Status__c = '申請中';
//             t.report.Reported_Date__c = date.today();
//             t.repoStartHourText = '9';
//             t.repoStartMinuteText = '00';
//             t.repoEndHourText = '18';
//             t.repoEndMinuteText = '00';
//             t.activities.get(0).actStartHourText = '9';
//             t.activities.get(0).actStartMinuteText = '00';
//             t.activities.get(0).actEndHourText = '18';
//             t.activities.get(0).actEndMinuteText = '00';
//             t.activities.get(0).act.Activity_Type2__c = '病院';
//             t.activities.get(0).act.Purpose__c = '目的(計画)';
//             t.activities.get(0).act.Description__c = '結果';
//             t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
//             t.activities.get(0).act.Location__c = accDepName;
//             t.activities.get(0).act.whatid__c = accDep.Id;
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = con1.id;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = con2.id;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = con3.id;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = con4.id;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = con5.id;
//             t.activities.get(0).act.Related_Opportunity1__c = '::引合1';
//             t.activities.get(0).act.Related_Opportunity2__c = '::引合2';
//             t.activities.get(0).act.Related_Opportunity3__c = '::引合3';
//             t.activities.get(0).act.Related_Opportunity4__c = '::引合4';
//             t.activities.get(0).act.Related_Opportunity5__c = '::引合5';
//             t.activities.get(0).act.Related_Opportunity1_ID__c = opp1.Id;
//             t.activities.get(0).act.Related_Opportunity2_ID__c = opp2.Id;
//             t.activities.get(0).act.Related_Opportunity3_ID__c = opp3.Id;
//             t.activities.get(0).act.Related_Opportunity4_ID__c = opp4.Id;
//             t.activities.get(0).act.Related_Opportunity5_ID__c = opp5.Id;
//             t.activities.get(0).act.Related_Service1__c = mc1.Name;
//             t.activities.get(0).act.Related_Service2__c = mc2.Name;
//             t.activities.get(0).act.Related_Service1_ID__c = mc1.Id;
//             t.activities.get(0).act.Related_Service2_ID__c = mc2.Id;
//             t.activities.get(0).planStartHourText = '9';
//             t.activities.get(0).planStartMinuteText = '00';
//             t.activities.get(0).planEndHourText = '18';
//             t.activities.get(0).planEndMinuteText = '00';
//             t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';
//             t.upsertActIndex = '0';
//             t.save();

//             System.Test.startTest();

//             Date d = date.today();
//             String strDate = d.year() + '/' + d.month() + '/' + d.day();

//             Datetime dt = datetime.now();
//             String strDatetime = '2000/01/01 10:10';

//             String imId = Add_Report.addReportIm(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Improvement', '戦略科室分類1 診療科1test', strDate, '結果');
//             imId = Add_Report.addReportIm(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Improvement', accDep.Id, strDate, '結果');
//             imId = Add_Report.addReportIm(u1.id, t.activities.get(0).act.Improvement_ID__c, t.report.id, t.activities.get(0).act.id, 'Improvement', accDep.Id, strDate, '結果');
//             if (imId != 'false') t.activities.get(0).act.Improvement_ID__c = imId;
//         //            t.activities.get(0).act.Improvement_ID__c = Add_Report.addReportIm(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Improvement', '戦略科室分類1 診療科1test', strDate, '結果');
//         //            t.activities.get(0).act.Improvement_ID__c = Add_Report.addReportIm(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Improvement', '戦略科室分類1 診療科1', strDate, '結果');
//         //            t.activities.get(0).act.Improvement_ID__c = Add_Report.addReportIm(u1.id, t.activities.get(0).act.Improvement_ID__c, t.report.id, t.activities.get(0).act.id, 'Improvement', '戦略科室分類1 診療科1', strDate, '結果');

//             String clId = Add_Report.addClaim(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Complaint', '戦略科室分類1 診療科1test', strDate, '結果');
//             clId = Add_Report.addClaim(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Complaint', accDep.Id, strDate, '結果');
//             clId = Add_Report.addClaim(u1.id, t.activities.get(0).act.Claim_ID__c, t.report.id, t.activities.get(0).act.id, 'Complaint', accDep.Id, strDate, '結果');

//             if (clId != 'false') t.activities.get(0).act.Claim_ID__c = clId;
//             //            t.activities.get(0).act.Claim_ID__c = Add_Report.addClaim(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Complaint', '戦略科室分類1 診療科1test', strDate, '結果');
//             //            t.activities.get(0).act.Claim_ID__c = Add_Report.addClaim(u1.id, t.report.id, t.report.id, t.activities.get(0).act.id, 'Complaint', '戦略科室分類1 診療科1', strDate, '結果');
//             //            t.activities.get(0).act.Claim_ID__c = Add_Report.addClaim(u1.id, t.activities.get(0).act.Claim_ID__c, t.report.id, t.activities.get(0).act.id, 'Complaint', '戦略科室分類1 診療科1', strDate, '結果');

//             t.save();
//             System.Test.stopTest();
//         }
//     }

    // @isTest
    // static void test03() {
    //     ControllerUtil.EscapeNFM001Trigger = true;
    //     init();

    //     System.runAs(u1) {
    //         //System.Test.startTest();
    //         init2();
    //         XinDailyReportController t = new XinDailyReportController();
    //         t.init();

    //         t.report_search.Reported_Date__c = date.today();
    //         PageReference ref = t.getDailyReport();
    //         Test.setCurrentPage(ref);
    //         t = new XinDailyReportController();
    //         t.init();
    //     //          t.getEvent();

    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();

    //         t.report.Status__c = '申請中';
    //         t.report.Reported_Date__c = date.today();
    //         t.repoStartHourText = '9';
    //         t.repoStartMinuteText = '00';
    //         t.repoEndHourText = '18';
    //         t.repoEndMinuteText = '00';
    //         t.activities.get(0).actStartHourText = '9';
    //         t.activities.get(0).actStartMinuteText = '00';
    //         t.activities.get(0).actEndHourText = '18';
    //         t.activities.get(0).actEndMinuteText = '00';
    //         t.activities.get(0).act.Activity_Type2__c = '病院';
    //         t.activities.get(0).act.Purpose__c = '目的(計画)';
    //         t.activities.get(0).act.Description__c = '結果';
    //         t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
    //         t.activities.get(0).act.Purpose_Type2__c = '客户咨询对应';
    //         t.activities.get(0).act.Purpose_Type3__c = '学会or展会对应';
    //         t.activities.get(0).act.Purpose_Type4__c = '信息搜集';
    //         t.activities.get(0).act.Purpose_Type5__c = '产品介绍or推广';
    //         t.activities.get(0).act.Location__c = '戦略科室分類1 診療科1';
    //         t.activities.get(0).act.Visitor1__c = '取引先責任者1';
    //         t.activities.get(0).act.Visitor1_ID__c = con1.id;
    //         t.activities.get(0).act.Visitor2__c = '取引先責任者2';
    //         t.activities.get(0).act.Visitor2_ID__c = con2.id;
    //         t.activities.get(0).act.Visitor3__c = '取引先責任者3';
    //         t.activities.get(0).act.Visitor3_ID__c = con3.id;
    //         t.activities.get(0).act.Visitor4__c = '取引先責任者4';
    //         t.activities.get(0).act.Visitor4_ID__c = con4.id;
    //         t.activities.get(0).act.Visitor5__c = '取引先責任者5';
    //         t.activities.get(0).act.Visitor5_ID__c = con5.id;
    //         t.activities.get(0).act.Related_Opportunity1__c = '引合1';
    //         t.activities.get(0).act.Related_Opportunity2__c = '引合2';
    //         t.activities.get(0).act.Related_Opportunity3__c = '引合3';
    //         t.activities.get(0).act.Related_Opportunity4__c = '引合4';
    //         t.activities.get(0).act.Related_Opportunity5__c = '引合5';
    //         t.activities.get(0).act.Related_Service1__c = mc1.Name;
    //         t.activities.get(0).act.Related_Service2__c = mc2.Name;
    //         t.activities.get(0).planStartHourText = '9';
    //         t.activities.get(0).planStartMinuteText = '00';
    //         t.activities.get(0).planEndHourText = '18';
    //         t.activities.get(0).planEndMinuteText = '00';
    //         t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(1).actStartHourText = '9';
    //         t.activities.get(1).actStartMinuteText = '00';
    //         t.activities.get(1).actEndHourText = '18';
    //         t.activities.get(1).actEndMinuteText = '00';
    //         t.activities.get(1).act.Activity_Type2__c = '病院';
    //         t.activities.get(1).act.Purpose__c = '目的(計画)';
    //         t.activities.get(1).act.Description__c = '結果';
    //         t.activities.get(1).act.Purpose_Type__c = 'OPD';
    //         t.activities.get(1).act.Purpose_Type2__c = '询价or维修询价跟进';
    //         t.activities.get(1).act.Purpose_Type3__c = '经销商协助or拜访';
    //         t.activities.get(1).act.Purpose_Type4__c = '合同商谈';
    //         t.activities.get(1).act.Purpose_Type5__c = '参加招标';
    //         t.activities.get(1).act.Location__c = '戦略科室分類1 診療科1';
    //         t.activities.get(1).planStartHourText = '9';
    //         t.activities.get(1).planStartMinuteText = '00';
    //         t.activities.get(1).planEndHourText = '18';
    //         t.activities.get(1).planEndMinuteText = '00';
    //         t.activities.get(1).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(2).actStartHourText = '9';
    //         t.activities.get(2).actStartMinuteText = '00';
    //         t.activities.get(2).actEndHourText = '18';
    //         t.activities.get(2).actEndMinuteText = '00';
    //         t.activities.get(2).act.Activity_Type2__c = '病院';
    //         t.activities.get(2).act.Purpose__c = '目的(計画)';
    //         t.activities.get(2).act.Description__c = '結果';
    //         t.activities.get(2).act.Purpose_Type__c = '签订合同';
    //         t.activities.get(2).act.Purpose_Type2__c = '納品(装机)';
    //         t.activities.get(2).act.Purpose_Type3__c = '送or取设备';
    //         t.activities.get(2).act.Purpose_Type4__c = '送or取文件类资料';
    //         t.activities.get(2).act.Purpose_Type5__c = '跟台';
    //         t.activities.get(2).act.Location__c = '戦略科室分類1 診療科1';
    //         t.activities.get(2).planStartHourText = '9';
    //         t.activities.get(2).planStartMinuteText = '00';
    //         t.activities.get(2).planEndHourText = '18';
    //         t.activities.get(2).planEndMinuteText = '00';
    //         t.activities.get(2).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(3).actStartHourText = '9';
    //         t.activities.get(3).actStartMinuteText = '00';
    //         t.activities.get(3).actEndHourText = '18';
    //         t.activities.get(3).actEndMinuteText = '00';
    //         t.activities.get(3).act.Activity_Type2__c = '病院';
    //         t.activities.get(3).act.Purpose__c = '目的(計画)';
    //         t.activities.get(3).act.Description__c = '結果';
    //         t.activities.get(3).act.Purpose_Type__c = '新品装机使用保养培训';
    //         t.activities.get(3).act.Purpose_Type2__c = 'NTC/TTC';
    //         t.activities.get(3).act.Purpose_Type3__c = '点検';
    //         t.activities.get(3).act.Purpose_Type4__c = '巡回';
    //         t.activities.get(3).act.Purpose_Type5__c = 'ON-CALL';
    //         t.activities.get(3).act.Location__c = '戦略科室分類1 診療科1';
    //         t.activities.get(3).planStartHourText = '9';
    //         t.activities.get(3).planStartMinuteText = '00';
    //         t.activities.get(3).planEndHourText = '18';
    //         t.activities.get(3).planEndMinuteText = '00';
    //         t.activities.get(3).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(4).actStartHourText = '9';
    //         t.activities.get(4).actStartMinuteText = '00';
    //         t.activities.get(4).actEndHourText = '18';
    //         t.activities.get(4).actEndMinuteText = '00';
    //         t.activities.get(4).act.Activity_Type2__c = '病院';
    //         t.activities.get(4).act.Purpose__c = '目的(計画)';
    //         t.activities.get(4).act.Description__c = '結果';
    //         t.activities.get(4).act.Purpose_Type__c = '修理説明';
    //         t.activities.get(4).act.Purpose_Type2__c = '投诉対応(含QIS）';
    //         t.activities.get(4).act.Purpose_Type3__c = '回款';
    //         t.activities.get(4).act.Location__c = '戦略科室分類1 診療科1';
    //         t.activities.get(4).planStartHourText = '9';
    //         t.activities.get(4).planStartMinuteText = '00';
    //         t.activities.get(4).planEndHourText = '18';
    //         t.activities.get(4).planEndMinuteText = '00';
    //         t.activities.get(4).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(5).actStartHourText = '9';
    //         t.activities.get(5).actStartMinuteText = '00';
    //         t.activities.get(5).actEndHourText = '18';
    //         t.activities.get(5).actEndMinuteText = '00';
    //         t.activities.get(5).act.Activity_Type2__c = '販売店';
    //         t.activities.get(5).act.Purpose__c = '目的(計画)';
    //         t.activities.get(5).act.Description__c = '結果';
    //         t.activities.get(5).act.Purpose_Type__c = '会議参加';
    //         t.activities.get(5).act.Purpose_Type2__c = '产品培训';
    //         t.activities.get(5).act.Purpose_Type3__c = '询价进行活动';
    //         t.activities.get(5).act.Purpose_Type4__c = '售后事宜';
    //         t.activities.get(5).act.Purpose_Type5__c = '库存管理';
    //         t.activities.get(5).planStartHourText = '9';
    //         t.activities.get(5).planStartMinuteText = '00';
    //         t.activities.get(5).planEndHourText = '18';
    //         t.activities.get(5).planEndMinuteText = '00';
    //         t.activities.get(5).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(6).actStartHourText = '9';
    //         t.activities.get(6).actStartMinuteText = '00';
    //         t.activities.get(6).actEndHourText = '18';
    //         t.activities.get(6).actEndMinuteText = '00';
    //         t.activities.get(6).act.Activity_Type2__c = '社内活動';
    //         t.activities.get(6).act.Purpose__c = '目的(計画)';
    //         t.activities.get(6).act.Description__c = '結果';
    //         t.activities.get(6).act.Purpose_Type__c = '備品検査';
    //         t.activities.get(6).act.Purpose_Type2__c = '軽修理';
    //         t.activities.get(6).act.Purpose_Type3__c = '会議';
    //         t.activities.get(6).act.Purpose_Type4__c = '培训';
    //         t.activities.get(6).act.Purpose_Type5__c = '顧客訪問対応';
    //         t.activities.get(6).planStartHourText = '9';
    //         t.activities.get(6).planStartMinuteText = '00';
    //         t.activities.get(6).planEndHourText = '18';
    //         t.activities.get(6).planEndMinuteText = '00';
    //         t.activities.get(6).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(7).actStartHourText = '9';
    //         t.activities.get(7).actStartMinuteText = '00';
    //         t.activities.get(7).actEndHourText = '18';
    //         t.activities.get(7).actEndMinuteText = '00';
    //         t.activities.get(7).act.Activity_Type2__c = '社内活動';
    //         t.activities.get(7).act.Purpose__c = '目的(計画)';
    //         t.activities.get(7).act.Description__c = '結果';
    //         t.activities.get(7).act.Purpose_Type__c = '資料作成';
    //         t.activities.get(7).act.Purpose_Type2__c = '电话拜访';
    //         t.activities.get(7).planStartHourText = '9';
    //         t.activities.get(7).planStartMinuteText = '00';
    //         t.activities.get(7).planEndHourText = '18';
    //         t.activities.get(7).planEndMinuteText = '00';
    //         t.activities.get(7).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(8).actStartHourText = '9';
    //         t.activities.get(8).actStartMinuteText = '00';
    //         t.activities.get(8).actEndHourText = '18';
    //         t.activities.get(8).actEndMinuteText = '00';
    //         t.activities.get(8).act.Activity_Type2__c = '社外イベント';
    //         t.activities.get(8).act.Purpose__c = '目的(計画)';
    //         t.activities.get(8).act.Description__c = '結果';
    //         t.activities.get(8).act.Purpose_Type__c = '礼节性拜访';
    //         t.activities.get(8).act.Purpose_Type2__c = '客户咨询对应';
    //         t.activities.get(8).act.Purpose_Type3__c = '学会or展会对应';
    //         t.activities.get(8).act.Purpose_Type4__c = '信息搜集';
    //         t.activities.get(8).act.Purpose_Type5__c = '产品介绍or推广';
    //         t.activities.get(8).planStartHourText = '9';
    //         t.activities.get(8).planStartMinuteText = '00';
    //         t.activities.get(8).planEndHourText = '18';
    //         t.activities.get(8).planEndMinuteText = '00';
    //         t.activities.get(8).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(9).actStartHourText = '9';
    //         t.activities.get(9).actStartMinuteText = '00';
    //         t.activities.get(9).actEndHourText = '18';
    //         t.activities.get(9).actEndMinuteText = '00';
    //         t.activities.get(9).act.Activity_Type2__c = '移動';
    //         t.activities.get(9).act.Purpose__c = '目的(計画)';
    //         t.activities.get(9).act.Description__c = '結果';
    //         t.activities.get(9).act.Purpose_Type__c = '移動';
    //         t.activities.get(9).planStartHourText = '9';
    //         t.activities.get(9).planStartMinuteText = '00';
    //         t.activities.get(9).planEndHourText = '18';
    //         t.activities.get(9).planEndMinuteText = '00';
    //         t.activities.get(9).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(10).actStartHourText = '9';
    //         t.activities.get(10).actStartMinuteText = '00';
    //         t.activities.get(10).actEndHourText = '18';
    //         t.activities.get(10).actEndMinuteText = '00';
    //         t.activities.get(10).act.Activity_Type2__c = '休暇';
    //         t.activities.get(10).act.Purpose__c = '目的(計画)';
    //         t.activities.get(10).act.Description__c = '結果';
    //         t.activities.get(10).act.Purpose_Type__c = '休暇';
    //         t.activities.get(10).planStartHourText = '9';
    //         t.activities.get(10).planStartMinuteText = '00';
    //         t.activities.get(10).planEndHourText = '18';
    //         t.activities.get(10).planEndMinuteText = '00';
    //         t.activities.get(10).planPurposeText = '次の活動予定目的(計画)';
    //         t.upsertActIndex = '0';
    //         t.openPDNew();

    //         t.upsertActIndex = '';
    //         //t.save();

    //         String actId = t.activities.get(0).act.id;

    //         Event e = new Event();
    //         e.ActivityDateTime = datetime.now();
    //         e.DurationInMinutes = 0;
    //         e.OwnerId = loginId;
    //         insert e;

    //         PageReference pageRef1 = Page.XinDailyReport;
    //         pageRef1.getParameters().put('id', e.id);
    //         Test.setCurrentPage(pageRef1);
    //         t.init();

    //         PageReference pageRef2 = Page.XinDailyReport;
    //         pageRef2.getParameters().put('id', actId);
    //         Test.setCurrentPage(pageRef2);
    //         t.init();

    //         t.report_search.Reported_Date__c = date.today();
    //         t.getDailyReport();
    //     //          t.getEvent();
    //         //System.Test.stopTest();
    //     }
    // }

    //@isTest
    // static void test04() {
    //     ControllerUtil.EscapeNFM001Trigger = true;
    //     init();

    //     System.runAs(u2) {
    //         //System.Test.startTest();
    //         init2();
    //         XinDailyReportController t = new XinDailyReportController();
    //         t.init();

    //         t.report_search.Reported_Date__c = date.today();
    //         PageReference ref = t.getDailyReport();
    //         Test.setCurrentPage(ref);
    //         t = new XinDailyReportController();
    //         t.init();
    //     //          t.getEvent();

    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();

    //         t.report.Status__c = '申請中';
    //         t.report.Reported_Date__c = date.today();
    //         t.repoStartHourText = '9';
    //         t.repoStartMinuteText = '00';
    //         t.repoEndHourText = '18';
    //         t.repoEndMinuteText = '00';
    //         t.activities.get(0).actStartHourText = '9';
    //         t.activities.get(0).actStartMinuteText = '00';
    //         t.activities.get(0).actEndHourText = '18';
    //         t.activities.get(0).actEndMinuteText = '00';
    //         t.activities.get(0).act.Activity_Type2__c = '病院';
    //         t.activities.get(0).act.Purpose__c = '目的(計画)';
    //         t.activities.get(0).act.Description__c = '結果';
    //         t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
    //         t.activities.get(0).act.Purpose_Type2__c = '客户咨询对应';
    //         t.activities.get(0).act.Purpose_Type3__c = '学会or展会对应';
    //         t.activities.get(0).act.Purpose_Type4__c = '信息搜集';
    //         t.activities.get(0).act.Purpose_Type5__c = '产品介绍or推广';
    //         t.activities.get(0).act.Location__c = '戦略科室分類1 診療科1';
    //         t.activities.get(0).act.Visitor1__c = '取引先責任者1';
    //         t.activities.get(0).act.Visitor1_ID__c = con1.id;
    //         t.activities.get(0).act.Visitor2__c = '取引先責任者2';
    //         t.activities.get(0).act.Visitor2_ID__c = con2.id;
    //         t.activities.get(0).act.Visitor3__c = '取引先責任者3';
    //         t.activities.get(0).act.Visitor3_ID__c = con3.id;
    //         t.activities.get(0).act.Visitor4__c = '取引先責任者4';
    //         t.activities.get(0).act.Visitor4_ID__c = con4.id;
    //         t.activities.get(0).act.Visitor5__c = '取引先責任者5';
    //         t.activities.get(0).act.Visitor5_ID__c = con5.id;
    //         t.activities.get(0).act.Related_Opportunity1__c = '引合1';
    //         t.activities.get(0).act.Related_Opportunity2__c = '引合2';
    //         t.activities.get(0).act.Related_Opportunity3__c = '引合3';
    //         t.activities.get(0).act.Related_Opportunity4__c = '引合4';
    //         t.activities.get(0).act.Related_Opportunity5__c = '引合5';
    //         t.activities.get(0).act.Related_Service1__c = mc1.Name;
    //         t.activities.get(0).act.Related_Service2__c = mc2.Name;
    //         t.activities.get(0).planStartHourText = '9';
    //         t.activities.get(0).planStartMinuteText = '00';
    //         t.activities.get(0).planEndHourText = '18';
    //         t.activities.get(0).planEndMinuteText = '00';
    //         t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(1).actStartHourText = '9';
    //         t.activities.get(1).actStartMinuteText = '00';
    //         t.activities.get(1).actEndHourText = '18';
    //         t.activities.get(1).actEndMinuteText = '00';
    //         t.activities.get(1).act.Activity_Type2__c = '病院';
    //         t.activities.get(1).act.Purpose__c = '目的(計画)';
    //         t.activities.get(1).act.Description__c = '結果';
    //         t.activities.get(1).act.Purpose_Type__c = 'OPD';
    //         t.activities.get(1).act.Purpose_Type2__c = '询价or维修询价跟进';
    //         t.activities.get(1).act.Purpose_Type3__c = '经销商协助or拜访';
    //         t.activities.get(1).act.Purpose_Type4__c = '合同商谈';
    //         t.activities.get(1).act.Purpose_Type5__c = '参加招标';
    //         t.activities.get(1).act.Location__c = '戦略科室分類1 診療科1';
    //         t.activities.get(1).planStartHourText = '9';
    //         t.activities.get(1).planStartMinuteText = '00';
    //         t.activities.get(1).planEndHourText = '18';
    //         t.activities.get(1).planEndMinuteText = '00';
    //         t.activities.get(1).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(2).actStartHourText = '9';
    //         t.activities.get(2).actStartMinuteText = '00';
    //         t.activities.get(2).actEndHourText = '18';
    //         t.activities.get(2).actEndMinuteText = '00';
    //         t.activities.get(2).act.Activity_Type2__c = '病院';
    //         t.activities.get(2).act.Purpose__c = '目的(計画)';
    //         t.activities.get(2).act.Description__c = '結果';
    //         t.activities.get(2).act.Purpose_Type__c = '签订合同';
    //         t.activities.get(2).act.Purpose_Type2__c = '納品(装机)';
    //         t.activities.get(2).act.Purpose_Type3__c = '送or取设备';
    //         t.activities.get(2).act.Purpose_Type4__c = '送or取文件类资料';
    //         t.activities.get(2).act.Purpose_Type5__c = '跟台';
    //         t.activities.get(2).act.Location__c = '戦略科室分類1 診療科1';
    //         t.activities.get(2).planStartHourText = '9';
    //         t.activities.get(2).planStartMinuteText = '00';
    //         t.activities.get(2).planEndHourText = '18';
    //         t.activities.get(2).planEndMinuteText = '00';
    //         t.activities.get(2).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(3).actStartHourText = '9';
    //         t.activities.get(3).actStartMinuteText = '00';
    //         t.activities.get(3).actEndHourText = '18';
    //         t.activities.get(3).actEndMinuteText = '00';
    //         t.activities.get(3).act.Activity_Type2__c = '病院';
    //         t.activities.get(3).act.Purpose__c = '目的(計画)';
    //         t.activities.get(3).act.Description__c = '結果';
    //         t.activities.get(3).act.Purpose_Type__c = '新品装机使用保养培训';
    //         t.activities.get(3).act.Purpose_Type2__c = 'NTC/TTC';
    //         t.activities.get(3).act.Purpose_Type3__c = '点検';
    //         t.activities.get(3).act.Purpose_Type4__c = '巡回';
    //         t.activities.get(3).act.Purpose_Type5__c = 'ON-CALL';
    //         t.activities.get(3).act.Location__c = '戦略科室分類1 診療科1';
    //         t.activities.get(3).planStartHourText = '9';
    //         t.activities.get(3).planStartMinuteText = '00';
    //         t.activities.get(3).planEndHourText = '18';
    //         t.activities.get(3).planEndMinuteText = '00';
    //         t.activities.get(3).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(4).actStartHourText = '9';
    //         t.activities.get(4).actStartMinuteText = '00';
    //         t.activities.get(4).actEndHourText = '18';
    //         t.activities.get(4).actEndMinuteText = '00';
    //         t.activities.get(4).act.Activity_Type2__c = '病院';
    //         t.activities.get(4).act.Purpose__c = '目的(計画)';
    //         t.activities.get(4).act.Description__c = '結果';
    //         t.activities.get(4).act.Purpose_Type__c = '修理説明';
    //         t.activities.get(4).act.Purpose_Type2__c = '投诉対応(含QIS）';
    //         t.activities.get(4).act.Purpose_Type3__c = '回款';
    //         t.activities.get(4).act.Location__c = '戦略科室分類1 診療科1';
    //         t.activities.get(4).planStartHourText = '9';
    //         t.activities.get(4).planStartMinuteText = '00';
    //         t.activities.get(4).planEndHourText = '18';
    //         t.activities.get(4).planEndMinuteText = '00';
    //         t.activities.get(4).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(5).actStartHourText = '9';
    //         t.activities.get(5).actStartMinuteText = '00';
    //         t.activities.get(5).actEndHourText = '18';
    //         t.activities.get(5).actEndMinuteText = '00';
    //         t.activities.get(5).act.Activity_Type2__c = '販売店';
    //         t.activities.get(5).act.Purpose__c = '目的(計画)';
    //         t.activities.get(5).act.Description__c = '結果';
    //         t.activities.get(5).act.Purpose_Type__c = '会議参加';
    //         t.activities.get(5).act.Purpose_Type2__c = '产品培训';
    //         t.activities.get(5).act.Purpose_Type3__c = '询价进行活动';
    //         t.activities.get(5).act.Purpose_Type4__c = '售后事宜';
    //         t.activities.get(5).act.Purpose_Type5__c = '库存管理';
    //         t.activities.get(5).planStartHourText = '9';
    //         t.activities.get(5).planStartMinuteText = '00';
    //         t.activities.get(5).planEndHourText = '18';
    //         t.activities.get(5).planEndMinuteText = '00';
    //         t.activities.get(5).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(6).actStartHourText = '9';
    //         t.activities.get(6).actStartMinuteText = '00';
    //         t.activities.get(6).actEndHourText = '18';
    //         t.activities.get(6).actEndMinuteText = '00';
    //         t.activities.get(6).act.Activity_Type2__c = '社内活動';
    //         t.activities.get(6).act.Purpose__c = '目的(計画)';
    //         t.activities.get(6).act.Description__c = '結果';
    //         t.activities.get(6).act.Purpose_Type__c = '備品検査';
    //         t.activities.get(6).act.Purpose_Type2__c = '軽修理';
    //         t.activities.get(6).act.Purpose_Type3__c = '会議';
    //         t.activities.get(6).act.Purpose_Type4__c = '培训';
    //         t.activities.get(6).act.Purpose_Type5__c = '顧客訪問対応';
    //         t.activities.get(6).planStartHourText = '9';
    //         t.activities.get(6).planStartMinuteText = '00';
    //         t.activities.get(6).planEndHourText = '18';
    //         t.activities.get(6).planEndMinuteText = '00';
    //         t.activities.get(6).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(7).actStartHourText = '9';
    //         t.activities.get(7).actStartMinuteText = '00';
    //         t.activities.get(7).actEndHourText = '18';
    //         t.activities.get(7).actEndMinuteText = '00';
    //         t.activities.get(7).act.Activity_Type2__c = '社内活動';
    //         t.activities.get(7).act.Purpose__c = '目的(計画)';
    //         t.activities.get(7).act.Description__c = '結果';
    //         t.activities.get(7).act.Purpose_Type__c = '資料作成';
    //         t.activities.get(7).act.Purpose_Type2__c = '电话拜访';
    //         t.activities.get(7).planStartHourText = '9';
    //         t.activities.get(7).planStartMinuteText = '00';
    //         t.activities.get(7).planEndHourText = '18';
    //         t.activities.get(7).planEndMinuteText = '00';
    //         t.activities.get(7).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(8).actStartHourText = '9';
    //         t.activities.get(8).actStartMinuteText = '00';
    //         t.activities.get(8).actEndHourText = '18';
    //         t.activities.get(8).actEndMinuteText = '00';
    //         t.activities.get(8).act.Activity_Type2__c = '社外イベント';
    //         t.activities.get(8).act.Purpose__c = '目的(計画)';
    //         t.activities.get(8).act.Description__c = '結果';
    //         t.activities.get(8).act.Purpose_Type__c = '礼节性拜访';
    //         t.activities.get(8).act.Purpose_Type2__c = '客户咨询对应';
    //         t.activities.get(8).act.Purpose_Type3__c = '学会or展会对应';
    //         t.activities.get(8).act.Purpose_Type4__c = '信息搜集';
    //         t.activities.get(8).act.Purpose_Type5__c = '产品介绍or推广';
    //         t.activities.get(8).planStartHourText = '9';
    //         t.activities.get(8).planStartMinuteText = '00';
    //         t.activities.get(8).planEndHourText = '18';
    //         t.activities.get(8).planEndMinuteText = '00';
    //         t.activities.get(8).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(9).actStartHourText = '9';
    //         t.activities.get(9).actStartMinuteText = '00';
    //         t.activities.get(9).actEndHourText = '18';
    //         t.activities.get(9).actEndMinuteText = '00';
    //         t.activities.get(9).act.Activity_Type2__c = '移動';
    //         t.activities.get(9).act.Purpose__c = '目的(計画)';
    //         t.activities.get(9).act.Description__c = '結果';
    //         t.activities.get(9).act.Purpose_Type__c = '移動';
    //         t.activities.get(9).planStartHourText = '9';
    //         t.activities.get(9).planStartMinuteText = '00';
    //         t.activities.get(9).planEndHourText = '18';
    //         t.activities.get(9).planEndMinuteText = '00';
    //         t.activities.get(9).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(10).actStartHourText = '9';
    //         t.activities.get(10).actStartMinuteText = '00';
    //         t.activities.get(10).actEndHourText = '18';
    //         t.activities.get(10).actEndMinuteText = '00';
    //         t.activities.get(10).act.Activity_Type2__c = '休暇';
    //         t.activities.get(10).act.Purpose__c = '目的(計画)';
    //         t.activities.get(10).act.Description__c = '結果';
    //         t.activities.get(10).act.Purpose_Type__c = '休暇';
    //         t.activities.get(10).planStartHourText = '9';
    //         t.activities.get(10).planStartMinuteText = '00';
    //         t.activities.get(10).planEndHourText = '18';
    //         t.activities.get(10).planEndMinuteText = '00';
    //         t.activities.get(10).planPurposeText = '次の活動予定目的(計画)';
    //         t.upsertActIndex = '0';
    //         t.openPDNew();

    //         t.upsertActIndex = '';
    //         t.save();

    //         String actId = t.activities.get(0).act.id;

    //         Event e = new Event();
    //         e.ActivityDateTime = datetime.now();
    //         e.DurationInMinutes = 0;
    //         e.OwnerId = loginId;
    //         insert e;

    //         PageReference pageRef1 = Page.XinDailyReport;
    //         pageRef1.getParameters().put('id', e.id);
    //         Test.setCurrentPage(pageRef1);
    //         t.init();

    //         PageReference pageRef2 = Page.XinDailyReport;
    //         pageRef2.getParameters().put('id', actId);
    //         Test.setCurrentPage(pageRef2);
    //         t.init();

    //         t.report_search.Reported_Date__c = date.today();
    //         t.getDailyReport();
    //     //          t.getEvent();
    //         //System.Test.stopTest();
    //     }
    // }

    // @isTest
    // static void test05() {
    //     ControllerUtil.EscapeNFM001Trigger = true;
    //     init();

    //     System.runAs(u3) {
    //         //System.Test.startTest();
    //         init2();
    //         XinDailyReportController t = new XinDailyReportController();
    //         t.init();

    //         t.report_search.Reported_Date__c = date.today();
    //         PageReference ref = t.getDailyReport();
    //         Test.setCurrentPage(ref);
    //         t = new XinDailyReportController();
    //         t.init();
    //         //          t.getEvent();

    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();

    //         t.report.Status__c = '申請中';
    //         t.report.Reported_Date__c = date.today();
    //         t.repoStartHourText = '9';
    //         t.repoStartMinuteText = '00';
    //         t.repoEndHourText = '18';
    //         t.repoEndMinuteText = '00';
    //         t.activities.get(0).actStartHourText = '9';
    //         t.activities.get(0).actStartMinuteText = '00';
    //         t.activities.get(0).actEndHourText = '18';
    //         t.activities.get(0).actEndMinuteText = '00';
    //         t.activities.get(0).act.Activity_Type2__c = '病院';
    //         t.activities.get(0).act.Purpose__c = '目的(計画)';
    //         t.activities.get(0).act.Description__c = '結果';
    //         t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
    //         t.activities.get(0).act.Purpose_Type2__c = '客户咨询对应';
    //         t.activities.get(0).act.Purpose_Type3__c = '学会or展会对应';
    //         t.activities.get(0).act.Purpose_Type4__c = '信息搜集';
    //         t.activities.get(0).act.Purpose_Type5__c = '产品介绍or推广';
    //         t.activities.get(0).act.Location__c = '戦略科室分類1 診療科1';
    //         t.activities.get(0).act.Visitor1__c = '取引先責任者1';
    //         t.activities.get(0).act.Visitor1_ID__c = con1.id;
    //         t.activities.get(0).act.Visitor2__c = '取引先責任者2';
    //         t.activities.get(0).act.Visitor2_ID__c = con2.id;
    //         t.activities.get(0).act.Visitor3__c = '取引先責任者3';
    //         t.activities.get(0).act.Visitor3_ID__c = con3.id;
    //         t.activities.get(0).act.Visitor4__c = '取引先責任者4';
    //         t.activities.get(0).act.Visitor4_ID__c = con4.id;
    //         t.activities.get(0).act.Visitor5__c = '取引先責任者5';
    //         t.activities.get(0).act.Visitor5_ID__c = con5.id;
    //         t.activities.get(0).act.Related_Opportunity1__c = '引合1';
    //         t.activities.get(0).act.Related_Opportunity2__c = '引合2';
    //         t.activities.get(0).act.Related_Opportunity3__c = '引合3';
    //         t.activities.get(0).act.Related_Opportunity4__c = '引合4';
    //         t.activities.get(0).act.Related_Opportunity5__c = '引合5';
    //         t.activities.get(0).act.Related_Service1__c = mc1.Name;
    //         t.activities.get(0).act.Related_Service2__c = mc2.Name;
    //         t.activities.get(0).planStartHourText = '9';
    //         t.activities.get(0).planStartMinuteText = '00';
    //         t.activities.get(0).planEndHourText = '18';
    //         t.activities.get(0).planEndMinuteText = '00';
    //         t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(1).actStartHourText = '9';
    //         t.activities.get(1).actStartMinuteText = '00';
    //         t.activities.get(1).actEndHourText = '18';
    //         t.activities.get(1).actEndMinuteText = '00';
    //         t.activities.get(1).act.Activity_Type2__c = '病院';
    //         t.activities.get(1).act.Purpose__c = '目的(計画)';
    //         t.activities.get(1).act.Description__c = '結果';
    //         t.activities.get(1).act.Purpose_Type__c = 'OPD';
    //         t.activities.get(1).act.Purpose_Type2__c = '询价or维修询价跟进';
    //         t.activities.get(1).act.Purpose_Type3__c = '经销商协助or拜访';
    //         t.activities.get(1).act.Purpose_Type4__c = '合同商谈';
    //         t.activities.get(1).act.Purpose_Type5__c = '参加招标';
    //         t.activities.get(1).act.Location__c = '戦略科室分類1 診療科1';
    //         t.activities.get(1).planStartHourText = '9';
    //         t.activities.get(1).planStartMinuteText = '00';
    //         t.activities.get(1).planEndHourText = '18';
    //         t.activities.get(1).planEndMinuteText = '00';
    //         t.activities.get(1).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(2).actStartHourText = '9';
    //         t.activities.get(2).actStartMinuteText = '00';
    //         t.activities.get(2).actEndHourText = '18';
    //         t.activities.get(2).actEndMinuteText = '00';
    //         t.activities.get(2).act.Activity_Type2__c = '病院';
    //         t.activities.get(2).act.Purpose__c = '目的(計画)';
    //         t.activities.get(2).act.Description__c = '結果';
    //         t.activities.get(2).act.Purpose_Type__c = '签订合同';
    //         t.activities.get(2).act.Purpose_Type2__c = '納品(装机)';
    //         t.activities.get(2).act.Purpose_Type3__c = '送or取设备';
    //         t.activities.get(2).act.Purpose_Type4__c = '送or取文件类资料';
    //         t.activities.get(2).act.Purpose_Type5__c = '跟台';
    //         t.activities.get(2).act.Location__c = '戦略科室分類1 診療科1';
    //         t.activities.get(2).planStartHourText = '9';
    //         t.activities.get(2).planStartMinuteText = '00';
    //         t.activities.get(2).planEndHourText = '18';
    //         t.activities.get(2).planEndMinuteText = '00';
    //         t.activities.get(2).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(3).actStartHourText = '9';
    //         t.activities.get(3).actStartMinuteText = '00';
    //         t.activities.get(3).actEndHourText = '18';
    //         t.activities.get(3).actEndMinuteText = '00';
    //         t.activities.get(3).act.Activity_Type2__c = '病院';
    //         t.activities.get(3).act.Purpose__c = '目的(計画)';
    //         t.activities.get(3).act.Description__c = '結果';
    //         t.activities.get(3).act.Purpose_Type__c = '新品装机使用保养培训';
    //         t.activities.get(3).act.Purpose_Type2__c = 'NTC/TTC';
    //         t.activities.get(3).act.Purpose_Type3__c = '点検';
    //         t.activities.get(3).act.Purpose_Type4__c = '巡回';
    //         t.activities.get(3).act.Purpose_Type5__c = 'ON-CALL';
    //         t.activities.get(3).act.Location__c = '戦略科室分類1 診療科1';
    //         t.activities.get(3).planStartHourText = '9';
    //         t.activities.get(3).planStartMinuteText = '00';
    //         t.activities.get(3).planEndHourText = '18';
    //         t.activities.get(3).planEndMinuteText = '00';
    //         t.activities.get(3).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(4).actStartHourText = '9';
    //         t.activities.get(4).actStartMinuteText = '00';
    //         t.activities.get(4).actEndHourText = '18';
    //         t.activities.get(4).actEndMinuteText = '00';
    //         t.activities.get(4).act.Activity_Type2__c = '病院';
    //         t.activities.get(4).act.Purpose__c = '目的(計画)';
    //         t.activities.get(4).act.Description__c = '結果';
    //         t.activities.get(4).act.Purpose_Type__c = '修理説明';
    //         t.activities.get(4).act.Purpose_Type2__c = '投诉対応(含QIS）';
    //         t.activities.get(4).act.Purpose_Type3__c = '回款';
    //         t.activities.get(4).act.Location__c = '戦略科室分類1 診療科1';
    //         t.activities.get(4).planStartHourText = '9';
    //         t.activities.get(4).planStartMinuteText = '00';
    //         t.activities.get(4).planEndHourText = '18';
    //         t.activities.get(4).planEndMinuteText = '00';
    //         t.activities.get(4).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(5).actStartHourText = '9';
    //         t.activities.get(5).actStartMinuteText = '00';
    //         t.activities.get(5).actEndHourText = '18';
    //         t.activities.get(5).actEndMinuteText = '00';
    //         t.activities.get(5).act.Activity_Type2__c = '販売店';
    //         t.activities.get(5).act.Purpose__c = '目的(計画)';
    //         t.activities.get(5).act.Description__c = '結果';
    //         t.activities.get(5).act.Purpose_Type__c = '会議参加';
    //         t.activities.get(5).act.Purpose_Type2__c = '产品培训';
    //         t.activities.get(5).act.Purpose_Type3__c = '询价进行活动';
    //         t.activities.get(5).act.Purpose_Type4__c = '售后事宜';
    //         t.activities.get(5).act.Purpose_Type5__c = '库存管理';
    //         t.activities.get(5).planStartHourText = '9';
    //         t.activities.get(5).planStartMinuteText = '00';
    //         t.activities.get(5).planEndHourText = '18';
    //         t.activities.get(5).planEndMinuteText = '00';
    //         t.activities.get(5).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(6).actStartHourText = '9';
    //         t.activities.get(6).actStartMinuteText = '00';
    //         t.activities.get(6).actEndHourText = '18';
    //         t.activities.get(6).actEndMinuteText = '00';
    //         t.activities.get(6).act.Activity_Type2__c = '社内活動';
    //         t.activities.get(6).act.Purpose__c = '目的(計画)';
    //         t.activities.get(6).act.Description__c = '結果';
    //         t.activities.get(6).act.Purpose_Type__c = '備品検査';
    //         t.activities.get(6).act.Purpose_Type2__c = '軽修理';
    //         t.activities.get(6).act.Purpose_Type3__c = '会議';
    //         t.activities.get(6).act.Purpose_Type4__c = '培训';
    //         t.activities.get(6).act.Purpose_Type5__c = '顧客訪問対応';
    //         t.activities.get(6).planStartHourText = '9';
    //         t.activities.get(6).planStartMinuteText = '00';
    //         t.activities.get(6).planEndHourText = '18';
    //         t.activities.get(6).planEndMinuteText = '00';
    //         t.activities.get(6).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(7).actStartHourText = '9';
    //         t.activities.get(7).actStartMinuteText = '00';
    //         t.activities.get(7).actEndHourText = '18';
    //         t.activities.get(7).actEndMinuteText = '00';
    //         t.activities.get(7).act.Activity_Type2__c = '社内活動';
    //         t.activities.get(7).act.Purpose__c = '目的(計画)';
    //         t.activities.get(7).act.Description__c = '結果';
    //         t.activities.get(7).act.Purpose_Type__c = '資料作成';
    //         t.activities.get(7).act.Purpose_Type2__c = '电话拜访';
    //         t.activities.get(7).planStartHourText = '9';
    //         t.activities.get(7).planStartMinuteText = '00';
    //         t.activities.get(7).planEndHourText = '18';
    //         t.activities.get(7).planEndMinuteText = '00';
    //         t.activities.get(7).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(8).actStartHourText = '9';
    //         t.activities.get(8).actStartMinuteText = '00';
    //         t.activities.get(8).actEndHourText = '18';
    //         t.activities.get(8).actEndMinuteText = '00';
    //         t.activities.get(8).act.Activity_Type2__c = '社外イベント';
    //         t.activities.get(8).act.Purpose__c = '目的(計画)';
    //         t.activities.get(8).act.Description__c = '結果';
    //         t.activities.get(8).act.Purpose_Type__c = '礼节性拜访';
    //         t.activities.get(8).act.Purpose_Type2__c = '客户咨询对应';
    //         t.activities.get(8).act.Purpose_Type3__c = '学会or展会对应';
    //         t.activities.get(8).act.Purpose_Type4__c = '信息搜集';
    //         t.activities.get(8).act.Purpose_Type5__c = '产品介绍or推广';
    //         t.activities.get(8).planStartHourText = '9';
    //         t.activities.get(8).planStartMinuteText = '00';
    //         t.activities.get(8).planEndHourText = '18';
    //         t.activities.get(8).planEndMinuteText = '00';
    //         t.activities.get(8).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(9).actStartHourText = '9';
    //         t.activities.get(9).actStartMinuteText = '00';
    //         t.activities.get(9).actEndHourText = '18';
    //         t.activities.get(9).actEndMinuteText = '00';
    //         t.activities.get(9).act.Activity_Type2__c = '移動';
    //         t.activities.get(9).act.Purpose__c = '目的(計画)';
    //         t.activities.get(9).act.Description__c = '結果';
    //         t.activities.get(9).act.Purpose_Type__c = '移動';
    //         t.activities.get(9).planStartHourText = '9';
    //         t.activities.get(9).planStartMinuteText = '00';
    //         t.activities.get(9).planEndHourText = '18';
    //         t.activities.get(9).planEndMinuteText = '00';
    //         t.activities.get(9).planPurposeText = '次の活動予定目的(計画)';

    //         t.activities.get(10).actStartHourText = '9';
    //         t.activities.get(10).actStartMinuteText = '00';
    //         t.activities.get(10).actEndHourText = '18';
    //         t.activities.get(10).actEndMinuteText = '00';
    //         t.activities.get(10).act.Activity_Type2__c = '休暇';
    //         t.activities.get(10).act.Purpose__c = '目的(計画)';
    //         t.activities.get(10).act.Description__c = '結果';
    //         t.activities.get(10).act.Purpose_Type__c = '休暇';
    //         t.activities.get(10).planStartHourText = '9';
    //         t.activities.get(10).planStartMinuteText = '00';
    //         t.activities.get(10).planEndHourText = '18';
    //         t.activities.get(10).planEndMinuteText = '00';
    //         t.activities.get(10).planPurposeText = '次の活動予定目的(計画)';
    //         t.upsertActIndex = '0';
    //         t.openPDNew();

    //         t.upsertActIndex = '';
    //         t.save();

    //         String actId = t.activities.get(0).act.id;

    //         Event e = new Event();
    //         e.ActivityDateTime = datetime.now();
    //         e.DurationInMinutes = 0;
    //         e.OwnerId = loginId;
    //         insert e;

    //         PageReference pageRef1 = Page.XinDailyReport;
    //         pageRef1.getParameters().put('id', e.id);
    //         Test.setCurrentPage(pageRef1);
    //         t.init();

    //         PageReference pageRef2 = Page.XinDailyReport;
    //         pageRef2.getParameters().put('id', actId);
    //         Test.setCurrentPage(pageRef2);
    //         t.init();

    //         t.report_search.Reported_Date__c = date.today();
    //         t.getDailyReport();
    //     //          t.getEvent();
    //         //System.Test.stopTest();
    //     }
    // }

    // @isTest
    // static void test06() {
    //     ControllerUtil.EscapeNFM001Trigger = true;
    //     init();

    //     System.runAs(u1) {
    //         //System.Test.startTest();
    //         init2();
    //         XinDailyReportController t = new XinDailyReportController();

    //         Event e = new Event();
    //         e.ActivityDateTime = datetime.now();
    //         e.DurationInMinutes = 0;
    //         e.OwnerId = u1.id;
    //         insert e;

    //         PageReference pageRef1 = Page.XinDailyReport;
    //         pageRef1.getParameters().put('id', e.id);
    //         Test.setCurrentPage(pageRef1);
    //         t.init();

    //         //System.Test.stopTest();
    //     }
    // }

    // @isTest
    // static void test07_01() {
    //     ControllerUtil.EscapeNFM001Trigger = true;
    //     init();
    //     //System.Test.startTest();
    //     XinDailyReportController t = new XinDailyReportController();
    //     t.init();
    //     System.runAs(u1) {
    //         init2();
    //         t.report_search.Reported_Date__c = date.today();
    //         PageReference ref = t.getDailyReport();
    //         Test.setCurrentPage(ref);
    //         t = new XinDailyReportController();
    //         t.init();
    //     //          t.getEvent();

    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();
    //         t.addActivity();

    //         t.report.Status__c = '申請中';
    //         t.report.Reported_Date__c = date.today();
    //         t.repoStartHourText = '9';
    //         t.repoStartMinuteText = '00';
    //         t.repoEndHourText = '18';
    //         t.repoEndMinuteText = '00';
    //         t.activities.get(0).actStartHourText = '9';
    //         t.activities.get(0).actStartMinuteText = '00';
    //         t.activities.get(0).actEndHourText = '18';
    //         t.activities.get(0).actEndMinuteText = '00';
    //         t.activities.get(0).act.Activity_Type2__c = '病院';
    //         t.activities.get(0).act.Purpose__c = '目的(計画)';
    //         t.activities.get(0).act.Description__c = '結果';
    //         t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
    //         t.activities.get(0).act.Purpose_Type2__c = '客户咨询对应';
    //         t.activities.get(0).act.Purpose_Type3__c = '学会or展会对应';
    //         t.activities.get(0).act.Purpose_Type4__c = '信息搜集';
    //         t.activities.get(0).act.Purpose_Type5__c = '产品介绍or推广';
    //         t.activities.get(0).act.Location__c = '戦略科室分類1 診療科1';
    //         t.activities.get(0).act.Visitor1__c = '取引先責任者1';
    //         t.activities.get(0).act.Visitor1_ID__c = con1.id;
    //         t.activities.get(0).act.Visitor2__c = '取引先責任者2';
    //         t.activities.get(0).act.Visitor2_ID__c = con2.id;
    //         t.activities.get(0).act.Visitor3__c = '取引先責任者3';
    //         t.activities.get(0).act.Visitor3_ID__c = con3.id;
    //         t.activities.get(0).act.Visitor4__c = '取引先責任者4';
    //         t.activities.get(0).act.Visitor4_ID__c = con4.id;
    //         t.activities.get(0).act.Visitor5__c = '取引先責任者5';
    //         t.activities.get(0).act.Visitor5_ID__c = con5.id;
    //         t.activities.get(0).act.Related_Opportunity1__c = '引合1';
    //         t.activities.get(0).act.Related_Opportunity2__c = '引合2';
    //         t.activities.get(0).act.Related_Opportunity3__c = '引合3';
    //         t.activities.get(0).act.Related_Opportunity4__c = '引合4';
    //         t.activities.get(0).act.Related_Opportunity5__c = '引合5';
    //         t.activities.get(0).act.Related_Service1__c = mc1.Name;
    //         t.activities.get(0).act.Related_Service2__c = mc2.Name;
    //         t.activities.get(0).planStartHourText = '9';
    //         t.activities.get(0).planStartMinuteText = '00';
    //         t.activities.get(0).planEndHourText = '18';
    //         t.activities.get(0).planEndMinuteText = '00';
    //         t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';
    //         t.activities.get(0).act.Free_Input__c = true;
    //         t.activities.get(0).act.Find_Imitations_Flag__c = true;
    //         t.save();
    //     }
    //     //System.Test.stopTest();
    // }

//     // 取引先などのマスタ検索
//     @isTest
//     static void test07_02_1() {
//         ControllerUtil.EscapeNFM001Trigger = true;
//         init();
//         //System.Test.startTest();
//         XinDailyReportController t = new XinDailyReportController();
//         t.init();
//         System.runAs(u3) {
//             t.report_search.Reported_Date__c = date.today();
//             t.report_search.Reporter__c = u1.id;
//             PageReference ref = t.getDailyReport();
//             Test.setCurrentPage(ref);
//             t = new XinDailyReportController();
//             t.init();
//             System.assertEquals(u1.id, t.report.Reporter__c);
//         //          t.getEvent();

//             t.report.Status__c = '承認';
//             t.save();

//             t.report.Feed_Back_From_Manager__c = 'OK';
//             t.report.Submit_Date_New__c = date.today();
//             t.save();

//             t.report.Status__c = '非承認';
//             t.save();
//         }
//         //System.Test.stopTest();
//     }
//     @isTest
//     static void test07_02_1_2() {
//         ControllerUtil.EscapeNFM001Trigger = true;
//         init();
//         //System.Test.startTest();
//         XinDailyReportController t = new XinDailyReportController();
//         t.init();
//         System.runAs(u3) {
//             t.report_search.Reported_Date__c = date.today();
//             t.report_search.Reporter__c = u1.id;
//             PageReference ref = t.getDailyReport();
//             Test.setCurrentPage(ref);
//             t = new XinDailyReportController();
//             t.init();
//             System.assertEquals(u1.id, t.report.Reporter__c);
//         //          t.getEvent();

//             t.report.Status__c = '承認';
//             t.save();

//             t.report.Feed_Back_From_Manager__c = 'OK';
//             t.report.Submit_Date_New__c = date.today();
//             t.save();

//             t.report.Status__c = '非承認';
//             t.save();
//         }
//         //System.Test.stopTest();
//     }
//     @isTest
//     static void test07_02_1_3() {
//         ControllerUtil.EscapeNFM001Trigger = true;
//         init();
//         //System.Test.startTest();
//         XinDailyReportController t = new XinDailyReportController();
//         t.init();
//         System.runAs(u3) {
//             Xin_SearchVisitorPlace_Campaign blgsvpc = new Xin_SearchVisitorPlace_Campaign();
//             PageReference pageRef4 = Page.Xin_SearchVisitorPlace_Campaign;
//             pageRef4.getParameters().put('q', '');
//             Test.setCurrentPage(pageRef4);
//             blgsvpc.search();

//             pageRef4.getParameters().put('q', 'test');
//             Test.setCurrentPage(pageRef4);
//             blgsvpc.search();
//             RestContext.response = new RestResponse();
//             Xin_SearchVisitorPlace_CampaignRest.doPost('test', '東京', '2000/01/01');

//             Xin_SearchVisitorPlace_Training blgsvpt = new Xin_SearchVisitorPlace_Training();
//             PageReference pageRef5 = Page.Xin_SearchVisitorPlace_Training;
//             pageRef5.getParameters().put('q', '');
//             Test.setCurrentPage(pageRef5);
//             blgsvpt.search();
//         }
//         //System.Test.stopTest();
//     }
//     @isTest
//     static void test07_02_1_4() {
//         ControllerUtil.EscapeNFM001Trigger = true;
//         init();
//         //System.Test.startTest();
//         XinDailyReportController t = new XinDailyReportController();
//         t.init();
//         System.runAs(u3) {
//             Xin_SearchVisitorPlace blgsv = new Xin_SearchVisitorPlace();
//             PageReference pageRef1 = Page.Xin_SearchVisitorPlace;
//             pageRef1.getParameters().put('q', '');
//             Test.setCurrentPage(pageRef1);
//             blgsv.search();

//             pageRef1.getParameters().put('q', 'test');
//             Test.setCurrentPage(pageRef1);
//             blgsv.search();
//             RestContext.response = new RestResponse();
//             Date Tling = Date.today();
//             Xin_SearchVisitorPlaceRest.doPost('診療科1', '東京', Date.today() + '');

//             Xin_SearchVisitorPlace_Sales blgsvp = new Xin_SearchVisitorPlace_Sales();
//             PageReference pageRef2 = Page.Xin_SearchVisitorPlace_Sales;
//             pageRef2.getParameters().put('q', '');
//             Test.setCurrentPage(pageRef2);
//             blgsvp.search();

//             pageRef2.getParameters().put('q', 'test');
//             pageRef2.getParameters().put('d', '2000/01/01');
//             Test.setCurrentPage(pageRef2);
//             blgsvp.search();
//             RestContext.response = new RestResponse();
//             Xin_SearchVisitorPlace_SalesRest.doPost('test', '東京', '2000/01/01');

//             Xin_SearchVisitorPlace_Training blgsvpt = new Xin_SearchVisitorPlace_Training();
//             PageReference pageRef5 = Page.Xin_SearchVisitorPlace_Training;
//             pageRef5.getParameters().put('q', '');
//             Test.setCurrentPage(pageRef5);
//             blgsvpt.search();
//         }
//         //System.Test.stopTest();
//     }
//     // 商談などのマスタ検索
//     //@isTest
//     static void test07_02_2() {
//         init();

//         XinDailyReportController t = new XinDailyReportController();
//         t.init();
//         System.runAs(u3) {
//             t.report_search.Reported_Date__c = date.today();
//             t.report_search.Reporter__c = u1.id;
//             PageReference ref = t.getDailyReport();
//             Test.setCurrentPage(ref);
//             t = new XinDailyReportController();
//             t.init();
//             System.assertEquals(u1.id, t.report.Reporter__c);
//             t.report.Status__c = '非承認';
//             t.save();

//             Xin_SearchOpportunity blgso = new Xin_SearchOpportunity();
//             PageReference pageRef3 = Page.Xin_SearchOpportunity;
//             pageRef3.getParameters().put('q', '');
//             pageRef3.getParameters().put('r', '');
//             Test.setCurrentPage(pageRef3);
//             blgso.search();

//             Account accTmp = [Select Id, Hospital__c from Account where id = : accDep.Id];
//             List<Opportunity> oppTmp = [Select Id from Opportunity where StageName IN ('引合', '注残', '出荷')];
//             pageRef3.getParameters().put('q', '::');
//             pageRef3.getParameters().put('r', accDep.Id);
//             Test.setCurrentPage(pageRef3);
//             blgso.search();
//             RestContext.response = new RestResponse();
//             Xin_SearchOpportunityRest.doPost('::', accDep.Id);

//             Xin_Maintenance_Contract blgmc = new Xin_Maintenance_Contract();
//             PageReference pageRef6 = Page.Xin_SearchMaintenanceContract;
//             pageRef6.getParameters().put('q', '');
//             pageRef6.getParameters().put('r', '');
//             Test.setCurrentPage(pageRef6);
//             blgmc.search();

//             pageRef6.getParameters().put('q', 'サービス');
//             pageRef6.getParameters().put('r', accDep.Id);
//             Test.setCurrentPage(pageRef6);
//             blgmc.search();
//             RestContext.response = new RestResponse();
//             Xin_Maintenance_ContractRest.doPost('サービス', accDep.Id);

//             PageReference pageRef7 = Page.SearchVisitor;
//             pageRef7.getParameters().put('vp', accDepId);
//             Test.setCurrentPage(pageRef7);
//             SearchVisitorController svc = new SearchVisitorController();
//             svc.rc.MyDr_Flg__c = true;
//             svc.rc.Search_LastName__c = 'testLastName';
//             svc.rc.FirstName = 'testFirstName';
//             svc.rc.Account_Visitor_Search__c = accDep.Id;
//             svc.rc.Type__c = 'test';
//             svc.rc.Doctor_Division1__c = 'test';
//             svc.regContact();

//             svc.sc.Search_LastName__c = 'testLastName';
//             svc.sc.Search_FirstName__c = 'testFirstName';
//             svc.sc.Type__c = 'test';
//             svc.sc.Doctor_Division1__c = 'test';
//             svc.sc.MyDr_Flg__c = true;
//             svc.serContact();

//             t.delIndex = '0';
//             t.deleteActivity();
//         }
//     }

//     // 部长/经理的反馈 编辑可能
//     @isTest
//     static void test08() {
//         Profile p = [select id from Profile where id = :System.Label.ProfileId_SystemAdmin];

//         u3 = new User(Test_staff__c = true);
//         u3.LastName = '_サンブリッジ';
//         u3.FirstName = 'う';
//         u3.Alias = 'う';
//         u3.Batch_User__c = true;
//         u3.Email = '<EMAIL>';
//         u3.Username = '<EMAIL>';
//         u3.CommunityNickname = 'う';
//         u3.IsActive = true;
//         u3.EmailEncodingKey = 'ISO-2022-JP';
//         u3.TimeZoneSidKey = 'Asia/Tokyo';
//         u3.LocaleSidKey = 'ja_JP';
//         u3.LanguageLocaleKey = 'ja';
//         u3.ProfileId = p.id;
//         u3.Job_Category__c = '支援';
//         u3.Province__c = '東京';
//         u3.Post__c = '部长';
//         insert u3;

//         u1 = new User(Test_staff__c = true);
//         u1.LastName = '_サンブリッジ';
//         u1.FirstName = 'あ';
//         u1.Alias = 'あ';
//         u1.Batch_User__c = true;
//         u1.Email = '<EMAIL>';
//         u1.Username = '<EMAIL>';
//         u1.CommunityNickname = 'あ1';
//         u1.IsActive = true;
//         u1.EmailEncodingKey = 'ISO-2022-JP';
//         u1.TimeZoneSidKey = 'Asia/Tokyo';
//         u1.LocaleSidKey = 'ja_JP';
//         u1.LanguageLocaleKey = 'ja';
//         u1.ProfileId = p.id;
//         u1.Job_Category__c = '销售服务';
//         u1.Province__c = '東京';
//         u1.ManagerId = u3.id;
//         u1.Post__c = '部长';
//         insert u1;

//         u2 = new User(Test_staff__c = true);
//         u2.LastName = '_サンブリッジ';
//         u2.FirstName = 'い';
//         u2.Alias = 'い';
//         u2.Batch_User__c = true;
//         u2.Email = '<EMAIL>';
//         u2.Username = '<EMAIL>';
//         u2.CommunityNickname = 'い';
//         u2.IsActive = true;
//         u2.EmailEncodingKey = 'ISO-2022-JP';
//         u2.TimeZoneSidKey = 'Asia/Tokyo';
//         u2.LocaleSidKey = 'ja_JP';
//         u2.LanguageLocaleKey = 'ja';
//         u2.ProfileId = p.id;
//         u2.Job_Category__c = '销售推广';
//         u2.Province__c = '東京';
//         u2.Post__c = '经理';
//         insert u2;

//         system.runAs(u3) {
//             Daily_Report__c dr = new Daily_Report__c();
//             dr.Reported_Date__c = Date.newInstance(2016, 1, 1);
//             dr.Status__c = '申請中';
//             dr.Reporter__c = u3.Id;
//             insert dr;

//             Datetime dt = Datetime.now();
//             Event__c e = new Event__c(
//                 Subject__c = 'test',
//                 StartDateTime__c = Datetime.newInstance(2016, 1, 1, 1, 0, 0),
//                 DurationInMinutes__c = 60,
//                 EndDateTime__c = Datetime.newInstance(2016, 1, 1, 2, 0, 0),
//                 ActivityDate__c = Date.today(),
//                 Daily_Report__c = dr.Id
//             );
//             insert e;

//             PageReference page = new PageReference('/apex/XinDailyReport?id=' + dr.Id);
//             System.Test.setCurrentPage(page);
//             XinDailyReportController controller = new XinDailyReportController();
//             //初始化
//             controller.init();
//             System.assertEquals(true, controller.ministerCommentEditFlg);
//             System.assertEquals(true, controller.managerCommentEditFlg);
//         }

//         System.runAs(u1) {
//             Daily_Report__c dr = new Daily_Report__c();
//             dr.Reported_Date__c = Date.newInstance(2016, 1, 1);
//             dr.Status__c = '申請中';
//             dr.Reporter__c = u2.Id;
//             insert dr;

//             PageReference page = new PageReference('/apex/XinDailyReport?id=' + dr.Id);
//             System.Test.setCurrentPage(page);
//             XinDailyReportController controller = new XinDailyReportController();
//             //初始化
//             controller.init();
//             System.assertEquals(false, controller.ministerCommentEditFlg);
//             System.assertEquals(true, controller.managerCommentEditFlg);

//             controller.activities[0].act.Minister_Comment__c = '111';
//             controller.saveComment();
//         }

//         System.runAs(u2) {
//             Daily_Report__c dr = new Daily_Report__c();
//             dr.Reported_Date__c = Date.newInstance(2019, 2, 19);
//             dr.Status__c = '申請中';
//             dr.Reporter__c = u1.Id;
//             insert dr;
//             Date t = Date.today();
//             //Date t1 = t.addMonths(6);
//             Event__c ec1 = new Event__c(
//                 Daily_Report__c = dr.Id,
//                 StartDateTime__c = Datetime.newInstance(t.year(), t.month(), t.day(), 1, 0, 0),
//                 EndDateTime__c = Datetime.newInstance(t.year(), t.month(), t.day(), 1, 0, 0),
//                 ActivityDate__c = dr.Reported_Date__c,
//                 Subject__c = 'eventc1'
//             );
//             insert ec1;
//             PageReference page = new PageReference('/apex/XinDailyReport?id=' + +ec1.Id);
//             System.Test.setCurrentPage(page);
//             XinDailyReportController controller = new XinDailyReportController();
//             //初始化
//             controller.init();
//             System.assertEquals(true, controller.ministerCommentEditFlg);
//             System.assertEquals(false, controller.managerCommentEditFlg);

//             controller.activities[0].act.Manager_Comment__c = '222';
//             controller.saveComment();
//         }
//     }

//     @isTest
//     static void test9() {
//         Profile p = [select id from Profile where id = :System.Label.ProfileId_SystemAdmin];

//         u3 = new User(Test_staff__c = true);
//         u3.LastName = '_サンブリッジ';
//         u3.FirstName = 'う';
//         u3.Alias = 'う';
//         u3.Batch_User__c = true;
//         u3.Email = '<EMAIL>';
//         u3.Username = '<EMAIL>';
//         u3.CommunityNickname = 'う';
//         u3.IsActive = true;
//         u3.EmailEncodingKey = 'ISO-2022-JP';
//         u3.TimeZoneSidKey = 'Asia/Tokyo';
//         u3.LocaleSidKey = 'ja_JP';
//         u3.LanguageLocaleKey = 'ja';
//         u3.ProfileId = p.id;
//         u3.Job_Category__c = '支援';
//         u3.Province__c = '東京';
//         u3.Post__c = '部长';
//         insert u3;

//         u1 = new User(Test_staff__c = true);
//         u1.LastName = '_サンブリッジ';
//         u1.FirstName = 'あ';
//         u1.Alias = 'あ';
//         u1.Batch_User__c = true;
//         u1.Email = '<EMAIL>';
//         u1.Username = '<EMAIL>';
//         u1.CommunityNickname = 'あ1';
//         u1.IsActive = true;
//         u1.EmailEncodingKey = 'ISO-2022-JP';
//         u1.TimeZoneSidKey = 'Asia/Tokyo';
//         u1.LocaleSidKey = 'ja_JP';
//         u1.LanguageLocaleKey = 'ja';
//         u1.ProfileId = p.id;
//         u1.Job_Category__c = '销售服务';
//         u1.Province__c = '東京';
//         u1.ManagerId = u3.id;
//         u1.Post__c = '部长';
//         insert u1;

//         u2 = new User(Test_staff__c = true);
//         u2.LastName = '_サンブリッジ';
//         u2.FirstName = 'い';
//         u2.Alias = 'い';
//         u2.Batch_User__c = true;
//         u2.Email = '<EMAIL>';
//         u2.Username = '<EMAIL>';
//         u2.CommunityNickname = 'い';
//         u2.IsActive = true;
//         u2.EmailEncodingKey = 'ISO-2022-JP';
//         u2.TimeZoneSidKey = 'Asia/Tokyo';
//         u2.LocaleSidKey = 'ja_JP';
//         u2.LanguageLocaleKey = 'ja';
//         u2.ProfileId = p.id;
//         u2.Job_Category__c = '销售推广';
//         u2.Province__c = '東京';
//         u2.Post__c = '经理';
//         insert u2;

//         System.runAs(u3) {
//             Daily_Report__c dr = new Daily_Report__c();
//             dr.Reported_Date__c = Date.newInstance(2016, 1, 1);
//             dr.Status__c = '申請中';
//             dr.Reporter__c = u3.Id;
//             insert dr;

//             Datetime dt = Datetime.now();
//             Event__c e = new Event__c(
//                 Subject__c = 'test',
//                 StartDateTime__c = Datetime.newInstance(2016, 1, 1, 1, 0, 0),
//                 DurationInMinutes__c = 60,
//                 EndDateTime__c = Datetime.newInstance(2016, 1, 1, 2, 0, 0),
//                 ActivityDate__c = Date.today(),
//                 Daily_Report__c = dr.Id
//             );
//             insert e;

//             PageReference page = new PageReference('/apex/XinDailyReport?id=' + dr.Id + '&dt=' + String.valueOf(dt));
//             System.Test.setCurrentPage(page);
//             XinDailyReportController controller = new XinDailyReportController();
//             //初始化
//             controller.init();
//             System.assertEquals(true, controller.ministerCommentEditFlg);
//             System.assertEquals(true, controller.managerCommentEditFlg);
//         }

//         System.runAs(u1) {
//             Daily_Report__c dr = new Daily_Report__c();
//             dr.Reported_Date__c = Date.newInstance(2016, 1, 1);
//             dr.Status__c = '申請中';
//             dr.Reporter__c = u2.Id;
//             insert dr;

//             PageReference page = new PageReference('/apex/XinDailyReport?id=' + dr.Id);
//             System.Test.setCurrentPage(page);
//             XinDailyReportController controller = new XinDailyReportController();
//             //初始化
//             controller.init();
//             System.assertEquals(false, controller.ministerCommentEditFlg);
//             System.assertEquals(true, controller.managerCommentEditFlg);

//             controller.activities[0].act.Minister_Comment__c = '111';
//             controller.saveComment();

//         }

//         System.runAs(u2) {
//             Daily_Report__c dr = new Daily_Report__c();
//             dr.Reported_Date__c = Date.newInstance(2016, 1, 1);
//             dr.Status__c = '申請中';
//             dr.Reporter__c = u1.Id;
//             insert dr;

//             PageReference page = new PageReference('/apex/XinDailyReport?dt=' + String.valueOf('20190215'));
//             //PageReference page = new PageReference('/apex/XinDailyReport?id=' + dr.Id +'&dt='+String.valueOf('20190215'));
//             System.Test.setCurrentPage(page);
//             XinDailyReportController controller = new XinDailyReportController();
//             //初始化
//             controller.init();
//         }
//         System.runAs(u2) {


//             PageReference page = new PageReference('/apex/XinDailyReport');
//             //PageReference page = new PageReference('/apex/XinDailyReport?id=' + dr.Id +'&dt='+String.valueOf('20190215'));
//             System.Test.setCurrentPage(page);
//             XinDailyReportController controller = new XinDailyReportController();
//             //初始化
//             controller.init();

//         }

//         System.runAs(u2) {


//             PageReference page = new PageReference('/apex/XinDailyReport?id=00Uabcd');
//             //PageReference page = new PageReference('/apex/XinDailyReport?id=' + dr.Id +'&dt='+String.valueOf('20190215'));
//             System.Test.setCurrentPage(page);
//             XinDailyReportController controller = new XinDailyReportController();
//             //初始化
//             controller.init();

//         }

//         System.runAs(u2) {


//             PageReference page = new PageReference('/apex/XinDailyReport?id=a0Aabcd');
//             //PageReference page = new PageReference('/apex/XinDailyReport?id=' + dr.Id +'&dt='+String.valueOf('20190215'));
//             System.Test.setCurrentPage(page);
//             XinDailyReportController controller = new XinDailyReportController();
//             //初始化
//             controller.init();

//         }

//         System.runAs(u2) {


//             PageReference page = new PageReference('/apex/XinDailyReport?id=UaMabcd');
//             //PageReference page = new PageReference('/apex/XinDailyReport?id=' + dr.Id +'&dt='+String.valueOf('20190215'));
//             System.Test.setCurrentPage(page);
//             XinDailyReportController controller = new XinDailyReportController();
//             //初始化
//             controller.init();

//         }
//     }

//     @isTest
//     static void test10() {
//         Profile p = [select id from Profile where id = :System.Label.ProfileId_SystemAdmin];

//         u3 = new User(Test_staff__c = true);
//         u3.LastName = '_サンブリッジ';
//         u3.FirstName = 'う';
//         u3.Alias = 'う';
//         u3.Batch_User__c = true;
//         u3.Email = '<EMAIL>';
//         u3.Username = '<EMAIL>';
//         u3.CommunityNickname = 'う';
//         u3.IsActive = true;
//         u3.EmailEncodingKey = 'ISO-2022-JP';
//         u3.TimeZoneSidKey = 'Asia/Tokyo';
//         u3.LocaleSidKey = 'ja_JP';
//         u3.LanguageLocaleKey = 'ja';
//         u3.ProfileId = p.id;
//         u3.Job_Category__c = '支援';
//         u3.Province__c = '東京';
//         u3.Post__c = '部长';
//         insert u3;

//         u1 = new User(Test_staff__c = true);
//         u1.LastName = '_サンブリッジ';
//         u1.FirstName = 'あ';
//         u1.Alias = 'あ';
//         u1.Batch_User__c = true;
//         u1.Email = '<EMAIL>';
//         u1.Username = '<EMAIL>';
//         u1.CommunityNickname = 'あ1';
//         u1.IsActive = true;
//         u1.EmailEncodingKey = 'ISO-2022-JP';
//         u1.TimeZoneSidKey = 'Asia/Tokyo';
//         u1.LocaleSidKey = 'ja_JP';
//         u1.LanguageLocaleKey = 'ja';
//         u1.ProfileId = p.id;
//         u1.Job_Category__c = '销售服务';
//         u1.Province__c = '東京';
//         u1.ManagerId = u3.id;
//         u1.Post__c = '部长';
//         insert u1;

//         u2 = new User(Test_staff__c = true);
//         u2.LastName = '_サンブリッジ';
//         u2.FirstName = 'い';
//         u2.Alias = 'い';
//         u2.Batch_User__c = true;
//         u2.Email = '<EMAIL>';
//         u2.Username = '<EMAIL>';
//         u2.CommunityNickname = 'い';
//         u2.IsActive = true;
//         u2.EmailEncodingKey = 'ISO-2022-JP';
//         u2.TimeZoneSidKey = 'Asia/Tokyo';
//         u2.LocaleSidKey = 'ja_JP';
//         u2.LanguageLocaleKey = 'ja';
//         u2.ProfileId = p.id;
//         u2.Job_Category__c = '销售推广';
//         u2.Province__c = '東京';
//         u2.Post__c = '经理';
//         insert u2;



//         System.runAs(u3) {
//             Daily_Report__c dr = new Daily_Report__c();
//             dr.Reported_Date__c = Date.newInstance(2016, 1, 1);
//             dr.Status__c = '申請中';
//             dr.Reporter__c = u3.Id;
//             insert dr;

//             Datetime dt = Datetime.now();
//             Event__c e = new Event__c(
//                 Subject__c = 'test',
//                 StartDateTime__c = Datetime.newInstance(2016, 1, 1, 1, 0, 0),
//                 DurationInMinutes__c = 60,
//                 EndDateTime__c = Datetime.newInstance(2016, 1, 1, 2, 0, 0),
//                 ActivityDate__c = Date.today(),
//                 Daily_Report__c = dr.Id
//             );
//             insert e;

//             PageReference page = new PageReference('/apex/XinDailyReport?id=' + dr.Id);
//             System.Test.setCurrentPage(page);
//             XinDailyReportController controller = new XinDailyReportController();
//             //初始化
//             controller.init();
//             System.assertEquals(true, controller.ministerCommentEditFlg);
//             System.assertEquals(true, controller.managerCommentEditFlg);
//         }

//         System.runAs(u1) {
//             Daily_Report__c dr = new Daily_Report__c();
//             dr.Reported_Date__c = Date.newInstance(2016, 1, 1);
//             dr.Status__c = '申請中';
//             dr.Reporter__c = u2.Id;
//             insert dr;

//             PageReference page = new PageReference('/apex/XinDailyReport?id=' + dr.Id);
//             System.Test.setCurrentPage(page);
//             XinDailyReportController controller = new XinDailyReportController();
//             //初始化
//             controller.init();
//             System.assertEquals(false, controller.ministerCommentEditFlg);
//             System.assertEquals(true, controller.managerCommentEditFlg);

//             controller.activities[0].act.Minister_Comment__c = '111';
//             controller.saveComment();

//             //Event__c event = [select id,Minister_Comment__c from Event__c where id = :e.id];
//             //System.assertEquals('111', event.Minister_Comment__c);
//         }

//         System.runAs(u2) {
//             Daily_Report__c dr = new Daily_Report__c();
//             dr.Reported_Date__c = Date.newInstance(2016, 1, 1);
//             dr.Status__c = '申請中';
//             dr.Reporter__c = u1.Id;
//             insert dr;

//             PageReference page = new PageReference('/apex/XinDailyReport?id=' + dr.Id);
//             System.Test.setCurrentPage(page);
//             XinDailyReportController controller = new XinDailyReportController();
//             //初始化
//             controller.init();
//             System.assertEquals(true, controller.ministerCommentEditFlg);
//             System.assertEquals(false, controller.managerCommentEditFlg);

//             controller.activities[0].act.Manager_Comment__c = '222';
//             controller.saveComment();

//             //Event__c event = [select id,Manager_Comment__c from Event__c where id = :e.id];
//             //System.assertEquals('222', event.Manager_Comment__c);
//         }
//     }

//     @isTest
//     static void test11() {
//         ControllerUtil.EscapeNFM001Trigger = true;
//         init();
//         //System.Test.startTest();
//         XinDailyReportController t = new XinDailyReportController();

//         t.init();
//         System.runAs(u1) {
//             init2();
//             t.report_search.Reported_Date__c = date.today();
//             PageReference ref = t.getDailyReport();
//             Test.setCurrentPage(ref);
//             t = new XinDailyReportController();
//             t.init();
//         //          t.getEvent();

//             t.addActivity();

//             t.report.Status__c = '作成中';
//             t.report.Reported_Date__c = date.today();
//             t.report.Daily_Report_Data_Type__c = '通常';
//             t.repoStartHourText = '9';
//             t.repoStartMinuteText = '00';
//             t.repoEndHourText = '18';
//             t.repoEndMinuteText = '00';
//             t.activities.get(0).actStartHourText = '9';
//             t.activities.get(0).actStartMinuteText = '00';
//             t.activities.get(0).actEndHourText = '18';
//             t.activities.get(0).actEndMinuteText = '00';
//             t.activities.get(0).act.Activity_Type2__c = '病院';
//             t.activities.get(0).act.Purpose__c = '目的(計画)';
//             t.activities.get(0).act.Description__c = '結果';
//             t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
//             t.activities.get(0).act.Purpose_Type2__c = '客户咨询对应';
//             t.activities.get(0).act.Purpose_Type3__c = '学会or展会对应';
//             t.activities.get(0).act.Purpose_Type4__c = '信息搜集';
//             t.activities.get(0).act.Purpose_Type5__c = '产品介绍or推广';
//             t.activities.get(0).act.Location__c = accDepName;
//             t.activities.get(0).act.whatid__c = accDep.Id;
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = con1.id;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = con2.id;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = con3.id;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = con4.id;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = con5.id;
//             t.activities.get(0).act.Related_Opportunity1__c = '引合1';
//             t.activities.get(0).act.Related_Opportunity2__c = '引合2';
//             t.activities.get(0).act.Related_Opportunity3__c = '引合3';
//             t.activities.get(0).act.Related_Opportunity4__c = '引合4';
//             t.activities.get(0).act.Related_Opportunity5__c = '引合5';
//             t.activities.get(0).act.Related_Opportunity1_ID__c = opp1.Id;
//             t.activities.get(0).act.Related_Opportunity2_ID__c = opp2.Id;
//             t.activities.get(0).act.Related_Opportunity3_ID__c = opp3.Id;
//             t.activities.get(0).act.Related_Opportunity4_ID__c = opp4.Id;
//             t.activities.get(0).act.Related_Opportunity5_ID__c = opp5.Id;
//             t.activities.get(0).act.Related_Service1__c = mc1.Name;
//             t.activities.get(0).act.Related_Service2__c = mc2.Name;
//             t.activities.get(0).act.Related_Service1_ID__c = mc1.Id;
//             t.activities.get(0).act.Related_Service2_ID__c = mc2.Id;
//             t.activities.get(0).planStartHourText = '9';
//             t.activities.get(0).planStartMinuteText = '00';
//             t.activities.get(0).planEndHourText = '18';
//             t.activities.get(0).planEndMinuteText = '00';
//             t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';
//             //t.activities.get(0).act.Free_Input__c = true;
//             t.activities.get(0).act.Find_Imitations_Flag__c = true;
//             //t.activities.get(0).act.Activity_PurposeFSE__c = '装机';
//             t.activities.get(0).act.nextPlanDate__c = date.today() + 30;
//             t.save();

//             //t.report.Status__c = '承认';
//             //t.upsertActIndex = '0';
//             //t.activities.get(0).act.Purpose_TypeEscFSE__c = '休假';
//             //t.activities.get(0).act.Purpose_TypeFSE__c = '例会';
//             //t.activities.get(0).act.Free_Input__c = true;
//             //t.save();
//         }
//         //System.Test.stopTest();
//     }

//     @isTest
//     static void test12() {
//         ControllerUtil.EscapeNFM001Trigger = true;
//         init();
//         //System.Test.startTest();
//         XinDailyReportController t = new XinDailyReportController();
//         t.upsertActVD = '';
//         t.upsertActVP = '';
//         t.upsertStartTime = date.today();
//         t.upsertEndTime = date.today() + 1;
//         //    t.noSltList.add(new SelectOption('123456789123456', 'Dummy User'));
//         //    t.hosptalList.add(new SelectOption('123456789123456', 'Dummy User'));
//         //    t.salesList.add(new SelectOption('123456789123456', 'Dummy User'));
//         //    t.outEventList.add(new SelectOption('123456789123456', 'Dummy User'));
//         //    t.inEventList.add(new SelectOption('123456789123456', 'Dummy User'));
//         //    t.moveList.add(new SelectOption('123456789123456', 'Dummy User'));
//         //    t.activitiesSize = 0;

//     }

//     @isTest
//     static void test13 () {
//         ControllerUtil.EscapeNFM001Trigger = true;
//         init();
//         //System.Test.startTest();
//         System.runAs(u1) {
//             Daily_Report__c dr = new Daily_Report__c();
//             dr.Reported_Date__c = Date.newInstance(2016, 1, 1);
//             dr.Status__c = '申請中';
//             dr.Reporter__c = u1.Id;
//             insert dr;

//             Event__c ec = new Event__c(
//                 Subject__c = 'test',
//                 StartDateTime__c = Datetime.newInstance(2016, 1, 1, 1, 0, 0),
//                 DurationInMinutes__c = 60,
//                 EndDateTime__c = Datetime.newInstance(2016, 1, 1, 2, 0, 0),
//                 ActivityDate__c = Date.today(),
//                 Daily_Report__c = dr.Id
//             );
//             insert ec;

//             XinDailyReportController t = new XinDailyReportController();

//             Event e = new Event();
//             e.ActivityDateTime = datetime.now();
//             e.DurationInMinutes = 0;
//             e.OwnerId = u1.id;
//             e.IsScheduled__c = true;
//             e.whatid__c = accDep.Id;
//             e.NextEventC_ID__c = ec.Id;
//             insert e;

//             PageReference pageRef1 = Page.XinDailyReport;
//             pageRef1.getParameters().put('id', e.id);
//             Test.setCurrentPage(pageRef1);
//             t.init();
//         }
//         //System.Test.stopTest();

//     }

//     // 任务和日报管理 2020-05-25 update by vivek start 
//     @isTest
//     static void test14 () {
//         ControllerUtil.EscapeNFM001Trigger = true;
//         init(); 
        
//         System.runAs(u1) {
//             // init2();
//             Daily_Report__c dr = new Daily_Report__c();
//             dr.Reported_Date__c = Date.newInstance(2016, 1, 1);
//             // dr.Status__c = '申請中';
//             dr.Status__c = '申請中';
//             dr.OwnerId = u1.Id;
//             dr.Reporter__c = u1.Id;
//             insert dr;
//             // Daily_Report__c dr2 = new Daily_Report__c();
//             // dr2.Reported_Date__c = Date.newInstance(2016, 1, 2);
//             // dr2.Status__c = '作成中';
//             // dr.OwnerId = u1.Id;
//             // dr2.Reporter__c = u1.Id;
//             // insert dr2;

//             task__c t1 = new task__c();
//             t1.Name = 'test';
//             t1.taskDifferent__c = '主动任务';
//             // insert t1;
//             task__c t2 = new task__c();
//             t2.Name = 'test';
//             t2.taskDifferent__c = '主动任务';
//             // insert t2;
//             task__c t3 = new task__c();
//             t3.Name = 'test';
//             t3.taskDifferent__c = '主动任务';
//             insert New List<task__c>{t1,t2,t3};

//             Event e1 = new Event();
//             e1.ActivityDateTime = Datetime.newInstance(2016, 1, 1, 1, 0, 0);
//             e1.DurationInMinutes = 60;
//             e1.ActivityDate = Date.newInstance(2016, 1, 1);
//             e1.OwnerId = u1.id;
//             e1.IsScheduled__c = true;
//             e1.whatid__c = accDep.Id;
//             // e1.NextEventC_ID__c = ec.Id;
//             e1.task_id__c = t1.Id;
//             // insert e1;

//             Event e2 = new Event();
//             e2.ActivityDateTime = Datetime.newInstance(2016, 1, 1, 2, 0, 0);
//             e2.ActivityDate = Date.newInstance(2016, 1, 1);
//             e2.DurationInMinutes = 60;
//             e2.OwnerId = u1.id;
//             e2.IsScheduled__c = true;
//             e2.whatid__c = accDep.Id;
//             // e2.NextEventC_ID__c = ec.Id;
//             e2.task_id__c = t2.Id;
//             // insert e2;
//             Event e3 = new Event();
//             e3.ActivityDateTime = Datetime.newInstance(2016, 1, 1, 2, 0, 0);
//             e3.ActivityDate = Date.newInstance(2016, 1, 1);
//             e3.DurationInMinutes = 60;
//             e3.OwnerId = u1.id;
//             e3.IsScheduled__c = true;
//             e3.whatid__c = accDep.Id;
//             // e2.NextEventC_ID__c = ec.Id;
//             e3.task_id__c = t3.Id;
//             // insert e3;
//             insert New List<Event>{e1,e2,e3};

//             Event__c ec1 = new Event__c(
//                 Subject__c = 'test',
//                 StartDateTime__c = Datetime.newInstance(2016, 1, 1, 1, 0, 0),
//                 DurationInMinutes__c = 60,
//                 EndDateTime__c = Datetime.newInstance(2016, 1, 1, 2, 0, 0),
//                 ActivityDate__c = Date.newInstance(2016, 1, 1),
//                 delayToDate__c = Date.newInstance(2016, 1, 2),
//                 eventStatus__c = '延期',
//                 Event_ID__c = e1.Id,
//                 Daily_Report__c = dr.Id
//             );
//             // insert ec1;
//             Event__c ec2 = new Event__c(
//                 Subject__c = 'test',
//                 StartDateTime__c = Datetime.newInstance(2016, 1, 1, 3, 0, 0),
//                 DurationInMinutes__c = 60,
//                 EndDateTime__c = Datetime.newInstance(2016, 1, 1, 4, 0, 0),
//                 ActivityDate__c = Date.newInstance(2016, 1, 1),
//                 delayToDate__c = Date.newInstance(2016, 1, 2),
//                 eventStatus__c = '延期',
//                 Event_ID__c = e2.Id,
//                 Daily_Report__c = dr.Id
//             );
//             // insert ec2;
//             Event__c ec3 = new Event__c(
//                 Subject__c = 'test',
//                 StartDateTime__c = Datetime.newInstance(2016, 1, 1, 3, 0, 0),
//                 DurationInMinutes__c = 60,
//                 EndDateTime__c = Datetime.newInstance(2016, 1, 1, 4, 0, 0),
//                 ActivityDate__c = Date.newInstance(2016, 1, 1),
//                 eventStatus__c = '取消',
//                 Event_ID__c = e3.Id,
//                 Daily_Report__c = dr.Id
//             );
//             // insert ec3;
//             insert New List<Event__c>{ec1,ec2,ec3};

//             XinDailyReportController t = new XinDailyReportController();
//             PageReference pageRef1 = Page.XinDailyReport;
//             pageRef1.getParameters().put('id', dr.id);
//             Test.setCurrentPage(pageRef1);
//             t.init();
//             t.report  = dr;
//             t.addActivity();

//             t.report.Daily_Report_Data_Type__c = '通常';
//             t.repoStartHourText = '9';
//             t.repoStartMinuteText = '00';
//             t.repoEndHourText = '18';
//             t.repoEndMinuteText = '00';
//             t.activities.get(0).actStartHourText = '9';
//             t.activities.get(0).actStartMinuteText = '00';
//             t.activities.get(0).actEndHourText = '18';
//             t.activities.get(0).actEndMinuteText = '00';
//             t.activities.get(0).act = ec1;
//             t.activities.get(0).act.Activity_Type2__c = '病院';
//             t.activities.get(0).act.Purpose__c = '目的(計画)';
//             t.activities.get(0).act.Description__c = '結果';
//             t.activities.get(0).act.Purpose_Type__c = '礼节性拜访';
//             t.activities.get(0).act.Purpose_Type2__c = '客户咨询对应';
//             t.activities.get(0).act.Purpose_Type3__c = '学会or展会对应';
//             t.activities.get(0).act.Purpose_Type4__c = '信息搜集';
//             t.activities.get(0).act.Purpose_Type5__c = '产品介绍or推广';
//             t.activities.get(0).act.Location__c = accDepName;
//             t.activities.get(0).act.whatid__c = accDep.Id;
//             t.activities.get(0).act.Visitor1__c = '取引先責任者1';
//             t.activities.get(0).act.Visitor1_ID__c = con1.id;
//             t.activities.get(0).act.Visitor2__c = '取引先責任者2';
//             t.activities.get(0).act.Visitor2_ID__c = con2.id;
//             t.activities.get(0).act.Visitor3__c = '取引先責任者3';
//             t.activities.get(0).act.Visitor3_ID__c = con3.id;
//             t.activities.get(0).act.Visitor4__c = '取引先責任者4';
//             t.activities.get(0).act.Visitor4_ID__c = con4.id;
//             t.activities.get(0).act.Visitor5__c = '取引先責任者5';
//             t.activities.get(0).act.Visitor5_ID__c = con5.id;
//             t.activities.get(0).act.Related_Opportunity1__c = '引合1';
//             t.activities.get(0).act.Related_Opportunity2__c = '引合2';
//             t.activities.get(0).act.Related_Opportunity3__c = '引合3';
//             t.activities.get(0).act.Related_Opportunity4__c = '引合4';
//             t.activities.get(0).act.Related_Opportunity5__c = '引合5';
//             t.activities.get(0).act.Related_Opportunity1_ID__c = opp1.Id;
//             t.activities.get(0).act.Related_Opportunity2_ID__c = opp2.Id;
//             t.activities.get(0).act.Related_Opportunity3_ID__c = opp3.Id;
//             t.activities.get(0).act.Related_Opportunity4_ID__c = opp4.Id;
//             t.activities.get(0).act.Related_Opportunity5_ID__c = opp5.Id;
//             t.activities.get(0).planStartHourText = '9';
//             t.activities.get(0).planStartMinuteText = '00';
//             t.activities.get(0).planEndHourText = '18';
//             t.activities.get(0).planEndMinuteText = '00';
//             t.activities.get(0).planPurposeText = '次の活動予定目的(計画)';
//             //t.activities.get(0).act.Free_Input__c = true;
//             t.activities.get(0).act.Find_Imitations_Flag__c = true;
//             //t.activities.get(0).act.Activity_PurposeFSE__c = '装机';
//             t.activities.get(0).act.nextPlanDate__c = date.today() + 30;
//             // t.save();
//             t.delIndex = '0';

//             // System.Test.startTest();
//             // t.cancelSave();
//             // t.delaysave();
//             // t.report.Status__c = '承認';
//             //t.upsertActIndex = '0';
//             //t.activities.get(0).act.Purpose_TypeEscFSE__c = '休假';
//             //t.activities.get(0).act.Purpose_TypeFSE__c = '例会';
//             //t.activities.get(0).act.Free_Input__c = true;
//             /*User u = [Select Id,Name,State_Hospital__c,Employee_No__c,
//                      Department,Job_Category__c,Category4__c,Category6__c,ManagerId,
//                      Manager.Email,BuchangApprovalManager__c,BuchangApprovalManagerSales__c,
//                      ProfileId FROM User where id = :u1.id];
//             System.assertEquals(null,u1);*/
//             t.save();
//             // ec2.delayToDate__c = Date.newInstance(2016, 2, 2);
//             // t.save();
//             // System.Test.stopTest();
//         }
// //         System.runAs(u3) {
// //             // PageReference pageRef2 = Page.XinDailyReport;
// //             t.report.Reporter__c = u1.Id;
// //             t.init();
// //             t.report_search.Reported_Date__c = Date.newInstance(2016, 1, 1);
// //             t.report_search.Reporter__c = u1.id;
// //             // t.report  = dr;
// //             // t.reportOwner = u1;
// //             t.report.Status__c = '申請中';
// //             t.getDailyReport();
// //             // Test.setCurrentPage(ref);
            
// //             t.reportOwner.ManagerId = u3.Id;
// //             t.repoStartHourText = '9';
// //             t.repoStartMinuteText = '00';
// //             t.repoEndHourText = '18';
// //             t.repoEndMinuteText = '00';
// //             t.report.Feed_Back_From_Manager__c = 'OK';
// //             // t.report.Feed_Back_From_Manager__c = 'OK';
// //             t.report.Submit_Date_New__c = date.today();
// //             // System.assertEquals(t.reportOwner+'2', dr+'1');
// //             System.assertEquals(u1.id, t.report.Reporter__c);

// // //             t.report.Status__c = '承認';
// //             // t.report.Status__c = '承認';
// //             // t.upsertActIndex = '0';
// //             // t.activities.get(0).act.Purpose_TypeEscFSE__c = '休假';
// //             // t.activities.get(0).act.Purpose_TypeFSE__c = '例会';
// //             // t.activities.get(0).act.Free_Input__c = true;
// //             t.save();
// //         }
//         //System.Test.stopTest();

//     }
//     @isTest
//     static void test15 () {
//         ControllerUtil.EscapeNFM001Trigger = true;
//         init();
//         System.runAs(u1) {
//         XinDailyReportController t = new XinDailyReportController();
        
//             // init2();
//             Daily_Report__c dr = new Daily_Report__c();
//             dr.Reported_Date__c = Date.newInstance(2016, 1, 1);
//             dr.Status__c = '申請中';
//             dr.OwnerId = u1.Id;
//             dr.Reporter__c = u1.Id;
//             insert dr;
//             XinDailyReportController xin1 = new XinDailyReportController(new ApexPages.StandardController(dr));

//             task__c t1 = new task__c();
//             t1.Name = 'test';
//             t1.taskDifferent__c = '主动任务';
//             insert t1;

//             Event e1 = new Event();
//             e1.ActivityDateTime = Datetime.newInstance(2016, 1, 1, 1, 0, 0);
//             e1.DurationInMinutes = 60;
//             e1.ActivityDate = Date.newInstance(2016, 1, 1);
//             e1.OwnerId = u1.id;
//             e1.IsScheduled__c = true;
//             e1.whatid__c = accDep.Id;
//             // e1.NextEventC_ID__c = ec.Id;
//             e1.task_id__c = t1.Id;
//             insert e1;

//             Event__c ec1 = new Event__c(
//                 Subject__c = 'test',
//                 StartDateTime__c = Datetime.newInstance(2016, 1, 1, 1, 0, 0),
//                 DurationInMinutes__c = 60,
//                 EndDateTime__c = Datetime.newInstance(2016, 1, 1, 2, 0, 0),
//                 ActivityDate__c = Date.newInstance(2016, 1, 1),
//                 delayToDate__c = Date.newInstance(2016, 1, 2),
//                 eventStatus__c = '取消',
//                 Event_ID__c = e1.Id,
//                 Daily_Report__c = dr.Id,
//                 nextPlanTimePurpose__c = 'test1,test2,test3,test4,test5,test6'
//             );
//             insert ec1;
            
//             PageReference pageRef1 = Page.XinDailyReport;
//             pageRef1.getParameters().put('id', dr.id);
//             Test.setCurrentPage(pageRef1);
//             t.IsAlertInputDep = true;
//             t.Alertlines = 'test';
//             t.init();
//             t.report  = dr;
//             t.addActivity();
//             //t.activities.get(0).act.Free_Input__c = true;
//             t.activities.get(0).act.Find_Imitations_Flag__c = true;
//             //t.activities.get(0).act.Activity_PurposeFSE__c = '装机';
//             t.activities.get(0).act.nextPlanDate__c = date.today() + 30;
//             t.delIndex = '0';
//             // System.Test.startTest();
//             // t.save();
//             t.cancelSave();
//             t.delaysave();
//             // t.report.Status__c = '承認';
//             //t.upsertActIndex = '0';
//             //t.activities.get(0).act.Purpose_TypeEscFSE__c = '休假';
//             //t.activities.get(0).act.Purpose_TypeFSE__c = '例会';
//             //t.activities.get(0).act.Free_Input__c = true;
//             t.save();
//             // System.Test.stopTest();
//             t.deleteActivity();
//             t.calcelActivity();
//         }

//     }

//     //2021-06-30  mzy  add
//     @isTest
//     static void test16 () {
//         // レコードタイプ取得
//         RecordType hospitalRec = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
//         RecordType sectionRec = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 消化科'];
//         RecordType departmentRec = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 消化科'];
        
//         // 病院作成
//         Account hospital = new Account();
//         hospital.RecordTypeId = hospitalRec.Id;
//         hospital.Name = 'TestHospital';
//         insert hospital;
        
//         // 病院を作ると戦略科室は、トリガーによって作られている
//         Account section = [select Management_Code__c, Management_Code_Auto__c, Name, Id from Account where Parent.Id = :hospital.Id and RecordTypeId = :sectionRec.Id limit 1];
        
//         // 診療科1を作成
//         Account depart1 = new Account();
//         depart1.RecordTypeId = departmentRec.Id;
//         depart1.Name = '*';
//         depart1.Department_Name__c  = 'TestDepart';
//         depart1.ParentId = section.Id;
//         depart1.Department_Class__c = section.Id;
//         depart1.Hospital__c = hospital.Id;
//         depart1.AgentCode_Ext__c = '9999997';
//         insert depart1;

//         ControllerUtil.EscapeNFM001Trigger = true;
//         init();
//         Date t = Date.today();
//         System.runAs(u1) {
//         XinDailyReportController tCon = new XinDailyReportController();
        
//             // init2();
//             Daily_Report__c dr = new Daily_Report__c();
//             dr.Reported_Date__c = Date.newInstance(t.year(), t.month(), t.day());
//             dr.Status__c = '申请中';
//             dr.OwnerId = u1.Id;
//             dr.Reporter__c = u1.Id;
//             dr.Working_Time_From__c = Datetime.newInstance(t.year(), t.month(), t.day(), 1, 0, 0);
//             dr.Working_Time_To__c = Datetime.newInstance(t.year(), t.month(), t.day(), 10, 0, 0);
//             insert dr;
//             XinDailyReportController xin1 = new XinDailyReportController(new ApexPages.StandardController(dr));
//             xin1.noSltList = new List<SelectOption>();
//             xin1.hosptalList = new List<SelectOption>();
//             xin1.salesList = new List<SelectOption>();
//             xin1.outEventList = new List<SelectOption>();
//             xin1.inEventList = new List<SelectOption>();
//             xin1.moveList = new List<SelectOption>();
//             xin1.vacationList = new List<SelectOption>();

//             OPDPlan__c oPDPlan0 = new OPDPlan__c();
//             oPDPlan0.Status__c = '审批中';
//             oPDPlan0.OPDType__c = '事件';
//             oPDPlan0.PlanProdDetail__c ='CV-290*4; GI-290镜子*1; GI-290镜子CF*1;';
//             oPDPlan0.Account_Laboratory__c =depart1.Id;
//             oPDPlan0.OPDPlan_ImplementDate__c = Date.today();
//             oPDPlan0.OPDPlan_ImplementDate_temp__c = Date.today();
//             oPDPlan0.NoOpp_Reason__c = 'HCP对应';
//             oPDPlan0.RentalReson__c = '无法进入手术室';
//             oPDPlan0.AttachmentCertificate__c = '0001.png';
//             insert oPDPlan0;


//             task__c t1 = new task__c();
//             t1.Name = 'test';
//             t1.taskDifferent__c = '主动任务';
//             t1.Opd_Plan__c = oPDPlan0.Id;
//             t1.taskStatus__c = '';
//             insert t1;

//             Event e1 = new Event();
//             e1.ActivityDateTime = Datetime.newInstance(t.year(), t.month(), t.day(), 1, 0, 0);
//             e1.DurationInMinutes = 60;
//             e1.ActivityDate = Date.newInstance(t.year(), t.month(), t.day());
//             e1.OwnerId = u1.id;
//             e1.IsScheduled__c = true;
//             e1.whatid__c = accDep.Id;
//             e1.CancelReason__c = '无合适病例（适合病例流失）OPD取消';
//             e1.task_id__c = t1.Id;            
//             e1.Opd_Plan__c = oPDPlan0.Id;
//             insert e1;

//             Event__c ec1 = new Event__c(
//                 Subject__c = 'test',
//                 StartDateTime__c = Datetime.newInstance(t.year(), t.month(), t.day(), 1, 0, 0),
//                 DurationInMinutes__c = 60,
//                 EndDateTime__c = Datetime.newInstance(t.year(), t.month(), t.day(), 2, 0, 0),
//                 ActivityDate__c = Date.newInstance(t.year(), t.month(), t.day()),
//                 delayToDate__c = Date.newInstance(t.year(), t.month(), t.day() +1),
//                 eventStatus__c = '取消',
//                 Event_ID__c = e1.Id,
//                 Daily_Report__c = dr.Id,
//                 nextPlanTimePurpose__c = 'test1,test2,test3,test4,test5,test6'
//             );
//             insert ec1;
            
//             PageReference pageRef1 = Page.XinDailyReport;
//             pageRef1.getParameters().put('id', dr.id);
//             Test.setCurrentPage(pageRef1);
//             tCon.IsAlertInputDep = true;
//             tCon.Alertlines = 'test';
//             tCon.init();
//             tCon.report  = dr;
//             tCon.addActivity();
//             //t.activities.get(0).act.Free_Input__c = true;
//             tCon.activities.get(0).act.Find_Imitations_Flag__c = true;
//             //t.activities.get(0).act.Activity_PurposeFSE__c = '装机';
//             tCon.activities.get(0).act.nextPlanDate__c = date.today() + 30;
//             tCon.delIndex = '0';
//             // System.Test.startTest();
//             // t.save();
//             tCon.cancelSave();
//             tCon.delaysave();
//             // t.report.Status__c = '承認';
//             //t.upsertActIndex = '0';
//             //t.activities.get(0).act.Purpose_TypeEscFSE__c = '休假';
//             //t.activities.get(0).act.Purpose_TypeFSE__c = '例会';
//             //t.activities.get(0).act.Free_Input__c = true;
//             tCon.save();
//             // System.Test.stopTest();
//             tCon.deleteActivity();
//             tCon.calcelActivity();
//         }

//     }
    // @isTest
    // static void test17 () {
    //     // レコードタイプ取得
    //     RecordType hospitalRec = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
    //     RecordType sectionRec = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 消化科'];
    //     RecordType departmentRec = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 消化科'];
        
    //     // 病院作成
    //     Account hospital = new Account();
    //     hospital.RecordTypeId = hospitalRec.Id;
    //     hospital.Name = 'TestHospital';
    //     insert hospital;
        
    //     // 病院を作ると戦略科室は、トリガーによって作られている
    //     Account section = [select Management_Code__c, Management_Code_Auto__c, Name, Id from Account where Parent.Id = :hospital.Id and RecordTypeId = :sectionRec.Id limit 1];
        
    //     // 診療科1を作成
    //     Account depart1 = new Account();
    //     depart1.RecordTypeId = departmentRec.Id;
    //     depart1.Name = '*';
    //     depart1.Department_Name__c  = 'TestDepart';
    //     depart1.ParentId = section.Id;
    //     depart1.Department_Class__c = section.Id;
    //     depart1.Hospital__c = hospital.Id;
    //     depart1.AgentCode_Ext__c = '9999997';
    //     insert depart1;

    //     ControllerUtil.EscapeNFM001Trigger = true;
    //     init();
    //     Date t = Date.today();
    //     System.runAs(u1) {
    //     XinDailyReportController tCon = new XinDailyReportController();
        
    //         // init2();
    //         XinDailyReportController xin1 = new XinDailyReportController();
    //         xin1.noSltList = new List<SelectOption>();
    //         xin1.hosptalList = new List<SelectOption>();
    //         xin1.salesList = new List<SelectOption>();
    //         xin1.outEventList = new List<SelectOption>();
    //         xin1.inEventList = new List<SelectOption>();
    //         xin1.moveList = new List<SelectOption>();
    //         xin1.vacationList = new List<SelectOption>();

    //         OPDPlan__c oPDPlan0 = new OPDPlan__c();
    //         oPDPlan0.Status__c = '审批中';
    //         oPDPlan0.OPDType__c = '事件';
    //         oPDPlan0.PlanProdDetail__c ='CV-290*4; GI-290镜子*1; GI-290镜子CF*1;';
    //         oPDPlan0.Account_Laboratory__c =depart1.Id;
    //         oPDPlan0.OPDPlan_ImplementDate__c = Date.today();
    //         oPDPlan0.OPDPlan_ImplementDate_temp__c = Date.today();
    //         oPDPlan0.NoOpp_Reason__c = 'HCP对应';
    //         oPDPlan0.RentalReson__c = '无法进入手术室';
    //         oPDPlan0.AttachmentCertificate__c = '0001.png';
    //         insert oPDPlan0;

    //         Daily_Report__c dr = new Daily_Report__c();
    //         dr.Reported_Date__c = Date.newInstance(t.year(), t.month(), t.day());
    //         dr.Status__c = '作成中';
    //         dr.OwnerId = u1.Id;
    //         dr.Reporter__c = u1.Id;
    //         dr.Working_Time_From__c = Datetime.newInstance(t.year(), t.month(), t.day(), 1, 0, 0);
    //         dr.Working_Time_To__c = Datetime.newInstance(t.year(), t.month(), t.day(), 10, 0, 0);
    //         insert dr;

    //         task__c t1 = new task__c();
    //         t1.Name = 'test';
    //         t1.taskDifferent__c = '主动任务';
    //         t1.Opd_Plan__c = oPDPlan0.Id;
    //         t1.delayToDate__c = Date.today().addMonths(1);
    //         t1.taskStatus__c = '02 接受';
    //         t1.delayReason__c = '无合适病例(适合病例流失)OPD延期';
    //         insert t1;


    //         Event e1 = new Event();
    //         e1.ActivityDateTime = Datetime.newInstance(t.year(), t.month(), t.day(), 1, 0, 0);
    //         e1.DurationInMinutes = 60;
    //         e1.ActivityDate = Date.newInstance(t.year(), t.month(), t.day());
    //         e1.OwnerId = u1.id;
    //         e1.IsScheduled__c = true;
    //         e1.whatid__c = accDep.Id;
    //         e1.delayReason__c = '无合适病例(适合病例流失)OPD延期';
    //         e1.task_id__c = t1.Id;
    //         e1.Opd_Plan__c = oPDPlan0.Id;
    //         e1.EventStatus__c =  '02 接受';
    //         insert e1;

    //         Event__c ec1 = new Event__c(
    //             Subject__c = 'test',
    //             StartDateTime__c = Datetime.newInstance(t.year(), t.month(), t.day(), 1, 0, 0),
    //             DurationInMinutes__c = 60,
    //             EndDateTime__c = Datetime.newInstance(t.year(), t.month(), t.day(), 2, 0, 0),
    //             ActivityDate__c = Date.newInstance(t.year(), t.month(), t.day()),
    //             delayToDate__c = Date.newInstance(t.year(), t.month(), t.day() +1),
    //             eventStatus__c = '取消',
    //             Event_ID__c = e1.Id,
    //             Daily_Report__c = dr.Id,
    //             nextPlanTimePurpose__c = 'test1,test2,test3,test4,test5,test6'
    //         );
    //         insert ec1;


    //         e1.EventC_ID__c = ec1.Id;
    //         update e1;

            
    //         PageReference pageRef1 = Page.XinDailyReport;
    //         pageRef1.getParameters().put('id', ec1.id);
    //         Test.setCurrentPage(pageRef1);
    //         tCon.IsAlertInputDep = true;
    //         tCon.Alertlines = 'test';
    //         tCon.init();
    //         tCon.addActivity();
    //         //t.activities.get(0).act.Free_Input__c = true;
    //         tCon.activities.get(0).act.Find_Imitations_Flag__c = true;
    //         //t.activities.get(0).act.Activity_PurposeFSE__c = '装机';
    //         tCon.activities.get(0).act.nextPlanDate__c = date.today() + 30;
    //         tCon.delIndex = '0';
    //         // System.Test.startTest();
    //         // t.save();
    //         tCon.cancelSave();
    //         tCon.delaysave();
    //         // t.report.Status__c = '承認';
    //         //t.upsertActIndex = '0';
    //         //t.activities.get(0).act.Purpose_TypeEscFSE__c = '休假';
    //         //t.activities.get(0).act.Purpose_TypeFSE__c = '例会';
    //         //t.activities.get(0).act.Free_Input__c = true;
    //         tCon.save();
    //         // System.Test.stopTest();
    //         tCon.deleteActivity();
    //         tCon.calcelActivity();
    //     }

    // }
    //2021-06-30  mzy  add 
    // 任务和日报管理 2020-05-25 update by vivek end 

}