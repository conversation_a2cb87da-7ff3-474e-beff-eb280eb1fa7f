<!--
  @description       : 
  <AUTHOR> <EMAIL>
  @group             : 
  @last modified on  : 07-08-2022
  @last modified by  : <EMAIL>
-->
<aura:component implements="force:lightningQuickActionWithoutHeader,force:hasRecordId,lightning:actionOverride,lightning:isUrlAddressable" access="global" 
                controller="NewAgencyContactController">
    
    <aura:attribute name = "recordId" type = "Id" default = ""/>
    <aura:attribute name="title" type="string"/>
    <aura:handler name="init" value="{!this}" action="{!c.doInit}" />
    <aura:attribute name="layout" type="LayoutDescriberHelper.LayoutWrapper"/>
    <aura:attribute name="record_data" type="Map"/>
    <aura:attribute name="section_names" type="List"/>
    <aura:attribute name="showSpinner" type="Boolean" default = "False"/>
    <aura:attribute name="staticResource" type="Map"/>
    <aura:attribute name="pi_fields_map" type="Map"/>  
    <aura:attribute name="isClone" type="Boolean" default = "False"/>
    <!-- <ltng:require scripts="{! $Resource.AWSService+'/AWSService.js' }" /> -->
    <ltng:require scripts="{! $Resource.jquery183minjs }" />
    <aura:registerEvent name="NewAgencyContactEvent" type="c:NewAgencyContactEvent"/>
    <aura:attribute name="agencyHospitalId" type="String"/>
    
	<div class="slds-modal__container">
        <!-- header -->
        <header class="slds-modal__header">
            <h2 id="modal-heading-01" class="slds-modal__title slds-hyphenate">
                {!v.title}
            </h2>
        </header>

        <div class="slds-modal__content slds-p-around_medium">
            <aura:if isTrue="{!v.showSpinner}">
                <lightning:spinner alternativeText="Loading" size="medium" />
            </aura:if>
            
            
            <lightning:recordEditForm objectApiName="Agency_Contact__c" onload="{!c.handleLoad}">
                
                <lightning:accordion activeSectionName="{! v.section_names }" allowMultipleSectionsOpen="true" class="greyyyy" >
                    <aura:iteration items="{!v.layout}" var="section">
                        <aura:if isTrue="{! section.editHeading }">
                            <lightning:accordionSection name="{! section.label }" label="{! section.label }">
                                
                                <aura:iteration items="{! section.layoutColumns}" var="col">
                                    <aura:iteration items="{! col.layoutItems}" var="field">
                                        <aura:if isTrue="{! field.behavior != 'Readonly' }">
                                        	<lightning:inputField required="{! field.behavior == 'Required' }" aura:id="{!field.field}"  fieldName="{! field.field}" value="{! field.value}" onchange="{!c.avoidPI}"/><!-- //deloitte-zhj 2023/07/18 防止选择***** -->
                                            
                                        </aura:if>
                                    </aura:iteration>
                                </aura:iteration>
                                
                                
                            </lightning:accordionSection>
                        </aura:if>
                    </aura:iteration>
                </lightning:accordion>
            </lightning:recordEditForm>
			
            <div  style="text-align:center;margin: 5px;">
                <lightning:button class="slds-button slds-button_neutral" label="取消" onclick="{! c.cancelClick }" />
                <lightning:button class="slds-button slds-button_brand" variant="brand" label="保存" onclick="{! c.saveClick }" />

            </div>
        </div>
    </div>
</aura:component>