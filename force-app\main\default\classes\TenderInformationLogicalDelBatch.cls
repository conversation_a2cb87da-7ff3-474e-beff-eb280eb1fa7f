global class TenderInformationLogicalDelBatch implements Database.Batchable<sObject> , Database.AllowsCallouts, Database.Stateful {
    public List <String> tenderIdList;
    public List<String> oppIdList;
    public String query;

    global TenderInformationLogicalDelBatch() {
        
    }

    //招标项目传进来的是 终止的招标项目
    global TenderInformationLogicalDelBatch(List<String> tenderIdList) {
        this.query = query;
        this.tenderIdList = tenderIdList;
    }

    global TenderInformationLogicalDelBatch(List<String> oppIdList,List<String> tenderIdList) {
        this.query = query;
        this.tenderIdList = tenderIdList;
        this.oppIdList = oppIdList;
    }

    global Database.QueryLocator start(Database.BatchableContext bc) {
        query = 'select Id,Retain_Tender__c from Tender_information__c where ';

        if(this.tenderIdList != null && this.tenderIdList.size()>0){
            query += 'Id IN :tenderIdList';
        }
        return Database.getQueryLocator(query);
    }
    global void execute(Database.BatchableContext BC, list<Tender_information__c> tenderList) {
        List<String> idList = new List<String>();
        List<Tender_information__c> updateList = new List<Tender_information__c>();
        for(Tender_information__c tender : tenderList){
            idList.add(tender.Retain_Tender__c);
        }
        Map<String,Tender_information__c> baoliuMap = new Map<String,Tender_information__c>([select Id from Tender_information__c where Id in: idList]);
        for(Tender_information__c tender : tenderList){
            Tender_information__c baoliu = baoliuMap.get(tender.Retain_Tender__c);
            TenderDeleteLwcController.compareTwo(tender,baoliu);
            updateList.add(baoliu);
        }
        if(updateList.size() > 0){
            update updateList;
        }
        
    }
    global void finish(Database.BatchableContext BC) {

    }
}