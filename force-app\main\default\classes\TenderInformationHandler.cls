//BEFORE -- 设置GI/SP 助理和5个战略科室所有人的共享
//NOW ----- 设置GI/SP助理 和5个医院下所有战略科室所有人的共享
// { 1. 根据OCSM管理省 给GI和SP助理  信息负责人(助理),行政窗口 ,备品窗口 赋值 信息负责人助理设置GI助理
//3.给关联医院的所有启用战略科室下的所有启用所有人设置销售共享 ,给招投标项目的行政窗口和备品窗口设置共享 ,给招投标项目的GI/SP助理设置共享}

//设置GI/SP/销售助理的值 和共享
public without sharing class TenderInformationHandler extends Oly_TriggerHandler {
    public static Boolean isFirst = true;
    private Map<Id, Tender_information__c> newMap;
    private Map<Id, Tender_information__c> oldMap;
    private List<Tender_information__c> newList;
    private List<Tender_information__c> oldList;


    public TenderInformationHandler() {
        this.newMap = (Map<Id, Tender_information__c>) Trigger.newMap;
        this.oldMap = (Map<Id, Tender_information__c>) Trigger.oldMap;
        this.newList = (List<Tender_information__c>) Trigger.new;
        this.oldList = (List<Tender_information__c>) Trigger.old;
    }
// chenjingwu 20250122 start
public void updateGovernmentPolicy(){
    for (Tender_information__c ten : newList) {
        if(ten.GovernmentPolicyManual__c != null){
            ten.GovernmentPolicy__c = ten.GovernmentPolicyManual__c;
            // chenjingwu 20250407 start
            // chenjingwu 20250318 start
        }else if(ten.Government_Purchase__c){
            ten.GovernmentPolicy__c = 'A.政府集采';
            // chenjingwu 20250318 end 
        // chenjingwu 20250401 start      
        }else if(ten.OthCentPurch__c){
            ten.GovernmentPolicy__c = 'B.其他集采';
        } 
        // chenjingwu 20250401 end
        else if(ten.Discountloan__c){
            ten.GovernmentPolicy__c = 'D.贴息贷款';
            // chenjingwu 20250407 end
        }
    }
}
// chenjingwu 20250122 end
    //根据新建的OCSM管理省字段，根据OCSM管理省对象GI，SP助理, 行政窗口 ,备品窗口  给这个招投标项目的GI，SP助理,行政窗口,备品窗口 ,信息负责人(助理)赋值；
    protected override void beforeInsert() {
        // chenjingwu 20250122 start
        updateGovernmentPolicy();
        // chenjingwu 20250122 end
        AssignValueToAssistant();
        // add 【委托】P-招标项目-手动创建的招标项目增加必填字段 2021/11/03 fxk Star
        for (Tender_information__c ten : newList) {
            if (ten.RecordTypeId == System.label.tender_1 || ten.RecordTypeId == System.label.tender_2) { //niwu  -0121m000000bKzA -01210000000VLZ8
                ten.OwnerId = System.label.tender_3;// niwu -00510000000gmxH
                if(ten.Hospital__c != null
                    || ten.Hospital1__c != null
                    || ten.Hospital2__c != null
                    || ten.Hospital3__c != null
                    || ten.Hospital4__c != null){
                    ten.IsRelateProject__c = '是';
                    ten.relativeTime__c = Datetime.now();
                }
                updateTenOwner();
            }
        }
        // add 【委托】P-招标项目-手动创建的招标项目增加必填字段 2021/11/03 fxk End
        checkIncludeDepart();//kk 20240813 新增包含科室check add
        updateSIShare();//lwt 更新SI相关
    }

    protected override void beforeUpdate() {
        // chenjingwu 20250122 start
         updateGovernmentPolicy();
         // chenjingwu 20250122 end
        // DB202406382590 招标项目：不应标申请里增加涉及科室 start
        checkIfNotAllBid();
        // DB202406382590 招标项目：不应标申请里增加涉及科室 end
        checkIncludeDepart();//kk 20240813 新增包含科室check add
        SetIsReactionOpp(); //lt DB202304062844 20230515 项目终止流程开发 add
        //反逻辑删除 fxk
        changeRelateOppDate();
        updateTenDel();
        AssignValueToAssistant();
        IsTerminateUpdateOpp();     //DB202404234371 20240506 是否终止 sx
        system.debug('===lalal==='+!StaticParameter.EscapeOtherUpdateTenOwner);
        //chenjingwu 20231221 start
        for (Tender_information__c info : newList) {
            if(!String.isBlank(info.Spot_Check_Result_Op__c) && String.isBlank(oldMap.get(info.Id).Spot_Check_Result_Op__c)){
                if(String.isBlank(info.SpotCheckByText__c)){
                    info.SpotCheckByText__c = UserInfo.getName();
                    info.SpotCheckDate__c = Datetime.now().date();
                }            
            }
          }
        //chenjingwu 20231221 end
        if (!StaticParameter.EscapeOtherUpdateTenOwner) {
            //DB202305613824 如果是主动更改所有人，那么通过战略科室就不能再更新所有人了，其他更新正常
            // for (Tender_information__c ten : newList) {
            //     system.debug(ten.status__c+'==='+ten.IFActiveUpdateOwner__c+'==laozhi==');
            //     if(ten.status__c=='04.待关联询价' && ten.IFActiveUpdateOwner__c==true){
            //     }else{
            //         updateTenOwner();
            //     }
              
            // }
            updateTenOwner();
        }else{
            // DB202305613824 you 如果是主动更改所有人，那么通过战略科室就不能再更新所有人了，其他更新正常
            for (Tender_information__c info : newList) {
              if((info.OpportunityNum__c == 0 || info.OpportunityNum__c == null) && info.ownerId != oldMap.get(info.Id).ownerId){
                info.IFActiveUpdateOwner__c = true;
              }
            }
        }
        // 20221011 ljh  SWAG-CK28WT start
        updateTenOwnerJC();
        // 20221011 ljh  SWAG-CK28WT end
        //20220804 you 询价任务
        updateOppotunityBeforeUpdate();
        List<Tender_information__c> tenderList = new List<Tender_information__c>();//20230918 lt DB202306372336 add
        //20230711 you DB202306372336 start 只有 做相关性确认，关联询价，增，删link，更改战略科室主担当人时会走
        //20240528 lt add || System.Label.TenderHomeWarnData == 1  --- 处理首页提醒历史数据使用
        
        //lt 20240703 首页提醒千里马入口重启首页提醒 start
        Boolean QLMWARNFLG = false; 
        for(Tender_information__c ten1: newList){
            if(ten1.ProjectRestartFLG__c && ten1.ProjectRestartFLG__c != oldMap.get(ten1.Id).ProjectRestartFLG__c){
                QLMWARNFLG = true;
                break;
            }
        }
        //lt 20240703 首页提醒千里马入口重启首页提醒 end
         
        //lt 20240703 首页提醒千里马入口重启首页提醒 add  || QLMWARNFLG
        if(!StaticParameter.EscapeTenderInformationUpdate || System.Label.TenderHomeWarnData == '1' || QLMWARNFLG){
            System.debug('202309191938========1');
            updateStrategic_department_MainId(this.newList);
        }
        //20230918 lt DB202306372336 start
        //2S6-2和管理员手动改可触发方法  /updateStrategic_department_MainId()方法加参数
        else if(System.Label.TenderWarnProfileId.contains(UserInfo.getProfileId())){
            for(Tender_information__c ten: newList){
                //20240517 lt 首页提醒优化 update : 加或条件
                if(ten.department_selection__c != oldMap.get(ten.Id).department_selection__c || 
                ten.department_selection1__c != oldMap.get(ten.Id).department_selection1__c || 
                ten.department_selection2__c != oldMap.get(ten.Id).department_selection2__c || 
                ten.department_selection3__c != oldMap.get(ten.Id).department_selection3__c || 
                ten.department_selection4__c != oldMap.get(ten.Id).department_selection4__c || 
                ten.Hospital__c != oldMap.get(ten.Id).Hospital__c || 
                ten.Hospital1__c != oldMap.get(ten.Id).Hospital1__c || 
                ten.Hospital2__c != oldMap.get(ten.Id).Hospital2__c || 
                ten.Hospital3__c != oldMap.get(ten.Id).Hospital3__c || 
                ten.Hospital4__c != oldMap.get(ten.Id).Hospital4__c){
                    System.debug('202309191938========2');
                    tenderList.add(ten);
                }
            }

            if(tenderList.Size() > 0){
                updateStrategic_department_MainId(tenderList);
            }
        }
        //20230918 lt DB202306372336 end
        //20230711 you DB202306372336 end
        updateSIShare();//lwt 更新SI相关
    }

    protected override void afterInsert() {
        // updateSIShare();//lwt 更新SI相关
        // 根据上面赋值的GI、SP助理,行政窗口,备品窗口，更新共享，共享原因不一样
        addShare();
        //sednMessage(); 20220729 you 招标项目 取消发送邮件通知
        // updateTenOwner();
    }
    protected override void afterUpdate() {
        // updateSIShare();//lwt 更新SI相关
        //20230918 lt add 条件 DB202308327700 跳过众成数据
        if(!StaticParameter.EscapeZCUpdateTender){
            addShare();
        }
                
        //sednMessage(); 20220729 you 招标项目 取消发送邮件通知
        updateWin();

        // 中标任务 废标流标时清空中标日
        //20221208 you DB202211594688
        //clearConfirmationofAward();

        //2022-3-29 yjk SWAG-CCL6R7
        //updateOpportunity();//2022-5-18 yjk XLIU-CEJ38N 注释掉 
    }
    //lwt 更新SI相关 start
    public void updateSIShare(){
        
        User userData = [SELECT Id, Profile.Name FROM User WHERE Id = :UserInfo.getUserId()];
        String userProfile = userData.Profile.Name;
        for (Tender_information__c record: newList) {


            Tender_information__c oldItem=null;
            if(oldMap!=null&&oldMap.containsKey(record.Id)){
                oldItem = oldMap.get(record.Id);
            }

            if( (oldItem!=null &&oldItem.SIShare_t__c)  || ( ( record.Keywords__c!=null && ( record.Keywords__c.contains('手术室') || record.Keywords__c.contains('数字化') || record.Keywords__c.contains('一体化')))   ||  ( record.InfoTitle__c!=null &&   ( record.InfoTitle__c.contains('手术室') || record.InfoTitle__c.contains('数字化') || record.InfoTitle__c.contains('一体化')) ) )){
                
                record.SIShare_t__c = true;
            }else{
                record.SIShare_t__c = false;
            }

            if(oldItem!=null && oldItem.SIShare__c!=record.SIShare__c){
                if(userProfile == '系统管理员'  && oldItem.SIShare__c==false && record.SIShare__c==true){
                    record.SIShare_t__c=true;
                }
            }else{
                if( record.SIShare__c || ( ( record.Keywords__c!=null && ( record.Keywords__c.contains('手术室') || record.Keywords__c.contains('数字化') || record.Keywords__c.contains('一体化')))   ||  ( record.InfoTitle__c!=null &&   ( record.InfoTitle__c.contains('手术室') || record.InfoTitle__c.contains('数字化') || record.InfoTitle__c.contains('一体化')) ))){
                    record.SIShare__c =true;
                }else{
                    record.SIShare__c =false;
                }
            }

            
        }
        // update newList;
    }
    //lwt 更新SI相关 end
    //修改项目阶段，由 结果变为其他，，相关任务取消
    public void updateOppotunityBeforeUpdate() {
        Set<String> Tenids = new Set<String>();//招标项目id，查询link
        Set<String> oppTens = new Set<String>();//招标项目id，查询link
        List<String> oppIds = new List<String>();//询价
        // 获得要更新的询价
        for (Tender_information__c record: newList) {
            //lt 20230425 招标终止 add  || ('批准'.equals(record.TerminateApprovalStatus__c) && !'批准'.equals((oldMap.get(record.Id).TerminateApprovalStatus__c)))
           if ( ( ('3-5：中标通知'.equals((oldMap.get(record.Id).subInfoType__c)) || '3-6：合同公告'.equals((oldMap.get(record.Id).subInfoType__c)) ) && ('3-1：废标公告'.equals(record.subInfoType__c)  || '3-2：流标公告'.equals(record.subInfoType__c) )) || (!'3：结果'.equals(record.InfoType__c) && '3：结果'.equals((oldMap.get(record.Id).InfoType__c))) || ('批准'.equals(record.TerminateApprovalStatus__c) && !'批准'.equals((oldMap.get(record.Id).TerminateApprovalStatus__c)))) {
                Tenids.add(record.id);
            }
        }
        Map<String,String> InfoTypeMap = new Map<String,String>(); //判断是否是因为中标修改得
        Map<String,String> TerminationStateMap = new Map<String,String>(); //lt 20230425 招标终止 add 
        if(null!=Tenids && Tenids.size()>0){
            //lt 20230425 招标终止 add Tender_information__r.TerminateApprovalStatus__c,
            List<Tender_Opportunity_Link__c> TenOppLinkList = [select id, Tender_information__c,Tender_information__r.InfoType__c, Tender_information__r.TerminateApprovalStatus__c, Opportunity__c from Tender_Opportunity_Link__c where Tender_information__c in :Tenids];
            if(null!=TenOppLinkList && TenOppLinkList.size()>0){
                for(Tender_Opportunity_Link__c topl :TenOppLinkList){
                    String oppid =String.valueOf(topl.Opportunity__c);
                    String tenid =String.valueOf(topl.Tender_information__c);
                    oppTens.add(oppid.subString(0,15)+tenid.subString(0,15));
                    if (!oppIds.contains(topl.Opportunity__c)) {
                        oppIds.add(topl.Opportunity__c);
                    }
                    //lt DB202304062844 20230515 项目终止流程开发 update if(!'3：结果'.equals(topl.Tender_information__r.InfoType__c)) --- if(!'3：结果'.equals(NewMap.get(topl.Tender_information__c).InfoType__c))
                    if(!'3：结果'.equals(NewMap.get(topl.Tender_information__c).InfoType__c)){
                       InfoTypeMap.put(topl.Opportunity__c,topl.Tender_information__c); 
                    }   
                    //lt 20230425 招标终止 start
                    System.debug('---lt123---topl.Tender_information__r.TerminateApprovalStatus__c:'+topl.Tender_information__r.TerminateApprovalStatus__c);
                    if('批准'.equals(newMap.get(topl.Tender_information__c).TerminateApprovalStatus__c)){
                        TerminationStateMap.put(topl.Opportunity__c,topl.Tender_information__c); 
                     }
                    //lt 20230425 招标终止 end
                }
            }
        }
        if (null!=oppTens && oppTens.size()>0) {
            //20221208 you DB202211594688 有确认任务的询价不清
            List<Opportunity> opportunities = [select id, Bidding_Project_Name_Bid__c, Opp_Order__c,ConfirmationofAward__c from Opportunity where id in :oppIds];
            //lt 20230425 招标终止 add Tender_information_Task__r.TerminateApprovalStatus__c   20230510 add and taskStatus__c <> '04 取消'
            //lt DB202304062844 20230515 项目终止流程开发 update  taskStatus__c <> '03 完成' and taskStatus__c <> '04 取消'  ---  taskStatus__c = '02 接受'
            List<task__c> taskList = [select id,taskStatus__c,RecordType.Name,Tender_information_Task__c,
                                            OpportunityId__c,Tender_information_Task__r.TerminateApprovalStatus__c,
                                            OpportunityId__r.ConfirmationofAward__c,
                                            OpportunityId__r.StageName,OpportunityId__r.Cnt_Lost_cancel_report__c //20231206 lt 任务清空新加条件 询价中 且Cnt_Lost_cancel_report__c=0 start  add
                                    from task__c 
                                    where ((RecordType.Name ='失单报告任务' and Opp_Tender__c in :oppTens) 
                                    or (RecordType.Name ='中标结果确认' and Opp_Tender__c in :oppTens)) 
                                    // and taskStatus__c = '02 接受'   //20231017 lt DB202308447473
                                    ];
            for(task__c tsk : taskList){
                System.debug('---lt123---TerminationStateMap:'+TerminationStateMap);
                System.debug('---lt123---tsk.Tender_information_Task__r.TerminateApprovalStatus__c:'+tsk.Tender_information_Task__r.TerminateApprovalStatus__c);
                //lt DB202304062844 20230515 项目终止流程开发 update !='03 完成' --- == '02 接受'
                if(tsk.RecordType.Name =='失单报告任务' && oppIds.contains(tsk.OpportunityId__c) && tsk.taskStatus__c == '02 接受'){
                  //不做操作
                  //20231007 lt DB202308447473  询价关联的招标项目申请项目终止后清空条件 start 
                  if(null!=TerminationStateMap && TerminationStateMap.containsKey(tsk.OpportunityId__c)){
                    tsk.taskStatus__c = '04 取消';
                    tsk.cancelDate__c = date.today();
                    tsk.cancelReasonSelect__c = '项目终止';
                  }
                  //20231007 lt DB202308447473  询价关联的招标项目申请项目终止后清空条件 end 
                  //20231017 lt DB202308447473  addif(tsk.RecordType.Name =='中标结果确认' && oppIds.contains(tsk.OpportunityId__c) && tsk.taskStatus__c == '02 接受')
                }else if(tsk.RecordType.Name =='中标结果确认' && oppIds.contains(tsk.OpportunityId__c) && tsk.taskStatus__c == '02 接受'){
                    tsk.taskStatus__c = '04 取消';
                    tsk.cancelDate__c = date.today();
                    if(null!=InfoTypeMap && InfoTypeMap.containsKey(tsk.OpportunityId__c)){
                       tsk.cancelReasonSelect__c = '修改项目阶段';
                    }
                    //lt 20230425 招标终止 start
                    else if(null!=TerminationStateMap && TerminationStateMap.containsKey(tsk.OpportunityId__c)){
                        tsk.cancelReasonSelect__c = '项目终止';
                    }
                    //lt 20230425 招标终止 end
                    else{
                       tsk.cancelReasonSelect__c = '流标/废标'; 
                    }
                }
            }
            update taskList;
            //20230728.......。。。20230808 start 
            //lt DB202304062844 20230515 项目终止流程开发  start
            // //if(taskList.size() > 0){
            // if(opportunities.size() > 0){
            //     for (Opportunity opp : opportunities) {
            //         Boolean upd_flg = null!=TerminationStateMap && TerminationStateMap.containsKey(opp.Id) && opp.ConfirmationofAward__c == '竞争对手中标' ? false : true;
            //         if (!upd_flg) {
            //             continue;
            //         }
            //         for (task__c tlink : taskList) {
            //             if (opp.Id == tlink.OpportunityId__c) {
            //                 //lt DB202304062844 20230515 项目终止流程开发 update !='03 完成' --- == '02 接受'
            //                 if(tlink.RecordType.Name =='失单报告任务' && tlink.taskStatus__c == '02 接受'){
            //                     upd_flg = false;
            //                     break;
            //                   //不做操作
            //                 // }else{
            //                 //    //20220718 you 询价任务 start
            //                 //     opp.ConfirmationofAward__c = null;
            //                 //     opp.Task_createTime__c = null;
            //                 //     opp.ConfirmationofAward_createTime__c =null;
            //                 //     opp.LostTask_comfirmTime__c =null;
            //                 //     opp.Is_ConfirmationofAward__c =null;
            //                 //     opp.LostTask_createTime__c =null;
            //                 //     //lt DB202304062844 20230515 项目终止流程开发 start
            //                 //     if(tlink.Tender_information_Task__r.TerminateApprovalStatus__c == '批准' && opp.ConfirmationofAward__c == 'OLY中标'){
            //                 //         opp.Closing_Bid_Date__c = null;
            //                 //     }
            //                 //     //lt DB202304062844 20230515 项目终止流程开发  end
            //                 //     //opp.Closing_Bid_Date__c = null;
            //                 //     //20220718 you 询价任务 end
            //                 }
            //             }
            //         }
            //         if (upd_flg) {
            //             //lt DB202304062844 20230515 项目终止流程开发 start
            //             if(null!=TerminationStateMap && TerminationStateMap.containsKey(opp.Id) && opp.ConfirmationofAward__c == 'OLY中标'){
            //                 opp.Closing_Bid_Date__c = null;
            //             } 
            //             //lt DB202304062844 20230515 项目终止流程开发 end
            //             opp.ConfirmationofAward__c = null;
            //             opp.Task_createTime__c = null;
            //             opp.ConfirmationofAward_createTime__c =null;
            //             opp.LostTask_comfirmTime__c =null;
            //             opp.Is_ConfirmationofAward__c =null;
            //             opp.LostTask_createTime__c =null;
            //         } 
            //     }
            // } 
            //lt DB202304062844 20230515 项目终止流程开发 end
            Opportunity opp = new Opportunity();
            Boolean upd_opp_flg = false;   //20230927 lt DB202309512420 项目终止审批报错 add
            if(taskList.size() > 0){
                for (task__c tlink : taskList){
                    opp.Id = tlink.OpportunityId__c;
                    System.debug('test20230927---taskList:'+taskList);
                    //20231206 lt 任务清空新加条件 询价中 且Cnt_Lost_cancel_report__c=0 start   add if条件
                    if(tlink.OpportunityId__r.Cnt_Lost_cancel_report__c == 0 && tlink.OpportunityId__r.StageName == '引合'){
                        if(tlink.RecordType.Name =='失单报告任务'){
                            //20231007 lt DB202308447473  询价关联的招标项目申请项目终止后清空条件 start 
                            // && tlink.taskStatus__c == '02 接受'  //20231017 lt DB202308447473 delete
                            // opp.Id = tlink.OpportunityId__c;
                            if(null!=TerminationStateMap && TerminationStateMap.containsKey(opp.Id)){
                                System.debug('test20231008--进---');
                                upd_opp_flg = true;
                                opp.Closing_Bid_Date__c = null;
                                opp.ConfirmationofAward__c = null;
                                opp.Task_createTime__c = null;
                                opp.ConfirmationofAward_createTime__c =null;
                                opp.LostTask_comfirmTime__c =null;
                                opp.Is_ConfirmationofAward__c =null;
                                opp.LostTask_createTime__c =null;
                            }
                            //20231007 lt DB202308447473  询价关联的招标项目申请项目终止后清空条件 end 
                            // continue;
    
                            //20231018 lt DB202308447473  询价关联的招标项目申请项目终止后清空条件 update else if
                            //add  if(tlink.RecordType.Name =='中标结果确认' && tlink.taskStatus__c != '03 完成')
                        }else if(tlink.RecordType.Name =='中标结果确认' && (tlink.taskStatus__c != '03 完成' || (tlink.taskStatus__c == '03 完成' && null!=TerminationStateMap && TerminationStateMap.containsKey(opp.Id)))){
                            // opp.Id = tlink.OpportunityId__c;
                            upd_opp_flg = true; //20230927 lt DB202309512420 项目终止审批报错 add
                            if(null!=TerminationStateMap && TerminationStateMap.containsKey(opp.Id) && tlink.OpportunityId__r.ConfirmationofAward__c == 'OLY中标'){
                                opp.Closing_Bid_Date__c = null;
                            } 
                            opp.ConfirmationofAward__c = null;
                            opp.Task_createTime__c = null;
                            opp.ConfirmationofAward_createTime__c =null;
                            opp.LostTask_comfirmTime__c =null;
                            opp.Is_ConfirmationofAward__c =null;
                            opp.LostTask_createTime__c =null;
                        }
                    }
                }
                StaticParameter.EscapeOppandStaTrigger = true;
                System.debug('test20230927---opp:'+opp);
                if(upd_opp_flg){ //20230927 lt DB202309512420 项目终止审批报错 add
                    update opp;
                }
                StaticParameter.EscapeOppandStaTrigger = false;
            }
            
            //20230728.......。。。20230808 end
            // StaticParameter.EscapeOppandStaTrigger = true; //lt DB202304062844 20230515 项目终止流程开发 跳过询价trigger start 
            // update opportunities;   //lt 注释 DB202308447473
            // StaticParameter.EscapeOppandStaTrigger = false; //lt DB202304062844 20230515 项目终止流程开发 end
        }
    }    
    //20220718 you 询价任务 end
    // 20221027 updateOpportunity() 全文搜索过 没有调用的，方法引用已经注释，覆盖率不够方法体也注释（SWAG-CKL5UC）,
    //2022-3-29 yjk SWAG-CCL6R7
    /*private void updateOpportunity(){
        for(Tender_information__c newOne : newList){
            Tender_information__c oldOne = oldMap.get(newOne.id);
            if(oldOne.ResultDate__c == null && newOne.ResultDate__c != null){
                List<Opportunity> oppList = [select id,Closing_Bid_Date__c  from Opportunity where Bidding_Project_Name_Bid__c = :newOne.id];
                if(oppList.size() > 0){
                    for(Opportunity opp : oppList){
                        opp.Closing_Bid_Date__c = newOne.ResultDate__c;
                    }
                    update oppList;
                }
            }
        }
    }*/
    //20220729 you 
    //public void sednMessage() {
    //    //规则条件
    //    //ISCHANGED(InfoType__c) && Text(InfoType__c) = '3：结果' && (Text(IsBid__c) = '是' || ( Text(IsBid__c) = '否' && OpportunityNum__c != null && OpportunityNum__c > 0) )
    //    // 1）没有关联询价，确认相关性，那么就发给GI和SP助理；
    //    // 2）关联询价后，询价的所有人、项目所有人，GI和SP助理，收邮件；
    //    //关联询价 发给询价所有人 招标项目所有人
    //    //没有关联询价并且做了相关性确认 发给招标项目所有人

    //    //1.判断是否符合大条件 拿到招标id
    //    Set<Id> zbIds = new Set<Id>();
    //    //拿到招标id , 招标所有人 GI/SP助理 的邮件地址
    //    Map<Id, Set<String>> zbOppMap = new Map<Id, Set<String>>();
    //    List<Tender_information__c> tlist = new List<Tender_information__c>();

    //    for (Tender_information__c ten : newList) {
    //        // if (ten.InfoType__c == '3：结果' &&
    //        //     (ten.IsBid__c == '是' || (ten.IsBid__c == '否' && ten.OpportunityNum__c != null && ten.OpportunityNum__c > 0))
    //        //     && (Trigger.isInsert || (Trigger.isUpdate && oldMap.get(ten.Id).InfoType__c != '3：结果'))) {
    //        //     zbIds.add(ten.Id);
    //        // }
    //        // 判断条件修改
    //        if (ten.InfoType__c == '3：结果' && (Trigger.isInsert || (Trigger.isUpdate && oldMap.get(ten.Id).InfoType__c != '3：结果'))) {
    //            // 如果招标所有人为系统管理员（OlympusSystem）时需要跳过
    //            if (ten.Ownerid != '00510000000gmxH') {
    //                zbIds.add(ten.Id);
    //            }
    //        }
    //    }
    //    if (zbIds.size() > 0) {
    //        tlist = [select Id, GI_assistant__r.Email, SP_assistant__r.Email, Hospital__r.Name, BudgetAmount__c, Name, department__r.Name, OpenBidingTime__c, Owner.Alias, Owner.Email from Tender_information__c where id in :zbIds];
    //        for (Tender_information__c ten : tlist) {
    //            if (!zbOppMap.containsKey(ten.Id)) {
    //                zbOppMap.put(ten.Id, new Set<String>());
    //            }
    //            zbOppMap.get(ten.Id).add(ten.Owner.Email);

    //        }
    //        //取得询价
    //        // 招标-询价关联修改 20210817 start
    //        // List<Opportunity> oppList = new List<Opportunity>();
    //        // oppList = [select id,Owner.Email,Bidding_Project_Name_Bid__c,Bidding_Project_Name_Bid__r.owner.Email from Opportunity where Bidding_Project_Name_Bid__c in :zbIds];
    //        // if (oppList.size() > 0) {
    //        //     for (Opportunity opp : oppList) {
    //        //         // 能走到这说明 询价的所有人、项目所有人 也得发邮件
    //        //         if (!zbOppMap.containsKey(opp.Bidding_Project_Name_Bid__c)) {
    //        //             zbOppMap.put(opp.Bidding_Project_Name_Bid__c, new Set<String>());
    //        //         }
    //        //         zbOppMap.get(opp.Bidding_Project_Name_Bid__c).add(opp.Owner.Email);
    //        //         // zbOppMap.get(opp.Bidding_Project_Name_Bid__c).add(opp.Bidding_Project_Name_Bid__r.owner.Email);
    //        //     }
    //        // }
    //        List<Tender_Opportunity_Link__c> link_list = [select id, Tender_information__c, Opportunity__r.Owner.Email from Tender_Opportunity_Link__c where Tender_information__c in :zbIds];
    //        if (link_list != null && link_list.size() > 0) {
    //            for (Tender_Opportunity_Link__c link : link_list) {
    //                // 能走到这说明 询价的所有人、项目所有人 也得发邮件
    //                if (!zbOppMap.containsKey(link.Tender_information__c)) {
    //                    zbOppMap.put(link.Tender_information__c, new Set<String>());
    //                }
    //                zbOppMap.get(link.Tender_information__c).add(link.Opportunity__r.Owner.Email);
    //            }
    //        }
    //        // 招标-询价关联修改 20210817 end
    //    }

    //    if (zbOppMap.size() > 0) {
    //        if (tlist.size() > 0) {
    //            List<Messaging.SingleEmailMessage> sendMails = new List<Messaging.SingleEmailMessage>();

    //            for (Tender_information__c ten : tlist) {
    //                if (zbOppMap.containsKey(ten.Id)) {
    //                    String body = '';
    //                    String title = '';
    //                    String BudgetAmount = ten.BudgetAmount__c == null ? '' : ten.BudgetAmount__c + '';
    //                    String HospitalName = ten.Hospital__c == null ? '' : ten.Hospital__r.Name;
    //                    String departmentName = ten.department__c == null ? '' : ten.department__r.Name;
    //                    String OpenBidingTime = ten.OpenBidingTime__c == null ? '' : ten.OpenBidingTime__c.format();

    //                    title = '中标结果：' + ten.Hospital__r.Name + ' 预算金额' + BudgetAmount + '已有中标结果请确认';

    //                    body += '项目名：' + ten.Name + '<br/>';
    //                    body += '预算金额：' + BudgetAmount + '<br/>';
    //                    body += '医院：' + HospitalName + '<br/>';
    //                    // body += '战略科室：'+departmentName + '<br/>';
    //                    body += '中标日：' + OpenBidingTime + '<br/>';
    //                    body += '主担当：' + ten.Owner.Alias + '<br/>';
    //                    body += '招投标链接： <br/>';
    //                    body += '<a href="' + URL.getSalesforceBaseUrl().toExternalForm() + '/' + ten.Id  + '">' + URL.getSalesforceBaseUrl().toExternalForm() + '/' + ten.Id + '</a><br/>';
    //                    // body += '收件人: '+zbOppMap.get(ten.Id);

    //                    List<String> toMailList = new List<String>();
    //                    // List<String> toccList = new List<String>();
    //                    // emas.add(UserInfo.getUserEmail());
    //                    //收信人
    //                    toMailList.addAll(zbOppMap.get(ten.Id));
    //                    // if (toMailList.contains(null)) {
    //                    // toMailList.remove(null);
    //                    // }
    //                    // toMailList.add('<EMAIL>');
    //                    //抄送人
    //                    // toccList.add('<EMAIL>');
    //                    // toccList.add('<EMAIL>');
    //                    Messaging.SingleEmailMessage messageNEW = new Messaging.SingleEmailMessage();
    //                    messageNEW.subject = title;
    //                    //messageNEW.plainTextBody = body;
    //                    messageNEW.htmlBody = body;
    //                    messageNEW.setCharset('UTF-8');
    //                    messageNEW.toAddresses = toMailList;
    //                    // messageNEW.ccAddresses = toccList;
    //                    sendMails.add(messageNEW);
    //                }
    //            }

    //            if (sendMails.size() > 0) {
    //                for (Messaging.SingleEmailMessage mc : sendMails) {
    //                    List<Messaging.SingleEmailMessage> tempsendMails = new List<Messaging.SingleEmailMessage>();
    //                    tempsendMails.add(mc);  
    //                    Messaging.SendEmailResult[] results = null;                      
    //                    if (!NFMUtil.isSandbox()) {
    //                        results = messaging.sendEmail(tempsendMails);
    //                    }
    //                    if (results != null && results.size() > 0) {
    //                        for (Integer i = 0; i < results.size(); i++) {
    //                            if (results[i].success == false) {
    //                                System.debug('邮件:::' + mc.getSubject() + '发送失败');
    //                            } else {
    //                                System.debug('邮件:::' + mc.getSubject() + '发送成功');
    //                            }
    //                        }
    //                    }
    //                }
    //            }
    //        }
    //    }
    //}
//20230711 you DB202306372336 start 
//20230918 lt DB202306372336 update 加参数
    public void updateStrategic_department_MainId(List<Tender_information__c> tenderupList){
        //20240517 lt 首页提醒优化

        //关联战略科室Map:DepartmentSelectionMap start  - 对应字段department_selection__c
        Map<String, String> DepartmentSelectionMap = new Map<String, String>();
        DepartmentSelectionMap.put('妇科',System.Label.tender_8);
        DepartmentSelectionMap.put('耳鼻喉科',System.Label.tender_9);
        DepartmentSelectionMap.put('消化科',System.Label.tender_4);
        DepartmentSelectionMap.put('泌尿科',System.Label.tender_7);
        DepartmentSelectionMap.put('呼吸科',System.Label.tender_5);
        DepartmentSelectionMap.put('普外科',System.Label.tender_6);
        DepartmentSelectionMap.put('其他',System.Label.tender_6);   //其他归普外
        //关联战略科室Map:DepartmentSelectionMap end

        //tenderIds:需要进行首页提醒的招标项目id,之后用于检索link
        List<String> tenderIds = new List<String>();
        //将“关联战略科室/1-4”赋值给“关联战略科室主担当id/1-4”
        //关联医院与关联战略科室1对1配套，Map里为招标项目对应的关联医院的关联战略科室
        //TenHpDepartmentMap:MapⅠ<tender.id , hpDepartmentMap:MapⅡ<hp.id , department_selection__c>>
        Map<String, Map<String, String>> TenHpDepartmentMap = new Map<String, Map<String, String>>();
        //待更新的<hp.id , 比对后的关联战略科室>
        Map<String, String> UpdHpDepartmentMap = new Map<String, String>();

        String zhanluekeshiAPI = System.Label.tender_4 + ';' + System.Label.tender_5 + ';' + System.Label.tender_6 + ';' + System.Label.tender_7 + ';' + System.Label.tender_8 + ';' + System.Label.tender_9 ;
        String zhanluekeshiLABEL = '消化科;泌尿科;妇科;普外科;耳鼻喉科;呼吸科' ;

        for (Tender_information__c ten : tenderupList) {
            tenderIds.add(ten.Id);
            
            Map<String, String> hpDepartmentMap = new Map<String, String>();
            if(ten.Hospital__c != null){
                if(ten.department_selection__c == null){
                    hpDepartmentMap.put(ten.Hospital__c,zhanluekeshiAPI);
                }else{
                    hpDepartmentMap.put(ten.Hospital__c,ten.department_selection__c);
                }
            }
            if(ten.Hospital1__c != null){
                if(ten.department_selection1__c == null){
                    hpDepartmentMap.put(ten.Hospital1__c,zhanluekeshiLABEL);
                }else{
                    hpDepartmentMap.put(ten.Hospital1__c,ten.department_selection1__c);
                }
            }
            if(ten.Hospital2__c != null){
                if(ten.department_selection2__c == null){
                    hpDepartmentMap.put(ten.Hospital2__c,zhanluekeshiLABEL);
                }else{
                    hpDepartmentMap.put(ten.Hospital2__c,ten.department_selection2__c);
                }
            }
            if(ten.Hospital3__c != null){
                if(ten.department_selection3__c == null){
                    hpDepartmentMap.put(ten.Hospital3__c,zhanluekeshiLABEL);
                }else{
                    hpDepartmentMap.put(ten.Hospital3__c,ten.department_selection3__c);
                }
            }
            if(ten.Hospital4__c != null){
                if(ten.department_selection4__c == null){
                    hpDepartmentMap.put(ten.Hospital4__c,zhanluekeshiLABEL);
                }else{
                    hpDepartmentMap.put(ten.Hospital4__c,ten.department_selection4__c);
                }
            }

            TenHpDepartmentMap.put(ten.Id,hpDepartmentMap);
        }
        
        if (tenderIds.size() > 0) {
            List<Tender_Opportunity_Link__c> linkList =
                [SELECT id,
                 Tender_information__c, 
                 Tender_information__r.ProjectRestartFLG__c, //lt
                 Tender_information__r.Hospital__c, 
                 Tender_information__r.Hospital1__c, 
                 Tender_information__r.Hospital2__c, 
                 Tender_information__r.Hospital3__c, 
                 Tender_information__r.Hospital4__c, 
                 Tender_information__r.department_selection__c, 
                 Tender_information__r.department_selection1__c, 
                 Tender_information__r.department_selection2__c, 
                 Tender_information__r.department_selection3__c, 
                 Tender_information__r.department_selection4__c, 
                 Opportunity__c, 
                 Opportunity__r.Hospital__c, 
                 Opportunity__r.StageName, 
                 Opportunity__r.Department_Class__r.Department_Class_Label__c 
                 FROM Tender_Opportunity_Link__c
                 WHERE Tender_information__c IN :tenderIds 
                 AND isDeleted =false 
                 //lt 20240529 注释 注释原因：DB202401275115
                 //直接关联取消的询价，项目不提醒  ； 项目重启标记true 时 关联取消的询价，项目提醒
                //  AND Opportunity__r.StageName != '削除'//lt 20240122 DB202401275115 
                ];

            if (linkList != null && linkList.Size() > 0) {
                //hpDepartmentMap1接hpDepartmentMap : hpDepartmentMap1:<hp.id , department_selection__c>
                // Map<String, String> hpDepartmentMap1 = new Map<String, String>();

                for (Tender_Opportunity_Link__c link : linkList) {

                    //lt 20240529 DB202401275115 start
                    //直接关联取消的询价，项目不提醒  ； 项目重启标记true 时 关联取消的询价，项目提醒
                    if(link.Tender_information__r.ProjectRestartFLG__c && link.Opportunity__r.StageName == '削除'){
                        continue;
                    }
                    //lt 20240529 DB202401275115 end

                    //询价的战略科室分类，其他算在普外
                    String Opp_Department_Class = link.Opportunity__r.Department_Class__r.Department_Class_Label__c == '其他' ? '普外科' : link.Opportunity__r.Department_Class__r.Department_Class_Label__c;
                    String Opp_Department_Class1 = DepartmentSelectionMap.get(link.Opportunity__r.Department_Class__r.Department_Class_Label__c);
                    //招标项目 待提醒的 关联战略科室
                    String Ten_Department_Selection = null;
                    String Ten_Department_Selection1 = null;

                    //用link的Tender_information__c取mapⅡ
                    if(TenHpDepartmentMap.containsKey(link.Tender_information__c)){
                        Map<String, String> hpDepartmentMap1 = TenHpDepartmentMap.get(link.Tender_information__c);
                        if(hpDepartmentMap1.containsKey(link.Opportunity__r.Hospital__c)){
                            String department = hpDepartmentMap1.get(link.Opportunity__r.Hospital__c);
                            department = department.replace(Opp_Department_Class, '').replace(Opp_Department_Class1, '').replace(';;',';');
                            hpDepartmentMap1.put(link.Opportunity__r.Hospital__c, department);
                            TenHpDepartmentMap.put(link.Tender_information__c, hpDepartmentMap1);
                        }
                    }
                }
            } 

            //循环MapⅠ的key 根据key从NewMap里拿到newList，给newList的“关联战略科室主担当id/1-4”赋值
            //UpdHpDepartmentMap<hp.id , 比对后的关联战略科室> 写回newList的“关联战略科室主担当id/1-4”
            // Map<String, String> hpDepartmentMap2 = new Map<String, String>();
            for(String tenId : TenHpDepartmentMap.keySet()){
                if(newMap.containsKey(tenId)){
                    Tender_information__c tender = newMap.get(tenId);
                    Map<String, String> hpDepartmentMap2 = TenHpDepartmentMap.get(tenId);
                    // DB202406382590 招标项目：不应标申请里增加涉及科室 start
                    String notBidsS = lexTransferRoomCompoentController.getApiIdByValue(tender.notBidsSelection__c);
                    System.debug('444==>'+notBidsS);
                    String[] notBidsSelection = String.isNotBlank(notBidsS) ? notBidsS.split(';') : new String[]{};
                    String[] notBidsSelection1 = String.isNotBlank(tender.notBidsSelection1__c) ? tender.notBidsSelection1__c.split(';') : new String[]{};
                    String[] notBidsSelection2 = String.isNotBlank(tender.notBidsSelection2__c) ? tender.notBidsSelection2__c.split(';') : new String[]{};
                    String[] notBidsSelection3 = String.isNotBlank(tender.notBidsSelection3__c) ? tender.notBidsSelection3__c.split(';') : new String[]{};
                    String[] notBidsSelection4 = String.isNotBlank(tender.notBidsSelection4__c) ? tender.notBidsSelection4__c.split(';') : new String[]{};
                    System.debug('555==>'+notBidsS);
                    String StrategicMainId = hpDepartmentMap2 != null && String.isNotBlank(tender.Hospital__c)? hpDepartmentMap2.get(tender.Hospital__c) : null;
                    String StrategicMainId1 = hpDepartmentMap2 != null && String.isNotBlank(tender.Hospital1__c) ? hpDepartmentMap2.get(tender.Hospital1__c) : null;
                    String StrategicMainId2 = hpDepartmentMap2 != null && String.isNotBlank(tender.Hospital2__c) ? hpDepartmentMap2.get(tender.Hospital2__c) : null;
                    String StrategicMainId3 = hpDepartmentMap2 != null && String.isNotBlank(tender.Hospital3__c) ? hpDepartmentMap2.get(tender.Hospital3__c) : null;
                    String StrategicMainId4 = hpDepartmentMap2 != null && String.isNotBlank(tender.Hospital4__c) ? hpDepartmentMap2.get(tender.Hospital4__c) : null; 
                    System.debug('666==>'+StrategicMainId);
                    for (String nbs : notBidsSelection) {
                        StrategicMainId = String.isNotBlank(StrategicMainId) ? StrategicMainId.replace(nbs, '').replace(';;',';') : null;
                    }
                    System.debug('777==>'+StrategicMainId);
                    for (String nbs : notBidsSelection1) {
                        StrategicMainId1 = String.isNotBlank(StrategicMainId1) ? StrategicMainId1.replace(nbs, '').replace(';;',';') : null;
                    }
                    for (String nbs : notBidsSelection2) {
                        StrategicMainId2 = String.isNotBlank(StrategicMainId2) ? StrategicMainId2.replace(nbs, '').replace(';;',';') : null;
                    }
                    for (String nbs : notBidsSelection3) {
                        StrategicMainId3 = String.isNotBlank(StrategicMainId3) ? StrategicMainId3.replace(nbs, '').replace(';;',';') : null;
                    }
                    for (String nbs : notBidsSelection4) {
                        StrategicMainId4 = String.isNotBlank(StrategicMainId4) ? StrategicMainId4.replace(nbs, '').replace(';;',';') : null;
                    }

                    tender.Strategic_department_MainId__c = StrategicMainId;
                    tender.Strategic_department_MainId1__c = StrategicMainId1;
                    tender.Strategic_department_MainId2__c = StrategicMainId2;
                    tender.Strategic_department_MainId3__c = StrategicMainId3;
                    tender.Strategic_department_MainId4__c = StrategicMainId4; 
                    // tender.Strategic_department_MainId__c = hpDepartmentMap2 != null && String.isNotBlank(tender.Hospital__c)? hpDepartmentMap2.get(tender.Hospital__c) : null;
                    // tender.Strategic_department_MainId1__c = hpDepartmentMap2 != null && String.isNotBlank(tender.Hospital1__c) ? hpDepartmentMap2.get(tender.Hospital1__c) : null;
                    // tender.Strategic_department_MainId2__c = hpDepartmentMap2 != null && String.isNotBlank(tender.Hospital2__c) ? hpDepartmentMap2.get(tender.Hospital2__c) : null;
                    // tender.Strategic_department_MainId3__c = hpDepartmentMap2 != null && String.isNotBlank(tender.Hospital3__c) ? hpDepartmentMap2.get(tender.Hospital3__c) : null;
                    // tender.Strategic_department_MainId4__c = hpDepartmentMap2 != null && String.isNotBlank(tender.Hospital4__c) ? hpDepartmentMap2.get(tender.Hospital4__c) : null;   
                    // DB202406382590 招标项目：不应标申请里增加涉及科室 end                 
                }
                
            }
            
        }
    }


//20230711 you  DB202306372336 end
    public void addShare() {
        //1.获得医院
        Set<Id> hpIdsets = new Set<Id>();
        for (Tender_information__c info : newList) {
            if (info.Hospital__c != null) {
                hpIdsets.add(info.Hospital__c);
            }
            if (info.Hospital1__c != null) {
                hpIdsets.add(info.Hospital1__c);
            }
            if (info.Hospital2__c != null) {
                hpIdsets.add(info.Hospital2__c);
            }
            if (info.Hospital3__c != null) {
                hpIdsets.add(info.Hospital3__c);
            }
            if (info.Hospital4__c != null) {
                hpIdsets.add(info.Hospital4__c);
            }

        }

        if (hpIdsets.contains(null)) {
            hpIdsets.remove(null);
        }

        List<Account> accList = new List<Account>();
        if (hpIdsets.size() > 0) {
            //获取医院下所有启用战略科室的启用所有人
            //取得战略科室:
            // accList = [select id,OwnerId,ParentId,OwnerIsActive__c from Account where Parent.RecordType_DeveloperName__c = 'HP'
            //                                          AND OwnerIsActive__c = true
            //                                          AND ParentId  in :hpIdsets];
            //获取医院上的主担当
            accList = [select id, GI_Main__c, GI_Main__r.IsActive, BF_owner__c, BF_owner__r.IsActive,
                       ET_owner__c, ET_owner__r.IsActive, SP_Main__c, SP_Main__r.IsActive, URO_owner_ID__c, URO_owner_ID__r.IsActive,
                       GYN_owner__c, GYN_owner__r.IsActive, ENT_owner_ID__c, ENT_owner_ID__r.IsActive, Energy_LeaderStr__c
                       from Account where  Id in :hpIdsets ];
            // updateTenOwner(newList, accList);
        }

        //获取共享原因
        String rowCauseGI = Schema.Tender_information__Share.RowCause.GI_assistant__c;
        String rowCauseSP = Schema.Tender_information__Share.RowCause.SP_assistant__c;
        String rowCauseSale = Schema.Tender_information__Share.RowCause.SalesOwner__c;
        String rowCauseAdministrative = Schema.Tender_information__Share.RowCause.Administrative_assistant__c;
        String rowCauseBeipin = Schema.Tender_information__Share.RowCause.Beipin_assistant__c;
        String rowCauseTender = Schema.Tender_information__Share.RowCause.TenderAssistant__c;
        String rowCauseEnergy = Schema.Tender_information__Share.RowCause.EnergyOwner__c; //DB202305613824 you能量担当
        //营业窗口的共享原因
        String rowCauseYingye = Schema.Tender_information__Share.RowCause.YingyeWindow__c;
        String rowCauseSIShare = Schema.Tender_information__Share.RowCause.SIShare__c;//lwt SI相关共享

        List<String> rowCauseList = new List<String>();
        rowCauseList.add(rowCauseGI);
        rowCauseList.add(rowCauseSP);
        rowCauseList.add(rowCauseSale);
        rowCauseList.add(rowCauseAdministrative);
        rowCauseList.add(rowCauseBeipin);
        rowCauseList.add(rowCauseTender);
        rowCauseList.add(rowCauseYingye);
        rowCauseList.add(rowCauseEnergy);//DB202305613824 you
        rowCauseList.add(rowCauseSIShare);//lwt SI相关共享


        //以 医院id 和战略科室所有人id集合为map
        // Map<Id,Set<Id>> hpDepartmentMap = new Map<Id,Set<Id>>();
        // if (accList.size() > 0) {
        //     for (Account department : accList) {
        //         if (!hpDepartmentMap.containsKey(department.ParentId)) {
        //             hpDepartmentMap.put(department.ParentId, new Set<Id>());
        //         }
        //         hpDepartmentMap.get(department.ParentId).add(department.OwnerId);
        //     }
        // }
        //能量担当需要单独查一下
        List<String> ddNameListtrim = new List<String>();
        List<String> ddNameList = new List<String>();
        for (Account hp : accList) {
            if (hp.Energy_LeaderStr__c != null && hp.Energy_LeaderStr__c != '') {
                // update fxk 2021/10/26 star
                String str = hp.Energy_LeaderStr__c.replaceAll(' ', '');
                String name = str.subString(0, 1) + ' ' + str.subString(1, str.length());
                ddNameListtrim.add(name);
                ddNameList.add(str);
                // update fxk 2021/10/26 end
            }
        }
        //查询用户 获取id
        List<User> ulist = new List<User>();
        ulist = [select id, Alias, IsActive, Name from user where IsActive = true and (Alias in :ddNameList or Alias in :ddNameListtrim)];
        Map<String, Id> uidmap = new Map<String, Id>();
        if (ulist != null && ulist.size() > 0) {
            for (User u : ulist) {
                uidmap.put(u.Name, u.Id);
            }
        }
        // upd 用户勾选了战略科室的话，这条招标信息只有关联医院对应的战略科室主担当可以看到，否则医院所有科室主担当都能看到 2021、9、22 fxk Star
        // Map<Id, Set<Id>> hpDepartmentMap = new Map<Id, Set<Id>>();
        Map<Id, Map<String, Id>> hpDepartmentMap = new Map<Id, Map<String, Id>>();
        if (accList.size() > 0) {
            for (Account hp : accList) {
                //消化科主担当
                // Set<Id> ddIds = new Set<Id>();
                Map<String, Id> ddmap = new Map<String, Id>();
                if (hp.GI_Main__c != null && hp.GI_Main__r.IsActive ) {
                    // System.debug('-------2------' + tenDepartMap.get(info.Id));
                    // ddIds.add(hp.GI_Main__c);
                    ddmap.put(System.label.tender_4, hp.GI_Main__c);
                    // kk ******** DB202309519775  start
                    ddmap.put('消化科', hp.GI_Main__c);
                    // kk ******** DB202309519775  startend
                }
                // 呼吸科主担当
                if (hp.BF_owner__c != null && hp.BF_owner__r.IsActive ) {
                    // System.debug('-------3------' + hp.BF_owner__c);
                    // ddIds.add(hp.BF_owner__c);
                    ddmap.put(System.label.tender_5, hp.BF_owner__c);
                    // kk ******** DB202309519775  start
                    ddmap.put('呼吸科', hp.BF_owner__c);
                    // kk ******** DB202309519775  startend
                }
                // ET科主担当
                // if (hp.ET_owner__c != null && hp.ET_owner__r.IsActive) {
                //     // System.debug('-------3------' + hp.ET_owner__r);
                //     // ddIds.add(hp.ET_owner__c);
                //     ddmap.put(hp.Department_Class__r.RecordTypeId, hp.GI_Main__c);
                // }
                // 普外科主担当
                if (hp.SP_Main__c != null && hp.SP_Main__r.IsActive) {
                    // System.debug('-------3------' + hp.SP_Main__r);
                    // ddIds.add(hp.SP_Main__c);
                    ddmap.put(System.Label.tender_6, hp.SP_Main__c);
                    // kk ******** DB202309519775  start
                    ddmap.put('普外科', hp.SP_Main__c);
                    // kk ******** DB202309519775  startend
                }
                // 泌尿科主担当
                if (hp.URO_owner_ID__c != null && hp.URO_owner_ID__r.IsActive) {
                    // System.debug('-------3------' + hp.URO_owner_ID__r);
                    // ddIds.add(hp.URO_owner_ID__c);
                    ddmap.put(System.label.tender_7, hp.URO_owner_ID__c);
                    // kk ******** DB202309519775  start
                    ddmap.put('泌尿科', hp.URO_owner_ID__c);
                    // kk ******** DB202309519775  startend
                }
                // 妇科主担当
                if (hp.GYN_owner__c != null && hp.GYN_owner__r.IsActive) {
                    // System.debug('-------3------' + hp.GYN_owner__r);
                    // ddIds.add(hp.GYN_owner__c);
                    ddmap.put(System.Label.tender_8, hp.GYN_owner__c);
                    // kk ******** DB202309519775  start
                    ddmap.put('妇科', hp.GYN_owner__c);
                    // kk ******** DB202309519775  startend
                }
                // 耳鼻喉科主担当
                if (hp.ENT_owner_ID__c != null && hp.ENT_owner_ID__r.IsActive) {
                    // System.debug('-------3------' + hp.ENT_owner_ID__r);
                    // ddIds.add(hp.ENT_owner_ID__c);
                    ddmap.put(System.label.tender_9, hp.ENT_owner_ID__c);
                    // kk ******** DB202309519775  start
                    ddmap.put('耳鼻喉科', hp.ENT_owner_ID__c);
                    // kk ******** DB202309519775  startend
                }
                //能量担当

                if (hp.Energy_LeaderStr__c != null) {

                    String str = hp.Energy_LeaderStr__c.replaceAll(' ', '');
                    String name = str.subString(0, 1) + ' ' + str.subString(1, str.length());
                    // update fxk 2021/10/26 star
                    if (uidmap.containsKey(hp.Energy_LeaderStr__c)) {
                        // ddIds.add(uidmap.get(hp.Energy_LeaderStr__c));
                        // ddIds.add(uidmap.get(name));
                        ddmap.put('nengliang', uidmap.get(hp.Energy_LeaderStr__c));

                    }else if(uidmap.containsKey(name)){
                        ddmap.put('nengliang', uidmap.get(name));
                    }
                    // update fxk 2021/10/26 end
                }
                // ddIds.remove(null);
                system.debug('==所有主担当==='+ddmap);
                ddmap.remove(null);
                if (ddmap.size() > 0) {
                    // hpDepartmentMap.put(hp.Id, ddIds);
                    hpDepartmentMap.put(hp.Id, ddmap);
                }
            }
        }
        system.debug('==hpDepartmentMap=='+hpDepartmentMap);
        // upd 用户勾选了战略科室的话，这条招标信息只有关联医院对应的战略科室主担当可以看到，否则医院所有科室主担当都能看到 2021、9、22 fxk End

        //lwt SI相关共享 start
        List<GroupMember>  gmList=[SELECT id,GroupId,UserOrGroupId from GroupMember where Group.Name='招标项目SI相关共享'];
        //lwt SI相关共享 end
        //待共享的数据
        //取值顺序是：所有人，之后是gi助理，接下来是sp助理，接下来是战略科室所有人 所以倒着来
        List<Tender_information__Share> tenShareList = new List<Tender_information__Share>();
        for (Tender_information__c tenc : newList) {
            // 20221013 ljh SWAG-CK28WT start
            // 集采项目 后续共享不需要，只把原来的共享删除
            if(tenc.CentralizedProject__c){
                continue;
            }
            // 20221013 ljh SWAG-CK28WT start
            //一个map为 针对一条招投标计划的共享
            Map<Id, Tender_information__Share> sharemap = new Map<Id, Tender_information__Share>();
            //1.先设置 医院担当的共享
            //获取5个医院
            Map<Id, String> hmap = new Map<Id, String>();
            hmap.put(tenc.Hospital__c, tenc.Hospital__c);
            hmap.put(tenc.Hospital1__c, tenc.Hospital1__c);
            hmap.put(tenc.Hospital2__c, tenc.Hospital2__c);
            hmap.put(tenc.Hospital3__c, tenc.Hospital3__c);
            hmap.put(tenc.Hospital4__c, tenc.Hospital4__c);
            hmap.remove(null);
            // kk  ********  DB202309519775 start
            // 列一个关联医院和关联战略科室的map
            Map<String,String>  hospitalMapping = new Map<String,String>();
            //新的字段没有 id  的api，所以这个得区分
            hospitalMapping.put(tenc.Hospital__c, tenc.department_selection__c);
            hospitalMapping.put(tenc.Hospital1__c, tenc.department_selection1__c);
            hospitalMapping.put(tenc.Hospital2__c, tenc.department_selection2__c);
            hospitalMapping.put(tenc.Hospital3__c, tenc.department_selection3__c);
            hospitalMapping.put(tenc.Hospital4__c, tenc.department_selection4__c);
            hospitalMapping.remove(null);
            //<医院id，分别是第几个医院>
            Map<String,String>  hospitalMappingNumber = new Map<String,String>();
            hospitalMappingNumber.put(tenc.Hospital__c, '0');
            hospitalMappingNumber.put(tenc.Hospital1__c, '1');
            hospitalMappingNumber.put(tenc.Hospital2__c, '2');
            hospitalMappingNumber.put(tenc.Hospital3__c, '3');
            hospitalMappingNumber.put(tenc.Hospital4__c, '4');
            hospitalMappingNumber.remove(null);
            // kk  ********  DB202309519775 end

            if (hpDepartmentMap.size() > 0) {
                for (Id hpId : hpDepartmentMap.keySet()) {
                    if (hmap.containsKey(hpId)) {
                        // upd 用户勾选了战略科室的话，这条招标信息只有关联医院对应的战略科室主担当可以看到，否则医院所有科室主担当都能看到 2021、9、22 fxk Star
                        //kk  ********  DB202309519775 start
                        //上面是所有的医院对应的 关联战略科室都放进去tempMap   上面的代码需优化
                        // 需要知道是第几个关联医院，才能用相对应的战略科室
                        Map<String, String> tempMap = new Map<String, String>();
                        String  departmentSelection = hospitalMapping.get(hpId);
                        String  mappingNumber = hospitalMappingNumber.get(hpId);
                        if(mappingNumber == '0'){
                        if (tenc.department_selection__c != null) {
                            System.debug('=========1=======' + tenc.department_selection__c);
                            List<String> tenDepartList = tenc.department_selection__c.split(';');
                            for (String departs : tenDepartList) {
                                tempMap.put(departs, '');
                            }
                             tempMap.put('nengliang', '');//DB202305613824 you 
                        } else {
                            System.debug('=========2=======' + tenc.department_selection__c);
                            tempMap.put(System.label.tender_4, '');
                            tempMap.put(System.label.tender_5, '');
                            tempMap.put(System.label.tender_6, '');
                            tempMap.put(System.label.tender_7, '');
                            tempMap.put(System.label.tender_8, '');
                            tempMap.put(System.label.tender_9, '');
                            tempMap.put('nengliang', '');
                            }        
                        }
                        else{
                            if (departmentSelection!= null) {
                                System.debug('=========新的关联战略科室=======' + departmentSelection);
                                List<String> tenDepartList = departmentSelection.split(';');
                                for (String departs : tenDepartList) {
                                    tempMap.put(departs, '');
                                }
                                 tempMap.put('nengliang', '');//DB202305613824 you 
                            } else {
                                System.debug('=========关联战略科室1、2、3、4=======' + departmentSelection);
                                tempMap.put('消化科', '');
                                tempMap.put('呼吸科', '');
                                tempMap.put('普外科', '');
                                tempMap.put('泌尿科', '');
                                tempMap.put('妇科', '');
                                tempMap.put('耳鼻喉科', '');
                                tempMap.put('nengliang', '');
                            }    
                        }
                        
                        // kk ******** DB202309519775  end
                        Map<String, Id> departToMainmap = hpDepartmentMap.get(hpId);
                        for (String depart : tempMap.keySet()) {
                            system.debug(depart+'========depart======='+departToMainmap);
                            if (departToMainmap.containsKey(depart)) {
                                if (tenc.OwnerId != departToMainmap.get(depart)) {
                                    Tender_information__Share aos = new Tender_information__Share();
                                        if(depart=='nengliang'){
                                            aos.RowCause = rowCauseEnergy;
                                        }else{
                                            aos.RowCause = rowCauseSale;
                                        }
                                        aos.ParentId = tenc.Id;
                                        aos.UserOrGroupId = departToMainmap.get(depart);
                                        aos.AccessLevel = 'Edit';
                                    sharemap.put(departToMainmap.get(depart), aos);
                                }
                            }
                        }
                        // for (Id uId : hpDepartmentMap.get(hpId)) {
                        //     if (tenc.OwnerId != uId) {
                        //         Tender_information__Share aos = new Tender_information__Share(
                        //             RowCause = rowCauseSale,
                        //             ParentId = tenc.Id,
                        //             UserOrGroupId = uId,
                        //             AccessLevel = 'Edit');
                        //         sharemap.put(uId, aos);

                        //     }
                        // }
                        // upd 用户勾选了战略科室的话，这条招标信息只有关联医院对应的战略科室主担当可以看到，否则医院所有科室主担当都能看到 2021、9、22 fxk End

                    }
                }
            }
            //行政窗口
            if (tenc.OwnerId != tenc.Window2__c  && tenc.Window2__c != null) {
                Tender_information__Share aos = new Tender_information__Share(
                    RowCause = rowCauseAdministrative,
                    ParentId = tenc.Id,
                    UserOrGroupId = tenc.Window2__c,
                    AccessLevel = 'Edit');
                sharemap.put(tenc.Window2__c, aos);
            }
            //备品窗口
            // if (tenc.OwnerId != tenc.beiPinwindow__c  && tenc.beiPinwindow__c != null){
            //     Tender_information__Share aos = new Tender_information__Share(
            //         RowCause = rowCauseBeipin,
            //         ParentId = tenc.Id,
            //         UserOrGroupId = tenc.beiPinwindow__c,
            //         AccessLevel = 'Edit');
            //     sharemap.put(tenc.beiPinwindow__c, aos);
            // }
            //招标项目助理2
            if (tenc.OwnerId != tenc.TenderAssistant2__c && tenc.TenderAssistant2__c != null) {
                Tender_information__Share aos = new Tender_information__Share(
                    RowCause = rowCauseTender,
                    ParentId = tenc.Id,
                    UserOrGroupId = tenc.TenderAssistant2__c,
                    AccessLevel = 'Edit');
                sharemap.put(tenc.TenderAssistant2__c, aos);
            }
            //招标项目助理1
            if (tenc.OwnerId != tenc.TenderAssistant1__c && tenc.TenderAssistant1__c != null) {
                Tender_information__Share aos = new Tender_information__Share(
                    RowCause = rowCauseTender,
                    ParentId = tenc.Id,
                    UserOrGroupId = tenc.TenderAssistant1__c,
                    AccessLevel = 'Edit');
                sharemap.put(tenc.TenderAssistant1__c, aos);
            }
            //营业窗口
            if (tenc.OwnerId != tenc.YingyeWindow__c && tenc.YingyeWindow__c != null) {
                Tender_information__Share aos = new Tender_information__Share(
                    RowCause = rowCauseYingye,
                    ParentId = tenc.Id,
                    UserOrGroupId = tenc.YingyeWindow__c,
                    AccessLevel = 'Edit');
                sharemap.put(tenc.YingyeWindow__c, aos);
            }

            //2.SP助理
            if (tenc.OwnerId != tenc.SP_assistant__c && tenc.SP_assistant__c != null) {
                Tender_information__Share aos = new Tender_information__Share(
                    RowCause = rowCauseSP,
                    ParentId = tenc.Id,
                    UserOrGroupId = tenc.SP_assistant__c,
                    AccessLevel = 'Edit');
                sharemap.put(tenc.SP_assistant__c, aos);
            }
            //3.GI助理
            if (tenc.OwnerId != tenc.GI_assistant__c  && tenc.GI_assistant__c != null) {
                Tender_information__Share aos = new Tender_information__Share(
                    RowCause = rowCauseGI,
                    ParentId = tenc.Id,
                    UserOrGroupId = tenc.GI_assistant__c,
                    AccessLevel = 'Edit');
                sharemap.put(tenc.GI_assistant__c, aos);
            }
            //lwt 招标项目SI相关 start
            if(tenc.SIShare__c && tenc.SIShare_t__c){
                for(GroupMember  gmIterm:gmList){
                    Tender_information__Share aos = new Tender_information__Share(
                        RowCause = rowCauseSIShare,
                        ParentId = tenc.Id,
                        UserOrGroupId = gmIterm.UserOrGroupId,
                        AccessLevel = 'Edit');
                    // sharemap.put(tenc.GI_assistant__c, aos);
                    tenShareList.add(aos);
                }
            }
            //lwt 招标项目SI相关 start
            tenShareList.addAll(sharemap.values());
        }

         
        //现在用的是全删全加的方法，没有再判断是否发生改变了
        //删除
        List<Tender_information__Share> beforeShareList =
            [select id from Tender_information__Share
             where ParentId in :newMap.keySet()
             and RowCause in :rowCauseList];
        //先删
        if (beforeShareList != null && beforeShareList.size() > 0) {
            delete beforeShareList;
        }
        //再加
        if (tenShareList.size() > 0) {
            insert tenShareList;
        }
    }

    /**
     * 2022-04-03 增加判断 当用户无效时也跳过设置
     */
    public void AssignValueToAssistant() {
        //根据ocsm管理省的助理设置当前招投标项目的gi/sp助理
        Map<String, OCM_Management_Province__c> mpMap = new Map<String, OCM_Management_Province__c>();

        for (OCM_Management_Province__c mp : [SELECT id, Name, GI_assistant__c, SP_assistant__c, SP_assistant__r.IsActive, GI_assistant__r.IsActive, Window2__c, Window2__r.IsActive, Admin_assistant3__c, Admin_assistant3__r.IsActive, TenderAssistant1__c, TenderAssistant1__r.IsActive, TenderAssistant2__c, TenderAssistant2__r.IsActive, Window1__c, Window1__r.IsActive FROM OCM_Management_Province__c]) {
           
            mpMap.put(mp.Name, mp);
        }
        //设置GI/SP助理  行政助理、备品窗口
        //直接设置 信息负责人(助理) 为GI助理
        for (Tender_information__c info : newList) {
            //用户自己选 所有人需要和用户选的人保持一致;
            // if (info.OwnerId != info.departmentOwner__c && info.departmentOwner__c != null) {
            //     info.OwnerId = info.departmentOwner__c;
            // }
            //不能判断ocsm管理省是否发生改变了 会有历史数据
            if (info.OCSMProvince__c != null) {
                OCM_Management_Province__c ocm = mpMap.get(info.OCSMProvince__c);
                if (ocm != null) {
                    //设置OCSM管理省查找字段的值 20210728 start  为了更新历史数据 直接赋值即可
                    // if (Trigger.isInsert || (Trigger.isUpdate && oldMap.get(info.Id).OCSMProvince__c != info.OCSMProvince__c)) {
                    info.OCSMProvinceS__c = ocm.Id;
                    // }
                    //设置OCSM管理省查找字段的值 20210728 end
                    // GI助理为空时__r.IsActive返回false 20210813
                    if (ocm.GI_assistant__r.IsActive ) {
                        if (info.GI_assistant__c != ocm.GI_assistant__c) {
                            info.GI_assistant__c = ocm.GI_assistant__c;
                        }
                        if (info.InfoOwner__c != ocm.GI_assistant__c) {
                            if(info.CentralizedProject__c == false){ // 20221012 ljh SWAG-CK28WT 添加的if
                                info.InfoOwner__c = ocm.GI_assistant__c;
                            }
                        }
                    }
                    // 增加GI助理为空时的处理，清空招标项目的GI助理和信息负责人（助理） 20210813
                    else if (ocm.GI_assistant__c == null || !ocm.GI_assistant__r.IsActive) {
                        info.GI_assistant__c = null;
                        if(info.CentralizedProject__c == false){ // 20221012 ljh SWAG-CK28WT 添加的if
                            info.InfoOwner__c = null;
                        }
                    }
                    if (ocm.SP_assistant__r.IsActive && info.SP_assistant__c != ocm.SP_assistant__c) {
                        info.SP_assistant__c = ocm.SP_assistant__c;
                    }
                    // 增加SP助理为空时的处理，清空招标项目的SP助理 20210813
                    else if (ocm.SP_assistant__c == null || !ocm.GI_assistant__r.IsActive) {
                        info.SP_assistant__c = null;
                    }
                    if (ocm.Window2__r.IsActive && info.Window2__c != ocm.Window2__c) {
                        info.Window2__c = ocm.Window2__c;
                    }
                    // 清空行政窗口 20210813
                    else if (ocm.Window2__c == null || !ocm.Window2__r.IsActive) {
                        info.Window2__c = null;
                    }
                    //
                    // if (ocm.Admin_assistant3__r.IsActive && info.beiPinwindow__c != ocm.Admin_assistant3__c) {
                    //     info.beiPinwindow__c = ocm.Admin_assistant3__c;
                    // }
                    //设置招标助理1和招标助理2的值
                    if (ocm.TenderAssistant1__r.IsActive && info.TenderAssistant1__c != ocm.TenderAssistant1__c) {
                        info.TenderAssistant1__c = ocm.TenderAssistant1__c;
                    }
                    // 清空招标助理1 20210813
                    else if (ocm.TenderAssistant1__c == null || !ocm.TenderAssistant1__r.IsActive) {
                        info.TenderAssistant1__c = null;
                    }
                    if (ocm.TenderAssistant2__r.IsActive && info.TenderAssistant2__c != ocm.TenderAssistant2__c) {
                        info.TenderAssistant2__c = ocm.TenderAssistant2__c;
                    }
                    // 清空招标助理2 20210813
                    else if (ocm.TenderAssistant2__c == null || !ocm.TenderAssistant2__r.IsActive) {
                        info.TenderAssistant2__c = null;
                    }

                    //设置营业窗口  add by rentx 20210721
                    if (ocm.Window1__r.IsActive && info.YingyeWindow__c != ocm.Window1__c) {
                        info.YingyeWindow__c = ocm.Window1__c;
                    }
                    // 清空营业窗口 20210813
                    else if (ocm.Window1__c == null || !ocm.Window1__r.IsActive) {
                        info.YingyeWindow__c = null;
                    }
                }
                // add 战略科室分类为普外科、泌尿科、妇科、耳鼻喉科时，信息负责人（助理）修改为 SP助理 2021、11、11 fxk star
                if(info.department_selection__c != null){
                    List<String> tenDepartList = info.department_selection__c.split(';');
                    if(tenDepartList.contains(System.label.tender_6)
                        || tenDepartList.contains(System.label.tender_7)
                        || tenDepartList.contains(System.label.tender_8)
                        || tenDepartList.contains(System.label.tender_9)){
                        System.debug('=====-----'+info.department_selection__c);
                        if(info.CentralizedProject__c == false){ // 20221012 ljh SWAG-CK28WT 添加的if
                            info.InfoOwner__c = info.SP_assistant__c;
                        }
                        
                    }
                }
                //kk ******** DB202309519775  start
                //复制战略科室分类 至1、2、3、4   上述只是改了一个字段的值，现在战略科室多了1、2、3、4 是否也需要做判断
                if(info.department_selection1__c != null){
                    List<String> tenDepartList1 = info.department_selection1__c.split(';');
                    if(tenDepartList1.contains('普外科')
                        || tenDepartList1.contains('泌尿科')
                        || tenDepartList1.contains('妇科')
                        || tenDepartList1.contains('耳鼻喉科')){
                        System.debug('=====-----'+info.department_selection1__c);
                        if(info.CentralizedProject__c == false){ // 20221012 ljh SWAG-CK28WT 添加的if
                            info.InfoOwner__c = info.SP_assistant__c;
                        }
                        
                    }
                }
                if(info.department_selection2__c != null){
                    List<String> tenDepartList2 = info.department_selection2__c.split(';');
                    if(tenDepartList2.contains('普外科')
                        || tenDepartList2.contains('泌尿科')
                        || tenDepartList2.contains('妇科')
                        || tenDepartList2.contains('耳鼻喉科')){
                        System.debug('=====-----'+info.department_selection2__c);
                        if(info.CentralizedProject__c == false){ // 20221012 ljh SWAG-CK28WT 添加的if
                            info.InfoOwner__c = info.SP_assistant__c;
                        }
                        
                    }
                }
                if(info.department_selection3__c != null){
                    List<String> tenDepartList3 = info.department_selection3__c.split(';');
                    if(tenDepartList3.contains('普外科')
                        || tenDepartList3.contains('泌尿科')
                        || tenDepartList3.contains('妇科')
                        || tenDepartList3.contains('耳鼻喉科')){
                        System.debug('=====-----'+info.department_selection3__c);
                        if(info.CentralizedProject__c == false){ // 20221012 ljh SWAG-CK28WT 添加的if
                            info.InfoOwner__c = info.SP_assistant__c;
                        }
                        
                    }
                }
                if(info.department_selection4__c != null){
                    List<String> tenDepartList4 = info.department_selection4__c.split(';');
                    if(tenDepartList4.contains('普外科')
                        || tenDepartList4.contains('泌尿科')
                        || tenDepartList4.contains('妇科')
                        || tenDepartList4.contains('耳鼻喉科')){
                        System.debug('=====-----'+info.department_selection4__c);
                        if(info.CentralizedProject__c == false){ // 20221012 ljh SWAG-CK28WT 添加的if
                            info.InfoOwner__c = info.SP_assistant__c;
                        }
                        
                    }
                }
                //kk ******** DB202309519775  end
                // add 战略科室分类为普外科、泌尿科、妇科、耳鼻喉科时，信息负责人（助理）修改为 SP助理 2021、11、11 fxk end
                // 20221012 ljh SWAG-CK28WT start
                if(info.CentralizedProject__c){
                    // 清除这些人赋值 （就没有权限了）
                    info.GI_assistant__c = null;
                    info.SP_assistant__c = null;
                    info.Window2__c = null;
                    info.TenderAssistant1__c = null;
                    info.TenderAssistant2__c = null;
                    info.YingyeWindow__c = null;
                }
                // 20221012 ljh SWAG-CK28WT end
            }
            // 20221027 ljh SWAG-CKL5UC add start
            // 项目中当结果记录日有值公告记录日为空时，给公告记录日赋值逻辑=结果记录日-15天
            if(info.ResultDate__c != null && info.publicDate__c == null){
                info.publicDate__c = info.ResultDate__c.addDays(-15);
            }
            // 20221027 ljh SWAG-CKL5UC add start
        }
        
    }
// fxk 2021/8/3 反逻辑删除 Star
    private void updateTenDel() {
        List<Tender_information__c> updateTenList = new List<Tender_information__c>();
        for (Tender_information__c TenoldInfo : newList) {
            if (TenoldInfo.Logical_delete__c == false && (TenoldInfo.Logical_delete__c != oldMap.get(TenoldInfo.Id).Logical_delete__c)) {
                TenoldInfo.Retain_Tender__c = null;
            }
            if (TenoldInfo.Retain_Tender__c == null && (TenoldInfo.Retain_Tender__c != oldMap.get(TenoldInfo.Id).Retain_Tender__c)) {
                TenoldInfo.Logical_delete__c = false;
            }
        }
    }

    private void changeRelateOppDate(){
        System.debug(LoggingLevel.INFO, '*** 进入方法: ');
        List<String> oldUserOrGroups = new List<String>();
        Map<Id,Tender_information__c> tOldMap = (Map<Id,Tender_information__c>) Trigger.oldMap;
        Map<Id,Tender_information__c> tNewMap = (Map<Id,Tender_information__c>) Trigger.newMap;
        List<Date> cd = new List<Date>();
        System.debug(LoggingLevel.INFO, '*** cd: ' + cd);
        System.debug(LoggingLevel.INFO, '*** tOldMap: ' + tOldMap);
        for(Tender_information__c t:(List<Tender_information__c>) Trigger.new){
            System.debug(LoggingLevel.INFO, '*** 进入for循环: ');
            System.debug(LoggingLevel.INFO, '*** t: ' + t);
            if(Trigger.isUpdate){
                if (String.isNotBlank(t.IsRelateProject__c) && t.IsRelateProject__c == '是' && t.relativeTime__c == null && System.Label.ProfileId_2S6.equals(UserInfo.getProfileId())) {
                    t.relativeTime__c = Datetime.now();
                }
                System.debug(LoggingLevel.INFO, '*** tOldMap: ' + tOldMap.get(t.Id).relativeDate__c);
                System.debug(LoggingLevel.INFO, '*** t.relativeDate__c: ' + t.relativeDate__c);
                // if(tOldMap.get(t.Id).relativeDate__c != null && t.relativeDate__c != tOldMap.get(t.Id).relativeDate__c){
                //lt 20221114 DB202211270805 【紧急】招标项目是否按时关联计算错误 start
                // if(t.relativeDate__c != null && t.relativeDate__c != tOldMap.get(t.Id).relativeDate__c){
                if(((t.OBSAP_relativeTime__c != null || t.relativeTime_F__c != null) && t.Tender_Olympus__c == null) || (t.relativeDate__c != null && t.relativeDate__c != tOldMap.get(t.Id).relativeDate__c)){
                //lt 20221114 DB202211270805 【紧急】招标项目是否按时关联计算错误 end
                    System.debug(LoggingLevel.INFO, '*** 进入if语句: ');
                    String datastr = String.valueOf(t.relativeDate__c);
                    cd.add( Date.valueOf(datastr.substring(0,10)) );
                    System.debug(LoggingLevel.INFO, '*** cd: ' + cd);
                }
            }
        }
        Map<Date,String> lm = new Map<Date,String>();
        if(cd.size() > 0){
            for(OlympusCalendar__c oc:[SELECT Id,Date__c FROM OlympusCalendar__c WHERE Date__c IN:cd]){
                lm.put(oc.Date__c,oc.Id); 
                System.debug(LoggingLevel.INFO, '*** map赋值: ');    
            }
    
            for(Tender_information__c l:(List<Tender_information__c>) Trigger.new){
                //lt 20221114 DB202211270805 【紧急】招标项目是否按时关联计算错误 start
                // if (l.relativeDate__c == null) {
                //     continue;
                // }
                if (l.relativeDate__c == null && l.OBSAP_relativeTime__c == null) {
                    continue;
                }
                Datetime relativetime = l.OBSAP_relativeTime__c != null ? l.OBSAP_relativeTime__c : l.relativeTime__c;
                // String datastr = String.valueOf(l.relativeDate__c);
                String datastr = String.valueOf(relativetime);
                //lt 20221114 DB202211270805 【紧急】招标项目是否按时关联计算错误 end
                String dt = lm.get(Date.valueOf(datastr.substring(0,10)));
                System.debug(LoggingLevel.INFO, '*** dt: '+ dt);
                l.Tender_Olympus__c = dt;
                System.debug(LoggingLevel.INFO, '*** l.Tender_Olympus__c: '+ l.Tender_Olympus__c);
            }
        }
        
    }
// fxk 2021/8/3 反逻辑删除 end
// 20210824 中标时修改关联询价的招标项目名和中标时间
    private void updateWin() {
        List<String> tenderIds = new List<String>();
        List<String> lostWinIds = new List<String>();
        // 20221028 ljh SWAG-CKL5UC start
        Map<Id,Tender_information__c>  tendMap = New Map<Id,Tender_information__c>(); 
        Map<Id,Opportunity> updateOpp = New Map<Id,Opportunity>();
        // 20221028 ljh SWAG-CKL5UC end
        for (Tender_information__c ten : newList) {
            /** 20220614 SWAG-CFD4SU you 更改，询价优先显示项目阶段最新的招标项目信息
            if ('3：结果'.equals(ten.InfoType__c) && !'3：结果'.equals((oldMap.get(ten.Id).InfoType__c))) {
                tenderIds.add(ten.Id);
            } else if (!'3：结果'.equals(ten.InfoType__c) && '3：结果'.equals((oldMap.get(ten.Id).InfoType__c))) {
                lostWinIds.add(ten.Id);
            }
            **/
            //上线处理老数据
            if((String.isNotBlank(ten.InfoType__c) && ten.InfoType__c != oldMap.get(ten.Id).InfoType__c) || (String.isNotBlank(ten.subInfoType__c) && ten.subInfoType__c != oldMap.get(ten.Id).subInfoType__c)  || System.Label.TenderToOpp == '1'){
               tenderIds.add(ten.Id);
            }
            // 20221028 ljh SWAG-CKL5UC add start
            // 关联询价的 询价页面创建日晚于招标项目公告记录日，漏单数为1
            // 关联询价的 && 更新有值 
            System.debug('20231109-漏单-询价数量：'+ten.OpportunityNum__c);
            System.debug('20231109-漏单-旧-招标日：'+oldMap.get(ten.Id).TenderDate__c);
            System.debug('20231109-漏单-新-招标日：'+ten.TenderDate__c);
            // && oldMap.get(ten.Id).publicDate__c == null && ten.publicDate__c != null  lt delete 20231109 -60  update
            //20231218 update lt DB202311665664 update publicDate__c  ==>  TenderDate__c  add TenderBeginTime__c,TenderEndTime__c,publicDate__c
            //lt 20240522 delete ten.publicDate__c != null &&   DB202403627483
            //lt 20240522 add  || (oldMap.get(ten.Id).InfoType__c != ten.InfoType__c)  DB202403627483
            if(ten.OpportunityNum__c > 0 && Trigger.isUpdate && 
              ((oldMap.get(ten.Id).TenderDate__c != ten.TenderDate__c) ||
               (oldMap.get(ten.Id).TenderBeginTime__c != ten.TenderBeginTime__c) ||
               (oldMap.get(ten.Id).TenderEndTime__c != ten.TenderEndTime__c) ||
               (oldMap.get(ten.Id).publicDate__c != ten.publicDate__c) 
               //|| (oldMap.get(ten.Id).InfoType__c != ten.InfoType__c)
              ) ){
                tendMap.put(ten.Id, ten);
            }
            // 20221028 ljh SWAG-CKL5UC add end
            
        }
        if (tenderIds.size() > 0) {
            //20220616 SWAG-CFD4SU you 增加order by
            //查询招标项目下所有中间表
            List<Tender_Opportunity_Link__c> links =
                [SELECT id,
                 Tender_information__c,
                 Tender_information__r.OpenBidingTime__c,
                 Tender_information__r.WinnerAmount__c,
                 Tender_information__r.ZhongBiaoUnit1__c,
                 Tender_information__r.Tender_Order__c,
                 Opportunity__c
                 FROM Tender_Opportunity_Link__c
                 WHERE Tender_information__c IN :tenderIds];// AND Opportunity__r.Bidding_Project_Name_Bid__c NOT IN :tenderIds
            if (links != null && links.size() > 0) {
                List<String> oppIds = new List<String>();
                for (Tender_Opportunity_Link__c link : links) {
                    oppIds.add(link.Opportunity__c);
                }
                if (oppIds.size() > 0) {
                    //查询询价下所有中间表
                    List<Tender_Opportunity_Link__c> links1 =
                    [SELECT id,
                     Tender_information__c,
                     Tender_information__r.OpenBidingTime__c,
                     Tender_information__r.WinnerAmount__c,
                     Tender_information__r.ZhongBiaoUnit1__c,
                     Tender_information__r.Tender_Order__c,
                     Tender_information__r.InfoType__c,
                     Tender_information__r.subInfoType__c,
                     Opportunity__c
                     FROM Tender_Opportunity_Link__c
                     WHERE Opportunity__c IN :oppIds order by Opportunity__c,Tender_information__r.Tender_Order__c desc, Tender_information__r.relativeTime_F__c desc];
                    List<Opportunity> opps = [select id, Bidding_Project_Name_Bid__c, Bidding_Project_Name_Bid__r.InfoType__c, Closing_Bid_Date_Bid__c,Opp_Order__c from Opportunity where Id in :oppIds];// and Bidding_Project_Name_Bid__r.InfoType__c != '3：结果'
                    if (opps != null && opps.size() > 0) {
                        for (Opportunity opp : opps) {
                            if(links1 !=null && links1.size() > 0){
                               for (Tender_Opportunity_Link__c link : links1) {
                                if (link.Opportunity__c == opp.Id) {
                                    //20220616 SWAG-CFD4SU you start 改造，询价实时显示项目阶段最新的招标项目信息
                                    if(opp.Opp_Order__c <= link.Tender_information__r.Tender_Order__c){
                                       opp.Bidding_Project_Name_Bid__c = link.Tender_information__c;
                                       break;
                                    }
                                    //20220616 SWAG-CFD4SU you end 改造，询价实时显示项目阶段最新的招标项目信息
                                }
                              } 
                            }
                            //20220830 you SWAG-CHL67J start
                            Integer returncount =0;
                             for (Tender_Opportunity_Link__c link : links1) {
                                if (link.Opportunity__c == opp.Id) {
                                    if(String.isNotBlank(link.Tender_information__r.InfoType__c) && link.Tender_information__r.InfoType__c!='1：预告' && String.isNotBlank(link.Tender_information__r.subInfoType__c) && link.Tender_information__r.subInfoType__c!='3-1：废标公告' && link.Tender_information__r.subInfoType__c!='3-2：流标公告'){
                                        system.debug('test2进来了');
                                          returncount += 1;
                                      }
                                }
                            }
                            opp.Tender_Number__c =returncount;
                            //20220830 you SWAG-CHL67J end   
                        }
                        // 需要跳过询价trigger 防止出现重复更新的情况
                        StaticParameter.EscapeOpportunityBefUpdTrigger = true;
                        update opps;
                        StaticParameter.EscapeOpportunityBefUpdTrigger = false;
                    }
                }
            }
        }
        /**20220616 SWAG-CFD4SU you
        if (lostWinIds.size() > 0) {
            List<Tender_Opportunity_Link__c> links_2 =
                [SELECT id,
                 Tender_information__c,
                 Opportunity__c
                 FROM Tender_Opportunity_Link__c
                 WHERE Tender_information__c NOT IN :lostWinIds
                 AND Tender_information__r.InfoType__c = '3：结果' 
                 AND Opportunity__r.Bidding_Project_Name_Bid__c IN :lostWinIds];
            if (links_2 != null && links_2.size() > 0) {
                List<String> oppIds = new List<String>();
                for (Tender_Opportunity_Link__c link : links_2) {
                    oppIds.add(link.Opportunity__c);
                }
                if (oppIds.size() > 0) {
                    List<Opportunity> opps_2 = [select id, Bidding_Project_Name_Bid__c, Bidding_Project_Name_Bid__r.InfoType__c, Closing_Bid_Date_Bid__c from Opportunity where Id in :oppIds and Bidding_Project_Name_Bid__r.InfoType__c != '3：结果'];
                    if (opps_2 != null && opps_2.size() > 0) {
                        for (Opportunity opp : opps_2) {
                            for (Tender_Opportunity_Link__c link : links_2) {
                                if (link.Opportunity__c == opp.Id) {
                                    opp.Bidding_Project_Name_Bid__c = link.Tender_information__c;
                                    break;
                                }
                            }
                        }
                        // 需要跳过询价trigger 防止出现重复更新的情况
                        StaticParameter.EscapeOpportunityBefUpdTrigger = true;
                        update opps_2;
                        StaticParameter.EscapeOpportunityBefUpdTrigger = false;
                    }
                }
            }
        }
        **/
        // 20221028 ljh SWAG-CKL5UC add start
        Set<Id> oppIdSet = new Set<Id>(); //DB202307367354 lt 【招标项目】漏单计算逻辑修改 20231115 add
        if(tendMap.size() > 0){
            List<Tender_Opportunity_Link__c> links = [select id, Opportunity__c, Tender_information__c, Opportunity__r.Created_Day__c ,Opportunity__r.LeakageNumber__c,Opportunity__r.LeadSource  from Tender_Opportunity_Link__c where Tender_information__c in :tendMap.keySet()];
            for(Tender_Opportunity_Link__c lk:links){
                //20230727 lt DB202307367354 【招标项目】漏单计算逻辑修改 delete  && lk.Opportunity__r.LeadSource == '招标网'
                //20231106 lt DB202307367354 【招标项目】漏单计算逻辑修改 add -60

                //DB202307367354 lt 【招标项目】漏单计算逻辑修改 20231115 start
                // System.debug('20231106-公告记录日-60：'+tendMap.get(lk.Tender_information__c).publicDate__c.addDays(-60));
                // if(lk.Opportunity__r.Created_Day__c  > tendMap.get(lk.Tender_information__c).publicDate__c.addDays(-60)){
                //     Opportunity opp = new Opportunity();
                //     opp.Id = lk.Opportunity__c;
                //     opp.LeakageNumber__c = 1;
                //     updateOpp.put(opp.Id,opp);
                // }
                //DB202307367354 lt 【招标项目】漏单计算逻辑修改 20231115 start
                oppIdSet.add(lk.Opportunity__c);
                //DB202307367354 lt 【招标项目】漏单计算逻辑修改 20231115 end
            }
        }
        //DB202307367354 lt 【招标项目】漏单计算逻辑修改 20231115 start
        // if(updateOpp.size() > 0){
        //     update updateOpp.values();
        // }

        if(oppIdSet.Size() > 0){
            TenderUtil.UpdLeakageNum(oppIdSet);
        }
        //DB202307367354 lt 【招标项目】漏单计算逻辑修改 20231115 end
        // 20221028 ljh SWAG-CKL5UC add end
    }

    public void updateTenOwner() {

        if (!isFirst) {
            return;
        }
        System.debug('12345678');
        //1.获得医院
        Set<Id> hpIdsets = new Set<Id>();
        // Map < 招投标Id，Map<关联医院 >> 对应招投标下的关联医院
        Map<Id, Map<Id, Id>> hpIdsetsMap = new Map<Id, Map<Id, Id>>();
        // 得到招投标的所有人
        Map<Id, Id> tenderinformationOwnerMap = new Map<Id, Id>();
        // 得到招投标的关联战略科室
        Map<Id, List<String>> department_selectionMap = new Map<Id, List<String>>();
        ////kk ******** DB202309519775  start
        Map<Id, List<String>> department_selectionMap1 = new Map<Id, List<String>>();
        Map<Id, List<String>> department_selectionMap2 = new Map<Id, List<String>>();
        Map<Id, List<String>> department_selectionMap3 = new Map<Id, List<String>>();
        Map<Id, List<String>> department_selectionMap4 = new Map<Id, List<String>>();
        ////kk ******** DB202309519775  start
        for (Tender_information__c info : newList) {
           // DB202305613824 you 如果是主动更改所有人，那么通过战略科室就不能再更新所有人了，其他更新正常
           system.debug('===info.OwnerIsActive__c=='+info.OwnerIsActive__c);
            if(info.status__c=='04.待关联询价' && info.IFActiveUpdateOwner__c==true && info.OwnerIsActive__c){
                continue;
            }
            Map<Id, Id> ressMap = new  Map<Id, Id>();
            // 多加一步判断 如果new的OwnerId和old的不同 就不再做判断了 start
            // add 【委托】P-招标项目-手动创建的招标项目增加必填字段 2021/11/03 fxk Star
            Tender_information__c oldTen = new Tender_information__c();
            if (oldMap!=null && oldMap.containskey(info.Id)) {
                oldTen = oldMap.get(info.Id);
            }
            // add 【委托】P-招标项目-手动创建的招标项目增加必填字段 2021/11/03 fxk End
            if (oldTen.OwnerId != null && !oldTen.OwnerId.equals(info.OwnerId)) {
                continue;
            }
            // 多加一步判断 如果new的OwnerId和old的不同 就不再做判断了 end
            tenderinformationOwnerMap.put(info.Id, info.OwnerId);
            System.debug('-------9=======' + info.Hospital__c);
            if (info.Hospital__c != null) {
                hpIdsets.add(info.Hospital__c);
                System.debug('-------7=======' + hpIdsets);
                ressMap.put(info.Hospital__c, info.Hospital__c);
                System.debug('-------8=======' + ressMap);
            }
            // if (info.Hospital1__c != null) {
            //     hpIdsets.add(info.Hospital1__c);
            //     ressMap.put(info.Hospital1__c, info.Hospital1__c);
            // }
            // if (info.Hospital2__c != null) {
            //     hpIdsets.add(info.Hospital2__c);
            //     ressMap.put(info.Hospital2__c, info.Hospital2__c);
            // }
            // if (info.Hospital3__c != null) {
            //     hpIdsets.add(info.Hospital3__c);
            //     ressMap.put(info.Hospital3__c, info.Hospital3__c);
            // }
            // if (info.Hospital4__c != null) {
            //     hpIdsets.add(info.Hospital4__c);
            //     ressMap.put(info.Hospital4__c, info.Hospital4__c);
            // }
            hpIdsetsMap.put(info.Id, ressMap);
            if (info.department_selection__c != null) {
                List<String> tenDepartList = info.department_selection__c.split(';');
                department_selectionMap.put(info.Id, tenDepartList);
            }
            //kk ******** DB202309519775  start
            if (info.department_selection1__c != null) {
                List<String> tenDepartList1 = info.department_selection1__c.split(';');
                department_selectionMap1.put(info.Id, tenDepartList1);
            }
            if (info.department_selection2__c != null) {
                List<String> tenDepartList2 = info.department_selection2__c.split(';');
                department_selectionMap2.put(info.Id, tenDepartList2);
            }
            if (info.department_selection3__c != null) {
                List<String> tenDepartList3 = info.department_selection3__c.split(';');
                department_selectionMap3.put(info.Id, tenDepartList3);
            }
            if (info.department_selection4__c != null) {
                List<String> tenDepartList4 = info.department_selection4__c.split(';');
                department_selectionMap4.put(info.Id, tenDepartList4);
            }
            //kk ******** DB202309519775  end
        }

        if (hpIdsets.contains(null)) {
            hpIdsets.remove(null);
        }

        List<Account> accList = new List<Account>();
        System.debug('-------4=======' + hpIdsets);
        if (hpIdsets.size() > 0) {
            System.debug('-------5=======');
            //获取医院下所有启用战略科室的启用所有人
            //取得战略科室:
            // accList = [select id,OwnerId,ParentId,OwnerIsActive__c from Account where Parent.RecordType_DeveloperName__c = 'HP'
            //                                          AND OwnerIsActive__c = true
            //                                          AND ParentId  in :hpIdsets];
            //获取医院上的主担当
            accList = [select id, GI_Main__c, GI_Main__r.IsActive, BF_owner__c, BF_owner__r.IsActive,
                       ET_owner__c, ET_owner__r.IsActive, SP_Main__c, SP_Main__r.IsActive, URO_owner_ID__c, URO_owner_ID__r.IsActive,
                       GYN_owner__c, GYN_owner__r.IsActive, ENT_owner_ID__c, ENT_owner_ID__r.IsActive, Energy_LeaderStr__c
                       from Account where  Id in :hpIdsets ];
        }




        // List<Tender_information__c> TenInfoList = new List<Tender_information__c>();
        // Map<Id,Map<String,Id>> ddd = new Map<Id,Map<String,Id>>();
        // for (Id  tenderId : hpIdsetsMap.keySet()) {
        //     // Tender_information__c ten = new Tender_information__c();
        //     // ten.Id = tenderId;
        //     Id tenderownerId = tenderinformationOwnerMap.get(tenderId);
        //     Map<Id, Id> ressMap = hpIdsetsMap.get(tenderId);
        //     for (Account hp : accList) {
        //         Map<String, Id> hptempMap = new Map<String, Id>();

        //         Map<String, Id> hptempMap1 = new Map<String, Id>();
        //         if (ressMap.containsKey(hp.Id) && ddd.containsKey(tenderId)) {
        //             hptempMap1 = ddd.get()
        //         }
        //         if (hp.GI_Main__c != null && hp.GI_Main__r.IsActive ) {
        //             hptempMap.put('01210000000QemLAAS', hp.GI_Main__c);
        //             // ttt = tenderownerId == hp.GI_Main__c;
        //             hptempMap1.put(hp.GI_Main__c,hp.GI_Main__c);
        //         }
        //         // 呼吸科主担当
        //         if (hp.BF_owner__c != null && hp.BF_owner__r.IsActive ) {
        //             hptempMap.put('01210000000QezZAAS', hp.BF_owner__c);
        //         }

        //         // 普外科主担当
        //         if (hp.SP_Main__c != null && hp.SP_Main__r.IsActive) {
        //             hptempMap.put('01210000000QezeAAC', hp.SP_Main__c);
        //         }
        //         // 泌尿科主担当
        //         if (hp.URO_owner_ID__c != null && hp.URO_owner_ID__r.IsActive) {
        //             hptempMap.put('01210000000QezjAAC', hp.URO_owner_ID__c);
        //         }
        //         // 妇科主担当
        //         if (hp.GYN_owner__c != null && hp.GYN_owner__r.IsActive) {
        //             hptempMap.put('01210000000QezoAAC', hp.GYN_owner__c);
        //         }
        //         // 耳鼻喉科主担当
        //         if (hp.ENT_owner_ID__c != null && hp.ENT_owner_ID__r.IsActive) {
        //             hptempMap.put('01210000000QeztAAC', hp.ENT_owner_ID__c);
        //         }

        //         // if (ressMap.containsKey(hp.Id) && !hptempMap1.containsKey(tenderownerId)) {
        //         //     ddd.put(tenderId, hptempMap);
        //         // }
        //     }


        // }
        // 用于更新招投标
        List<Tender_information__c> TenInfoList = new List<Tender_information__c>();

        Map<Id, Map<String, Id>> hpTypeToMainMap = new Map<Id, Map<String, Id>>();
        // 存招投标对应的医院主担当
        Map<Id, Map<String, Id>> hpMainMap = new Map<Id, Map<String, Id>>();
        for (Id  tenderId : hpIdsetsMap.keySet()) {
            // 招投标 所有人
            Id tenderownerId = tenderinformationOwnerMap.get(tenderId);
            // 存医院
            Map<Id, Id> ressMap = hpIdsetsMap.get(tenderId);
            System.debug('-------3=======' + accList);
            Map<String, Id> hpTypeToMaintempMap = new Map<String, Id>();
            if (accList.size() > 0) {
                for (Account hp : accList) {
                    // 医院主担当 记录类型Id 对应 医院主担当
                    hpTypeToMaintempMap = new Map<String, Id>();
                    // 存放 医院主担当
                    Map<String, Id> hpMaintempMap = new Map<String, Id>();
                    // 关联医院
                    if (ressMap.containsKey(hp.Id) && hpMainMap.containsKey(tenderId)) {
                        hpMaintempMap = hpMainMap.get(tenderId);
                    }
                    if (hp.GI_Main__c != null && hp.GI_Main__r.IsActive ) {
                        hpTypeToMaintempMap.put(System.label.tender_4, hp.GI_Main__c);
                        // kk  ********  DB202309519775 start
                        // 新增的关联战略科室1、2、3、4和原来映射不一样，得新增映射
                        hpTypeToMaintempMap.put('消化科', hp.GI_Main__c);
                        // kk ********  DB202309519775 end
                        hpMaintempMap.put(hp.GI_Main__c, hp.GI_Main__c);
                    }
                    // 呼吸科主担当
                    if (hp.BF_owner__c != null && hp.BF_owner__r.IsActive ) {
                        hpTypeToMaintempMap.put(System.label.tender_5, hp.BF_owner__c);
                        // kk  ********  DB202309519775 start
                        // 新增的关联战略科室1、2、3、4和原来映射不一样，得新增映射
                        hpTypeToMaintempMap.put('呼吸科', hp.BF_owner__c);
                        // kk ********  DB202309519775 end
                        hpMaintempMap.put(hp.BF_owner__c, hp.BF_owner__c);
                    }
                    // 普外科主担当
                    if (hp.SP_Main__c != null && hp.SP_Main__r.IsActive) {
                        hpTypeToMaintempMap.put(System.label.tender_6, hp.SP_Main__c);
                        // kk  ********  DB202309519775 start
                        // 新增的关联战略科室1、2、3、4和原来映射不一样，得新增映射
                        hpTypeToMaintempMap.put('普外科', hp.SP_Main__c);
                        // kk ********  DB202309519775 end
                        hpMaintempMap.put(hp.SP_Main__c, hp.SP_Main__c);
                    }
                    // 泌尿科主担当
                    if (hp.URO_owner_ID__c != null && hp.URO_owner_ID__r.IsActive) {
                        hpTypeToMaintempMap.put(System.label.tender_7, hp.URO_owner_ID__c);
                        // kk  ********  DB202309519775 start
                        // 新增的关联战略科室1、2、3、4和原来映射不一样，得新增映射
                        hpTypeToMaintempMap.put('泌尿科', hp.URO_owner_ID__c);
                        // kk ********  DB202309519775 end
                        hpMaintempMap.put(hp.URO_owner_ID__c, hp.URO_owner_ID__c);
                    }
                    // 妇科主担当
                    if (hp.GYN_owner__c != null && hp.GYN_owner__r.IsActive) {
                        hpTypeToMaintempMap.put(System.label.tender_8, hp.GYN_owner__c);
                        // kk  ********  DB202309519775 start
                        // 新增的关联战略科室1、2、3、4和原来映射不一样，得新增映射
                        hpTypeToMaintempMap.put('妇科', hp.GYN_owner__c);
                        // kk ********  DB202309519775 end
                        hpMaintempMap.put(hp.GYN_owner__c, hp.GYN_owner__c);
                    }
                    // 耳鼻喉科主担当
                    if (hp.ENT_owner_ID__c != null && hp.ENT_owner_ID__r.IsActive) {
                        hpTypeToMaintempMap.put(System.label.tender_9, hp.ENT_owner_ID__c);
                        // kk  ********  DB202309519775 start
                        // 新增的关联战略科室1、2、3、4和原来映射不一样，得新增映射
                        hpTypeToMaintempMap.put('耳鼻喉科', hp.ENT_owner_ID__c);
                        // kk ********  DB202309519775 end
                        hpMaintempMap.put(hp.ENT_owner_ID__c, hp.ENT_owner_ID__c);
                    }
                    // 存招投标下所有关联医院 的所有主担当
                    if (ressMap.containsKey(hp.Id) ) {
                        hpMainMap.put(tenderId, hpMaintempMap);
                    }
                }
            }
            // 所有关联医院的主担当不包含 招投标所有人
            System.debug('-------2=======' + hpMainMap);
            if (hpMainMap.get(tenderId) != null) {
                // 增加判断战略科室
                // kk 20231207 DB202309519775 优化 department_selectionMap.get(tenderId) start
                // if (department_selectionMap.get(tenderId) != null) {
                if (department_selectionMap.containsKey(tenderId) && department_selectionMap.get(tenderId) != null) {
                    boolean changeOwner = true;
                    Map<String, Id> tenHpMainMap = hpMainMap.get(tenderId);
                    for (String type : department_selectionMap.get(tenderId)) {
                        if (tenHpMainMap.get(type) != null && tenHpMainMap.get(type).equals(tenderownerId)) {
                            changeOwner = false;
                            break;
                        }
                    }
                    //kk ******** DB202309519775   start
                    //增加关联战略科室1、2、3、4的判断
                    if (department_selectionMap1.containsKey(tenderId) && department_selectionMap1.get(tenderId) != null) {
                        for (String type : department_selectionMap1.get(tenderId)) {
                            if (tenHpMainMap.get(type) != null && tenHpMainMap.get(type).equals(tenderownerId)) {
                                changeOwner = false;
                                break;
                            }
                        }
                    }
                    
                    if (department_selectionMap2.containsKey(tenderId) && department_selectionMap2.get(tenderId) != null) {
                        for (String type : department_selectionMap2.get(tenderId)) {
                            if (tenHpMainMap.get(type) != null && tenHpMainMap.get(type).equals(tenderownerId)) {
                                changeOwner = false;
                                break;
                            }
                        }
                    }
                    if (department_selectionMap3.containsKey(tenderId) && department_selectionMap3.get(tenderId) != null) {
                        for (String type : department_selectionMap3.get(tenderId)) {
                            if (tenHpMainMap.get(type) != null && tenHpMainMap.get(type).equals(tenderownerId)) {
                                changeOwner = false;
                                break;
                            }
                        }
                    }
                    if (department_selectionMap4.containsKey(tenderId) && department_selectionMap4.get(tenderId) != null) {
                        for (String type : department_selectionMap4.get(tenderId)) {
                            if (tenHpMainMap.get(type) != null && tenHpMainMap.get(type).equals(tenderownerId)) {
                                changeOwner = false;
                                break;
                            }
                        }
                    }
                    
                    //kk ******** DB202309519775   end
                    if (changeOwner) {
                        hpTypeToMainMap.put(tenderId, hpTypeToMaintempMap);
                    }
                } else {
                    hpTypeToMaintempMap.put(System.label.tender_3, System.label.tender_3);
                    hpTypeToMainMap.put(tenderId, hpTypeToMaintempMap);
                }
            } else {
                hpTypeToMaintempMap.put(System.label.tender_3, System.label.tender_3);
                hpTypeToMainMap.put(tenderId, hpTypeToMaintempMap);
            }

        }
        System.debug('-------1=======' + hpTypeToMainMap);
        Map<Id, Id> UpdateTenMap = new Map<Id, Id>();
        if (hpTypeToMainMap.size() > 0) {
            // 查招投标-询价关联表
            List<Tender_Opportunity_Link__c> TenOppLinkList = [select Id, Opportunity__c, Tender_information__c, Opportunity__r.OwnerId, Opportunity__r.Owner.IsActive, Name
                    from Tender_Opportunity_Link__c where Tender_information__c in :hpTypeToMainMap.keySet() and  Opportunity__r.Owner.IsActive = true
                                    order by Tender_information__c, Name asc];
            Map<Id, Id> oppMainMap = new Map<Id, Id>();
            for (Tender_Opportunity_Link__c oppLinks : TenOppLinkList) {
                if (!oppMainMap.containsKey(oppLinks.Tender_information__c)) {
                    oppMainMap.put(oppLinks.Tender_information__c, oppLinks.Opportunity__r.OwnerId );
                }
            }
            for (Id  tenderId : hpTypeToMainMap.keySet()) {
                for (Tender_information__c info : newList) {
                    // add 【委托】P-招标项目-手动创建的招标项目增加必填字段 2021/11/03 fxk Star
                    Tender_information__c oldTen = new Tender_information__c();
                    if (oldMap!=null && oldMap.containskey(info.Id)) {
                        oldTen = oldMap.get(info.Id);
                    }
                    // add 【委托】P-招标项目-手动创建的招标项目增加必填字段 2021/11/03 fxk End
                    // 招投标没关联询价
                    if (!oppMainMap.containsKey(tenderId)) {
                        System.debug('----------5---------' + oppMainMap);
                        // 招投标 关联战略科室（字段） 有值
                        if (department_selectionMap.containsKey(tenderId)) {
                            System.debug('----------4---------' + department_selectionMap);
                            // 战略科室的 记录类型
                            String typeId = department_selectionMap.get(tenderId) == null ? '' : department_selectionMap.get(tenderId)[0];
                            System.debug('----------6---------' + typeId);
                            Map<String, Id> hpTypeToMaintempMap = hpTypeToMainMap.get(tenderId);
                            System.debug('----------7---------' + hpTypeToMaintempMap);
                            if (hpTypeToMaintempMap.containsKey(typeId)) {
                                UpdateTenMap.put(tenderId, hpTypeToMaintempMap.get(typeId));
                                System.debug('----------8---------' + UpdateTenMap);
                            } else {
                                UpdateTenMap.put(tenderId, System.label.tender_3);
                                System.debug('----------9---------' + UpdateTenMap);
                            }
                        } else {
                            // 20220421 SWAG-CC58ME ljh add 是否相关：是；关联医院:！null; 关联战略科室 null; start
                            // 是否应标：有值 ：所有人：启用 不需要更新
                            // UpdateTenMap.put(tenderId, '00510000000gmxH');
                            // System.debug('----------10---------' + UpdateTenMap);
                            if(!(String.isNotBlank(info.IsBid__c) && info.OwnerIsActive__c)){
                                UpdateTenMap.put(tenderId, System.label.tender_3);
                                System.debug('----------10---------' + UpdateTenMap);
                            }
                            // 20220421 SWAG-CC58ME ljh add 是否相关：是；关联医院:！null; 关联战略科室 null; end
                        }
                     } else {
                        /*
                        20220422 SWAG-CC58ME ljh update   
                        针对如下场景做的修改：
                        相关性确认：之后所有人取了医院+第一个战略科室的主担当A，然后关联询价，询价所有人是B，
                        所有人取了B。然后B离职了，运行Batch，变成A，在把B启用，不变会来B
                        */
                        // 20220422 ljh update start
                        System.debug('zheli:'+oppMainMap.get(tenderId));
                        Boolean a = oldTen.OwnerId.equals(info.OwnerId);
                        Boolean b = !hpMainMap.get(tenderId).containskey(info.ownerId);
                        System.debug('zheli:'+a+b);
                        // if ((oldTen.OwnerId != null && oldTen.OwnerId.equals(info.OwnerId)) 
                        //      && !hpMainMap.get(tenderId).containskey(info.ownerId)) {
                        // 20220422 ljh update end
                        UpdateTenMap.put(tenderId, oppMainMap.get(tenderId));
                        System.debug('----------11---------' + UpdateTenMap);
                    }
                }
            }
        }
        // StaticParameter.EscapeOtherUpdateTenOwner = false;
        if (UpdateTenMap.size() > 0) {
            for (Tender_information__c info : newList) {
                if (UpdateTenMap.containsKey(info.Id)) {
                    info.OwnerId = UpdateTenMap.get(info.Id);
                }
            }
        }
        // StaticParameter.EscapeOtherUpdateTenOwner = true;
        isFirst = false;
    }
    /*
    集采项目导致所有人更新
    **/
    // 20221011 ljh  SWAG-CK28WT
    public void updateTenOwnerJC() {
        // 
        String OCSM_province = '市场企划本部';
        Id OwnerIdStr;
        List<OCM_Management_Province__c> ompList = [select Id,TenderAssistant1__c,TenderAssistant1__r.IsActive,TenderAssistant2__c from OCM_Management_Province__c where name =:OCSM_province];
        if(ompList.size() > 0 && ompList[0].TenderAssistant1__r.IsActive){
            OwnerIdStr = ompList[0].TenderAssistant1__c;
        }
        System.debug('zheli00:'+OwnerIdStr);
        for (Tender_information__c nObj : newList) {
            Tender_information__c oObj = oldMap.get(nObj.Id);
            if (nObj.CentralizedProject__c != oObj.CentralizedProject__c) {
                if(nObj.CentralizedProject__c && OwnerIdStr != null){
                    /**
                    本部自动变成市场企划本部，项目助理和所有人都是市场企划本部的人，
                    取OCSM管理省页面维护的市场企划本部，招标项目助理1，
                    */
                    system.debug('===进这里了1===');
                    nObj.OwnerId = OwnerIdStr;
                    nObj.InfoOwner__c = OwnerIdStr;
                }else if(!nObj.CentralizedProject__c){
                    /**
                    取消勾选后返回营业部门的省和本部，
                    所有人根据关联医院和战略科室取担当的名字。
                    */
                    system.debug('===进这里了2===');
                    updateTenOwner();
                } 
            }
            // 如果触发 更新所有人 没有询价则 还是市场企划本部
            if (!StaticParameter.EscapeOtherUpdateTenOwner) {
                if(nObj.CentralizedProject__c && OwnerIdStr != null && (nObj.OpportunityNum__c == 0 || nObj.OpportunityNum__c == null)){
                    system.debug('===进这里了3===');
                    nObj.OwnerId = OwnerIdStr;
                    nObj.InfoOwner__c = OwnerIdStr;
                }
            }
        }
    }
    /** 
    //20221208 you DB202211594688
    // 中标任务 废标流标时清空中标日
    public void clearConfirmationofAward() {
        List<String> tenders = new List<String>();
        String labelTypes = System.Label.ClearConfirmationofAwardTypes;
        List<String> subTypes = labelTypes.split(',');
        for (Tender_information__c tender : this.newList) {
            Tender_information__c old_tender = this.oldMap.get(tender.Id);
            if (subTypes.contains(tender.subInfoType__c) && !tender.subInfoType__c.equals(old_tender.subInfoType__c)) {
                tenders.add(tender.Id);
            }
        }
        if (tenders.size() > 0) {
            List<Tender_Opportunity_Link__c> links = [select Id, Opportunity__c from Tender_Opportunity_Link__c where Tender_information__c in :tenders];
            if (links != null && links.size() > 0) {
                List<String> oppids = new List<String>();
                for (Tender_Opportunity_Link__c link : links) {
                    oppids.add(link.Opportunity__c);
                }
                // 状态=询价且没有做合同申请的
                List<Opportunity> opps = [select Id, Closing_Bid_Date__c 
                                        from Opportunity 
                                        where StageName = '引合' 
                                        and IF_Submit__c = false
                                        and Id in :oppids];
                if (opps != null && opps.size() > 0) {
                    for (Opportunity opp : opps) {
                        opp.Closing_Bid_Date__c = null;
                    }
                    StaticParameter.EscapeOpportunityBefUpdTrigger = true;
                    update opps;
                    StaticParameter.EscapeOpportunityBefUpdTrigger = false;
                }
            }
        }
    }
    **/

    //lt DB202304062844 20230515 项目终止流程开发 start  subInfoType__c
    //如果项目阶段发生变化 或者阶段补充说明发生变化 或者终止审批发生变化且审批通过 把招标项目上的IsReactionOpp__c设置为true
    public void SetIsReactionOpp() {

        List<Tender_information__c> tenreportList = new List<Tender_information__c>();//20240513 终止
        //lt 20240522 DB202311608156 招标项目阶段回退补充阶段记录日 start
        String UserId = UserInfo.getUserId();
        String JumpUser = System.Label.System_User_Id;
        System.debug('20240619---UserId:'+UserId);
        System.debug('20240619---JumpUser:'+JumpUser);
        //lt 20240522 DB202311608156 招标项目阶段回退补充阶段记录日 end
        for(Tender_information__c newTen : newList){
            Tender_information__c oldTen = oldMap.get(newTen.Id);
            if(newTen.InfoType__c != oldTen.InfoType__c || newTen.subInfoType__c != oldTen.subInfoType__c || (newTen.TerminateApprovalStatus__c != oldTen.TerminateApprovalStatus__c && newTen.TerminateApprovalStatus__c == '批准')){
                newTen.IsReactionOpp__c = true;
            }
            //lt 20240522 DB202311608156 招标项目阶段回退补充阶段记录日 start
            if (!JumpUser.contains(UserId)){
                System.debug('跳过系统用户20240619');
                if(newTen.InfoType__c == '2：公告' && oldTen.InfoType__c == '3：结果'){
                    newTen.publicDate__c = newTen.publicDate__c == null ? oldTen.ResultDate__c : newTen.publicDate__c;//20240619 修改点：有值不覆盖 清空结果记录日
                    newTen.ResultDate__c = null;
                }else if(newTen.InfoType__c == '1：预告' && oldTen.InfoType__c == '3：结果'){
                    newTen.noticeDate__c = newTen.noticeDate__c == null ? oldTen.ResultDate__c : newTen.noticeDate__c;
                    newTen.ResultDate__c = null;
                    newTen.publicDate__c = null;
                }else if(newTen.InfoType__c == '1：预告' && oldTen.InfoType__c == '2：公告'){
                    newTen.noticeDate__c = newTen.noticeDate__c == null ? oldTen.publicDate__c : newTen.noticeDate__c;
                    newTen.publicDate__c = null;
                }else if(newTen.InfoType__c == '2：公告' && oldTen.InfoType__c == '1：预告'){  //阶段前进
                    newTen.publicDate__c = newTen.publicDate__c == null ? oldTen.noticeDate__c : newTen.publicDate__c;
                    newTen.noticeDate__c = null;
                }else if(newTen.InfoType__c == '3：结果' && oldTen.InfoType__c == '1：预告'){
                    newTen.ResultDate__c = newTen.ResultDate__c == null ? oldTen.noticeDate__c : newTen.ResultDate__c;
                    newTen.noticeDate__c = null;
                }else if(newTen.InfoType__c == '3：结果' && oldTen.InfoType__c == '2：公告'){
                    newTen.ResultDate__c = newTen.ResultDate__c == null ? oldTen.publicDate__c : newTen.ResultDate__c;
                    // newTen.publicDate__c = null;
                }
            }
            //lt 20240522 DB202311608156 招标项目阶段回退补充阶段记录日 end                       


            //lt 20240118 DB202401275115 紧急：招标项目终止状态的逻辑调整 start 
            //3：结果阶段除流标废标外，非经销商的原因的招标项目要重启
            if(newTen.InfoType__c == '3：结果' && newTen.subInfoType__c != oldTen.subInfoType__c 
              && newTen.subInfoType__c != '3-1：废标公告' && newTen.subInfoType__c != '3-2：流标公告'
              && oldTen.TerminateApprovalStatus__c == '批准' && oldTen.TerminateReason__c != '经销商原因'){
                    newTen.ProjectRestartFLG__c = true;
                    newTen.IsTerminate__c = null;
                    newTen.TerminateReason__c = null;
                    newTen.TerminateApprovalStatus__c = null;
                    newTen.TerminateExtraContent__c = null;
                    newTen.TerminateApprovalTime__c = null ; //批准时间清

                    tenreportList.add(newTen);  //20240513 终止

            }
            //lt 20240118 DB202401275115 紧急：招标项目终止状态的逻辑调整 end 
        }

        //lt 项目重启后首页提醒
        System.debug('DB202304062844========20240415---tenreportList:'+tenreportList);
        if(tenreportList.Size() > 0){
            updateStrategic_department_MainId(tenreportList);
        }
        //lt 项目重启后首页提醒

        Integer i = 0;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
    }
    //lt DB202304062844 20230515 项目终止流程开发 end

    //sx DB202404234371 20240506 是否终止 通过Batch更新询价 start
    public void IsTerminateUpdateOpp(){
        List<String> tenderIds = new List<String>();
        for (Tender_information__c info : newList) {
            // 新建 【是否终止】为‘是’ 或者 更新招标【是否终止】字段为‘是’
            // 项目终止审批通过后更新询价上的字段
            System.debug('info.OpportunityNum__c==='+info.OpportunityNum__c);
            // if((info.TerminateApprovalStatus__c == '批准' && oldMap.get(info.Id).TerminateApprovalStatus__c != '批准') || (info.TerminateApprovalStatus__c == '驳回' && oldMap.get(info.Id).TerminateApprovalStatus__c != '驳回')){
            if((info.TerminateApprovalStatus__c == '批准' && oldMap.get(info.Id).TerminateApprovalStatus__c != '批准')){
                tenderIds.add(info.Id);
            }else if(String.isBlank(info.IsTerminate__c) && String.isNotBlank(oldMap.get(info.Id).IsTerminate__c)){
                tenderIds.add(info.Id);
            }else if(info.TerminateApprovalStatus__c == '批准' && info.OpportunityNum__c != oldMap.get(info.Id).OpportunityNum__c){
                tenderIds.add(info.Id);
            // chenjingwu DB202505290075 20250611 start
            }else if(info.CopyNotIndicateReasons__c != oldMap.get(info.Id).CopyNotIndicateReasons__c){
                tenderIds.add(info.Id);
            // chenjingwu DB202505290075 20250611 end
            }
        }
        System.debug('tenderIds=='+tenderIds);
        if(tenderIds.size() > 0){
            TenderinformatioBatch tBatch = new TenderinformatioBatch(tenderIds);
            DataBase.executeBatch(tBatch);
        }
    }
    //sx DB202404234371 20240506 是否终止 通过Batch更新询价 end
    //kk  新增包含科室check 20240813 start
    public void checkIncludeDepart(){
        if(oldMap !=null){//update
            List<String> insertTenAccList = new List<String>();
            Map<String,List<String>>  accAndDepartMap = new Map<String,List<String>>();
            for(Tender_information__c tender :  newList){
                if(tender.Hospital__c != null && (oldMap.get(tender.Id).Hospital__c != tender.Hospital__c || oldMap.get(tender.Id).department_selection__c != tender.department_selection__c)){
                    system.debug('kk 0813 01');
                    insertTenAccList.add(tender.Id);
                }
                if(tender.Hospital1__c != null && (oldMap.get(tender.Id).Hospital1__c != tender.Hospital1__c || oldMap.get(tender.Id).department_selection1__c != tender.department_selection1__c)){
                    insertTenAccList.add(tender.Id);
                }
                if(tender.Hospital2__c != null && (oldMap.get(tender.Id).Hospital2__c != tender.Hospital2__c || oldMap.get(tender.Id).department_selection2__c != tender.department_selection2__c)){
                    insertTenAccList.add(tender.Id);
                }
                if(tender.Hospital3__c != null && (oldMap.get(tender.Id).Hospital3__c != tender.Hospital3__c || oldMap.get(tender.Id).department_selection3__c != tender.department_selection3__c)){
                    insertTenAccList.add(tender.Id);
                }
                if(tender.Hospital4__c != null && (oldMap.get(tender.Id).Hospital4__c != tender.Hospital4__c || oldMap.get(tender.Id).department_selection4__c != tender.department_selection4__c)){
                    insertTenAccList.add(tender.Id);
                }
            }
            if(insertTenAccList != null){
                accAndDepartMap = lexTransferRoomCompoentController.getInitIncludeDepartList(insertTenAccList);
                system.debug('kk 0813 02');
                for(Tender_information__c tender :  newList ){
                    String hospital = tender.Hospital__c;
                    String hospital1 = tender.Hospital1__c;
                    String hospital2 = tender.Hospital2__c;
                    String hospital3 = tender.Hospital3__c;
                    String hospital4 = tender.Hospital4__c;
                    String depar = tender.department_selection__c;
                    String depar1 = tender.department_selection1__c;
                    String depar2 = tender.department_selection2__c;
                    String depar3 = tender.department_selection3__c;
                    String depar4 = tender.department_selection4__c;
                    if((oldMap.get(tender.Id).Hospital__c != hospital || oldMap.get(tender.Id).department_selection__c != depar) && hospital !=null && depar != null && accAndDepartMap.containsKey(hospital)){
                        String realDeapart = lexTransferRoomCompoentController.getApiIdData(depar);
                        List<String> deparList = realDeapart.split(';');
                        for(String dep : deparList){
                            if(!accAndDepartMap.get(hospital).contains(dep)){
                                tender.department_selection__c.addError('此关联战略科室不在关联医院的包含科室的范围当中,请检查');
                            }
                        }
                    }
                    if((oldMap.get(tender.Id).Hospital1__c != hospital1 || oldMap.get(tender.Id).department_selection1__c != depar1) && hospital1 !=null && depar1 != null && accAndDepartMap.containsKey(hospital1)){
                        List<String> deparList = depar1.split(';');
                        for(String dep : deparList){
                            if(!accAndDepartMap.get(hospital1).contains(dep)){
                                tender.department_selection1__c.addError('此关联战略科室不在关联医院的包含科室的范围当中,请检查');
                            }
                        }
                    }
                    if((oldMap.get(tender.Id).Hospital2__c != hospital2 || oldMap.get(tender.Id).department_selection2__c != depar2) && hospital2 !=null && depar2 != null && accAndDepartMap.containsKey(hospital2)){
                        List<String> deparList = depar2.split(';');
                        for(String dep : deparList){
                            if(!accAndDepartMap.get(hospital2).contains(dep)){
                                tender.department_selection2__c.addError('此关联战略科室不在关联医院的包含科室的范围当中,请检查');
                            }
                        }
                    }
                    if((oldMap.get(tender.Id).Hospital3__c != hospital3 || oldMap.get(tender.Id).department_selection3__c != depar3) && hospital3 !=null && depar3 != null && accAndDepartMap.containsKey(hospital3)){
                        List<String> deparList = depar3.split(';');
                        for(String dep : deparList){
                            if(!accAndDepartMap.get(hospital3).contains(dep)){
                                tender.department_selection3__c.addError('此关联战略科室不在关联医院的包含科室的范围当中,请检查');
                            }
                        }
                    }
                    if((oldMap.get(tender.Id).Hospital4__c != hospital4 || oldMap.get(tender.Id).department_selection4__c != depar4) && hospital4 !=null && depar4 != null && accAndDepartMap.containsKey(hospital4)){
                        List<String> deparList = depar4.split(';');
                        for(String dep : deparList){
                            if(!accAndDepartMap.get(hospital4).contains(dep)){
                                tender.department_selection4__c.addError('此关联战略科室不在关联医院的包含科室的范围当中,请检查');
                            }
                        }
                    }
                }
            }
        }else{//insert
            List<String> insertTenAccList = new List<String>();
            Map<String,List<String>>  accAndDepartMap = new Map<String,List<String>>();
            for(Tender_information__c tender :  newList){
                if(tender.Hospital__c != null){
                    insertTenAccList.add(tender.Id);
                }
                if(tender.Hospital1__c != null){
                    insertTenAccList.add(tender.Id);
                }
                if(tender.Hospital2__c != null){
                    insertTenAccList.add(tender.Id);
                }
                if(tender.Hospital3__c != null){
                    insertTenAccList.add(tender.Id);
                }
                if(tender.Hospital4__c != null){
                    insertTenAccList.add(tender.Id);
                }
            }
            if(insertTenAccList != null){
                accAndDepartMap = lexTransferRoomCompoentController.getInitIncludeDepartList(insertTenAccList);
                for(Tender_information__c tender :  newList ){
                    String hospital = tender.Hospital__c;
                    String hospital1 = tender.Hospital1__c;
                    String hospital2 = tender.Hospital2__c;
                    String hospital3 = tender.Hospital3__c;
                    String hospital4 = tender.Hospital4__c;
                    String depar = tender.department_selection__c;
                    String depar1 = tender.department_selection1__c;
                    String depar2 = tender.department_selection2__c;
                    String depar3 = tender.department_selection3__c;
                    String depar4 = tender.department_selection4__c;
                    if(hospital !=null && depar != null && accAndDepartMap.containsKey(hospital)){
                        String realDeapart = lexTransferRoomCompoentController.getApiIdData(depar);
                        List<String> deparList = realDeapart.split(';');
                        for(String dep : deparList){
                            if(!accAndDepartMap.get(hospital).contains(dep)){
                                tender.department_selection__c.addError('此关联战略科室不在关联医院的包含科室的范围当中,请检查');
                            }
                        }
                    }
                    if(hospital1 !=null && depar1 != null && accAndDepartMap.containsKey(hospital1)){
                        List<String> deparList = depar1.split(';');
                        for(String dep : deparList){
                            if(!accAndDepartMap.get(hospital1).contains(dep)){
                                tender.department_selection1__c.addError('此关联战略科室不在关联医院的包含科室的范围当中,请检查');
                            }
                        }
                    }
                    if(hospital2 !=null && depar2 != null && accAndDepartMap.containsKey(hospital2)){
                        List<String> deparList = depar2.split(';');
                        for(String dep : deparList){
                            if(!accAndDepartMap.get(hospital2).contains(dep)){
                                tender.department_selection2__c.addError('此关联战略科室不在关联医院的包含科室的范围当中,请检查');
                            }
                        }
                    }
                    if(hospital3 !=null && depar3 != null && accAndDepartMap.containsKey(hospital3)){
                        List<String> deparList = depar3.split(';');
                        for(String dep : deparList){
                            if(!accAndDepartMap.get(hospital4).contains(dep)){
                                tender.department_selection3__c.addError('此关联战略科室不在关联医院的包含科室的范围当中,请检查');
                            }
                        }
                    }
                    if(hospital4 !=null && depar4 != null && accAndDepartMap.containsKey(hospital4) && !accAndDepartMap.get(hospital4).contains(depar4)){
                        List<String> deparList = depar4.split(';');
                        for(String dep : deparList){
                            if(!accAndDepartMap.get(hospital4).contains(dep)){
                                tender.department_selection4__c.addError('此关联战略科室不在关联医院的包含科室的范围当中,请检查');
                            }
                        }
                    }
                }
            }
        }
    }
    //kk  新增包含科室check 20240813 end
    // DB202406382590 招标项目：不应标申请里增加涉及科室 start
    public void checkIfNotAllBid(){
        List<Tender_information__c> tenderList = new List<Tender_information__c>();
        for(Tender_information__c ten1: newList){
            ten1.IfNotAllBid__c = true;
            String dstion = lexTransferRoomCompoentController.getApiIdData(ten1.department_selection__c);
            String[] department_selection = String.isNotBlank(dstion) ? dstion.split(';') : new String[]{};
            String[] department_selection1 = String.isNotBlank(ten1.department_selection1__c) ? ten1.department_selection1__c.split(';') : new String[]{};
            String[] department_selection2 = String.isNotBlank(ten1.department_selection2__c) ? ten1.department_selection2__c.split(';') : new String[]{};
            String[] department_selection3 = String.isNotBlank(ten1.department_selection3__c) ? ten1.department_selection3__c.split(';') : new String[]{};
            String[] department_selection4 = String.isNotBlank(ten1.department_selection4__c) ? ten1.department_selection4__c.split(';') : new String[]{};
            String[] notBidsSelection = String.isNotBlank(ten1.notBidsSelection__c) ? ten1.notBidsSelection__c.split(';') : new String[]{};
            String[] notBidsSelection1 = String.isNotBlank(ten1.notBidsSelection1__c) ? ten1.notBidsSelection1__c.split(';') : new String[]{};
            String[] notBidsSelection2 = String.isNotBlank(ten1.notBidsSelection2__c) ? ten1.notBidsSelection2__c.split(';') : new String[]{};
            String[] notBidsSelection3 = String.isNotBlank(ten1.notBidsSelection3__c) ? ten1.notBidsSelection3__c.split(';') : new String[]{};
            String[] notBidsSelection4 = String.isNotBlank(ten1.notBidsSelection4__c) ? ten1.notBidsSelection4__c.split(';') : new String[]{};
            System.debug('111==>'+dstion+'---'+ten1.notBidsSelection__c);
            // DB202406382590 招标项目：不应标申请里增加涉及科室 fy start
            // if(department_selection.size() > notBidsSelection.size()
            // || department_selection1.size() > notBidsSelection1.size()
            // || department_selection2.size() > notBidsSelection2.size()
            // || department_selection3.size() > notBidsSelection3.size()
            // || department_selection4.size() > notBidsSelection4.size()){
            if(
            (department_selection.size() == 0 
            && department_selection1.size() == 0 
            && department_selection2.size() == 0 
            && department_selection3.size() == 0 
            && department_selection4.size() == 0
            // DB202406382590 招标项目：不应标申请里增加涉及科室 fy start 
            && ten1.NotBidApprovalStatus__c != '批准'
            // DB202406382590 招标项目：不应标申请里增加涉及科室 fy end
            ) ||
            (department_selection.size() > notBidsSelection.size()
            || department_selection1.size() > notBidsSelection1.size()
            || department_selection2.size() > notBidsSelection2.size()
            || department_selection3.size() > notBidsSelection3.size()
            || department_selection4.size() > notBidsSelection4.size())
            ){
            // DB202406382590 招标项目：不应标申请里增加涉及科室 fy end
                ten1.IfNotAllBid__c = false;
            }else{
                for (String ds : department_selection) {
                    String containnotBids = String.isNotBlank(ten1.notBidsSelection__c) ? ten1.notBidsSelection__c : '';
                    if(!containnotBids.contains(ds)){
                        ten1.IfNotAllBid__c = false;
                    }
                    System.debug('222==>'+!containnotBids.contains(ds));
                    System.debug('333==>'+ds);
                }
                for (String ds : department_selection1) {
                    String containnotBids = String.isNotBlank(ten1.notBidsSelection1__c) ? ten1.notBidsSelection1__c : '';
                    if(!containnotBids.contains(ds)){
                        ten1.IfNotAllBid__c = false;
                    }
                }
                for (String ds : department_selection2) {
                    String containnotBids = String.isNotBlank(ten1.notBidsSelection2__c) ? ten1.notBidsSelection2__c : '';
                    if(!containnotBids.contains(ds)){
                        ten1.IfNotAllBid__c = false;
                    }
                }
                for (String ds : department_selection3) {
                    String containnotBids = String.isNotBlank(ten1.notBidsSelection3__c) ? ten1.notBidsSelection3__c : '';
                    if(!containnotBids.contains(ds)){
                        ten1.IfNotAllBid__c = false;
                    }
                }
                for (String ds : department_selection4) {
                    String containnotBids = String.isNotBlank(ten1.notBidsSelection4__c) ? ten1.notBidsSelection4__c : '';
                    if(!containnotBids.contains(ds)){
                        ten1.IfNotAllBid__c = false;
                    }
                }
            }
            if(ten1.notBidsSelection__c != oldMap.get(ten1.Id).notBidsSelection__c || 
                ten1.notBidsSelection1__c != oldMap.get(ten1.Id).notBidsSelection1__c || 
                ten1.notBidsSelection2__c != oldMap.get(ten1.Id).notBidsSelection2__c || 
                ten1.notBidsSelection3__c != oldMap.get(ten1.Id).notBidsSelection3__c || 
                ten1.notBidsSelection4__c != oldMap.get(ten1.Id).notBidsSelection4__c){
                tenderList.add(ten1);
            }
        }
        if(tenderList.Size() > 0){
            updateStrategic_department_MainId(tenderList);
        }
    }
    // DB202406382590 招标项目：不应标申请里增加涉及科室 end
}