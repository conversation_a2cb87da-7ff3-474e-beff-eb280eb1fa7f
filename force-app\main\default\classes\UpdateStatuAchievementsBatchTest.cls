@isTest
private class UpdateStatuAchievementsBatchTest {

    static testMethod void myUnitTest() {
        Opportunity opp = new Opportunity(Name='aiueo', StageName='出荷', CloseDate=Date.today());
        insert opp;
        
        Statu_Achievements__c sta = new Statu_Achievements__c(
            Name='test01',
            Opportunity__c = opp.Id,
            DeliveryStatus__c = 'test',
            PaymentRate__c = '1234',
            End_User_price__c = 0,
            AssignmentStatus__c = '全部分配',
            Last_week__c = '5 货齐,未付款,无用户合同',
            X2weeks_ago__c = '4 备货中',
            X3Weeks_ago__c = '3 已付款,无用户合同',
            X4weeks_ago__c = '2 待付款',
            X5weeks_ago__c = '1 全没有'
        );
        Statu_Achievements__c sta2 = new Statu_Achievements__c(
            Name='test02',
            Opportunity__c = opp.Id,
            DeliveryStatus__c = 'test',
            PaymentRate__c = '1234',
            End_User_price__c = 0,
            AssignmentStatus__c = '全部分配',
            Last_week__c = '5 货齐,未付款,无用户合同',
            X2weeks_ago__c = '4 备货中',
            X3Weeks_ago__c = '3 已付款,无用户合同',
            X4weeks_ago__c = '2 待付款',
            X5weeks_ago__c = '1 全没有',
            Last_Batch_Day__c = Date.today().addDays(-14)
        );
        Statu_Achievements__c sta3 = new Statu_Achievements__c(
            Name='test03',
            Opportunity__c = opp.Id,
            DeliveryStatus__c = 'test',
            PaymentRate__c = '1234',
            End_User_price__c = 0,
            AssignmentStatus__c = '全部分配',
            Last_week__c = '5 货齐,未付款,无用户合同',
            X2weeks_ago__c = '4 备货中',
            X3Weeks_ago__c = '3 已付款,无用户合同',
            X4weeks_ago__c = '2 待付款',
            X5weeks_ago__c = '1 全没有',
            Last_Batch_Day__c = Date.today().addDays(-21)
        );
        Statu_Achievements__c sta4 = new Statu_Achievements__c(
            Name='test04',
            Opportunity__c = opp.Id,
            DeliveryStatus__c = 'test',
            PaymentRate__c = '1234',
            End_User_price__c = 0,
            AssignmentStatus__c = '全部分配',
            Last_week__c = '5 货齐,未付款,无用户合同',
            X2weeks_ago__c = '4 备货中',
            X3Weeks_ago__c = '3 已付款,无用户合同',
            X4weeks_ago__c = '2 待付款',
            X5weeks_ago__c = '1 全没有',
            Last_Batch_Day__c = Date.today().addDays(-28)
        );
        Statu_Achievements__c sta5 = new Statu_Achievements__c(
            Name='test05',
            Opportunity__c = opp.Id,
            DeliveryStatus__c = 'test',
            PaymentRate__c = '1234',
            End_User_price__c = 0,
            AssignmentStatus__c = '全部分配',
            Last_week__c = '5 货齐,未付款,无用户合同',
            X2weeks_ago__c = '4 备货中',
            X3Weeks_ago__c = '3 已付款,无用户合同',
            X4weeks_ago__c = '2 待付款',
            X5weeks_ago__c = '1 全没有',
            Last_Batch_Day__c = Date.today().addDays(-35)
        );
        insert new Statu_Achievements__c[] {sta,sta2,sta3,sta4,sta5};
        
        Statu_Achievements__c sta1 = [select Current_status__c,Last_week__c,X2weeks_ago__c,X3Weeks_ago__c,X4weeks_ago__c,X5weeks_ago__c from Statu_Achievements__c where Id = :sta.Id];
        System.assertEquals('6 货齐,已付款,无用户合同', sta1.Current_status__c);
        System.assertEquals('5 货齐,未付款,无用户合同', sta1.Last_week__c);
        System.assertEquals('4 备货中', sta1.X2weeks_ago__c);
        System.assertEquals('3 已付款,无用户合同', sta1.X3Weeks_ago__c);
        System.assertEquals('2 待付款', sta1.X4weeks_ago__c);
        System.assertEquals('1 全没有', sta1.X5weeks_ago__c);
        
        System.Test.StartTest();
        Id execBTId = Database.executeBatch(new UpdateStatuAchievementsBatch(), 5);
        System.Test.StopTest();
        
        Statu_Achievements__c sta_1 = [select Current_status__c,Last_week__c,X2weeks_ago__c,X3Weeks_ago__c,X4weeks_ago__c,X5weeks_ago__c from Statu_Achievements__c where Id = :sta.Id];
        System.assertEquals('6 货齐,已付款,无用户合同', sta_1.Current_status__c);
        System.assertEquals('6 货齐,已付款,无用户合同', sta_1.Last_week__c);
        System.assertEquals('5 货齐,未付款,无用户合同', sta_1.X2weeks_ago__c);
        System.assertEquals('4 备货中', sta_1.X3Weeks_ago__c);
        System.assertEquals('3 已付款,无用户合同', sta_1.X4weeks_ago__c);
        System.assertEquals('2 待付款', sta_1.X5weeks_ago__c);
        
        Statu_Achievements__c sta_2 = [select Current_status__c,Last_week__c,X2weeks_ago__c,X3Weeks_ago__c,X4weeks_ago__c,X5weeks_ago__c from Statu_Achievements__c where Id = :sta2.Id];
        System.assertEquals('6 货齐,已付款,无用户合同', sta_2.Current_status__c);
        System.assertEquals('6 货齐,已付款,无用户合同', sta_2.Last_week__c);
        System.assertEquals(null, sta_2.X2weeks_ago__c);
        System.assertEquals('5 货齐,未付款,无用户合同', sta_2.X3Weeks_ago__c);
        System.assertEquals('4 备货中', sta_2.X4weeks_ago__c);
        System.assertEquals('3 已付款,无用户合同', sta_2.X5weeks_ago__c);
        
        Statu_Achievements__c sta_3 = [select Current_status__c,Last_week__c,X2weeks_ago__c,X3Weeks_ago__c,X4weeks_ago__c,X5weeks_ago__c from Statu_Achievements__c where Id = :sta3.Id];
        System.assertEquals('6 货齐,已付款,无用户合同', sta_3.Current_status__c);
        System.assertEquals('6 货齐,已付款,无用户合同', sta_3.Last_week__c);
        System.assertEquals(null, sta_3.X2weeks_ago__c);
        System.assertEquals(null, sta_3.X3Weeks_ago__c);
        System.assertEquals('5 货齐,未付款,无用户合同', sta_3.X4weeks_ago__c);
        System.assertEquals('4 备货中', sta_3.X5weeks_ago__c);
        
        Statu_Achievements__c sta_4 = [select Current_status__c,Last_week__c,X2weeks_ago__c,X3Weeks_ago__c,X4weeks_ago__c,X5weeks_ago__c from Statu_Achievements__c where Id = :sta4.Id];
        System.assertEquals('6 货齐,已付款,无用户合同', sta_4.Current_status__c);
        System.assertEquals('6 货齐,已付款,无用户合同', sta_4.Last_week__c);
        System.assertEquals(null, sta_4.X2weeks_ago__c);
        System.assertEquals(null, sta_4.X3Weeks_ago__c);
        System.assertEquals(null, sta_4.X4weeks_ago__c);
        System.assertEquals('5 货齐,未付款,无用户合同', sta_4.X5weeks_ago__c);
        
        Statu_Achievements__c sta_5 = [select Current_status__c,Last_week__c,X2weeks_ago__c,X3Weeks_ago__c,X4weeks_ago__c,X5weeks_ago__c from Statu_Achievements__c where Id = :sta5.Id];
        System.assertEquals('6 货齐,已付款,无用户合同', sta_5.Current_status__c);
        System.assertEquals('6 货齐,已付款,无用户合同', sta_5.Last_week__c);
        System.assertEquals(null, sta_5.X2weeks_ago__c);
        System.assertEquals(null, sta_5.X3Weeks_ago__c);
        System.assertEquals(null, sta_5.X4weeks_ago__c);
        System.assertEquals(null, sta_5.X5weeks_ago__c);
    }
}