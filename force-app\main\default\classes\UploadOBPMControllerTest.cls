@isTest
private class UploadOBPMControllerTest {
    static Campaign cam;
    static User user2;

    static testMethod void testSample_01() {
        StaticParameter.EscapeContactToUser = true;
        Oly_TriggerHandler.bypass('UserProfileHandler');
        List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Campaign' and Name = '1.学会/会议'];
        if (rectCo.size() == 0) {
            throw new ControllerUtil.myException('not found 1.学会/会议 recordtype');
        }
        System.runAs(new User(Id = Userinfo.getUserId())) {
            User user = new User(Test_staff__c = true);
            user.LastName = '_サンブリッジ';
            user.FirstName = 'う';
            user.Alias = 'う';
            user.Email = '<EMAIL>';
            user.Username = '<EMAIL>';
            user.IsActive = true;
            user.EmailEncodingKey = 'ISO-2022-JP';
            user.TimeZoneSidKey = 'Asia/Tokyo';
            user.LocaleSidKey = 'ja_JP';
            user.LanguageLocaleKey = 'ja';
            user.ProfileId = System.Label.ProfileId_SystemAdmin;
            user.Province__c = '北京';
            user.Dept__c = '医疗华北营业本部';
            user.Use_Start_Date__c = Date.today().addMonths(-6);
            insert user;
            user2 = new User(Test_staff__c = true);
            user2.LastName = '_サンブリッジ';
            user2.FirstName = 'う';
            user2.Alias = 'う';
            user2.Email = '<EMAIL>';
            user2.Username = '<EMAIL>';
            user2.CommunityNickname = 'う';
            user2.IsActive = true;
            user2.EmailEncodingKey = 'ISO-2022-JP';
            user2.TimeZoneSidKey = 'Asia/Tokyo';
            user2.LocaleSidKey = 'ja_JP';
            user2.LanguageLocaleKey = 'ja';
            user2.ProfileId = System.Label.ProfileId_SystemAdmin;
            user2.Job_Category__c = '销售推广';
            user2.Province__c = '上海市';
            user2.Use_Start_Date__c = Date.today().addMonths(-6);
            user2.Employee_No__c = '0000022174';
            user2.Stay_or_not__c = '在职';
            user2.IsActive = true;
            insert user2;
            cam = new Campaign();
            cam.Name = 'test campaign';
            cam.StartDate = Date.today().addDays(15);
            cam.EndDate = Date.today().addDays(18);
            cam.Name2__c = '1234';
            cam.Status = '申请中';
            cam.Mailflg_after45__c = true;
            cam.IF_Approved__c = true;
            cam.Mailflg_cancel__c = true;
            cam.Mailflg_before15__c = true;
            cam.Mailflg_before7__c = true;
            cam.Mailflg_after3__c = true;
            cam.HostName__c = '1';
            cam.cooperatorCompany__c = '1';
            cam.RecordTypeId = rectCo[0].Id;
            cam.OwnerId = user.Id;
            cam.Shared_Editing__c = user2.Employee_No__c;
            cam.Carbon_Copy__c = 'om123456';
            insert cam;
        }
        PageReference page = new PageReference('/apex/BatchSelectRepairPage?id='+cam.Id);
        System.Test.setCurrentPage(page);
        System.Test.StartTest();
        UploadOBPMController ub = new UploadOBPMController();
        ub.init();
        ub.OBPMInfoList[0].cam.Shared_User__c = user2.Id;
        ub.OBPMInfoList[0].Role = '共同编辑人';
        ub.OBPMInfoList[1].cam.Shared_User__c = UserInfo.getUserId();
        ub.OBPMInfoList[1].Role = '抄送人';
        ub.addLine();
        ub.deleteLine();
        ub.uploadOBPM();
        // ub.toUploadOBPM(cam.Id);
        System.Test.StopTest();
    }

}