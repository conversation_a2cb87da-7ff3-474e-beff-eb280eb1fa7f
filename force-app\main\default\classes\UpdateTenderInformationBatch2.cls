global class UpdateTenderInformationBatch2 implements Database.Batchable<sObject>, Database.Stateful{
    //历史数据处理时设置成false
    Boolean IsNeedExecute = true;
    //邮件信息
    List<String> emailMessages = new List<String>();

    //招投标: 报错的招投标Id
    String TenderlogStr = '招标项目 : ';

    //招投标: 报错信息
    String TendererrorStr = '';

    //招投标: 总件数
    Integer TendertotalCount = 0;

    //招投标: 失败件数
    Integer TenderfailedCount = 0; 

    //传过来的招标项目id集合
    List<String> TenderIdList2 = new List<String>();

    //从反应询价状态进来的时候设为true
    Boolean IsNeedfalg = false;

    public UpdateTenderInformationBatch2() {
    }
    public UpdateTenderInformationBatch2(Boolean falg) {
        this.IsNeedExecute=falg;
    }
    public UpdateTenderInformationBatch2(List<String> TenderIdList) {
        this.TenderIdList2=TenderIdList;
        this.IsNeedExecute=false;
        this.IsNeedfalg=true;
    }
    global Database.QueryLocator start(Database.BatchableContext bc) {
        Date today = Date.today();
        Date yesterday = Date.today().addDays(-1);
        Datetime firstDatetime=Datetime.newInstance(yesterday.year(),yesterday.month(),yesterday.day(),0,0,0);
        Datetime lasttDatetime=Datetime.newInstance(today.year(),today.month(),today.day(),23,59,59);
        String query = 'select Id,Tender_information__c,Opportunity__c ';
               query += 'FROM Tender_Opportunity_Link__c ';
        if(this.IsNeedExecute){
            query += 'WHERE Opportunity__r.LastModifiedDate >=:firstDatetime and Opportunity__r.LastModifiedDate <=:lasttDatetime';
        }
        if(this.IsNeedfalg){
            query += 'WHERE Tender_information__c =:TenderIdList2';
        }
        return Database.getQueryLocator(query);
    }
    global void execute(Database.BatchableContext BC, list<Tender_Opportunity_Link__c> TenderList) {
        Set<Id> enderOpportunityId = new Set<Id>();
        if(TenderList.size()>0){
            for (Tender_Opportunity_Link__c TenderOpportunity : TenderList) {
                enderOpportunityId.add(TenderOpportunity.Tender_information__c);
            }
        }
        system.debug('TenderList+++'+TenderList);
        List<Tender_Opportunity_Link__c> TenderOpportunityLinkList2 = [select Id,Tender_information__c,Opportunity__r.NumberOfBids__c,Opportunity__r.BidWinningNumber__c,Opportunity__r.Tender_Number_Flag__c from Tender_Opportunity_Link__c where Tender_information__c in:enderOpportunityId];
        Map<String,Tender_information__c> TenderinformationMap = new Map<String,Tender_information__c>();
        system.debug('TenderOpportunityLinkList2+++'+TenderOpportunityLinkList2);
        if(TenderOpportunityLinkList2.size()>0){
            for(Tender_Opportunity_Link__c TenderOpportunity2 :TenderOpportunityLinkList2){
                    if(TenderinformationMap.containsKey(TenderOpportunity2.Tender_information__c)){
                        Tender_information__c Tender_informationvalue = new Tender_information__c();
                        Tender_informationvalue = TenderinformationMap.get(TenderOpportunity2.Tender_information__c);
                        if(TenderOpportunity2.Opportunity__r.NumberOfBids__c==1&&Tender_informationvalue.NumberOfBids__c==0){
                            Tender_informationvalue.NumberOfBids__c=1;
                        }
                        if(Tender_informationvalue.BidWinningNumber__c==1&&TenderOpportunity2.Opportunity__r.BidWinningNumber__c==0){
                            Tender_informationvalue.BidWinningNumber__c=1;
                        }
                        if(TenderOpportunity2.Opportunity__r.Tender_Number_Flag__c==1&&Tender_informationvalue.Tender_Number__c==0){
                            Tender_informationvalue.Tender_Number__c=1;
                        }
                        TenderinformationMap.put(TenderOpportunity2.Tender_information__c, Tender_informationvalue);
                    }else{
                        Tender_information__c Tender_informationvalue = new Tender_information__c();
                        Tender_informationvalue.Id=TenderOpportunity2.Tender_information__c;
                        Tender_informationvalue.NumberOfBids__c=TenderOpportunity2.Opportunity__r.NumberOfBids__c;
                        Tender_informationvalue.BidWinningNumber__c=TenderOpportunity2.Opportunity__r.BidWinningNumber__c;
                        Tender_informationvalue.Tender_Number__c=TenderOpportunity2.Opportunity__r.Tender_Number_Flag__c;
                        TenderinformationMap.put(TenderOpportunity2.Tender_information__c, Tender_informationvalue);
                    }
            }
        }
        List<Tender_information__c> Tender_informationList = new List<Tender_information__c>();
        for (Tender_information__c value : TenderinformationMap.values()) {
            Tender_informationList.add(value);
        }
        system.debug('Tender_informationList+++'+Tender_informationList);
        if(Tender_informationList.size()>0){
            //一个招投标项目更新失败
            List<String> failedTenderList = new List<String>();
            Database.SaveResult[] saveTenderResults = Database.update(Tender_informationList,false);
            //招投标项目的总数
            TendertotalCount += saveTenderResults.size();

            for(Integer i = 0;i<saveTenderResults.size();i++) {
                if(!saveTenderResults.get(i).isSuccess() ){                   
                    TenderlogStr += Tender_informationList.get(i).id +' ,';
                    TendererrorStr += '失败招标项目 :'+Tender_informationList.get(i).id+'  失败原因:'+ String.ValueOf(saveTenderResults.get(i).getErrors()[0]).split(';')[2].split('=')[1] 
                    +' : '+String.ValueOf(saveTenderResults.get(i).getErrors()[0]).split(';')[1].split('=')[1] + '\r\n';
                    TenderfailedCount++ ;
                }
            }
        }

    }
    global void finish(Database.BatchableContext BC) {
        BatchIF_Log__c TenderIfLog = new BatchIF_Log__c();
        TenderIfLog.Type__c = 'UpdateTenderInformationBatch2ByTenderErrorLog';

        if (TenderlogStr.length() > 60000) {
            TenderlogStr = TenderlogStr.substring(0, 60000);
        }
        TenderIfLog.Log__c = TenderlogStr;
        TenderIfLog.Log__c += '\n end';
        if (TendererrorStr.length() > 60000) {
            TenderIfLog.ErrorLog__c = TendererrorStr.substring(0, 60000);
        } else {
            TenderIfLog.ErrorLog__c = TendererrorStr.substring(0, TendererrorStr.length());
        }

        insert TenderIfLog;

        emailMessages.add('失败日志ID为：' + TenderIfLog.Id + '\r\n失败信息:\r\n'+TendererrorStr);

        //发送邮件
        sendFieldEmail();
    }
    // 发送提醒邮件
    private void sendFieldEmail() {
        PretechBatchEmailUtil be = new PretechBatchEmailUtil();
        String[] toList = new String[] {UserInfo.getUserEmail()};
        String title = '招标项目招标数，中标数或者应标数更新失败';
        String[] ccList = new String[] {'<EMAIL>'};
        //增加抄送IT fy start
        String[] ccList2 = System.Label.OppWarningEmail.split(',');
        ccList.addAll(ccList2);
        //增加抄送IT fy end
        if (System.Test.isRunningTest()) {
            be.successMail('', 1);
        }
        if (emailMessages.size() > 0 && TenderfailedCount > 0) {
            be.failedMail(toList, ccList, title, this.emailMessages.get(0)+'\n',
                            TendertotalCount, TendertotalCount - TenderfailedCount, TenderfailedCount,'',true);
            if(!Test.isRunningTest()){
                be.send();
            }
        }       
    }
}