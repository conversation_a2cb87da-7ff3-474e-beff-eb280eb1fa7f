/**
 * ods更新user表，user表更新contact表
 * 由于中间有futrue方法，所以使用队列来执行此功能
 * wangweipeng   更新user表                  2021/11/19
 */
global class UserUpsertQueueable implements Queueable  {

    private List<User> addUser;
    private List<User> updateUser;
    public UserUpsertQueueable(List<User> userAddData,List<User> userUpdateData){
        this.addUser = userAddData;
        this.updateUser = userUpdateData;
    }

    global void execute(QueueableContext context) {
        System.debug('---------------进入Queueable'+addUser+'---------'+updateUser);
        //新增user
        if(addUser != null && addUser.size() > 0){
            //insert addUser;
            Database.SaveResult[] lsr2 = Database.insert(addUser, false);
            for (Integer tIdx = 0; tIdx < lsr2.size(); tIdx++) {
                Database.SaveResult sr = lsr2[tIdx];
                if (!sr.isSuccess()) {
                    System.debug('-------------ods数据保存到user表时新增报错：'+addUser[tIdx].Employee_No__c+'。报错日志为：' + sr.getErrors()[0]);
                }
            }
        }
        //修改user
        if(updateUser != null && updateUser.size() > 0){
            //update updateUser;
            Database.SaveResult[] lsr = Database.update(updateUser, false);
            if(lsr.size() > 0){
                for (Integer tIdx = 0; tIdx < lsr.size(); tIdx++) {
                    Database.SaveResult sr = lsr[tIdx];
                    if (!sr.isSuccess()) {
                        System.debug('-------------ods数据保存到user表时修改报错：'+updateUser[tIdx].Employee_No__c+'。报错日志为：' + sr.getErrors()[0]);
                    }
                }
            }
        }
    }
}