@isTest
private class UpdateThreeMonthlyContactScheduleTest {
    
    @isTest static void test_method_one() {
        // Implement test code
        String CRON_EXP = '0 0 0 3 9 ? 2022';
        System.Test.startTest();
        // Schedule the test job
        String jobId = system.schedule('UpdateThreeMonthlyContactScheduleTest', CRON_EXP, new UpdateThreeMonthlyContactSchedule());
        // Get the information from the CronTrigger API object
       
        System.Test.StopTest();
    }
}