@RestResource(urlMapping='/Xin_SearchVisitorPlace/*')
global with sharing class Xin_SearchVisitorPlaceRest {

    /**
     * reportDate yyyy/MM/dd
     */
    @HttpPost
    global static void doPost(String query, String reporterState, String reportDate) {
        system.debug('Xin_SearchVisitorPlaceRest.start');
        Xin_SearchVisitorPlace ctl = new Xin_SearchVisitorPlace();
        ctl.search(query, reporterState);
        // 検索結果をMapのListに変換
        List<Map<String, String>> rsList = new List<Map<String, String>>();
        for (Account acc : ctl.results) {
            Map<String, String> rs = new Map<String, String>();
            if (rsList.size() == ctl.results.size() - 1  && ctl.getIsOverLimit() == true) {
                rs.put('Id', '');
                rs.put('Name', '');
                rs.put('DcId', '');
                rs.put('DcName', '');
                rs.put('HpId', '');
                rs.put('HpName', '');
                rs.put('DisplayName', '检索结果超过了50件');
                rsList.add(0, rs);
            } else {
                rs.put('Id', acc.Id);
                rs.put('Name', acc.Name);
                rs.put('DcId', acc.Department_Class__c);
                rs.put('DcName', acc.Department_Class__r.Name);
                rs.put('HpId', acc.Hospital__c);
                rs.put('HpName', acc.Hospital__r.Name);
                rs.put('DisplayName', acc.Name);
                rsList.add(rs);
            }
        }
        // JSONを戻す
        RestResponse res = RestContext.response;
        res.addHeader('Content-Type', 'application/json');
        res.statusCode = 200;
        String jsonResponse = '{"'+ System.Label.OFSErrorStatus +'": "'+ System.Label.OFSErrorSuccess +'", "'+ System.Label.OFSErrorMessage +'": ' + JSON.serialize(rsList) + '}';
        res.responseBody = blob.valueOf(jsonResponse);
        return;
    }
}