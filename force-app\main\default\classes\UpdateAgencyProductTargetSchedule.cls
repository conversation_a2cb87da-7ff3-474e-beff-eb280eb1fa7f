/**
system.schedule('UpdateAgencyProductTargetSetSchedule1','0 0 8 * * ? 2015-2035', new UpdateAgencyProductTargetSchedule());
system.schedule('UpdateAgencyProductTargetSetSchedule2','0 0 12 * * ? 2015-2035', new UpdateAgencyProductTargetSchedule());
system.schedule('UpdateAgencyProductTargetSetSchedule3','0 0 18 * * ? 2015-2035', new UpdateAgencyProductTargetSchedule());
*/
global class UpdateAgencyProductTargetSchedule implements Schedulable {
    global void execute(SchedulableContext sc) {
        //MyBatchClass b = new MyBatchClass();
        //database.executebatch(b);
        UpdateAgencyOppProductTargetBatch b1 = new UpdateAgencyOppProductTargetBatch();
        Database.executeBatch(b1, 1);
        UpdateAgencyRptProductTargetBatch b2 = new UpdateAgencyRptProductTargetBatch();
        Database.executeBatch(b2, 1);
    }

    public static void assignOneMinute() {
        // delete 実行済み
        Datetime addOneM = System.now().addMinutes(2);
        String CRON_EXP = '0 ' + addOneM.minute() + ' ' + addOneM.hour() + ' ' + addOneM.day() + ' ' + addOneM.month() + ' ? ' + addOneM.year();
        List<CronTrigger> oldcron = [select Id from CronTrigger where CronExpression = :CRON_EXP and CronJobDetail.Name like 'UpdateAgencyProductTarget%'];
        if (oldcron.size() == 0) {
            System.schedule('UpdateAgencyProductTarget' + CRON_EXP, CRON_EXP, new UpdateAgencyProductTargetSchedule());
        }
        for (CronTrigger ct :
                [SELECT Id FROM CronTrigger WHERE State = 'DELETED' and CronJobDetail.Name like 'UpdateAgencyProductTarget%']) {
            System.abortJob(ct.id);
        }
    }
}