@isTest
private class TenderTansforSubmmitHandlerTest {
    static testMethod void testMethod1() {
        ControllerUtil.EscapeNFM001Trigger = true;
        ControllerUtil.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
        StaticParameter.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeAccountTrigger = true;
        Oly_TriggerHandler.bypass('UpdateContractAimAmountHandler');
        Oly_TriggerHandler.bypass('NFM701ControllerHandler');
        Oly_TriggerHandler.bypass('AccountTrigger');
        Oly_TriggerHandler.bypass('AccountEffectiveContract');
        Oly_TriggerHandler.bypass('TenderInformationHandler');
        Oly_TriggerHandler.bypass(ContactTriggerHandler.class.getName());
            Oly_TriggerHandler.bypass(AgencyHospitalHandler.class.getName());
            Oly_TriggerHandler.bypass('AssetHandler');
            Oly_TriggerHandler.bypass('AgencyHospitalHandler');
            Oly_TriggerHandler.bypass('FixtureSetHandler');
            Oly_TriggerHandler.bypass('RentalApplyTriggerHandler');
            Oly_TriggerHandler.bypass('AgencyOppUpdHandler ');
            Oly_TriggerHandler.bypass('NFM202OppHandler ');
            Oly_TriggerHandler.bypass('NFM001AgencyContract');

        
        RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName IN ('Department_GI', 'Department_BF') order by DeveloperName desc];
        
        Account acc = new Account();
        acc.RecordTypeId = rectCo.Id;
        acc.Name = 'HP test1';
        acc.Assume_Change__c = true;
        insert acc;
        Account acc1 = new Account();
        acc1.RecordTypeId = rectCo.Id;
        acc1.Name = 'HP test1';
        acc1.Assume_Change__c = true;
        insert acc1;
        Account acc2 = new Account();
        acc2.RecordTypeId = rectCo.Id;
        acc2.Name = 'HP test1';
        acc2.Assume_Change__c = true;
        insert acc2;
        // Account acc3 = new Account();
        // acc3.RecordTypeId = rectCo.Id;
        // acc3.Name = 'HP test1';
        // acc3.Assume_Change__c = true;
        // insert acc3;
        // Account acc4 = new Account();
        // acc4.RecordTypeId = rectCo.Id;
        // acc4.Name = 'HP test1';
        // acc4.Assume_Change__c = true;
        // insert acc4;

        Tender_information__c Ten = new Tender_information__c();
        Ten.Name = 'TenTest01';
        Ten.Hospital__c = acc.Id;
        Ten.Hospital1__c = acc1.Id;
        Ten.Hospital2__c = acc2.Id;
        // Ten.Hospital3__c = acc3.Id;
        // Ten.Hospital4__c = acc4.Id;

        insert Ten;
        List<RecordType> rectCos = [select Id from RecordType where IsActive = true and SobjectType = 'TenderTansforSubmmit__c' and Name = '不应标'];
        TenderTansforSubmmit__c res = new TenderTansforSubmmit__c();
        res.status__c ='草案中';
        res.TenderInfo__c = Ten.Id;
        insert res;
        res.status__c = '批准' ;
        UPDATE res;     
    }
    static testMethod void testMethod2() {
        ControllerUtil.EscapeNFM001Trigger = true;
        ControllerUtil.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
        StaticParameter.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeAccountTrigger = true;
        Oly_TriggerHandler.bypass('UpdateContractAimAmountHandler');
        Oly_TriggerHandler.bypass('NFM701ControllerHandler');
        Oly_TriggerHandler.bypass('AccountTrigger');
        Oly_TriggerHandler.bypass('AccountEffectiveContract');
        Oly_TriggerHandler.bypass('TenderInformationHandler');
        Oly_TriggerHandler.bypass(ContactTriggerHandler.class.getName());
            Oly_TriggerHandler.bypass(AgencyHospitalHandler.class.getName());
            Oly_TriggerHandler.bypass('AssetHandler');
            Oly_TriggerHandler.bypass('AgencyHospitalHandler');
            Oly_TriggerHandler.bypass('FixtureSetHandler');
            Oly_TriggerHandler.bypass('RentalApplyTriggerHandler');
            Oly_TriggerHandler.bypass('AgencyOppUpdHandler ');
            Oly_TriggerHandler.bypass('NFM202OppHandler ');
            Oly_TriggerHandler.bypass('NFM001AgencyContract');

        
        RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName IN ('Department_GI', 'Department_BF') order by DeveloperName desc];
        
        Account acc = new Account();
        acc.RecordTypeId = rectCo.Id;
        acc.Name = 'HP test1';
        acc.Assume_Change__c = true;
        insert acc;
        Account acc1 = new Account();
        acc1.RecordTypeId = rectCo.Id;
        acc1.Name = 'HP test1';
        acc1.Assume_Change__c = true;
        insert acc1;
        Account acc2 = new Account();
        acc2.RecordTypeId = rectCo.Id;
        acc2.Name = 'HP test1';
        acc2.Assume_Change__c = true;
        insert acc2;
        // Account acc3 = new Account();
        // acc3.RecordTypeId = rectCo.Id;
        // acc3.Name = 'HP test1';
        // acc3.Assume_Change__c = true;
        // insert acc3;
        // Account acc4 = new Account();
        // acc4.RecordTypeId = rectCo.Id;
        // acc4.Name = 'HP test1';
        // acc4.Assume_Change__c = true;
        // insert acc4;

        Tender_information__c Ten = new Tender_information__c();
        Ten.Name = 'TenTest01';
        Ten.Hospital__c = acc.Id;
        Ten.Hospital1__c = acc1.Id;
        Ten.Hospital2__c = acc2.Id;
        // Ten.Hospital3__c = acc3.Id;
        // Ten.Hospital4__c = acc4.Id;

        insert Ten;
        List<RecordType> rectCos = [select Id from RecordType where IsActive = true and SobjectType = 'TenderTansforSubmmit__c' and Name = '不应标'];
        TenderTansforSubmmit__c res = new TenderTansforSubmmit__c();
        res.status__c ='草案中';
        res.TenderInfo__c = Ten.Id;
        insert res;
        res.status__c = '批准' ;
        res.RecordTypeId = rectCos[0].Id;
        UPDATE res;     
    }
}