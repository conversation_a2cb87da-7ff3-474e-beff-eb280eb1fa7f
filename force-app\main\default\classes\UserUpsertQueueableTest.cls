@isTest
private class UserUpsertQueueableTest {
    static testMethod void testMethod1() {
        ODS__c ods = new ODS__c();
        ods.ALIAS__C = 'om002857666';
        ods.BRANCH__C = '';
        ods.CATEGORY3__C = '医疗华北东北营业统括本部';
        ods.CATEGORY4__C = '医疗华北营业本部';
        ods.CATEGORY5__C = '华北营业二部';
        ods.CATEGORY6__C = '河北GIR推广课';
        ods.EMAIL__C = '<EMAIL>';
        ods.EMPLOYEE_NO__C = 'om006666';
        ods.FIRSTNAME__C = 'Chengchao';
        ods.HIRE_DATE__C = Date.today();
        ods.JOB_CATEGORY__C = '推广';
        ods.JOB_TYPE__C = '正式-本地员工';
        ods.LASTNAME__C = 'An';
        ods.MOBILEPHONE__C = '13832596948';
        ods.NAME__C = '安承超';
        ods.POST__C = '一般';
        ods.PRODUCT_SPECIALIST_INCHARGE_PRODUCT__C = '';
        ods.SALES_SPECIALITY__C = '';
        ods.UNIQUEID__C = '11181';
        ods.WORK_LOCATION__C = '石家庄';
        ods.Stay_or_not__c = '在职';
        ods.LeaveDate__c = Date.today();

        insert ods;

        Profile p = [select Id from Profile where id =:System.Label.ProfileId_SystemAdmin];

        User hpOwner = new User(Test_staff__c = true, LastName = 'hp', FirstName = 'owner', Alias = 'hp', CommunityNickname = 'hpOwner', Email = '<EMAIL>', Username = '<EMAIL>', IsActive = true, EmailEncodingKey = 'ISO-2022-JP', TimeZoneSidKey = 'Asia/Tokyo', LocaleSidKey = 'ja_JP',EMPLOYEE_NO__C = 'om007777', LanguageLocaleKey = 'ja', ProfileId = p.id);
        insert hpOwner;
        User ur = [select Id,Employee_No__c from User limit 1];

        ods.LeaveDate__c = Date.today();
        ods.JOB_CATEGORY__C = '服务';
        ods.EMPLOYEE_NO__C = 'om007777';
        ods.MOBILEPHONE__C = '13832596949';
        update ods;
    }
}