/**
 *数据信息来源于会议的主页面的学员信息栏表格，数据每个季度末保留一个值
 *  备份岗位状态、岗位状态更新日期、跟进状态、开展工作.是否有过被带教经历、带教时长（月）、是否负责内镜工作，备份日期等等
 */
global class UpdateThreeMonthlyContactBatch implements Database.Batchable<sObject> {

  public List<String> idList;
  public string testid;

  Boolean IsNeedExecute = false; // 2021-03-03  mzy  WLIG-BYHD79  SFDC环境batch合并调查  是否符合执行条件

  global UpdateThreeMonthlyContactBatch() {
    idList = null;

  }
  global UpdateThreeMonthlyContactBatch(List<String> idlist) {
    this.idList = idlist;
  }

  global UpdateThreeMonthlyContactBatch(string testid) {
    this.testid = testid;
  }

  //2021-03-03  mzy  WLIG-BYHD79  SFDC环境batch合并调查  start
  global UpdateThreeMonthlyContactBatch(Boolean needExecute) {
    idList = null;
    this.IsNeedExecute = needExecute;
  }
  //2021-03-03  mzy  WLIG-BYHD79  SFDC环境batch合并调查  end

  global Database.QueryLocator start(Database.BatchableContext BC) {
    String sql = 'select id,Name,Campaign__c,OnJobState__c,Follow_state__c,ProcessingWorkWithoutNumber__c,IsTaught__c,' +
                 'teachMonth_real__c, JobStatusUpdateDate__c, IsEndoscope__c from Contact where Campaign__c !=null ';
    if (idList != null ) {
      sql +=  + ' and id in: idList';
    }
    if (!string.isblank(testid)) {
      sql +=  ' and id =: testid';
    }

    // 2021-03-03  mzy  WLIG-BYHD79  SFDC环境batch合并调查  start
    //  1月 4月 7月 10月 1日  不满足运行时间，可以使查询到的结果为空
    //Date mon1stDate = Date.newInstance(today.year(), today.month(), 1);
    Integer executeMonth = Date.today().month();
    Integer executeDay = Date.today().day();
    Boolean inexecutionDateFlag = true;
    if ( executeMonth == 1 || executeMonth == 4 || executeMonth == 7 || executeMonth == 10) {
      if (executeDay == 1){
          inexecutionDateFlag = false;
      }
    }
    if(inexecutionDateFlag && IsNeedExecute == true){
        sql = 'Select Id from Contact where Name = \'\' AND Name != \'\' ';
    }
    //2021-03-03  mzy  WLIG-BYHD79  SFDC环境batch合并调查 end


    return Database.getQueryLocator(sql);
  }

  global void execute(Database.BatchableContext BC, List<sObject> scope) {

    List<ThreeMonthlyContact__c> TMCList = new List<ThreeMonthlyContact__c>();
    String temp = '';
    Integer m = 0;
    if (Date.today().day() == 1 && Date.today().month() != 1) {
      m =  Date.today().month() - 1;
    } else if (Date.today().day() == 1 && Date.today().month() == 1) {
      m = 12;
    } else {
      m = Date.today().month();
    }

    if ( m >= 1 && m <= 3) {
      temp = 'S1';
    }
    if ( m >= 4 && m <= 6) {
      temp = 'S2';
    }
    if ( m >= 7 && m <= 9) {
      temp = 'S3';
    }
    if ( m >= 10 && m <= 12) {
      temp = 'S4';
    }
    for (sObject sco : scope) {
      Contact clist = (Contact) sco;
      ThreeMonthlyContact__c TMC = new ThreeMonthlyContact__c();
      TMC.Name = clist.Name;
      TMC.Campaign__c = clist.Campaign__c;
      TMC.OnJobState__c = clist.OnJobState__c;
      TMC.Follow_state__c = clist.Follow_state__c;
      TMC.ProcessingWork__c = clist.ProcessingWorkWithoutNumber__c;
      TMC.IsTaught__c = clist.IsTaught__c;
      TMC.teachmonth__c = clist.teachMonth_real__c;
      TMC.JobStatusUpdateDate__c = clist.JobStatusUpdateDate__c;
      TMC.Contact__c = clist.id;
      TMC.IsEndoscope__c = clist.IsEndoscope__c;
      Integer y = Date.today().month() == 1 ? Date.today().year() : Date.today().year() - 1;
      TMC.year__c = String.valueOf(y);

      TMC.season__c = temp;

      if ( Date.today().day() == 1 && Date.today().month() == 1 ) {
        TMC.season__c = 'S4';
      }
      if ( Date.today().day() == 1 && Date.today().month() == 4 ) {
        TMC.season__c = 'S1';
      }
      if ( Date.today().day() == 1 && Date.today().month() == 7 ) {
        TMC.season__c = 'S2';
      }
      if ( Date.today().day() == 1 && Date.today().month() == 10 ) {
        TMC.season__c = 'S3';
      }
      TMC.date__c = Date.today();
      TMCList.add(TMC);
    }
    insert TMCList;

  }


  global void finish(Database.BatchableContext BC) {

    //2021-03-03  mzy  WLIG-BYHD79  SFDC环境batch合并调查  start
        if(!Test.isRunningTest() &&IsNeedExecute==true){
          //batch里调用下一个batch时，希望跟原有的Schedule里面传的条数保持一致
          Id execBTId = Database.executebatch(new UpdateMonthlyContactBatch(true),50);
        }
    //2021-03-03  mzy  WLIG-BYHD79  SFDC环境batch合并调查 end
  }

}