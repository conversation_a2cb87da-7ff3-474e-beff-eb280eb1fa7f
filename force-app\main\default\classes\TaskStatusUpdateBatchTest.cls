@isTest
private class TaskStatusUpdateBatchTest {

    @TestSetup
    public static void setup(){

        OlympusCalendar__c oc = new OlympusCalendar__c();
        oc.Date__c = System.Today();
        oc.Before_1_WorkDay__c = oc.Date__c.addDays(-1);
        insert oc;

        Daily_Report__c dr1 = new Daily_Report__c();
        dr1.Reporter__c = UserInfo.getUserId();
        dr1.Reported_Date__c = date.today().addDays(-2);
        dr1.Status__c = '作成中';


        insert dr1;

        /*Event__c event = new Event__c();
        event.ActivityDate__c = date.today().addDays(-2);
        event.Daily_Report__c = dr1.Id;
        event.StartDateTime__c = Datetime.newInstance(2020, 8, 17, 10, 0, 0);
        event.EndDateTime__c = Datetime.newInstance(2020, 8, 17, 17, 0, 0);
        insert event;

        Event__c event2 = new Event__c();
        event2.ActivityDate__c = date.today().addDays(-2);
        event2.Daily_Report__c = dr1.Id;
        event2.StartDateTime__c = Datetime.newInstance(2020, 8, 17, 10, 0, 0);
        event2.EndDateTime__c = Datetime.newInstance(2020, 8, 17, 17, 0, 0);
        insert event2;

        List<task__c> tasks = new List<task__c>();
        task__c t1 = new task__c();
        t1.Name = 'test';
        t1.taskDifferent__c = '主动任务';
        t1.taskStatus__c = '02 接受';
        t1.assignee__c = Userinfo.getUserId();
        t1.Event__c = event.Id;
        t1.Daily_Report__c = dr1.Id;
        t1.Activity_Date__c = Date.newInstance(2020, 8, 17);
        tasks.add(t1);

        task__c t2 = new task__c();
        t2.Name = 'test';
        t2.taskDifferent__c = '主动任务';
        t2.taskStatus__c = '02 接受';
        t2.assignee__c = Userinfo.getUserId();
        t2.Event__c = event2.Id;
        t2.Daily_Report__c = dr1.Id;
        t2.Activity_Date__c = Date.newInstance(2020, 8, 17);
        tasks.add(t2);

        task__c t3 = new task__c();
        t3.Name = 'test3';
        t3.taskDifferent__c = '主动任务';
        t3.taskStatus__c = '02 接受';
        t3.assignee__c = Userinfo.getUserId();
        t3.Activity_Date__c = Date.newInstance(2020, 8, 17);
        tasks.add(t3);
        insert tasks;*/

        // Event e = new Event();
        // e.ActivityDate = date.today().addDays(-2);
        // e.ActivityDateTime = Datetime.now()-48;
        // e.Task_ID__c = t3.Id;
        // e.DurationInMinutes = 0;
        // insert e;

        //2021-07-26   mzy  update  CHAN-C4G5DK  任务的未执行判断时间修改  start
        Date today = Date.toDay();
        Event__c event = new Event__c();
        event.ActivityDate__c = today.addDays(-2);
        event.Daily_Report__c = dr1.Id;
        event.StartDateTime__c = Datetime.newInstance(today.year(), today.month(), today.day(), 10, 0, 0);
        event.EndDateTime__c = Datetime.newInstance(today.year(), today.month(), today.day(), 17, 0, 0);
        insert event;

        Event__c event2 = new Event__c();
        event2.ActivityDate__c = date.today().addDays(-2);
        event2.Daily_Report__c = dr1.Id;
        event2.StartDateTime__c = Datetime.newInstance(today.year(), today.month(), today.day(), 10, 0, 0);
        event2.EndDateTime__c = Datetime.newInstance(today.year(), today.month(), today.day(), 17, 0, 0);
        insert event2;

        List<task__c> tasks = new List<task__c>();
        task__c t1 = new task__c();
        t1.Name = 'test';
        t1.taskDifferent__c = '主动任务';
        t1.taskStatus__c = '02 接受';
        t1.assignee__c = Userinfo.getUserId();
        t1.Event__c = event.Id;
        t1.Daily_Report__c = dr1.Id;
        t1.Activity_Date__c = Date.newInstance(today.year(), today.month(), today.day());
        tasks.add(t1);

        task__c t2 = new task__c();
        t2.Name = 'test';
        t2.taskDifferent__c = '被动任务';
        t2.taskStatus__c = '02 接受';
        t2.assignee__c = Userinfo.getUserId();
        t2.Event__c = event2.Id;
        t2.Daily_Report__c = dr1.Id;
        t2.Activity_Date__c = Date.newInstance(today.year(), today.month(), today.day());
        tasks.add(t2);

        task__c t3 = new task__c();
        t3.Name = 'test3';
        t3.taskDifferent__c = '被动任务';
        t3.taskStatus__c = '02 接受';
        t3.assignee__c = Userinfo.getUserId();
        t3.Activity_Date__c = Date.newInstance(today.year(), today.month(), today.day());
        tasks.add(t3);

        //2021-10-22  mzy  任务管理改善  start
        task__c t4 = new task__c();
        t4.Name = 'test4';
        t4.taskDifferent__c = '上级分配任务';
        t4.HopeCompletionDate__c = Date.today().addDays(2);
        t4.taskStatus__c = '02 接受';
        t4.assignee__c = Userinfo.getUserId();
        t4.Activity_Date__c = Date.today();
        //t4.RecordTypeId = '0121m000000woLl';  
        t4.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('Other').getRecordTypeId(); //2022-1-3 yjk 修改recordetyep赋值方式
        tasks.add(t4);
        //2021-10-22  mzy  任务管理改善  end

        task__c t5 = new task__c();
        t5.Name = 'test5';
        t5.taskDifferent__c = '上级分配任务';
        t5.HopeCompletionDate__c = Date.today().addDays(-2);
        t5.taskStatus__c = '02 接受';
        t5.assignee__c = Userinfo.getUserId();
        t5.Activity_Date__c = Date.today();
        t5.taskTypeFlow__c = '其他';
        //t4.RecordTypeId = '0121m000000woLl';  
        t5.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('Other').getRecordTypeId(); //2022-1-3 yjk 修改recordetyep赋值方式
        tasks.add(t5);

        task__c t6 = new task__c();
        t6.Name = 'test6';
        t6.taskDifferent__c = '上级分配任务';
        t6.HopeCompletionDate__c = Date.today().addDays(2);
        t6.taskStatus__c = '02 接受';
        t6.assignee__c = Userinfo.getUserId();
        t6.Activity_Date__c = Date.today();
        t6.taskTypeFlow__c = 'OPD';
        //t4.RecordTypeId = '0121m000000woLl';  
        t6.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('Other').getRecordTypeId(); //2022-1-3 yjk 修改recordetyep赋值方式
        tasks.add(t6);

        insert tasks;

        Event e = new Event();
        e.ActivityDate = date.today().addDays(-2);
        e.ActivityDateTime = Datetime.now()-48;
        e.Task_ID__c = t5.Id;
        e.DurationInMinutes = 0;
        insert e;

        Event e1 = new Event();
        e1.ActivityDate = date.today().addDays(-2);
        e1.ActivityDateTime = Datetime.now()-48;
        e1.Task_ID__c = t6.Id;
        e1.DurationInMinutes = 0;
        insert e1;

        //2021-07-26  mzy  update    CHAN-C4G5DK  任务的未执行判断时间修改  end
    }
    @isTest
    public static void testMethod1() {
        System.Test.StartTest();
        TaskStatusUpdateBatch.testI();
        Id execBTId = Database.executeBatch(new TaskStatusUpdateBatch());
        System.Test.StopTest();
    }
    @isTest
    public static void testMethod2() {
        System.Test.StartTest();
        Id execBTId = Database.executeBatch(new TaskStatusUpdateBatch(false));
        System.Test.StopTest();
    }

}