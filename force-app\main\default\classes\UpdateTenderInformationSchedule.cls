global class UpdateTenderInformationSchedule implements Schedulable {
    global void execute(SchedulableContext sc) {
       Id execBTId = database.executebatch(new UpdateTenderInformationBatch(true),1); 
    }
    public static void assignOneHours() {
         // delete 実行済み
        Datetime Now = System.now();

        String CRON_EXP =  '0 ' + '0 ' + (Now.hour()+1) + ' '
                           + Now.day() + ' ' + Now.month() + ' ? ' + Now.year();
        List<CronTrigger> oldcron = [select Id from CronTrigger where CronExpression = :CRON_EXP
                                     and CronJobDetail.Name like 'UpdateTenderInformationSchedule%'];
        if (oldcron.size() == 0 &&  Now.hour() < 23) {
            System.schedule('UpdateTenderInformationSchedulex' + CRON_EXP, CRON_EXP, new UpdateTenderInformationSchedule());
        }

        for (CronTrigger ct :
                [SELECT Id FROM CronTrigger WHERE State = 'DELETED'
                        and CronJobDetail.Name like 'UpdateTenderInformationSchedulex%']) {
            System.abortJob(ct.id);
        }
    }
    public static void test(){
        integer i = 0;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
    }
}