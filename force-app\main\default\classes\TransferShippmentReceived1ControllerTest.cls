@isTest
private class TransferShippmentReceived1ControllerTest {
    static TransferTestDataFactory Factory;
    static void setupTestData1() { 
        Factory = new TransferTestDataFactory();
        Factory.CreateTa('CenterToCenter');
        TransferApply__c raObj1 = [SELECT Id,Name,OwnerId,Status__c,Status_Text__c,Request_time__c,Request_approval_time__c  FROM TransferApply__c WHERE RecordType.DeveloperName = 'CenterToCenter' limit 1];       
        raObj1.Status__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
        raObj1.Status_Text__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
        raObj1.Request_time__c = System.now();
        raObj1.Request_approval_time__c = System.now();
        update raObj1;
    }
	static testMethod void test_init_error() {
        Test.startTest();
        setupTestData1();
        List<TransferApply__c> taList = [SELECT Id,OwnerId,Status__c FROM TransferApply__c]; 
        List<TransferApplySummary__c> essList;
        essList = [select TransferApply__r.Loaner_centre_mail_address__c, TransferApply__r.Name, TransferApply__c, Id, Name
                        from TransferApplySummary__c WHERE TransferApply__c = :taList[0].Id ];

        PageReference ref2 = new PageReference('/apex/TransferShippmentReceived1?id=' + '1234');
        Test.setCurrentPage(ref2);
        TransferShippmentReceived1Controller controller = new TransferShippmentReceived1Controller();
        controller.init();
        Test.stopTest();
        System.assertEquals(0, controller.esdList.size());
    }
	static testMethod  void test_save() {
        Test.startTest();
        setupTestData1();
        List<TransferApply__c> taList = [SELECT Id,OwnerId,Status__c FROM TransferApply__c]; 
        List<TransferApplySummary__c> esList;
        esList = [select TransferApply__r.Loaner_centre_mail_address__c, TransferApply__r.Name, TransferApply__c, Id, Name
                        from TransferApplySummary__c WHERE TransferApply__c = :taList[0].Id ];
        PageReference ref = new PageReference('/apex/TransferShippmentReceived1?id=' + esList[0].id);
        Test.setCurrentPage(ref);

        TransferShippmentReceived1Controller controller = new TransferShippmentReceived1Controller();

        //controller.ApplyId = '*';
        controller.init();

        controller.esdList[0].editable = true; 
        controller.esdList[0].rec.StockDown__c = true;

        controller.esdList[1].editable = true;
        controller.esdList[1].rec.StockDown__c = true;

        controller.esdList[2].editable = true;
        controller.esdList[2].rec.StockDown__c = false;

        controller.save();
        controller.Shippment1();
        controller.Shippment2();
        Test.stopTest();

        List<TransferApplyDetail__c> erList;
        erList = [select  StockDown__c,StockDown_time__c,StockDown_staff__c 
                        from TransferApplyDetail__c];
        System.assertEquals(true, erList[0].StockDown__c);
    }
}