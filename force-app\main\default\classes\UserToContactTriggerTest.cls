/**
 * This class contains unit tests for validating the behavior of Apex classes
 * and triggers.
 *
 * Unit tests are class methods that verify whether a particular piece
 * of code is working properly. Unit test methods take no arguments,
 * commit no data to the database, and are flagged with the testMethod
 * keyword in the method definition.
 *
 * All test methods in an organization are executed whenever Apex code is deployed
 * to a production organization to confirm correctness, ensure code
 * coverage, and prevent regressions. All Apex classes are
 * required to have at least 75% code coverage in order to be deployed
 * to a production organization. In addition, all triggers must have some code coverage.
 * 
 * The @isTest class annotation indicates this class only contains test
 * methods. Classes defined with the @isTest annotation do not count against
 * the organization size limit for all Apex scripts.
 *
 * See the Apex Language Reference for more information about Testing and Code Coverage.
 */
@isTest
public class UserToContactTriggerTest {
    @isTest
    public static void testInsert() {
        Id rtId = '01210000000Qtky';
        User user = new User();
        user.LastName = 'LastName';
        user.FirstName = 'FirstName';
        user.Alias = 'Alias';
        user.Email = '<EMAIL>';
        user.Username = '<EMAIL>';
        user.CommunityNickname = 'CommunityNickname';
        user.IsActive = true;
        user.EmailEncodingKey = 'ISO-2022-JP';
        user.TimeZoneSidKey = 'Asia/Tokyo';
        user.LocaleSidKey = 'ja_JP';
        user.LanguageLocaleKey = 'ja';
        user.ProfileId = System.Label.ProfileId_SystemAdmin;
        user.Job_Category__c = '销售推广';
        user.Province__c = '上海市';
        user.Post__c = '经理';
        user.MobilePhone = '54321';
        user.Mobile_Phone__c = '12345';
        user.Employee_No__c = '112233';
        user.Work_Location__c = '北京';
        user.Use_Start_Date__c = Date.today().addMonths(-6);
        user.SendToComPlat__c = false;
        user.Stay_or_not__c = '待入职';
        user.IsMEBG__c = true;
        
        System.runAs(new User(Id = Userinfo.getUserId())) {
            System.Test.startTest();
            insert user;
            
            // List<Contact> con = [select id,RecordTypeId,AccountId,FirstName,LastName,Email,MobilePhone,Employee_No_manual__c,Work_Location_manual__c,Post_picklist__c,Job_Category_picklist__c from Contact where User__c = :user.Id];
            // System.assertEquals(rtId, con[0].RecordTypeId);
            // System.assertEquals(System.Label.Olympus_AccountID_Internal_staff, con[0].AccountId);
            // System.assertEquals(user.FirstName, con[0].FirstName);
            // System.assertEquals(user.LastName, con[0].LastName);
            // System.assertEquals(user.Email, con[0].Email);
            // System.assertEquals(user.Mobile_Phone__c, con[0].MobilePhone);
            // System.assertEquals(user.Employee_No__c, con[0].Employee_No_manual__c);
            // System.assertEquals(user.Work_Location__c, con[0].Work_Location_manual__c);
            // System.assertEquals(user.Post__c, con[0].Post_picklist__c);
            // System.assertEquals(user.Job_Category__c, con[0].Job_Category_picklist__c);
            System.Test.stopTest();
        }
        
        
        
    }

    public static testMethod void testUpdate() {
        Id rtId = '01210000000Qtky';
        User local = new User();
        local.LastName = 'LastName';
        local.FirstName = 'FirstName';
        local.Alias = 'Alias';
        local.Email = '<EMAIL>';
        local.Username = '<EMAIL>';
        local.CommunityNickname = 'CommunityNickname';
        local.IsActive = true;
        local.EmailEncodingKey = 'ISO-2022-JP';
        local.TimeZoneSidKey = 'Asia/Tokyo';
        local.LocaleSidKey = 'ja_JP';
        local.LanguageLocaleKey = 'ja';
        local.ProfileId = System.Label.ProfileId_SystemAdmin;
        local.Job_Category__c = '销售推广';
        local.Province__c = '上海市';
        local.Post__c = '经理';
        local.MobilePhone = '54321';
        local.Mobile_Phone__c = '12345';
        local.Employee_No__c = '112233';
        local.Work_Location__c = '北京';
        local.Use_Start_Date__c = Date.today().addMonths(-6);
        local.SendToComPlat__c = false;
        local.Stay_or_not__c = '待入职';
        local.IsMEBG__c = true;
        
        insert local;
        Contact tmp = new Contact();
        tmp.RecordTypeId = rtId;
        tmp.AccountId = System.Label.Olympus_AccountID_Internal_staff;
        tmp.User__c = local.Id;
        tmp.FirstName = local.FirstName;
        tmp.LastName = local.LastName;
        tmp.Email = local.Email;
        tmp.MobilePhone = local.Mobile_Phone__c;
        tmp.Employee_No_manual__c = local.Employee_No__c;
        tmp.Work_Location_manual__c = local.Work_Location__c;
        tmp.Post_picklist__c = local.Post__c;
        tmp.Job_Category_picklist__c = local.Job_Category__c;
        insert tmp;

        User user = new User(Id = local.id);
        user.LastName = 'LastName1';
        user.FirstName = 'FirstName1';
        user.Email = 'olympustest03@sunbridge.com1';
        user.Job_Category__c = '服务管理';
        user.Post__c = '主管';
        user.Mobile_Phone__c = '123451';
        user.Employee_No__c = '112233';         // 変更できない、Keyです。
        user.Work_Location__c = '北京';
        user.Pregnant_Rest__c = true;
        user.IsMEBG__c = true;
        user.ProfileId = System.Label.ProfileId_SystemAdmin;
        user.FederationIdentifier = '呵呵';
        System.Test.startTest();
        System.runAs (local){
            update user;
        }
        System.Test.stopTest();
        
        List<Contact> con = [select id,RecordTypeId,AccountId,FirstName,LastName,Email,MobilePhone,Employee_No_manual__c,Work_Location_manual__c,Post_picklist__c,Job_Category_picklist__c,Pregnant_Rest__c from Contact where User__c = :user.Id];
        System.assertEquals(user.FirstName, con[0].FirstName);
        System.assertEquals(user.LastName, con[0].LastName);
        System.assertEquals(user.Email, con[0].Email);
        System.assertEquals(user.Mobile_Phone__c, con[0].MobilePhone);
        System.assertEquals(user.Employee_No__c, con[0].Employee_No_manual__c);
        System.assertEquals(user.Work_Location__c, con[0].Work_Location_manual__c);
        //System.assertEquals(user.Post__c, con[0].Post_picklist__c);
        System.assertEquals(user.Job_Category__c, con[0].Job_Category_picklist__c);
        System.assertEquals(user.Pregnant_Rest__c, con[0].Pregnant_Rest__c);

    }


}