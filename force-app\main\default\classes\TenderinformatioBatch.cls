/**
 * 
 * CreateBy sx
 * CreateTime 2024-4-30
 * Reason DB202404234371
 * 
 */
global class TenderinformatioBatch implements Database.Batchable<sObject> , Database.AllowsCallouts, Database.Stateful {

    private BatchIF_Log__c iflog;
    public List <String> tenderIdList;
    public List<String> oppIdList;
    public String query;

    global TenderinformatioBatch() {
        
    }

    //招标项目传进来的是 终止的招标项目
    global TenderinformatioBatch(List<String> tenderIdList) {
        this.query = query;
        this.tenderIdList = tenderIdList;
    }

    global TenderinformatioBatch(List<String> oppIdList,List<String> tenderIdList) {
        this.query = query;
        this.tenderIdList = tenderIdList;
        this.oppIdList = oppIdList;
    }

    global Database.QueryLocator start(Database.BatchableContext bc) {
        system.debug('执行start');
        iflog = new BatchIF_Log__c();
        iflog.Type__c = 'PushNotification';
        iflog.Log__c  = 'TenderinformatioBatch start\n';
        iflog.ErrorLog__c = '';
        insert iflog;

        query = 'select Id from Tender_information__c where ';

        //查询需要执行的询价
        if(this.tenderIdList != null && this.tenderIdList.size()>0){
            query += 'Id IN :tenderIdList';
        }
        return Database.getQueryLocator(query);
    }

    global void execute(Database.BatchableContext BC, list<Tender_information__c> tenderList) {
        System.debug(LoggingLevel.INFO, '*** excute start: ' );
        // chenjingwu DB202406188227 20240717 start
        List<String> idList = new List<String>();
        for(Tender_information__c tender : tenderList){
            idList.add(tender.Id);
        }
        // chenjingwu DB202406188227 20240717 end
        //通过中间表查询询价关联的询价
        List<Tender_Opportunity_Link__c> tenderLinkList = new List<Tender_Opportunity_Link__c>();
        tenderLinkList = [SELECT Id, Opportunity__c, Opportunity__r.IsTerminate__c ,Tender_information__c, Tender_information__r.IsTerminate__c
                                                    // chenjingwu DB202406188227 20240717 start
                                                    ,Tender_information__r.TerminateReason__c
                                                    // chenjingwu DB202406188227 20240717 end
                                                    // chenjingwu DB202505290075 20250611 start
                                                    ,Tender_information__r.CopyNotIndicateReasons__c
                                                    // chenjingwu DB202505290075 20250611 end
                                                    FROM Tender_Opportunity_Link__c 
                                                    // chenjingwu DB202406188227 20240717 start
                                                    WHERE Tender_information__c in :idList];
                                                    // chenjingwu DB202406188227 20240717 end
        // List<Id> oppIdList = new List<Id>();
        if(oppIdList == null){
            oppIdList = new List<String>();
        }
        for(Tender_Opportunity_Link__c tol : tenderLinkList){
            oppIdList.add(tol.Opportunity__c);
        }
        tenderLinkList = [SELECT Id, Opportunity__c, Opportunity__r.IsTerminate__c ,Tender_information__c, Tender_information__r.IsTerminate__c
                            // chenjingwu DB202406188227 20240717 start
                            ,Tender_information__r.TerminateReason__c
                            // chenjingwu DB202406188227 20240717 end
                            // chenjingwu DB202505290075 20250611 start
                            ,Tender_information__r.CopyNotIndicateReasons__c
                            // chenjingwu DB202505290075 20250611 end
                            FROM Tender_Opportunity_Link__c 
                            // chenjingwu DB202406188227 20240717 start
                            WHERE Tender_information__c in :idList OR Opportunity__c in :oppIdList];
                            // chenjingwu DB202406188227 20240717 end

        System.debug('tenderLinkList'+tenderLinkList);
        // if(tenderLinkList.size() > 0){
            //DB202404234371  新增字段“是否终止”：
            //如果一个询价关联了多个招标项目，其中一条招标项目做了终止，请将招标项目中的“是否终止”带到询价中。如果询价关联的所有项目都更新为空那么就要把询价的【是否终止】清空
            List<String> oppIds = new List<String>();
            //Map<询价Id,List<招标项目>>
            Map<Id,List<Tender_information__c>> oppTenderMap = new Map<Id,List<Tender_information__c>>();

            for(Tender_Opportunity_Link__c tol : tenderLinkList){
                if(oppTenderMap.containsKey(tol.Opportunity__c)){
                    Tender_information__c tempTender1 = new Tender_information__c();
                    tempTender1.Id = tol.Tender_information__c;
                    tempTender1.IsTerminate__c = tol.Tender_information__r.IsTerminate__c;
                    // chenjingwu DB202406188227 20240717 start
                    tempTender1.TerminateReason__c = tol.Tender_information__r.TerminateReason__c;
                    // chenjingwu DB202406188227 20240717 end
                    // chenjingwu DB202505290075 20250611 start
                    tempTender1.CopyNotIndicateReasons__c = tol.Tender_information__r.CopyNotIndicateReasons__c;
                    // chenjingwu DB202505290075 20250611 end
                    oppTenderMap.get(tol.Opportunity__c).add(tempTender1);
                }else{
                    List<Tender_information__c> tempTenderList = new List<Tender_information__c>();
                    Tender_information__c tempTender2 = new Tender_information__c();
                    tempTender2.Id = tol.Tender_information__c;
                    tempTender2.IsTerminate__c = tol.Tender_information__r.IsTerminate__c;
                    // chenjingwu DB202406188227 20240717 start
                    tempTender2.TerminateReason__c = tol.Tender_information__r.TerminateReason__c;
                    // chenjingwu DB202406188227 20240717 end
                    // chenjingwu DB202505290075 20250611 start
                    tempTender2.CopyNotIndicateReasons__c = tol.Tender_information__r.CopyNotIndicateReasons__c;
                    // chenjingwu DB202505290075 20250611 end
                    tempTenderList.add(tempTender2);
                    oppTenderMap.put(tol.Opportunity__c, tempTenderList);
                }
            }
            
            for(String oppId: oppIdList){
                if(!oppTenderMap.containsKey((ID)oppId)){
                    oppTenderMap.put((ID)oppId,new List<Tender_information__c>());
                }
            }
            System.debug('oppIdList==='+oppIdList);
            System.debug('oppTenderMap==='+oppTenderMap);
            //更新询价
            List<Opportunity> updateOpp = new List<Opportunity>(); 
            for(String key : oppTenderMap.keySet()){
                Opportunity tempOpp = new Opportunity();
                tempOpp.Id = key;
                tempOpp.IsTerminate__c = null;
                for(Tender_information__c t :oppTenderMap.get(key)){
                    if(t.IsTerminate__c == '是'){
                        tempOpp.IsTerminate__c = '是';
                        break;
                        //tempOpp.Closing_Bid_Date__c = null;
                    }  
                }
                // chenjingwu DB202406188227 20240717 start
                List<String> reasonList = new List<String>();
                // chenjingwu DB202505290075 20250611 start
                tempOpp.SupplementaryItems__c = null;
                // chenjingwu DB202505290075 20250611 end
                for(Tender_information__c t :oppTenderMap.get(key)){
                    if(t.TerminateReason__c != null){
                        reasonList.add(t.TerminateReason__c);
                    } 
                    // chenjingwu DB202505290075 20250611 start
                    if(t.CopyNotIndicateReasons__c != null){
                        tempOpp.SupplementaryItems__c = 1;
                    } 
                    // chenjingwu DB202505290075 20250611 end
                }
                String reasonStr = '';
                for(Integer i = 0;i < reasonList.size();i++){
                    if(!reasonStr.contains(reasonList[i])){
                        if(i + 1 >= reasonList.size()){
                            reasonStr += reasonList[i];
                        }else{
                            reasonStr += reasonList[i] + ',';
                        } 
                    }
                }
                if(reasonStr != '' && reasonStr.lastIndexOf(',') == reasonStr.length() - 1){
                    reasonStr = reasonStr.subString(0,reasonStr.length() - 1);
                }
                tempOpp.ResonForCancal__c = reasonStr;
                // chenjingwu DB202406188227 20240717 end
                updateOpp.add(tempOpp);
            }
            System.debug('updateOpp=='+updateOpp);
            if(updateOpp.size()>0){
                UPDATE updateOpp;
            }
        // }
        // chenjingwu DB202406188227 20240717 start
        oppIdList = new List<String>();
        // chenjingwu DB202406188227 20240717 end
    }

    global void finish(Database.BatchableContext BC) {

    }
}