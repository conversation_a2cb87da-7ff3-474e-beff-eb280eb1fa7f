@isTest
private class TaskStatusUpdateBatchScheduleTest {
    
    static testMethod void test_method_one() {
        OlympusCalendar__c oc = new OlympusCalendar__c();
        oc.Date__c = System.Today();
        oc.Before_1_WorkDay__c = oc.Date__c.addDays(-1);
        insert oc;
        
        String CRON_EXP = '0 0 0 3 10 ? 2021';

        System.Test.startTest();
        // Schedule the test job
        String jobId = system.schedule('TaskStatusUpdateBatchSchedule', CRON_EXP, new TaskStatusUpdateBatchSchedule());
        // Get the information from the CronTrigger API object
        CronTrigger ct = [SELECT Id, CronExpression, TimesTriggered, NextFireTime FROM CronTrigger WHERE id = :jobId];
        // Verify the expressions are the same
        System.assertEquals(CRON_EXP, ct.CronExpression);
        // Verify the job has not run
        System.assertEquals(0, ct.TimesTriggered);
        // Verify the next time the job will run
        System.assertEquals('2021-10-03 00:00:00', String.valueOf(ct.NextFireTime));
        System.Test.StopTest();
    }   
    
}