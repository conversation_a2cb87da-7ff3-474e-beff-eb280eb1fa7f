@isTest
private class TenderDeleteLwcControllerTest {
    // static testMethod void testMethod1() {
    //     FixtureUtil.SkipTrigger = true;
    //     Oly_TriggerHandler.bypass('ContactTrigger');
    //     Oly_TriggerHandler.bypass('ContactTriggerHandler');
    //     Oly_TriggerHandler.bypass('AssetTrigger');
    //     Oly_TriggerHandler.bypass('MaintenanceContractTrigger');
    //     Oly_TriggerHandler.bypass('MaintenanceContractAfterUpdate');
    //     Oly_TriggerHandler.bypass('MaintenanceContractBeforeDelete');
    //     Oly_TriggerHandler.bypass('MaintenanceContractHpDeptUpd');
    //     Oly_TriggerHandler.bypass('NFM106Controller');
    //     Oly_TriggerHandler.bypass('NFM106Trigger');
    //     Oly_TriggerHandler.bypass('AssetRecordTypeUpd');
    //     Oly_TriggerHandler.bypass('NFM101Controller');
    //     Oly_TriggerHandler.bypass('NFM001Controller');
    //     Oly_TriggerHandler.bypass('SyncProduct2');
    //     // Oly_TriggerHandler.bypass('RentalApplyBeforeUpdate');
    //     Oly_TriggerHandler.bypass('ContactTriggerHandler');

    //     ControllerUtil.EscapeNFM001Trigger = true;
    //     StaticParameter.EscapeNFM001Trigger = true;
    //     StaticParameter.EscapeNFM001AgencyContractTrigger = true;
    //     StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
    //     StaticParameter.EscapeOppandStaTrigger = true;
    //     CampaignCopyToOPDPlanHandler.HandlerFlag = true;
  
    //     ControllerUtil.EscapeNFM001Trigger = true;
    //     ControllerUtil.EscapeNFM001Trigger = true;
    //     StaticParameter.EscapeNFM001Trigger = true;
    //     StaticParameter.EscapeNFM001AgencyContractTrigger = true;
    //     StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
    //     StaticParameter.EscapeOppandStaTrigger = true;
    //     List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院' Limit 1];
    //     if (rectCo.size() == 0) {
    //         return;
    //     }
    //     List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 呼吸科' Limit 1];
    //     if (rectSct.size() == 0) {
    //         return;
    //     }
    //     List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 消化科' Limit 1];
    //     if (rectDpt.size() == 0) {
    //         return;
    //     }

    //     //List<Tender_information__c> TenInfo = [Select Id, InfoId__c, Logical_delete__c, ProjectId__c, Retain_Tender__c From Tender_information__c Where id = : 'a4J1m0000009Tv5'];
    //     List<RecordType> rectDpts = [select Id from RecordType where IsActive = true and SobjectType = 'Tender_information__c' and Name = '手工录入' Limit 1];
    //     if (rectDpts.size() == 0) {
    //         return;
    //     }
    //     Tender_information__c TenInfo = new Tender_information__c();
    //     Tender_information__c TenInfo1 = new Tender_information__c();
    //     TenInfo1.Name = 'QLMTEST08111057-02';
    //     TenInfo1.RecordTypeId=rectDpts[0].Id;
    //     TenInfo1.InfoTitle__c = '招标项目1';
    //     TenInfo1.InfoType__c = '1：结果';
    //     TenInfo1.subInfoType__c='1-1：意见征集'; // ******** ljh SWAG-CKB9NR add
    //     insert TenInfo1;

    //     TenInfo.Name = '2345';
    //     TenInfo.InfoId__c = '1234';
    //     TenInfo.Retain_Tender__c = TenInfo1.Id;
    //     TenInfo.RecordTypeId=rectDpts[0].Id;
    //     TenInfo.InfoTitle__c = '招标项目2';
    //     TenInfo.TenderManageCode__c ='123';
    //     TenInfo.InfoType__c = '3：结果';
    //     TenInfo.subInfoType__c='1-1：意见征集'; // ******** ljh SWAG-CKB9NR add
    //     insert TenInfo;
    //     Tender_information_details__c detail = new Tender_information_details__c();
    //     detail.Name = 'test-detail';
    //     detail.Tender_information__c = TenInfo.Id;
    //     detail.ProjectId__c = TenInfo.ProjectId__c;
    //     detail.source__c = '众成';
    //     insert detail;
    //     Tender_information_details__c detail1 = new Tender_information_details__c();
    //     detail1.Name = 'test-detail';
    //     detail1.Tender_information__c = TenInfo1.Id;
    //     detail1.ProjectId__c = TenInfo1.ProjectId__c;
    //     detail1.source__c = '众成';
    //     insert detail1;

    //     //List<Tender_Opportunity_Link__c> BlinksList = [select Opportunity__c from Tender_Opportunity_Link__c where Tender_information__c = :BTen.Id];
    //     Account company = new Account();
    //     company.RecordTypeId = rectCo[0].Id;
    //     company.Name         = 'NFM007TestCompany';
    //     upsert company;
    //     Account section = new Account();
    //     section.RecordTypeId = rectSct[0].Id;
    //     section.Name         = '*';
    //     section.Department_Class_Label__c = '消化科';
    //     section.ParentId                  = company.Id;
    //     section.Hospital_Department_Class__c = company.Id;
    //     upsert section;
    //     Account depart = new Account();
    //     depart.RecordTypeId = rectDpt[0].Id;
    //     depart.Name         = '*';
    //     depart.Department_Name__c  = 'NFM007TestDepart';
    //     depart.ParentId            = section.Id;
    //     depart.Department_Class__c = section.Id;
    //     depart.Hospital__c         = company.Id;
    //     upsert depart;

    //     Opportunity opp = new Opportunity();
    //     opp.AccountId           = depart.Id;
    //     opp.Department_Class__c = section.Id;
    //     opp.Hospital__c         = company.Id;
    //     opp.SAP_Send_OK__c      = false;
    //     opp.Name                = 'GZ-SP-NFM007_1';
    //     opp.Trade__c            = '内貿';
    //     opp.StageName           = '引合';
    //     opp.CloseDate           = date.newinstance(2025, 11, 30);
    //     opp.Stock_apply_status__c = '申请中';
    //     opp.Whether_Bidding__c = '否';
    //     insert opp;

    //     Tender_Opportunity_Link__c BlinksList = new Tender_Opportunity_Link__c();
    //     BlinksList.Opportunity__c = opp.Id;
    //     BlinksList.CurrencyIsoCode = 'CNY';
    //     BlinksList.Tender_information__c = TenInfo1.Id;
    //     insert BlinksList;

    //     Tender_Opportunity_Link__c linksList = new Tender_Opportunity_Link__c();
    //     linksList.Opportunity__c = opp.Id;
    //     linksList.CurrencyIsoCode = 'CNY';
    //     linksList.Tender_information__c = TenInfo.Id;
    //     insert linksList;

    //     //Tender_information__c BTen = [select Id, InfoId__c From Tender_information__c Where Id = : TenInfo.Retain_Tender__c];
    //     Tender_information__c BTen = new Tender_information__c();
    //     BTen.InfoId__c = '1122';
    //     BTen.InfoType__c = '3：结果';
    //     TenInfo.Retain_Tender__c = BTen.Id;
    //     String BTenInfo = BTen.InfoId__c;
    //     BTen.InfoId__c = TenInfo.InfoId__c;
    //     TenInfo.InfoId__c = BTenInfo;
    //     TenInfo.Logical_delete__c = true;
    //     TenInfo.InfoType__c = '3：结果';
    //     List<Tender_information__c> updateTenInfoList = new List<Tender_information__c>();
    //     updateTenInfoList.add(TenInfo);
    //     updateTenInfoList.add(BTen);

    //     //Apexpages.currentPage().getParameters().put('id', TenInfo.Id);
    //     PageReference peg = new PageReference('/apex/TenderDeletePage?id=' + TenInfo.Id);
    //     System.Test.setCurrentPage(peg);
    //     // TenderDeleteLwcController tenderDeleteController = new TenderDeleteLwcController();
    //     System.Test.StartTest();
    //     String test =TenderDeleteLwcController.GetTenderinformationcData(TenInfo.Id);
    //     // Id,Name,InfoId__c,Logical_delete__c,ProjectId__c,Retain_Tender__c
    //     system.debug('test+++'+test);
    //     List<Tender_information__c> TenInfos=(List<Tender_information__c>)JSON.deserializeStrict(test,List<Tender_information__c>.class);
    //     test =JSON.serialize(TenInfos[0]);
    //     TenderDeleteLwcController.searchTender(TenInfo1.TenderManageCode__c);
    //     //TenderDeleteLwcController.saveData(test);
    //     System.Test.StopTest();
    // }
    static testMethod void testMethod2() {
        Oly_TriggerHandler.bypass('ContactTrigger');
        Oly_TriggerHandler.bypass('ContactTriggerHandler');
        Oly_TriggerHandler.bypass('AssetTrigger');
        Oly_TriggerHandler.bypass('MaintenanceContractTrigger');
        Oly_TriggerHandler.bypass('MaintenanceContractAfterUpdate');
        Oly_TriggerHandler.bypass('MaintenanceContractBeforeDelete');
        Oly_TriggerHandler.bypass('MaintenanceContractHpDeptUpd');
        Oly_TriggerHandler.bypass('NFM106Controller');
        Oly_TriggerHandler.bypass('NFM106Trigger');
        Oly_TriggerHandler.bypass('AssetRecordTypeUpd');
        Oly_TriggerHandler.bypass('NFM101Controller');
        Oly_TriggerHandler.bypass('NFM001Controller');
        Oly_TriggerHandler.bypass('SyncProduct2');
        // Oly_TriggerHandler.bypass('RentalApplyBeforeUpdate');
        Oly_TriggerHandler.bypass('ContactTriggerHandler');
        Oly_TriggerHandler.bypass('TenderInformationHandler');
        ControllerUtil.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
        StaticParameter.EscapeOppandStaTrigger = true;
        StaticParameter.EscapeOpportunityBefUpdTrigger = true;
        StaticParameter.EscapeOpportunityHpDeptUpdTrigger = true;
        StaticParameter.EscapeSyncOpportunityTrigger = true;
        StaticParameter.EscapeOpportunityownerUpdate  = true;
        StaticParameter.EscapeAgencyOpportunityTrigger=true;
        List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院' Limit 1];
        if (rectCo.size() == 0) {
            return;
        }
        List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 呼吸科' Limit 1];
        if (rectSct.size() == 0) {
            return;
        }
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 消化科' Limit 1];
        if (rectDpt.size() == 0) {
            return;
        }

        //List<Tender_information__c> TenInfo = [Select Id, InfoId__c, Logical_delete__c, ProjectId__c, Retain_Tender__c From Tender_information__c Where id = : 'a4J1m0000009Tv5'];
        List<RecordType> rectDpts = [select Id from RecordType where IsActive = true and SobjectType = 'Tender_information__c' and Name = '手工录入' Limit 1];
        if (rectDpts.size() == 0) {
            return;
        }
        Tender_information__c TenInfo = new Tender_information__c();
        Tender_information__c TenInfo1 = new Tender_information__c();
        TenInfo1.Name = 'QLMTEST08111057-02';
        TenInfo1.RecordTypeId=rectDpts[0].Id;
        TenInfo1.InfoTitle__c = '招标项目1';
        TenInfo1.InfoType__c = '3：结果';
        TenInfo1.subInfoType__c='3-5：中标通知'; // ******** ljh SWAG-CKB9NR add
        insert TenInfo1;

        TenInfo.Name = '2345';
        TenInfo.InfoId__c = '1234';
        TenInfo.Retain_Tender__c = TenInfo1.Id;
        TenInfo.RecordTypeId=rectDpts[0].Id;
        TenInfo.InfoTitle__c = '招标项目2';
        TenInfo.TenderManageCode__c ='123';
        TenInfo.InfoType__c = '3：结果';
        TenInfo.subInfoType__c='3-1：废标公告'; // ******** ljh SWAG-CKB9NR add
        insert TenInfo;

        Tender_information_details__c detail = new Tender_information_details__c();
        detail.Name = 'test-detail';
        detail.Tender_information__c = TenInfo.Id;
        detail.ProjectId__c = TenInfo.ProjectId__c;
        detail.source__c = '众成';
        insert detail;
        Tender_information_details__c detail1 = new Tender_information_details__c();
        detail1.Name = 'test-detail';
        detail1.Tender_information__c = TenInfo1.Id;
        detail1.ProjectId__c = TenInfo1.ProjectId__c;
        detail1.source__c = '千里马';
        insert detail1;

        //List<Tender_Opportunity_Link__c> BlinksList = [select Opportunity__c from Tender_Opportunity_Link__c where Tender_information__c = :BTen.Id];
        Account company = new Account();
        company.RecordTypeId = rectCo[0].Id;
        company.Name         = 'NFM007TestCompany';
        upsert company;
        Account section = new Account();
        section.RecordTypeId = rectSct[0].Id;
        section.Name         = '*';
        section.Department_Class_Label__c = '消化科';
        section.ParentId                  = company.Id;
        section.Hospital_Department_Class__c = company.Id;
        upsert section;
        Account depart = new Account();
        depart.RecordTypeId = rectDpt[0].Id;
        depart.Name         = '*';
        depart.Department_Name__c  = 'NFM007TestDepart';
        depart.ParentId            = section.Id;
        depart.Department_Class__c = section.Id;
        depart.Hospital__c         = company.Id;
        upsert depart;

        Opportunity opp = new Opportunity();
        opp.AccountId           = depart.Id;
        opp.Department_Class__c = section.Id;
        opp.Hospital__c         = company.Id;
        opp.SAP_Send_OK__c      = false;
        opp.Name                = 'GZ-SP-NFM007_1';
        opp.Trade__c            = '内貿';
        opp.StageName           = '引合';
        opp.CloseDate           = date.newinstance(2025, 11, 30);
        opp.Stock_apply_status__c = '申请中';
        opp.Whether_Bidding__c = '否';
        insert opp;

        Tender_Opportunity_Link__c BlinksList = new Tender_Opportunity_Link__c();
        BlinksList.Opportunity__c = opp.Id;
        BlinksList.CurrencyIsoCode = 'CNY';
        BlinksList.Tender_information__c = TenInfo1.Id;
        // insert BlinksList;

        Tender_Opportunity_Link__c linksList = new Tender_Opportunity_Link__c();
        linksList.Opportunity__c = opp.Id;
        linksList.CurrencyIsoCode = 'CNY';
        linksList.Tender_information__c = TenInfo.Id;
        insert linksList;

        Tender_Opportunity_Link__c addlinksList = new Tender_Opportunity_Link__c();
        addlinksList.Opportunity__c = opp.Id;
        addlinksList.CurrencyIsoCode = 'CNY';
        addlinksList.Tender_information__c = TenInfo1.Id;
        // insert addlinksList;
        //Tender_information__c BTen = [select Id, InfoId__c From Tender_information__c Where Id = : TenInfo.Retain_Tender__c];
        Tender_information__c BTen = new Tender_information__c();
        BTen.InfoId__c = '1122';
        BTen.InfoType__c = '3：结果';
        TenInfo.Retain_Tender__c = BTen.Id;
        String BTenInfo = BTen.InfoId__c;
        BTen.InfoId__c = TenInfo.InfoId__c;
        TenInfo.InfoId__c = BTenInfo;
        TenInfo.Logical_delete__c = true;
        TenInfo.InfoType__c = '3：结果';
        List<Tender_information__c> updateTenInfoList = new List<Tender_information__c>();
        updateTenInfoList.add(TenInfo);
        updateTenInfoList.add(BTen);

        //Apexpages.currentPage().getParameters().put('id', TenInfo.Id);
        PageReference peg = new PageReference('/apex/TenderDeletePage?id=' + TenInfo.Id);
        System.Test.setCurrentPage(peg);
        // TenderDeleteLwcController tenderDeleteController = new TenderDeleteLwcController();
        System.Test.StartTest();
        String test =TenderDeleteLwcController.GetTenderinformationcData(TenInfo.Id);
        // Id,Name,InfoId__c,Logical_delete__c,ProjectId__c,Retain_Tender__c
        system.debug('test+++'+test);
        List<Tender_information__c> TenInfos=(List<Tender_information__c>)JSON.deserializeStrict(test,List<Tender_information__c>.class);
        test =JSON.serialize(TenInfos[0]);
        TenderDeleteLwcController.searchTender(TenInfo1.TenderManageCode__c);
        TenderDeleteLwcController.saveData(test);
        TenderDeleteLwcController.saveData(test);
        System.Test.StopTest();
    }
    static testMethod void testMethod3() {
        Oly_TriggerHandler.bypass('ContactTrigger');
        Oly_TriggerHandler.bypass('ContactTriggerHandler');
        Oly_TriggerHandler.bypass('AssetTrigger');
        Oly_TriggerHandler.bypass('MaintenanceContractTrigger');
        Oly_TriggerHandler.bypass('MaintenanceContractAfterUpdate');
        Oly_TriggerHandler.bypass('MaintenanceContractBeforeDelete');
        Oly_TriggerHandler.bypass('MaintenanceContractHpDeptUpd');
        Oly_TriggerHandler.bypass('NFM106Controller');
        Oly_TriggerHandler.bypass('NFM106Trigger');
        Oly_TriggerHandler.bypass('AssetRecordTypeUpd');
        Oly_TriggerHandler.bypass('NFM101Controller');
        Oly_TriggerHandler.bypass('NFM001Controller');
        Oly_TriggerHandler.bypass('SyncProduct2');
        // Oly_TriggerHandler.bypass('RentalApplyBeforeUpdate');
        Oly_TriggerHandler.bypass('ContactTriggerHandler');
        Oly_TriggerHandler.bypass('TenderInformationHandler');
        ControllerUtil.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
        StaticParameter.EscapeOppandStaTrigger = true;
        StaticParameter.EscapeOpportunityBefUpdTrigger = true;
        StaticParameter.EscapeOpportunityHpDeptUpdTrigger = true;
        StaticParameter.EscapeSyncOpportunityTrigger = true;
        StaticParameter.EscapeOpportunityownerUpdate  = true;
        StaticParameter.EscapeAgencyOpportunityTrigger=true;
        List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院' Limit 1];
        if (rectCo.size() == 0) {
            return;
        }
        List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 呼吸科' Limit 1];
        if (rectSct.size() == 0) {
            return;
        }
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 消化科' Limit 1];
        if (rectDpt.size() == 0) {
            return;
        }

        //List<Tender_information__c> TenInfo = [Select Id, InfoId__c, Logical_delete__c, ProjectId__c, Retain_Tender__c From Tender_information__c Where id = : 'a4J1m0000009Tv5'];
        List<RecordType> rectDpts = [select Id from RecordType where IsActive = true and SobjectType = 'Tender_information__c' and Name = '手工录入' Limit 1];
        if (rectDpts.size() == 0) {
            return;
        }
        Tender_information__c TenInfo = new Tender_information__c();
        Tender_information__c TenInfo1 = new Tender_information__c();
        TenInfo1.Name = 'QLMTEST08111057-02';
        TenInfo1.RecordTypeId=rectDpts[0].Id;
        TenInfo1.InfoTitle__c = '招标项目1';
        TenInfo1.InfoType__c = '3：结果';
        TenInfo1.subInfoType__c='3-1：废标公告'; // ******** ljh SWAG-CKB9NR add
        insert TenInfo1;

        TenInfo.Name = '2345';
        TenInfo.InfoId__c = '1234';
        TenInfo.Retain_Tender__c = TenInfo1.Id;
        TenInfo.RecordTypeId=rectDpts[0].Id;
        TenInfo.InfoTitle__c = '招标项目2';
        TenInfo.TenderManageCode__c ='123';
        TenInfo.InfoType__c = '3：结果';
        TenInfo.subInfoType__c='3-5：中标通知'; // ******** ljh SWAG-CKB9NR add
        insert TenInfo;


        Tender_information_details__c detail = new Tender_information_details__c();
        detail.Name = 'test-detail';
        detail.Tender_information__c = TenInfo.Id;
        detail.ProjectId__c = TenInfo.ProjectId__c;
        detail.source__c = '千里马';
        insert detail;
        Tender_information_details__c detail1 = new Tender_information_details__c();
        detail1.Name = 'test-detail';
        detail1.Tender_information__c = TenInfo1.Id;
        detail1.ProjectId__c = TenInfo1.ProjectId__c;
        detail1.source__c = '千里马';
        insert detail1;

        //List<Tender_Opportunity_Link__c> BlinksList = [select Opportunity__c from Tender_Opportunity_Link__c where Tender_information__c = :BTen.Id];
        Account company = new Account();
        company.RecordTypeId = rectCo[0].Id;
        company.Name         = 'NFM007TestCompany';
        upsert company;
        Account section = new Account();
        section.RecordTypeId = rectSct[0].Id;
        section.Name         = '*';
        section.Department_Class_Label__c = '消化科';
        section.ParentId                  = company.Id;
        section.Hospital_Department_Class__c = company.Id;
        upsert section;
        Account depart = new Account();
        depart.RecordTypeId = rectDpt[0].Id;
        depart.Name         = '*';
        depart.Department_Name__c  = 'NFM007TestDepart';
        depart.ParentId            = section.Id;
        depart.Department_Class__c = section.Id;
        depart.Hospital__c         = company.Id;
        upsert depart;

        Opportunity opp = new Opportunity();
        opp.AccountId           = depart.Id;
        opp.Department_Class__c = section.Id;
        opp.Hospital__c         = company.Id;
        opp.SAP_Send_OK__c      = false;
        opp.Name                = 'GZ-SP-NFM007_1';
        opp.Trade__c            = '内貿';
        opp.StageName           = '引合';
        opp.CloseDate           = date.newinstance(2025, 11, 30);
        opp.Stock_apply_status__c = '申请中';
        opp.Whether_Bidding__c = '否';
        insert opp;

        Tender_Opportunity_Link__c BlinksList = new Tender_Opportunity_Link__c();
        BlinksList.Opportunity__c = opp.Id;
        BlinksList.CurrencyIsoCode = 'CNY';
        BlinksList.Tender_information__c = TenInfo1.Id;
        // insert BlinksList;

        Tender_Opportunity_Link__c linksList = new Tender_Opportunity_Link__c();
        linksList.Opportunity__c = opp.Id;
        linksList.CurrencyIsoCode = 'CNY';
        linksList.Tender_information__c = TenInfo.Id;
        insert linksList;

        Tender_Opportunity_Link__c addlinksList = new Tender_Opportunity_Link__c();
        addlinksList.Opportunity__c = opp.Id;
        addlinksList.CurrencyIsoCode = 'CNY';
        addlinksList.Tender_information__c = TenInfo1.Id;
        // insert addlinksList;
        //Tender_information__c BTen = [select Id, InfoId__c From Tender_information__c Where Id = : TenInfo.Retain_Tender__c];
        Tender_information__c BTen = new Tender_information__c();
        BTen.InfoId__c = '1122';
        BTen.InfoType__c = '3：结果';
        TenInfo.Retain_Tender__c = BTen.Id;
        String BTenInfo = BTen.InfoId__c;
        BTen.InfoId__c = TenInfo.InfoId__c;
        TenInfo.InfoId__c = BTenInfo;
        TenInfo.Logical_delete__c = true;
        TenInfo.InfoType__c = '3：结果';
        List<Tender_information__c> updateTenInfoList = new List<Tender_information__c>();
        updateTenInfoList.add(TenInfo);
        updateTenInfoList.add(BTen);

        //Apexpages.currentPage().getParameters().put('id', TenInfo.Id);
        PageReference peg = new PageReference('/apex/TenderDeletePage?id=' + TenInfo.Id);
        System.Test.setCurrentPage(peg);
        // TenderDeleteLwcController tenderDeleteController = new TenderDeleteLwcController();
        System.Test.StartTest();
        String test =TenderDeleteLwcController.GetTenderinformationcData(TenInfo.Id);
        // Id,Name,InfoId__c,Logical_delete__c,ProjectId__c,Retain_Tender__c
        system.debug('test+++'+test);
        List<Tender_information__c> TenInfos=(List<Tender_information__c>)JSON.deserializeStrict(test,List<Tender_information__c>.class);
        test =JSON.serialize(TenInfos[0]);
        TenderDeleteLwcController.searchTender(TenInfo1.TenderManageCode__c);
        TenderDeleteLwcController.saveData(test);
        TenderDeleteLwcController.saveData(test);
        System.Test.StopTest();
    }
    static testMethod void testMethod4() {
        Oly_TriggerHandler.bypass('ContactTrigger');
        Oly_TriggerHandler.bypass('ContactTriggerHandler');
        Oly_TriggerHandler.bypass('AssetTrigger');
        Oly_TriggerHandler.bypass('MaintenanceContractTrigger');
        Oly_TriggerHandler.bypass('MaintenanceContractAfterUpdate');
        Oly_TriggerHandler.bypass('MaintenanceContractBeforeDelete');
        Oly_TriggerHandler.bypass('MaintenanceContractHpDeptUpd');
        Oly_TriggerHandler.bypass('NFM106Controller');
        Oly_TriggerHandler.bypass('NFM106Trigger');
        Oly_TriggerHandler.bypass('AssetRecordTypeUpd');
        Oly_TriggerHandler.bypass('NFM101Controller');
        Oly_TriggerHandler.bypass('NFM001Controller');
        Oly_TriggerHandler.bypass('SyncProduct2');
        // Oly_TriggerHandler.bypass('RentalApplyBeforeUpdate');
        Oly_TriggerHandler.bypass('ContactTriggerHandler');
        Oly_TriggerHandler.bypass('TenderInformationHandler');
        ControllerUtil.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
        StaticParameter.EscapeOppandStaTrigger = true;
        StaticParameter.EscapeOpportunityBefUpdTrigger = true;
        StaticParameter.EscapeOpportunityHpDeptUpdTrigger = true;
        StaticParameter.EscapeSyncOpportunityTrigger = true;
        StaticParameter.EscapeOpportunityownerUpdate  = true;
        StaticParameter.EscapeAgencyOpportunityTrigger=true;
        List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院' Limit 1];
        if (rectCo.size() == 0) {
            return;
        }
        List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 呼吸科' Limit 1];
        if (rectSct.size() == 0) {
            return;
        }
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 消化科' Limit 1];
        if (rectDpt.size() == 0) {
            return;
        }

        //List<Tender_information__c> TenInfo = [Select Id, InfoId__c, Logical_delete__c, ProjectId__c, Retain_Tender__c From Tender_information__c Where id = : 'a4J1m0000009Tv5'];
        List<RecordType> rectDpts = [select Id from RecordType where IsActive = true and SobjectType = 'Tender_information__c' and Name = '手工录入' Limit 1];
        if (rectDpts.size() == 0) {
            return;
        }
        Tender_information__c TenInfo = new Tender_information__c();
        Tender_information__c TenInfo1 = new Tender_information__c();
        TenInfo1.Name = 'QLMTEST08111057-02';
        TenInfo1.RecordTypeId=rectDpts[0].Id;
        TenInfo1.InfoTitle__c = '招标项目1';
        TenInfo1.InfoType__c = '3：结果';
        TenInfo1.subInfoType__c='3-1：废标公告'; // ******** ljh SWAG-CKB9NR add
        insert TenInfo1;

        TenInfo.Name = '2345';
        TenInfo.InfoId__c = '1234';
        TenInfo.Retain_Tender__c = TenInfo1.Id;
        TenInfo.RecordTypeId=rectDpts[0].Id;
        TenInfo.InfoTitle__c = '招标项目2';
        TenInfo.TenderManageCode__c ='123';
        TenInfo.InfoType__c = '3：结果';
        TenInfo.subInfoType__c='3-5：废标公告'; // ******** ljh SWAG-CKB9NR add
        insert TenInfo;

        Tender_information_details__c detail = new Tender_information_details__c();
        detail.Name = 'test-detail';
        detail.Tender_information__c = TenInfo.Id;
        detail.ProjectId__c = TenInfo.ProjectId__c;
        detail.source__c = '众成';
        insert detail;
        Tender_information_details__c detail1 = new Tender_information_details__c();
        detail1.Name = 'test-detail';
        detail1.Tender_information__c = TenInfo1.Id;
        detail1.ProjectId__c = TenInfo1.ProjectId__c;
        detail1.source__c = '千里马';
        insert detail1;

        //List<Tender_Opportunity_Link__c> BlinksList = [select Opportunity__c from Tender_Opportunity_Link__c where Tender_information__c = :BTen.Id];
        Account company = new Account();
        company.RecordTypeId = rectCo[0].Id;
        company.Name         = 'NFM007TestCompany';
        upsert company;
        Account section = new Account();
        section.RecordTypeId = rectSct[0].Id;
        section.Name         = '*';
        section.Department_Class_Label__c = '消化科';
        section.ParentId                  = company.Id;
        section.Hospital_Department_Class__c = company.Id;
        upsert section;
        Account depart = new Account();
        depart.RecordTypeId = rectDpt[0].Id;
        depart.Name         = '*';
        depart.Department_Name__c  = 'NFM007TestDepart';
        depart.ParentId            = section.Id;
        depart.Department_Class__c = section.Id;
        depart.Hospital__c         = company.Id;
        upsert depart;

        Opportunity opp = new Opportunity();
        opp.AccountId           = depart.Id;
        opp.Department_Class__c = section.Id;
        opp.Hospital__c         = company.Id;
        opp.SAP_Send_OK__c      = false;
        opp.Name                = 'GZ-SP-NFM007_1';
        opp.Trade__c            = '内貿';
        opp.StageName           = '引合';
        opp.CloseDate           = date.newinstance(2025, 11, 30);
        opp.Stock_apply_status__c = '申请中';
        opp.Whether_Bidding__c = '否';
        insert opp;

        Tender_Opportunity_Link__c BlinksList = new Tender_Opportunity_Link__c();
        BlinksList.Opportunity__c = opp.Id;
        BlinksList.CurrencyIsoCode = 'CNY';
        BlinksList.Tender_information__c = TenInfo1.Id;
        // insert BlinksList;

        Tender_Opportunity_Link__c linksList = new Tender_Opportunity_Link__c();
        linksList.Opportunity__c = opp.Id;
        linksList.CurrencyIsoCode = 'CNY';
        linksList.Tender_information__c = TenInfo.Id;
        insert linksList;

        Tender_Opportunity_Link__c addlinksList = new Tender_Opportunity_Link__c();
        addlinksList.Opportunity__c = opp.Id;
        addlinksList.CurrencyIsoCode = 'CNY';
        addlinksList.Tender_information__c = TenInfo1.Id;
        // insert addlinksList;
        //Tender_information__c BTen = [select Id, InfoId__c From Tender_information__c Where Id = : TenInfo.Retain_Tender__c];
        Tender_information__c BTen = new Tender_information__c();
        BTen.InfoId__c = '1122';
        BTen.InfoType__c = '3：结果';
        TenInfo.Retain_Tender__c = BTen.Id;
        String BTenInfo = BTen.InfoId__c;
        BTen.InfoId__c = TenInfo.InfoId__c;
        TenInfo.InfoId__c = BTenInfo;
        TenInfo.Logical_delete__c = true;
        TenInfo.InfoType__c = '3：结果';
        List<Tender_information__c> updateTenInfoList = new List<Tender_information__c>();
        updateTenInfoList.add(TenInfo);
        updateTenInfoList.add(BTen);

        //Apexpages.currentPage().getParameters().put('id', TenInfo.Id);
        PageReference peg = new PageReference('/apex/TenderDeletePage?id=' + TenInfo.Id);
        System.Test.setCurrentPage(peg);
        // TenderDeleteLwcController tenderDeleteController = new TenderDeleteLwcController();
        System.Test.StartTest();
        String test =TenderDeleteLwcController.GetTenderinformationcData(TenInfo.Id);
        // Id,Name,InfoId__c,Logical_delete__c,ProjectId__c,Retain_Tender__c
        system.debug('test+++'+test);
        List<Tender_information__c> TenInfos=(List<Tender_information__c>)JSON.deserializeStrict(test,List<Tender_information__c>.class);
        test =JSON.serialize(TenInfos[0]);
        TenderDeleteLwcController.searchTender(TenInfo1.TenderManageCode__c);
        TenderDeleteLwcController.saveData(test);
        TenderDeleteLwcController.saveData(test);
        System.Test.StopTest();
    }
    static testMethod void testMethod5() {
        Oly_TriggerHandler.bypass('ContactTrigger');
        Oly_TriggerHandler.bypass('ContactTriggerHandler');
        Oly_TriggerHandler.bypass('AssetTrigger');
        Oly_TriggerHandler.bypass('MaintenanceContractTrigger');
        Oly_TriggerHandler.bypass('MaintenanceContractAfterUpdate');
        Oly_TriggerHandler.bypass('MaintenanceContractBeforeDelete');
        Oly_TriggerHandler.bypass('MaintenanceContractHpDeptUpd');
        Oly_TriggerHandler.bypass('NFM106Controller');
        Oly_TriggerHandler.bypass('NFM106Trigger');
        Oly_TriggerHandler.bypass('AssetRecordTypeUpd');
        Oly_TriggerHandler.bypass('NFM101Controller');
        Oly_TriggerHandler.bypass('NFM001Controller');
        Oly_TriggerHandler.bypass('SyncProduct2');
        // Oly_TriggerHandler.bypass('RentalApplyBeforeUpdate');
        Oly_TriggerHandler.bypass('ContactTriggerHandler');
        Oly_TriggerHandler.bypass('TenderInformationHandler');
        ControllerUtil.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
        StaticParameter.EscapeOppandStaTrigger = true;
        StaticParameter.EscapeOpportunityBefUpdTrigger = true;
        StaticParameter.EscapeOpportunityHpDeptUpdTrigger = true;
        StaticParameter.EscapeSyncOpportunityTrigger = true;
        StaticParameter.EscapeOpportunityownerUpdate  = true;
        StaticParameter.EscapeAgencyOpportunityTrigger=true;
        List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院' Limit 1];
        if (rectCo.size() == 0) {
            return;
        }
        List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 呼吸科' Limit 1];
        if (rectSct.size() == 0) {
            return;
        }
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 消化科' Limit 1];
        if (rectDpt.size() == 0) {
            return;
        }

        //List<Tender_information__c> TenInfo = [Select Id, InfoId__c, Logical_delete__c, ProjectId__c, Retain_Tender__c From Tender_information__c Where id = : 'a4J1m0000009Tv5'];
        List<RecordType> rectDpts = [select Id from RecordType where IsActive = true and SobjectType = 'Tender_information__c' and Name = '手工录入' Limit 1];
        if (rectDpts.size() == 0) {
            return;
        }
        Tender_information__c TenInfo = new Tender_information__c();
        Tender_information__c TenInfo1 = new Tender_information__c();
        TenInfo1.Name = 'QLMTEST08111057-02';
        TenInfo1.RecordTypeId=rectDpts[0].Id;
        TenInfo1.InfoTitle__c = '招标项目1';
        TenInfo1.InfoType__c = '3：结果';
        TenInfo1.subInfoType__c='2-2：重新招标'; // ******** ljh SWAG-CKB9NR add
        insert TenInfo1;

        TenInfo.Name = '2345';
        TenInfo.InfoId__c = '1234';
        TenInfo.Retain_Tender__c = TenInfo1.Id;
        TenInfo.RecordTypeId=rectDpts[0].Id;
        TenInfo.InfoTitle__c = '招标项目2';
        TenInfo.TenderManageCode__c ='123';
        TenInfo.InfoType__c = '3：结果';
        TenInfo.subInfoType__c='2-2：重新招标'; // ******** ljh SWAG-CKB9NR add
        insert TenInfo;

        Tender_information_details__c detail = new Tender_information_details__c();
        detail.Name = 'test-detail';
        detail.Tender_information__c = TenInfo.Id;
        detail.ProjectId__c = TenInfo.ProjectId__c;
        detail.source__c = '众成';
        insert detail;
        Tender_information_details__c detail1 = new Tender_information_details__c();
        detail1.Name = 'test-detail';
        detail1.Tender_information__c = TenInfo1.Id;
        detail1.ProjectId__c = TenInfo1.ProjectId__c;
        detail1.source__c = '千里马';
        insert detail1;

        //List<Tender_Opportunity_Link__c> BlinksList = [select Opportunity__c from Tender_Opportunity_Link__c where Tender_information__c = :BTen.Id];
        Account company = new Account();
        company.RecordTypeId = rectCo[0].Id;
        company.Name         = 'NFM007TestCompany';
        upsert company;
        Account section = new Account();
        section.RecordTypeId = rectSct[0].Id;
        section.Name         = '*';
        section.Department_Class_Label__c = '消化科';
        section.ParentId                  = company.Id;
        section.Hospital_Department_Class__c = company.Id;
        upsert section;
        Account depart = new Account();
        depart.RecordTypeId = rectDpt[0].Id;
        depart.Name         = '*';
        depart.Department_Name__c  = 'NFM007TestDepart';
        depart.ParentId            = section.Id;
        depart.Department_Class__c = section.Id;
        depart.Hospital__c         = company.Id;
        upsert depart;

        Opportunity opp = new Opportunity();
        opp.AccountId           = depart.Id;
        opp.Department_Class__c = section.Id;
        opp.Hospital__c         = company.Id;
        opp.SAP_Send_OK__c      = false;
        opp.Name                = 'GZ-SP-NFM007_1';
        opp.Trade__c            = '内貿';
        opp.StageName           = '引合';
        opp.CloseDate           = date.newinstance(2025, 11, 30);
        opp.Stock_apply_status__c = '申请中';
        opp.Whether_Bidding__c = '否';
        insert opp;

        Tender_Opportunity_Link__c BlinksList = new Tender_Opportunity_Link__c();
        BlinksList.Opportunity__c = opp.Id;
        BlinksList.CurrencyIsoCode = 'CNY';
        BlinksList.Tender_information__c = TenInfo1.Id;
        // insert BlinksList;

        Tender_Opportunity_Link__c linksList = new Tender_Opportunity_Link__c();
        linksList.Opportunity__c = opp.Id;
        linksList.CurrencyIsoCode = 'CNY';
        linksList.Tender_information__c = TenInfo.Id;
        insert linksList;

        Tender_Opportunity_Link__c addlinksList = new Tender_Opportunity_Link__c();
        addlinksList.Opportunity__c = opp.Id;
        addlinksList.CurrencyIsoCode = 'CNY';
        addlinksList.Tender_information__c = TenInfo1.Id;
        // insert addlinksList;
        //Tender_information__c BTen = [select Id, InfoId__c From Tender_information__c Where Id = : TenInfo.Retain_Tender__c];
        Tender_information__c BTen = new Tender_information__c();
        BTen.InfoId__c = '1122';
        BTen.InfoType__c = '3：结果';
        TenInfo.Retain_Tender__c = BTen.Id;
        String BTenInfo = BTen.InfoId__c;
        BTen.InfoId__c = TenInfo.InfoId__c;
        TenInfo.InfoId__c = BTenInfo;
        TenInfo.Logical_delete__c = true;
        TenInfo.InfoType__c = '3：结果';
        List<Tender_information__c> updateTenInfoList = new List<Tender_information__c>();
        updateTenInfoList.add(TenInfo);
        updateTenInfoList.add(BTen);

        //Apexpages.currentPage().getParameters().put('id', TenInfo.Id);
        PageReference peg = new PageReference('/apex/TenderDeletePage?id=' + TenInfo.Id);
        System.Test.setCurrentPage(peg);
        // TenderDeleteLwcController tenderDeleteController = new TenderDeleteLwcController();
        System.Test.StartTest();
        String test =TenderDeleteLwcController.GetTenderinformationcData(TenInfo.Id);
        // Id,Name,InfoId__c,Logical_delete__c,ProjectId__c,Retain_Tender__c
        system.debug('test+++'+test);
        List<Tender_information__c> TenInfos=(List<Tender_information__c>)JSON.deserializeStrict(test,List<Tender_information__c>.class);
        test =JSON.serialize(TenInfos[0]);
        TenderDeleteLwcController.searchTender(TenInfo1.TenderManageCode__c);
        TenderDeleteLwcController.saveData(test);
        TenderDeleteLwcController.saveData(test);
        TenderDeleteLwcController.specialChange(TenInfos[0],TenInfos[0],TenInfos[0],TenInfos[0]);
        System.Test.StopTest();
    }

    static testMethod void testMethod6() {
        TenderDeleteLwcController.test();
    }
    
}