({
    CallBackAction  : function(component,action_name,para,callback) {
		var action = component.get("c." + action_name.trimStart().replace("c.",""));
        if(para){
            action.setParams(para);
        }
        if(callback){
            action.setCallback(this,function(data){
                callback(data);
            });
        }
        
        $A.enqueueAction(action);
	},
    ShowToast : function(paras){
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams(paras);
        toastEvent.fire();
    },
    refreshTable : function(component, event, helper,cols,contactInfoList,contactsInfo){
        let that = this;
        let myTableDiv = document.getElementById("QueryResult");
        let table = document.createElement('TABLE');
        table.border = '1';
        table.id = 'customTable';
        table.setAttribute('class','slds-table slds-table_cell-buffer slds-table_bordered');
        let tableBody = document.createElement('TBODY');
        table.appendChild(tableBody);
        let headerTR = document.createElement('TR');
        headerTR.setAttribute('class','slds-line-height_reset');
        let colsHeader = [' ','姓名','医院', '医生区分(职务)','分类'];
        tableBody.appendChild(headerTR);
        for (let i = 0; i < colsHeader.length; i++) {
            let td = document.createElement('TH');
            td.width = '75';
            td.setAttribute('class','slds-truncate slds-border_right');
            td.appendChild(document.createTextNode(colsHeader[i]));
            headerTR.appendChild(td);
        }
        //3. Init the AWS data
        for (let i = 0; i < contactInfoList.length; i++) {
            let tr = document.createElement('TR');
            tableBody.appendChild(tr);
            let contactInfoTemp = contactInfoList[i];
            
            //再加一列选择button
            debugger
            let td = document.createElement('TD');
            td.width = '75';
            td.id = contactInfoTemp.Id;
            console.log('contactInfoTemp = ' + JSON.stringify(contactInfoTemp))
            // 创建按钮元素
            var button = document.createElement('button');
            button.classList.add('slds-button','slds-button_neutral');
            button.id = contactInfoTemp.sfRecordId != null ? contactInfoTemp.sfRecordId : '';
            // 设置按钮的文本内容
            var buttonText = document.createTextNode('选择');
            button.appendChild(buttonText);
            button.addEventListener("click", function (obj) {
                that.redirectToParentPage(component, event, helper, obj);
            });
            button.value = contactInfoTemp[cols[0]] != null ? contactInfoTemp[cols[0]] : ''
            td.appendChild(button);
            tr.appendChild(td);
            for (let j = 0; j < cols.length; j++) {
                let td = document.createElement('TD');
                td.width = '75';
                td.id = contactInfoTemp.sfRecordId;
                td.appendChild(document.createTextNode(contactInfoTemp[cols[j]] != null ? contactInfoTemp[cols[j]] : ''));
                                tr.appendChild(td);
            }
        }
        that.resetTable();
        myTableDiv.appendChild(table);
        component.find('button').set('v.disabled', false);
    },
    redirectToParentPage : function(component, event, helper,obj){
        let agencyContactName = obj.currentTarget.value;
        let id = obj.currentTarget.id;
        component.set('v.ac_name',agencyContactName);
        let layout = component.get('v.layout');
        for(let s of layout){
            for(let c of s.layoutColumns){                
                for(let item of c.layoutItems){
                    if(item.field == 'Agency_Contact__c'){
                        item.value = id;
                    }
                }
            }
        }
        component.set('v.iconName', 'utility:close');  //deloitte-zhj 20231104
        component.set("v.isModalOpen", false);
        component.set('v.searchKeyWord', '');
    },
    resetTable : function(){
        // let queryResult = document.getElementById('QueryResult');
        // let table = document.getElementById('customTable');
        // console.log('table:' + table);
        // if(table.length > 1){
        //     for(var i=1;i<table.length;i++){
        //         queryResult.removeChild(table[i])
        //     }
        // }
        let customTable = document.getElementById('customTable');
        if(customTable){
            customTable.parentNode.removeChild(customTable);
        }
    },
    preparePayloadForSearchAgencyContact : function(component, event, helper){
        let that = this;
        let agencyHospitalId = '';
        let accountId = '';
        let layout = component.get('v.layout');
        for(let s of layout){
            for(let c of s.layoutColumns){                
                for(let item of c.layoutItems){
                    if(item.field == 'Agency_Hospital__c'){
                        agencyHospitalId = item.value;
                    }
                    if(item.field == 'Account_Opp__c'){
                        accountId = item.value;
                    }
                }
            }
        }
        var action = component.get('c.getAWSDataIds');
        let searchAgencyContactName = component.get('v.searchKeyWord');
        action.setParams({
            "agencyHospitalId" : agencyHospitalId,
            "accountId" : accountId,
            "searchKeyword" : searchAgencyContactName
        });
        action.setCallback(this,function(response){
            var state = response.getState();
            if(state == 'SUCCESS'){
                var rv = response.getReturnValue();
                if(rv.Data.length == 0){
                    component.find('button').set('v.disabled', false);
                    let message = "";
                    if (searchAgencyContactName) {
                        message = "没有查询到该客户人员";
                    } else {
                        message = "该经销商医院下面没有客户人员";
                    }
                    helper.ShowToast({
                        "message" : message,
                        "type" : "warning"
                    });
                    return
                }
                component.set('v.showSpinner2',true);
                let cols = ['Name', 'AgencyHospital', 'DoctorDivision1', 'Type'];
                let contactInfoList = [];
                let contactsInfo = '';
                for(var i = 0; i < rv.Data.length; i++){
                    let contactInfo = new Object();
                    //需要修改
                    contactInfo.Name = rv.Data[i].Name;
                    contactInfo.AgencyHospital = '';
                    contactInfo.DoctorDivision1 = rv.Data[i].Doctor_Division1__c;
                    contactInfo.Type = rv.Data[i].Type__c;
                    contactInfo.sfRecordId = rv.Data[i].Id;
                    contactInfo.Id = rv.Data[i].Id;
                    if (rv.Data[i]['Agency_Hospital__r']) {
                        contactInfo.AgencyHospital = rv.Data[i]['Agency_Hospital__r']['Name'];
                    }
                    if (rv.Data[i]['Department_Class__r']) {
                        contactInfo.AgencyHospital = rv.Data[i]['Department_Class__r']['Parent']['Name'];
                    }
                    contactInfoList.push(contactInfo);
                }                    
                that.refreshTable(component, event, helper,cols,contactInfoList,contactsInfo);
                component.set('v.showSpinner2',false);
            }
        });
        $A.enqueueAction(action);
    },
    //deloitte-zhj 20230831 改造lightning start
    lightningJump : function (component, event, helper){
        debugger
        let rid = component.get('v.recordId');
        if(rid == null || rid == ''){
            helper.toListAgencyOpportunity(component, event, helper);
        }else{
            helper.toIdAgencyOpportunity(component, event, helper);
        }
    },

    toListAgencyOpportunity: function (component, event, helper) {
        let navService = component.find("navService");
        let pageReferenceTemp = {
            type: 'standard__objectPage',
            attributes: {
                objectApiName: 'Agency_Opportunity__c',
                actionName: 'list'
            }
        };
        component.set("v.pageReference", pageReferenceTemp);
        let pageReference = component.get("v.pageReference");
        event.preventDefault();
        navService.navigate(pageReference);
    },

    toIdAgencyOpportunity: function (component, event, helper) {
        let rid = component.get('v.recordId');
        window.location.href = '/' + rid;
        // let navService = component.find("navService");
        // let rid = component.get('v.recordId');
        // console.log('rid = ' + rid);
        // console.log('navService = ' + navService);
        // let pageReferenceTemp = {
        //     type: 'standard__objectPage',
        //     attributes: {
        //         actionName: "view",
        //         recordId: rid,
        //         objectApiName: 'Agency_Opportunity__c',
        //     }
        // };
        // component.set("v.pageReference", pageReferenceTemp);
        // let pageReference = component.get("v.pageReference");
        // event.preventDefault();
        // navService.navigate(pageReference);
    },

    sortMetaLayouts: function(layout) {
        console.log('enter sortMetaLayouts');
        layout.forEach(s => {
            let c1 = (s.layoutColumns[0].layoutItems != null) ? s.layoutColumns[0].layoutItems : [];
            let c2 = [];
            if (s.layoutColumns.length > 1) {
                c2 = (s.layoutColumns[1].layoutItems != null) ? s.layoutColumns[1].layoutItems : [];
            }
            else {
                s.layoutColumns.push({ "reserved": null, "layoutItems": [] });
            }
            let emptySpaceItem = {
                "width": null,
                "showScrollbars": null,
                "showLabel": null,
                "scontrol": null,
                "reportChartComponent": null,
                "page_x": null,
                "height": null,
                "field": null,
                "emptySpace": true,
                "customLink": null,
                "component": null,
                "canvas": null,
                "behavior": null,
                "analyticsCloudComponent": null
            }
            let maxSize = 0
            if (c1.length > c2.length) {
                maxSize = c1.length;
                for (let i = 0; i < maxSize; i++) {
                    if (c2.length == maxSize) {
                        break;
                    }
                    c2.push(emptySpaceItem);
                }
            } else {
                maxSize = c2.length;
                for (let i = 0; i < maxSize; i++) {
                    if (c1.length == maxSize) {
                        break;
                    }
                    c1.push(emptySpaceItem);
                }
            }
            let tempList = [];
            let index = 0;
            for (let i = 0; i < maxSize; i++) {
                tempList.push(c1[i]);
                if (tempList.length == maxSize) {
                    s.layoutColumns[index].layoutItems = tempList;
                    tempList = [];
                    tempList.push(c2[i]);
                    index++;
                } else {
                    tempList.push(c2[i]);
                    if (tempList.length == maxSize) {
                        s.layoutColumns[index].layoutItems = tempList;
                        tempList = [];
                        index++;
                    }
                }
            }
        })
        layout = [...layout];
        console.log('end sortMetaLayouts');
        return layout;
    }
    //deloitte-zhj 20230831 改造lightning end
})