@isTest
private class UserBeforeTriggerTest {
    // update user
    static testMethod void myUnitTest() {
        User loginUser = [Select Id, Alias, Province__c from User where Id =: UserInfo.getUserId()];
        loginUser.Photo__c = '<img alt="User-added image" src="https://www.abc.com/image?eid=xxx&amp;feoid=yyy&amp;refid=zzz"></img>';//<img alt="User-added image" height="339" src="https://c.cs5.content.force.com/servlet/rtaImage?eid=xxx&amp;feoid=yyy&amp;refid=zzz" width="500"></img>
        update loginUser;

        loginUser = [Select Id, Alias, Photo_Text__c from User where Id =: UserInfo.getUserId()];
//        System.assertEquals('https://www.abc.com/image?eid=xxx&feoid=yyy&refid=zzz', loginUser.Photo_Text__c);
    }

    // insert user
    static testMethod void userInserttest() {
        User user = new User();
        user.LastName = 'LastName';
        user.FirstName = 'FirstName';
        user.Alias = 'Alias';
        user.Email = '<EMAIL>';
        user.Username = '<EMAIL>';
        user.CommunityNickname = 'CommunityNickname';
        user.IsActive = true;
        user.EmailEncodingKey = 'ISO-2022-JP';
        user.TimeZoneSidKey = 'Asia/Tokyo';
        user.LocaleSidKey = 'ja_JP';
        user.LanguageLocaleKey = 'ja';
        user.ProfileId = System.Label.ProfileId_SystemAdmin;
        user.Job_Category__c = '销售推广';
        user.Province__c = '上海市';
        user.Post__c = '经理';
        user.MobilePhone = '54321';
        user.Mobile_Phone__c = '12345';
        user.Employee_No__c = '112233';
        user.Work_Location__c = 'Location';
        user.Use_Start_Date__c = Date.today().addMonths(-6);
        
        insert user;
        List<user> con = [select id,name,Employee_No__c,FederationIdentifier from User where Id = :user.Id];
       //System.assertEquals('<EMAIL>', con[0].FederationIdentifier);
    }

    // update 联盟ID user
    static testMethod void userUpdatetest() {
        User user = new User();
        user.LastName = 'LastName';
        user.FirstName = 'FirstName';
        user.Alias = 'Alias';
        user.Email = '<EMAIL>';
        user.Username = '<EMAIL>';
        user.CommunityNickname = 'CommunityNickname';
        user.IsActive = true;
        user.Test_staff__c = true;
        user.EmailEncodingKey = 'ISO-2022-JP';
        user.TimeZoneSidKey = 'Asia/Tokyo';
        user.LocaleSidKey = 'ja_JP';
        user.LanguageLocaleKey = 'ja';
        user.ProfileId = System.Label.ProfileId_SystemAdmin;
        user.Job_Category__c = '销售推广';
        user.Province__c = '上海市';
        user.Post__c = '经理';
        user.MobilePhone = '54321';
        user.Mobile_Phone__c = '12345';
        user.Employee_No__c = '112233';
        user.Work_Location__c = 'Location';
        user.Use_Start_Date__c = Date.today().addMonths(-6);
        System.Test.startTest();
        insert user;
        user.Test_staff__c = false;
        update user;
        System.Test.stopTest();
        List<user> con = [select id,name,Employee_No__c,FederationIdentifier from User where Id = :user.Id];
        //System.assertEquals('<EMAIL>', con[0].FederationIdentifier);
    }
}