({
    doinit : function(component, event, helper) {
        // this.report_date_list(component, event, helper, 10);

        component.set('v.login',true);
        var recordId = component.get('v.recordId');
        var action = component.get('c.getcontactdata');
         action.setParams({
            "recordId" : recordId
        });
        action.setCallback(this,function(response){
            var state = response.getState();
            if(state == 'SUCCESS'){
                var res = response.getReturnValue();
                // component.set('v.alldata',res.reports);
                // component.set('v.fieldsmap',res.fieldsMap);
                 component.set('v.allselectlist',res.allselectlist);
                console.log('res.allselectlist:'+res.allselectlist);
                // component.set('v.doclist',res.doclist);
                component.find('select_agency_person').set('v.options', this.conv_selected(res.allselectlist.AgencyPerson__c));
                // component.find('select_department').set('v.options', this.conv_selected(res.allselectlist.Department_Cateogy__c));
                // component.find('select_purpose_type').set('v.options', this.conv_selected(res.allselectlist.Purpose_Type__c));
                // component.find('select_result').set('v.options', this.conv_selected(res.allselectlist.Result__c));
                // component.find('select_stageName').set('v.options', this.conv_selected(res.allselectlist.StageName__c));
                // component.set('v.selected_agency_person', res.allselectlist.AgencyPerson__c[0].label);
                // component.set('v.dialog_type', '新建');
                
                //this.get_reports(component, event, helper, component.find('select_date').get('v.value'), component.find('select_agency_person').get('v.value'));
                
                component.set('v.login',false);
            }
            else{
                this.error('doinit failed.');
                component.set('v.login',false);
            }
        });
        $A.enqueueAction(action);
    },
    select_agency_change : function(component, event, helper) {
        var select_value = component.find('select_agency_person').get('v.value');
        var new_label = this.get_agency_person_name(component, select_value);
        component.set('v.selected_agency_person', new_label);
        
        // this.get_reports(component, event, helper, component.find('select_date').get('v.value'), select_value);
        // this.new_button_disabled(component, event, helper);

        // component.set('v.select_report_data_radio', '');
        // component.find('copy_button').set('v.disabled', true);
    },

    save_record : function(component, event, helper) {
        // component.find('save_button').set('v.disabled', true);
        // 保存処理
        component.set('v.login',true);
        var contactId = component.find('select_agency_person').get('v.value');
        var recordId = component.get('v.recordId');
        var action = component.get('c.saveagencyOppContact');
        action.setParams({
            "recordId" : recordId,
            "contactId" : contactId
        });
        
        action.setCallback(this,function(response){
            var state = response.getState();
            if(state == 'SUCCESS'){
                this.success('保存成功');
                $A.get('e.force:refreshView').fire();
                // component.find('save_button').set('v.disabled', false);
                component.set('v.login',false);
            }
            else{
                this.error('save_record failed.');
                component.find('save_button').set('v.disabled', false);
                component.set('v.login',false);
            }
        });
        $A.enqueueAction(action);
    },

    conv_selected : function(obj) {
        for (var i = 0; i < obj.length; i++) {
            if (obj[i].selected) {
                if (obj[i].selected == 'true') {
                    obj[i].selected = true;
                } else {
                    obj[i].selected = false;
                }
            }
        }
        
        return obj;
    },
    success : function(message) {
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams({
            "title" : "成功!",
            "message" : message,
            "type" : "success",
            "duration" : "5000"
        });
        toastEvent.fire();
    },
    
    hideCmp : function(component, event, helper) {
        var div1 = component.find('report')
        var div2 = component.find('report_list')
        $A.util.addClass(div1, 'disp_none');
        $A.util.addClass(div2, 'disp_none');
    }
})