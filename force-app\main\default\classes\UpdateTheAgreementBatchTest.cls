@isTest
private class UpdateTheAgreementBatchTest {
    @isTest
    static  void testMethod1() {
        List<RecordType> rectContract = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '契約'];
        List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '販売店'];
        if (rectCo.size() == 0) {
            return;
        }
         // 省
         Address_Level__c al = new Address_Level__c();
         al.Name = '辽宁省';
         al.Level1_Code__c = 'CN-99';
         al.Level1_Sys_No__c = '999999';
         insert al;
         // 市
         Address_Level2__c al2 = new Address_Level2__c();
         al2.Level1_Code__c = 'CN-99';
         al2.Level1_Sys_No__c = '999999';
         al2.Level1_Name__c = '東京';
         al2.Name = '大连市';
         al2.Level2_Code__c = 'CN-9999';
         al2.Level2_Sys_No__c = '9999999';
         al2.Address_Level__c = al.id;
         insert al2;

        //经销商
       Account myAccount1 = new Account(name='Testaccount001',
                                       Dealer_discount__c =20,
                                       RecordTypeId = rectCo[0].Id);
       insert myAccount1;
       //经销商有效合同
       Account myAccount2 = new Account(name='Testaccount002',
                                           RecordTypeId = rectContract[0].Id,
                                           Contract_Decide_Start_Date__c = Date.today().addDays(-1),
                                           Contract_Decide_End_Date__c =Date.today().addDays(1),
                                           Agent_Ref__c =myAccount1.Id,
                                           ParentId =myAccount1.Id,
                                           Term_Contract_No__c = '151P-HD519',
                                           City_Master__c  = al2.Id);
        insert myAccount2;
        RecordType rectOpp = [select id from RecordType where IsActive = true and SobjectType = 'Opportunity' and DeveloperName = 'Opportunity' ];
        Opportunity opp = new Opportunity(
               Name='testOpp1',
               StageName='引合',
               CloseDate=Date.today(),
               AccountId=myAccount1.Id,
               Sales_Root__c = '販売店',
               Competitor__c ='A',
               Click_Close_Date__c = null,
               RecordType = rectOpp,
                SpecialAgency_Shipments__c = myAccount2.Id,
                SpecialAgency_Shipments_Text__c = myAccount2.Name
        );
        insert opp;


        Account myAccount3 = new Account(name='Testaccount002',
                                           RecordTypeId = rectContract[0].Id,
                                           Contract_Decide_Start_Date__c = Date.today().addDays(-1),
                                           Contract_Decide_End_Date__c =Date.today().addDays(1),
                                           Agent_Ref__c =myAccount1.Id,
                                           ParentId =myAccount1.Id,
                                           Term_Contract_No__c = '151P-HD519更2025-02-12',
                                           City_Master__c  = al2.Id);
        insert myAccount3;

        List<Account> accList = new List<Account>();
        accList.add(myAccount3);
        System.enqueueJob(new UpdateTheAgreementBatchJob(accList));
    }
}