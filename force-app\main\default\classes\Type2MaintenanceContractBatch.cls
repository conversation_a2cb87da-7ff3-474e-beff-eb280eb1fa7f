// create tcm 20211203 更改维修合同——用户类型和合同种类
public class Type2MaintenanceContractBatch implements Database.Batchable<sObject> {

	public List<String> IdList;

	public Type2MaintenanceContractBatch() {
	}

	public Type2MaintenanceContractBatch(List<String> IdList) {
		this.IdList=IdList;
	}

	public Database.QueryLocator start(Database.BatchableContext BC) {
		//查维修合同
		string sql = 'select id from Maintenance_Contract__c where Hospital__r.RecordTypeId=\''+System.label.Hospital_RecordType+'\'';
		if (IdList != null && IdList.size() > 0) {
			sql += ' and Hospital__c in :IdList';
		}
		return Database.getQueryLocator(sql);
	}

	public void execute(Database.BatchableContext BC, List<Maintenance_Contract__c> mcList) {
		List<String> mcIdList=new List<String>();
		for (Maintenance_Contract__c mc : mcList) {
			mcIdList.add(mc.Id);
		}

		List<Maintenance_Contract_Asset__c> mcaList=[select id,Asset__c,Maintenance_Contract__c,New_Contract_TypeF__c from Maintenance_Contract_Asset__c where Maintenance_Contract__c in : mcIdList and Maintenance_Contract__r.RecordTypeId!=:System.label.maintenance_contract and Maintenance_Contract__r.Status__c in ('契約','契約満了') and Maintenance_Contract__c!=:System.Label.maintenance_contract_1 order by Maintenance_Contract__r.Contract_Start_Date__c];

		List<String> mcIdStrList=new List<String>();    //合同IdList 存在跳出for
		List<Maintenance_Contract__c> mcupdateList=new List<Maintenance_Contract__c>();
		for (Maintenance_Contract_Asset__c mcai : mcaList) {
			if (mcIdStrList.contains(mcai.Maintenance_Contract__c)) {
				continue;
			}
			mcIdStrList.add(mcai.Maintenance_Contract__c);
			List<string> strList=new List<string> ();
			for (Maintenance_Contract_Asset__c mcaj : mcaList) {
				if (mcai.Maintenance_Contract__c==mcaj.Maintenance_Contract__c) {
					strList.add(mcaj.New_Contract_TypeF__c);
				}
			}

			Maintenance_Contract__c mc=new Maintenance_Contract__c();
			mc.Id=mcai.Maintenance_Contract__c;
			if (strList.contains('续签合同')) {
				mc.New_Contract_TypeF_Text__c='续签合同';

			}else if(!strList.contains('非续签合同')&&strList.contains('首签合同')) {
				mc.New_Contract_TypeF_Text__c='首签合同';

			}else if(!strList.contains('非续签合同')&&!strList.contains('首签合同')&&strList.contains('新品合同')) {
				mc.New_Contract_TypeF_Text__c='新品合同';
			}else if(strList.contains('非续签合同')&&(strList.contains('新品合同')||strList.contains('首签合同'))) {
				mc.New_Contract_TypeF_Text__c='续签合同';
			}else {
				mc.New_Contract_TypeF_Text__c='非续签合同（空白期1年以上）';

			}




			mcupdateList.add(mc);
		}
		update mcupdateList;
	}
	public void finish(Database.BatchableContext BC) {

	}
}

// List<String> IdList=new List<String>{'0011000000V9QTF'};
// Database.executeBatch(new Type2MaintenanceContractBatch(IdList));

// select id, Asset__c, New_Contract_TypeF__c, Asset__r.CurrentContract_F__c, Maintenance_Contract__r.name, Maintenance_Contract__r.New_Contract_TypeF_Text__c, Maintenance_Contract__r.Contract_Start_Date__c, Maintenance_Contract__r.Contract_end_Date__c, asset__r.InstallDate from Maintenance_Contract_Asset__c where Maintenance_Contract__r.Hospital__c= '0011000000V9QTF' and Maintenance_Contract__r.RecordTypeId!='01210000000gTYv' and Maintenance_Contract__r.Status__c in ('契約', '契約満了') order by Maintenance_Contract__c, Maintenance_Contract__r.Contract_Start_Date__c