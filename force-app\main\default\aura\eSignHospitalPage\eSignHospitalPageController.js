({
    myAction : function(component, event, helper) {
    
    },
    // 初始化函数
    doInit : function(cmp, event, helper) {
        // 设置明细页表格的构造
        helper.getColumnAndAction(cmp);
        // 从数据库里面读取数据
        helper.geteSign(cmp);
    },
    // 明细页表格的翻页下一页 
    handleNext : function(cmp, event, helper) { 
        helper.handleNext(cmp);
    },
     // 明细页表格的翻页上一页 
    handlePrev : function(cmp, event, helper) {
        helper.handlePrev(cmp);
    },
    // 明细页表格的翻页首页
    handleHome : function(cmp, event, helper) {
        helper.handleHome(cmp);
    },
     // 明细页表格的翻页尾页
    handleLast : function(cmp, event, helper) {
        helper.handleLast(cmp);
    },
    // 表格确认更改
    handleSaveEdition: function (cmp, event, helper) {
        //获取更改内容
        var draftValues = event.getParam('draftValues');
        helper.saveEdition(cmp, draftValues);
    },
    // 文件上传结果后的处理
    // 文件上传结果后的处理
    handleUploadFinished: function (cmp, event) {
        // 获取上传成功文件的信息
        var uploadedFiles = event.getParam("files");
        var count;
        var fileName;
        var nameList =[];
        if(uploadedFiles.length > 0){
            // 之所以在这里写这个弹出框，因为写在helper里面不知道为啥弹不出来；
            cmp.set("v.IsHaveFile", true);
            
            // alert("请您点击提交按钮上传数据！");
            //获取文件上传数量
            var size = cmp.get("v.fileSize");
            if(size){
               cmp.set('v.fileSize',size+uploadedFiles.length);
            }else{
                cmp.set('v.fileSize',uploadedFiles.length);
            }
            count =  cmp.get("v.fileSize");
            
            // console.log(file.name)
            // 打印上传成功文件的名字
            uploadedFiles.forEach(file => fileName=file.name );
            var name = cmp.get("v.flName");
            if(name){
                cmp.set('v.flName',name+','+fileName);
                // nameList.pust(name);
            }else{
                cmp.set('v.flName',fileName);
            }
            fileName = cmp.get("v.flName");
            
        }
        if(count>0){
            // alert("您已经成功上传"+count+"个文件,提交申请请点击提交按钮。");
                cmp.find('notifLibPlanA').showToast({
                "title": "Success!",
                "message": "您已经成功上传"+count+"个文件,提交申请请点击提交按钮。"
            });

            //列出文件名01 所有文件名都用逗号隔开 根据逗号拆分，存放数组
            var nameList =fileName.split(',');
            //存放到变量中 用于上一页下一页连点
            cmp.set('v.nameList',nameList);
            if (nameList.length > 0) {
                var strList = '';
                for (var i = 0; i < nameList.length; i++) {
                    var num = Number(i) + 1;
                    var id = 'file0' + num;             
                    //标识文件是否已经上传  精琢技术 thh 2021-09-26 start
                    if(isUploadName[nameList[i]]) {
                        var str = '<button id="' + id + '" class="slds-button" title="Field 3" style="text-align:left;width:250px;font-weight:bold;font-size: 15px;color:Green;" name="' + nameList[i] + '">' + nameList[i] + '</button><br>';
                    } else{
                        var str = '<button id="' + id + '" class="slds-button" title="Field 3" style="text-align:left;width:250px;font-weight:bold;font-size: 15px;color:Gray;" name="' + nameList[i] + '">' + nameList[i] + '</button><br>';
                    }            
                    //标识文件是否已经上传  精琢技术 thh 2021-09-26 end
                    //拼接p标签 列出文件名id不同
                    strList += str;
                }
                var obj = document.getElementById('file00');
                obj.innerHTML = strList;
            }
            //列出文件名 02  显示弹窗
            //     cmp.find('notifLibPlanB').showNotice({
            //     "variant":"成功",
            //     "header":"已上传文件",
            //     "message":fileName
            //     // ,
            //     // closeCallback:function(){
            //     //     alert('您关闭了警报！');
            //     // }
            // });

        }

    },
    hospitalDetailsPageNextClick : function (cmp, event, helper) {
        helper.hospitalDetailsPageNextClick(cmp);
    },
    handleChange :  function (cmp, event, helper) {
        console.log('点击复选框!');
        var s = cmp.get("v.check");
        console.log('q:'+s);
    },
    // 明细页跳转至文件上传页
    handleDetailsPageNextClick : function (cmp, event, helper) {
        var draftValues = event.getParam('draftValues');
        helper.IsWhole(cmp);
        helper.handleDetailsPageNextClick(cmp);
    },
    handleShowPageNextClick :function (cmp, event, helper) {
        helper.handleShowPageNextClick(cmp);
    },
    handleShowFielePageNextClick:function (cmp, event, helper) {
        helper.handleShowFielePageNextClick(cmp);
    },
    submitClick: function(cmp, event, helper){
        helper.submitClick(cmp);
    },
    //主页跳首页
    BackToHomePage : function(cmp, event, helper){
        // if(confirm('确认回到首页吗？')){
        //         var messageEvent = cmp.getEvent('componentEvent');
        //         messageEvent.setParam('DNName',cmp.get('v.DNName'));
                
        //         messageEvent.fire();
        //         cmp.set("v.HomePage", false);
        //     }
        

        helper.BackToHomePage(cmp);
    },
    handleShowFielePageNextClick : function(cmp, event, helper){
        helper.handleShowFielePageNextClick(cmp);
    },
    handleShowPageNextClick : function(cmp, event, helper){
        helper.handleShowPageNextClick(cmp);
    },
    GoodsChanged:function(cmp, event, helper){
        helper.GoodsChanged(cmp);
    },
    BatchUpdateByCase : function(cmp, event, helper){
        
        helper.BatchUpdateByCase(cmp, event);

    },
    allBatchUpdateByCase : function(cmp, event, helper){
        
        helper.allBatchUpdateByCase(cmp, event);

    },
    searchByCaseNumber : function(cmp, event, helper){
        helper.searchByCaseNumber(cmp,event);
    },
    handleSectionToggle: function (cmp, event) {
        var openSections = event.getParam('openSections');
        var nameList = cmp.get('v.nameList');
        if (nameList.length > 0) {           
            var strList = '';
            for (var i = 0; i < nameList.length; i++) {
                var num = Number(i) + 1;
                var id = 'file0' + num;              
                //标识文件是否已经上传  精琢技术 thh 2021-09-26 start
                if(isUploadName[nameList[i]]) {
                    var str = '<button id="' + id + '" class="slds-button" title="Field 3" style="text-align:left;width:250px;font-weight:bold;font-size: 15px;color:Green;" name="' + nameList[i] + '">' + nameList[i] + '</button><br>';
                } else{
                    var str = '<button id="' + id + '" class="slds-button" title="Field 3" style="text-align:left;width:250px;font-weight:bold;font-size: 15px;color:Gray;" name="' + nameList[i] + '">' + nameList[i] + '</button><br>';
                }            
                //标识文件是否已经上传  精琢技术 thh 2021-09-26 end
                //拼接p标签 列出文件名id不同
                strList += str;
            }
            var obj = document.getElementById('file00');
            obj.innerHTML = strList;
        }
        if (openSections.length === 0) {
            cmp.set('v.activeSectionsMessage', "All sections are closed");
        } else {
            cmp.set('v.activeSectionsMessage', "Open sections: " + openSections.join(', '));
        }
    },
    doSave: function(cmp, event, helper) {
        var fileName;
        var count;
        var fileName1;
        //先判断 已上传的文件是否有值
        var nameUpLoadList = cmp.get('v.nameUpLoadList');
        if (cmp.find("fileId").get("v.files").length > 0) {
            //以下是用于存放已经上传的文件名 用作对比(相同文件时不能上传的，故不用内容判断)
            fileName = cmp.find("fileId").get("v.files")[0]['name'];
            var nameList = cmp.get("v.nameList");
            //文件列表为空时，不允许上传 精琢技术 thh 2021-09-13 
            if(nameList.indexOf(fileName) >= 0){
                //定义一个标识 用来判断点击过上传文件按钮
                cmp.set('v.uploadFlag', true);
                //增加标记哪个文件已上传  精琢技术 thh 2021-09-09 start
                //把文件名和该文件是否已经上传关联起来
                var isUploadName = cmp.get("v.isUploadName");
                isUploadName[fileName] = true;
                cmp.set('v.isUploadName', isUploadName);
                //增加标记哪个文件已上传  精琢技术 thh 2021-09-09 end
                //获取文件上传数量
                var size = cmp.get("v.fileUpLoadSize");
                if (size) {
                    cmp.set('v.fileUpLoadSize', size + cmp.find("fileId").get("v.files").length);
                } else {
                    cmp.set('v.fileUpLoadSize', cmp.find("fileId").get("v.files").length);
                }
                fileName1 = fileName;
                var name = cmp.get("v.flUpLoadName");
                if (name && name != '') {
                    cmp.set('v.flUpLoadName', name + ',' + fileName1);
                } else {
                    cmp.set('v.flUpLoadName', fileName1);
                }
                fileName = cmp.get("v.flUpLoadName");
                console.log("fileName:" + fileName);
                helper.uploadHelper(cmp, event);
                
            }else {
                alert('没有附件需要上传！');
            }
        } else {
            alert('请您重新选择上传图片！');

            if (nameUpLoadList.length < 0 || nameUpLoadList.length == 0) {
                //先判空 有可能一打开页面就点击上传按钮
                //说明第一次点击选择图片，没有上传又点击了第二次
                if (document.getElementById('file01')) {
                    document.getElementById('file01').remove();
                }

            } else {
                cmp.set('v.nameList', nameUpLoadList);
                //重新定义选中文件的数量
                cmp.set('v.fileSize', nameList.length);
                if (nameUpLoadList.length > 0) {
                    var strList = '';
                    for (var i = 0; i < nameList.length; i++) {
                        var num = Number(i) + 1;
                        var id = 'file0' + num;              
                       //标识文件是否已经上传  精琢技术 thh 2021-09-26 start
                       if(isUploadName[nameList[i]]) {
                        var str = '<button id="' + id + '" class="slds-button" title="Field 3" style="text-align:left;width:250px;font-weight:bold;font-size: 15px;color:Green;" name="' + nameList[i] + '">' + nameList[i] + '</button><br>';
                    } else{
                        var str = '<button id="' + id + '" class="slds-button" title="Field 3" style="text-align:left;width:250px;font-weight:bold;font-size: 15px;color:Gray;" name="' + nameList[i] + '">' + nameList[i] + '</button><br>';
                    }            
                    //标识文件是否已经上传  精琢技术 thh 2021-09-26 end
                        //拼接p标签 列出文件名id不同
                        strList += str;
                    }
                    var obj = document.getElementById('file00');
                    obj.innerHTML = strList;
                }
            }
        }
    },
    //上传图片后 预览图片
    imgName: function(cmp, event) {
        //级联操作 点击子标签  进入父标签点击事件
        //获取点击的子标签
        var name = event.target.lastChild.data;
        console.log(name);
        var imgSrcList = cmp.get("v.imgList");
        var imgSrc = imgSrcList[name];
        document.getElementById('image').src = imgSrc;
    },
    //获取图片src
    getImgSrc: function(imgSrcList, name) {
        return imgSrcList[name];
    },
    //制作数组 key：name vlaue：src
    setImgSrc: function(str1, prop, val) {
        // 如果 val 被忽略
        if (typeof val === "undefined") {
            // 删除属性
            delete str1[prop];
        } else {
            // 添加 或 修改
            str1[prop] = val;
        }
        return str1;
    },
    handleFilesChange: function(cmp, event, helper) {
        //图片预览
        var imgList = {};
        var fileName = 'No File Selected..';
        var count;
        var fileName1;
        var nameList = [];
        var uploadedFiles = event.getParam("files");    
        var isUploadName = cmp.get("v.isUploadName"); 
        var reader = new FileReader();
        reader.readAsDataURL(uploadedFiles[0]);
        //文件为pdf时,隐藏预览图片区域,显示文字“PDF文件暂不支持预览”  精琢技术 thh 2021-09-13 start
        var name = event.getSource().get("v.files")[0]['name'];
        var indexPdf = name.lastIndexOf('.');
        var pdf = name.substring(indexPdf, name.length); 
        if(pdf == '.pdf') {
            document.getElementById('loaded').style.display = 'none';
            document.getElementById('fontpdf').style.display = 'block';
        } else {
            document.getElementById('loaded').style.display = 'block';
            document.getElementById('fontpdf').style.display = 'none';
        }
        //文件为pdf时,隐藏预览图片区域,显示文字“PDF文件暂不支持预览”  精琢技术 thh 2021-09-13 end
        reader.onload = function(e) {
            var imgBase = e.target.result;
            var nameList = cmp.get('v.nameUpLoadList');
            //图片预览
            cmp.set("v.imageSrc", imgBase);
            //清除div文字
            document.getElementById("font").textContent = "";
            //预览图片
            document.getElementById('image').style.display = 'block';
            // 如果 val 被忽略
            if (typeof imgBase === "undefined") {
                // 删除属性
                delete imgList[uploadedFiles[0].name];
            } else {
                // 添加 或 修改
                imgList[uploadedFiles[0].name] = imgBase;
            }
            //增加点击文件列表切换查看图片附件  精琢技术 thh 2021-08-23 start
            //把预览图片的src存入map，把图片名字和src连接起来
            var fontsrc = cmp.get("v.FontSrc");
            fontsrc[uploadedFiles[0].name] = imgBase;
            cmp.set('v.FontSrc', fontsrc);
            //增加点击文件列表切换查看图片附件  精琢技术 thh 2021-08-23 end
            
        }
        var flag = cmp.get('v.uploadFlag');
        console.log('uploadedFiles:' + uploadedFiles);
        console.log("文件长度：" + uploadedFiles.length);
        //先判断 已上传的文件是否有值
        var nameUpLoadList = cmp.get('v.nameUpLoadList');
        if (event.getSource().get("v.files").length > 0) {
            fileName = event.getSource().get("v.files")[0]['name'];
            //标识预览图片名字  精琢技术 thh 2021-09-09 start
            if(fileName.length != 1 && isUploadName[fileName]) {
                var str = '<span>当前文件：</span><span id="nowfile" class="field-title" style="color:Green;" title="' + fileName + '">' + fileName + '</span>';
                document.getElementById("uploadicon").style.display = 'inline-block';
            } else{
                var str = '<span>当前文件：</span><span id="nowfile" class="field-title" style="color:Gray;" title="' + fileName + '">' + fileName + '</span>';
                document.getElementById("uploadicon").style.display = 'none';
            }
            var obj = document.getElementById('filenow');
            obj.innerHTML = str;
            //标识预览图片名字  精琢技术 thh 2021-09-09 end
            cmp.set("v.imageSrc", fileName);
            // 之所以在这里写这个弹出框，因为写在helper里面不知道为啥弹不出来；
            cmp.set("v.IsHaveFile", true);
            if (nameUpLoadList.length < 0 || nameUpLoadList.length == 0) {
                fileName1 = fileName;
                cmp.set('v.flName', fileName1);
                fileName = cmp.get("v.flName");
                //写死是因为上传文件一次只能上传一个
                count = 1;

                cmp.set("v.fileSize", count);
            } else {
                var size = cmp.get("v.fileSize");
                if (size) {
                    cmp.set('v.fileSize', size + uploadedFiles.length);
                } else {
                    cmp.set('v.fileSize', uploadedFiles.length);
                }
                count = cmp.get("v.fileSize");
                // 打印选中文件的名字
                fileName1 = fileName;
                var name = cmp.get("v.flName");
                if (name) {
                    cmp.set('v.flName', name + ',' + fileName1);
                    // nameList.pust(name);
                } else {
                    cmp.set('v.flName', fileName1);
                }
                fileName = cmp.get("v.flName");
            }

            if (count > 0) {
                //列出文件名01 所有文件名都用逗号隔开 根据逗号拆分，存放数组
                var nameList = fileName.split(',');
                for (var i = 0; i < nameList.length; i++) {
                    for (var j = i + 1; j < nameList.length; j++) {
                        if (nameList[i] == nameList[j]) { //第一个等同于第二个，splice方法删除第二个
                            nameList.splice(j, 1);
                            j--;
                        }
                    }
                }
                count = nameList.length;
                // alert("您已经成功上传"+count+"个文件,提交申请请点击提交按钮。");
                cmp.find('notifLibPlanA').showToast({
                    "title": "Success!",
                    "message": "您已经选中" + count + "个文件,请点击上传文件按钮进行上传。"
                });

                //列出文件名01 所有文件名都用逗号隔开 根据逗号拆分，存放数组
                // var nameList =fileName.split(',');
                //存放到变量中 用于上一页下一页连点
                cmp.set('v.nameList', nameList);
                if (nameList.length > 0) {
                    var strList = '';
                    for (var i = 0; i < nameList.length; i++) {
                        var num = Number(i) + 1;
                        var id = 'file0' + num; 
                        //标识文件是否已经上传  精琢技术 thh 2021-09-26 start
                        if(isUploadName[nameList[i]]) {
                            var str = '<button id="' + id + '" class="slds-button" title="Field 3" style="text-align:left;width:250px;font-weight:bold;font-size: 15px;color:Green;" name="' + nameList[i] + '">' + nameList[i] + '</button><br>';
                        } else{
                            var str = '<button id="' + id + '" class="slds-button" title="Field 3" style="text-align:left;width:250px;font-weight:bold;font-size: 15px;color:Gray;" name="' + nameList[i] + '">' + nameList[i] + '</button><br>';
                        }            
                        //标识文件是否已经上传  精琢技术 thh 2021-09-26 end
                        //拼接p标签 列出文件名id不同
                        strList += str;
                    }
                    var obj = document.getElementById('file00');
                    obj.innerHTML = strList;
                }
            }
        } else {
            if (nameUpLoadList.length < 0 || nameUpLoadList.length == 0) {
                //说明第一次点击选择图片，没有上传又点击了第二次
                cmp.set('v.nameList', null);

            } else {
                cmp.set('v.nameList', nameUpLoadList);
                if (nameUpLoadList.length > 0) {
                    var strList = '';
                    for (var i = 0; i < nameList.length; i++) {
                        var num = Number(i) + 1;
                        var id = 'file0' + num;  
                        //标识文件是否已经上传  精琢技术 thh 2021-09-26 start
                        if(isUploadName[nameList[i]]) {
                            var str = '<button id="' + id + '" class="slds-button" title="Field 3" style="text-align:left;width:250px;font-weight:bold;font-size: 15px;color:Green;" name="' + nameList[i] + '">' + nameList[i] + '</button><br>';
                        } else{
                            var str = '<button id="' + id + '" class="slds-button" title="Field 3" style="text-align:left;width:250px;font-weight:bold;font-size: 15px;color:Gray;" name="' + nameList[i] + '">' + nameList[i] + '</button><br>';
                        }            
                        //标识文件是否已经上传  精琢技术 thh 2021-09-26 end
                        //拼接p标签 列出文件名id不同
                        strList += str;
                    }
                    var obj = document.getElementById('file00');
                    obj.innerHTML = strList;
                }

            }

        }
        cmp.set("v.fileName", fileName);
    },
    showHPQR: function(cmp, event, helper) {
        var check = cmp.get('v.chkQR');
        if (check) {
            cmp.set('v.showQR', true);
        } else {
            cmp.set('v.showQR', false);
        }

    },
    hideHPQR: function(cmp, event, helper) {
        cmp.set('v.showQR', false);
        cmp.set('v.chkQR', false);
    },

    //wangweipeng 20210616    预览图片放大功能
    imgeBig: function(cmp, event, helper) {
        var imgeObj = document.getElementById("image");
        //图片预览
        cmp.set("v.imageBigSrc", imgeObj.src);

        //预览图片
        document.getElementById('image').style.display = 'none';
        document.getElementById('outerdiv').style.display = 'block';
    },
    //wangweipeng 20210616    预览图片关闭按钮
    imgeSmall: function() {
        document.getElementById('image').style.display = 'block';
        document.getElementById('outerdiv').style.display = 'none';
    },
    //增加点击文件列表切换查看图片附件  精琢技术 thh 2021-08-23 start
    //点击文件列表里的名字实现图片的切换
    buttonClicked: function(cmp, event) {
        var name = event.target.name;
        var srcmap = cmp.get("v.FontSrc");
        var value = srcmap[name];
        //文件为pdf时,隐藏预览图片区域,显示文字“PDF文件暂不支持预览”  精琢技术 thh 2021-09-13 start
        var indexPdf = name.lastIndexOf('.');
        var pdf = name.substring(indexPdf, name.length); 
        if(pdf == '.pdf') {    
            document.getElementById('loaded').style.display = 'none';
            document.getElementById('fontpdf').style.display = 'block';
        } else {
            document.getElementById('loaded').style.display = 'block';
            document.getElementById('image').src = value;
            document.getElementById('image').style.display = 'block';
            document.getElementById('fontpdf').style.display = 'none';
        }
        //文件为pdf时,隐藏预览图片区域  精琢技术 thh 2021-09-13 end
        //标识预览图片名字  精琢技术 thh 2021-09-10 start
        var isUploadName = cmp.get("v.isUploadName");
        if(isUploadName[name]) {
            var str = '<span>当前文件：</span><span id="nowfile" class="field-title" style="color:Green;" title="' + name + '">' + name + '</span>';
            document.getElementById("uploadicon").style.display = 'inline-block';
        } else{
            var str = '<span>当前文件：</span><span id="nowfile" class="field-title" style="color:Gray;" title="' + name + '">' + name + '</span>';
            document.getElementById("uploadicon").style.display = 'none';
        }
        var obj = document.getElementById('filenow');
        obj.innerHTML = str;
        //标识预览图片名字  精琢技术 thh 2021-09-10 end
    },
    //增加点击文件列表切换查看图片附件  精琢技术 thh 2021-08-23 end
    //增加文件列表删除功能  精琢技术 thh 2021-08-25 start
    deleteClick: function(cmp, event, helper) {
        var srcmap = cmp.get("v.FontSrc");
        var nameList = cmp.get("v.nameList");
        var nameUpLoadList = cmp.get('v.nameUpLoadList');
        var flUpLoadName = cmp.get("v.flUpLoadName");
        var flName = cmp.get("v.flName");
        var filesrc = cmp.get("v.FileSrc");
        var base64List = cmp.get("v.base64List");
        var baseArray = cmp.get("v.baseArray");
        var isUploadName = cmp.get("v.isUploadName");
        //根据当前文件获取当前文件名
        if(nameList != ''){
            var name = document.getElementById("nowfile").title;
        } else{
            alert("没有附件，无法删除！");
        }
        //把要删的src分段存入baseFile，然后通过遍历删除base64List中的数据
        var baseFile = cmp.get("v.baseFile");
        var startPosition = 0;
        var fileContents = filesrc[name];
        //如果fileContents有值，说明附件已经上传
        if(fileContents){
            var fileLength = Math.ceil(fileContents.length / helper.CHUNK_SIZE);
            for(var i = 0; i < fileLength; i++){
                baseFile = cmp.get("v.baseFile");
                var endPosition = Math.min(
                    fileContents.length,
                    startPosition + helper.CHUNK_SIZE
                );
                var getchunk = fileContents.substring(startPosition, endPosition);
                fileContents = fileContents.substring(endPosition);    
                if(baseFile){
                    cmp.set('v.baseFile',baseFile+','+getchunk);
                }else{
                    cmp.set('v.baseFile',getchunk);
                }
                baseFile = cmp.get("v.baseFile");
            }
            //增加标记哪个文件已上传  精琢技术 thh 2021-09-09 start
            //删除siUploadName中name对应的判断
            delete isUploadName[name];
            //增加标记哪个文件已上传  精琢技术 thh 2021-09-09 end
            //删除flUpLoadName中的name
            var Arrflag = flUpLoadName instanceof Array;
            if(!Arrflag){
                flUpLoadName = flUpLoadName.split(',');
            }   
            for(var i = 0; i < flUpLoadName.length; i++) {
                if(flUpLoadName [i] == name) {
                    flUpLoadName.splice(i, 1);
                    break;
                }
            }
            cmp.set("v.flUpLoadName", flUpLoadName);
            //删除一个数据之后fileUpLoadSize中的数量要-1
            var fileUpLoadSize = cmp.get("v.fileUpLoadSize");
            var deletefileUpLoadSize = fileUpLoadSize - 1;
            cmp.set("v.fileUpLoadSize", deletefileUpLoadSize);
        }   
        //删除srcname中name对应的src
        delete srcmap[name]; 
        //删除filesrc中name对应的src
        delete filesrc[name];
        //删除nameList中的name 
        for(var i = 0; i < nameList.length; i++) {
            if(nameList [i] == name) {
                nameList.splice(i, 1);
                break;
            }
        }
        cmp.set("v.nameList", nameList);
        //删除nameUpLoadList中的name
        for(var i = 0; i < nameUpLoadList.length; i++) {
            if(nameUpLoadList [i] == name) {
                nameUpLoadList.splice(i, 1);
                break;
            }
        }
        cmp.set("v.nameUpLoadList", nameUpLoadList);
        //删除flName中的name
        var Arryflag = flName instanceof Array;
        if(!Arryflag){
            flName = flName.split(',');
        }
        for(var i = 0; i < flName.length; i++) {
            if(flName [i] == name) {
                flName.splice(i, 1);
                break;
            }
        }
        cmp.set("v.flName", flName);
        //通过遍历删除base64List中的数据
        baseFile = cmp.get("v.baseFile");
        baseArray = baseFile.split(',');
        for(var j = 0; j < baseArray.length; j++){
            for(var i = 0; i < base64List.length; i++) {
                var baseArray100;
                if(baseArray[j].length > 100){
                    baseArray100 = baseArray[j].substring(0, 100);
                }else{
                    baseArray100 = baseArray[j].substring(0);
                }
                if(base64List[i].indexOf(baseArray100) >= 0) {
                    base64List.splice(i, 1);
                    break;
                }
            }    
        }
        cmp.set("v.base64List", base64List);
        //拼接删除完之后的文件列表,以及图片的显示
        var strList = '';
        if(nameList.length == 0) {
            //文件删完之后隐藏图片预览区域以及绿色小√,预览区域显示文字“PDF文件暂不支持预览”   精琢技术 thh 2021-09-15 start
            document.getElementById('loaded').style.display = 'none';
            document.getElementById("uploadicon").style.display = 'none';
            //文件删完之后隐藏图片预览区域以及绿色小√,预览区域显示文字“PDF文件暂不支持预览”  精琢技术 thh 2021-09-15 end
            //文件删完之后,预览区域的pdf文字也不显示 
            document.getElementById('fontpdf').style.display = 'none';
            //文件删完之后,预览区域的pdf文字也不显示 
            var str = '';
            var obj = document.getElementById('file00');
            obj.innerHTML = str;
            //标识预览图片名字  精琢技术 thh 2021-09-10 start
            var obj1 = document.getElementById('filenow');
            obj1.innerHTML = str;
            //标识预览图片名字  精琢技术 thh 2021-09-10 end
        }else {
            var value = srcmap[nameList[0]];
            //文件为pdf时展示空白图片,显示文字“PDF文件暂不支持预览”  精琢技术 thh 2021-09-15 start
            var indexPdf = nameList[0].lastIndexOf('.');
            var pdf = nameList[0].substring(indexPdf, nameList[0].length); 
            if(pdf == '.pdf') {    
                document.getElementById('loaded').style.display = 'none';
                document.getElementById('fontpdf').style.display = 'block';
            } else {
                document.getElementById('loaded').style.display = 'block';
                document.getElementById('image').src = value;
                document.getElementById('image').style.display = 'block';
                document.getElementById('fontpdf').style.display = 'none';
            }  
            //文件为pdf时展示空白图片,显示文字“PDF文件暂不支持预览”  精琢技术 thh 2021-09-15 end
            for (var i = 0; i < nameList.length; i++) {
                var num = Number(i) + 1;
                var id = 'file0' + num;    
                //标识文件是否已经上传  精琢技术 thh 2021-09-26
                if(isUploadName[nameList[0]]) {
                    var str = '<button id="' + id + '" class="slds-button" title="Field 3" style="text-align:left;width:250px;font-weight:bold;font-size: 15px;color:Green;" name="' + nameList[i] + '">' + nameList[i] + '</button><br>';
                } else{
                    var str = '<button id="' + id + '" class="slds-button" title="Field 3" style="text-align:left;width:250px;font-weight:bold;font-size: 15px;color:Gray;" name="' + nameList[i] + '">' + nameList[i] + '</button><br>';
                }     
                //拼接p标签 列出文件名id不同
                strList += str;
            }
            var obj = document.getElementById('file00');
            obj.innerHTML = strList;
            //标识文件是否已经上传  精琢技术 thh 2021-09-26 start
            if(isUploadName[nameList[0]]) {
                var str = '<span>当前文件：</span><span id="nowfile" class="field-title" style="color:Green;" title="' + nameList[0] + '">' + nameList[0] + '</span>';
                document.getElementById("uploadicon").style.display = 'inline-block';
            } else{
                var str = '<span>当前文件：</span><span id="nowfile" class="field-title" style="color:Gray;" title="' + nameList[0] + '">' + nameList[0] + '</span>';
                document.getElementById("uploadicon").style.display = 'none';
            }
            var obj = document.getElementById('filenow');
            obj.innerHTML = str;
            //标识文件是否已经上传  精琢技术 thh 2021-09-26 end
        }
        //删除完成后baseFile需要置空
        cmp.set("v.baseFile", '');
        var baseFile = cmp.get("v.baseFile");
        //删除一个数据之后fileSize中的数量要-1
        var count = cmp.get("v.fileSize");
        var deletecount = count - 1;
        cmp.set("v.fileSize", deletecount);
        //关联后端代码删除签收单录入表中的附件
        var action = cmp.get("c.deleteChunk");
        var attachmentID = cmp.get("v.attachmentID");
        var AttachmentID = attachmentID[name];
        action.setParams({
            AttachmentId : AttachmentID
        });
        //通过页面断点验证action方法是否成功
        action.setCallback(this, function(response) {
            var state = response.getState();
        });
        //执行action方法
        $A.enqueueAction(action);    
    },
    //增加文件列表删除功能  精琢技术 thh 2021-09-02 end
})