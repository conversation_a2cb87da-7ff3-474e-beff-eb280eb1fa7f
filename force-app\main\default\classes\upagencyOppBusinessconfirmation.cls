global without sharing class upagencyOppBusinessconfirmation {
    WebService static String Businessconfirmation(String agoppId) {
        if (!UserInfo.getProfileId().equals(System.label.ProfileId_2S1) && 
            !UserInfo.getProfileId().equals(System.Label.ProfileId_SystemAdmin)) {
            return '您不是营业担当，无法确认。';
        }
        Agency_Opportunity__c ao = [select id,Business_confirmation__c 
        from Agency_Opportunity__c where id =:agoppId ];
        if (ao.Business_confirmation__c == true) {
            return '询价已经确认，不需要重复确认。';
        }else{
            ao.Business_confirmation__c = true;
            Savepoint sp = Database.setSavepoint();
            try{
                update ao;
                return '';
            }catch (Exception e) {
                Database.rollback(sp);
                throw e;
            }
        }
    }
}