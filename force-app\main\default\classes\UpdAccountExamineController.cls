public class UpdAccountExamineController {

    public String processId;
    public String AccId;
    public ProcessInstance objProcessInstance;
    public Account_Delay_Apply__c objAcc {get; set;}
    public string Comments {get;set;}
    public string ApprovalAction {get;set;}
    public PageReference redirectPage;
    public String testlink {get; set;}

    // 登陆用户
    public User loginUser { get; set; }
    
    //初始化
    public UpdAccountExamineController()
    {   
        loginUser = [Select Id, Salesdepartment__c, Province__c, ProfileId, Job_Category__c, Sales_Speciality__c From User where Id = :Userinfo.getUserId()];

        testlink = ApexPages.currentPage().getParameters().get('testlink'); //当前节点名字
        processId = ApexPages.currentPage().getParameters().get('id'); //获取当前的工作流ID
        AccId = ApexPages.currentPage().getParameters().get('AccId'); //获取当前case ID
        system.debug(processId+'-----lt123-----'+AccId);
        objAcc = [select Name, Is_Active__c, InstitutionalType__c, WhetherRiskPassing__c, HospitalType__c,government_department__c from Account_Delay_Apply__c where id =:AccId]; 
        redirectPage = new PageReference('/'+AccId);
     }
    //审批
     public PageReference Approval(){   
      try
        {
        	System.debug('lt123---ApprovalAction:'+ApprovalAction);
            System.debug('lt123---testlink审批节点:'+testlink);
            
            if(ApprovalAction == 'Approve' || ApprovalAction == 'Reject'){
                if(testlink.contains('营业窗口审批')){
                    if(ApprovalAction == 'Reject'){
                        objAcc.Is_Active__c = '草案中';
                     }   
                }else if(testlink.contains('医院变更审批_事业推进部') ){
                    System.debug('lt123---testlink审批节点1进来啦----------------:');
                    //接受
                    if(ApprovalAction == 'Approve'){
                        // if(objAcc.RejectionReason__c != null){
                        //     ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR, '您已填写驳回理由，批准不需要驳回理由。'));
                        //     return null;
                        // }
                        if(objAcc.InstitutionalType__c == null){
                            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR, '批准前，机构类型必填。'));
                            return null;
                        }
                        if(objAcc.InstitutionalType__c == '非医疗机构' && objAcc.HospitalType__c == null){
                            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR, '非医疗机构请选择医院类型。'));
                            return null;
                        }
                        if(objAcc.InstitutionalType__c == '医疗机构' && objAcc.HospitalType__c != null){
                            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR, '医疗机构不需要选择医院类型。'));
                            return null;
                        }
                        if(objAcc.InstitutionalType__c == '医疗机构'){
                            objAcc.Is_Active__c = '审批通过';
                            objAcc.Approved_Confirm_Date__c = Date.today();
                        }

                    }

                    // 拒绝
                    if(ApprovalAction == 'Reject'){
                         if(objAcc.InstitutionalType__c == null){
                             ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR, '拒绝前，机构类型必填。'));
                             return null;
                         }
                        if(objAcc.InstitutionalType__c == '医疗机构'){
                            objAcc.Is_Active__c = '驳回';
                        }else if(objAcc.InstitutionalType__c == '非医疗机构'){
                            objAcc.Is_Active__c = '草案中';
                        }
                    }
                }else if(testlink.contains('质量法规二级部长') ){
                    if(ApprovalAction == 'Approve'){
                       if(objAcc.InstitutionalType__c == '非医疗机构' && objAcc.HospitalType__c == '高等院校'){
                          objAcc.Is_Active__c = '审批通过';
                          objAcc.Approved_Confirm_Date__c = Date.today();
                       }
                    }
                     if(ApprovalAction == 'Reject'){
                        objAcc.Is_Active__c = '驳回';
                      }     

                 }else if(testlink.contains('经销商管理部一级审批')){
                    System.debug('lt123---testlink审批节点66666进来啦----------------:');
                    
                    if(ApprovalAction == 'Approve'){
                        if(objAcc.WhetherRiskPassing__c == null){
                            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR, '是否为有风险通过 必填。'));
                            return null;
                        }
                       if(objAcc.InstitutionalType__c == '非医疗机构' && objAcc.HospitalType__c == '企业集团' && objAcc.WhetherRiskPassing__c == '否'){
                          objAcc.Is_Active__c = '审批通过';
                          objAcc.Approved_Confirm_Date__c = Date.today();
                       }
                    }
                    if(ApprovalAction == 'Reject'){
                        objAcc.Is_Active__c = '驳回';
                    } 

                }else if(testlink.contains('总经理审批')){
                    if(ApprovalAction == 'Approve'){
                        objAcc.Is_Active__c = '审批通过';
                        objAcc.Approved_Confirm_Date__c = Date.today();
                    }
                     if(ApprovalAction == 'Reject'){
                        objAcc.Is_Active__c = '驳回';
                     } 
                }else{
                     if(ApprovalAction == 'Reject'){
                        objAcc.Is_Active__c = '驳回';
                      }   
                }
                
                System.debug('you1----------------:'+objAcc.Is_Active__c);
               
                Approval.ProcessWorkitemRequest approvalNode = new Approval.ProcessWorkitemRequest();
               
                approvalNode.setComments(Comments);
                approvalNode.setAction(ApprovalAction);                
                approvalNode.setWorkitemId(processId);

                system.debug('lt123---processID'+processId);
                system.debug('更新结果1'+objAcc);
                objAcc.CustomizePageFlg__c = true;    
                update objAcc;
                system.debug('更新结果2'+objAcc);
                Approval.ProcessResult result = Approval.process(approvalNode);
                system.debug('lt123---result:'+result.isSuccess());
                
             }
            else
            {
                //system.debug('ApprovalAction:'+this.ApprovalAction);
            }
        }
        catch(Exception ex)
        {
            system.debug('Ex:'+ex.getMessage());
         }
         return redirectPage;
    }
}