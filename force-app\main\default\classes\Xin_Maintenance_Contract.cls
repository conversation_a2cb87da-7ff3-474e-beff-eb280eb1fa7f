public with sharing class Xin_Maintenance_Contract {
    public List<Maintenance_Contract__c> results {get; private set;}
    public Boolean getIsOverLimit() {
        if (results != null && results.size() > 30) {
            return true;
        }
        return false;
    }

    public Xin_Maintenance_Contract() {
        this.results = new List<Maintenance_Contract__c>();
    }

    public void search() {

        // 検索条件を用意
        String query = System.currentPageReference().getParameters().get('q');
        String visitorPlaceId = System.currentPageReference().getParameters().get('r');
        System.debug('visitorPlaceId:' + visitorPlaceId);
        
        if(query == null || query == '' || query.length() < 2) {
            return;
        }
        this.search(query, visitorPlaceId);
    }
    
    public void search(String query, String visitorPlaceId) {
        // Xin_SearchOpportunityと同じにする
        List<String> qwords = new List<String>();
        List<String> qwordstmp = query.split(' ');
        String nameCondition = '';
        for (String qword : qwordstmp) {
            if (String.isBlank(qword) == false) {
                qwords.add('%' + String.escapeSingleQuotes(qword.replaceAll('%', '')) + '%');
                nameCondition += ' Name like \'' + qwords[qwords.size() - 1] + '\' and';
            }
        }
        
        Account a = null;
        try {
            a = [select Hospital__c from Account where Id=:visitorPlaceId];
        }
        catch ( System.QueryException qe) {
            this.results = new List<Maintenance_Contract__c>();
            return;
        }
        
//        String nameCondition = '%' + String.escapeSingleQuotes(query.replaceAll('%', '')) + '%';
        system.debug('cond=' + nameCondition);
        
        // 検索
//        this.results = [select Id, Name from Maintenance_Contract__c where Name like :nameCondition and Department__c = :visitorPlaceId order by Name limit 30];
        String queryString = 'select Id, Name,Management_Code__c,Status__c from Maintenance_Contract__c where' + nameCondition + ' Hospital__c=\'' + a.Hospital__c + '\' AND Status__c in (\'契約\',\'契約満了\')  order by Status__c ASC limit 31';
        this.results = (List<Maintenance_Contract__c>) Database.query(queryString);
    }
}