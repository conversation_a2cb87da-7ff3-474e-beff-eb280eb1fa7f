public without sharing class TenderTansforSubmmitHandler extends Oly_TriggerHandler {
    private Map<Id, TenderTansforSubmmit__c> newMap;
    private Map<Id, TenderTansforSubmmit__c> oldMap;
    private List<TenderTansforSubmmit__c> newList;
    private List<TenderTansforSubmmit__c> oldList;


    public TenderTansforSubmmitHandler() {
        this.newMap = (Map<Id, TenderTansforSubmmit__c>) Trigger.newMap;
        this.oldMap = (Map<Id, TenderTansforSubmmit__c>) Trigger.oldMap;
        this.newList = (List<TenderTansforSubmmit__c>) Trigger.new;
        this.oldList = (List<TenderTansforSubmmit__c>) Trigger.old;
    }


    protected override void afterUpdate() {
        //新的数据写至招标项目
        dataWriteBackToTender();

    }

    public void dataWriteBackToTender() {
        System.debug('dataWriteBackToTender'+ newList);
        List<Tender_information__c> tInfoList = new List<Tender_information__c>();
        // DB202406382590 招标项目：不应标申请里增加涉及科室 start
        List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'TenderTansforSubmmit__c' and Name = '不应标'];
        Set<Id> enderId = new Set<Id>();
        for(TenderTansforSubmmit__c ttc : newList){
            if(ttc.RecordTypeId == rectCo[0].Id){
                enderId.add(ttc.TenderInfo__c);
            }
        }
        List<Id> enderIdList = new List<Id>(enderId);
        List<Tender_information__c> tenderList = [select Id,NotBidApprovalStatus__c,notBidsSelection__c,notBidsSelection1__c,notBidsSelection2__c,notBidsSelection3__c,notBidsSelection4__c from Tender_information__c where Id in :enderId];
        Map<Id,Tender_information__c> tInfoMap = new Map<Id,Tender_information__c>();
        for(Tender_information__c t : tenderList){
            tInfoMap.put(t.Id,t);
        }
        // DB202406382590 招标项目：不应标申请里增加涉及科室 end
        for(TenderTansforSubmmit__c ttc : newList){
            //前后状态不同 且  状态等于批准
            if((ttc.status__c != oldMap.get(ttc.Id).status__c) && ttc.status__c == '批准'){
                String tenderId = ttc.TenderInfo__c;
                System.debug('进入For');
                //将转科室新的数据信息写回招标
                Tender_information__c tInfo = new Tender_information__c();
                tInfo.Id = tenderId;
                // DB202406382590 招标项目：不应标申请里增加涉及科室 start
                if(ttc.RecordTypeId != rectCo[0].Id){
                // DB202406382590 招标项目：不应标申请里增加涉及科室 end
                tInfo.Hospital__c = ttc.TransFom_Hospital__c;
                tInfo.Hospital1__c = ttc.TransFom_Hospital1__c;
                tInfo.Hospital2__c = ttc.TransFom_Hospital2__c;
                tInfo.Hospital3__c = ttc.TransFom_Hospital3__c;
                tInfo.Hospital4__c = ttc.TransFom_Hospital4__c;
                //招标项目的department_selection__c的api是id，tender_selection__c对应的api是正常的值  所以需要映射
                tInfo.department_selection__c = lexTransferRoomCompoentController.getApiIdByValue(ttc.tender_selection__c);

                System.debug('getApiIdByValue' + tInfo.department_selection__c);
                tInfo.department_selection1__c = ttc.tender_selection1__c;
                tInfo.department_selection2__c = ttc.tender_selection2__c;
                tInfo.department_selection3__c = ttc.tender_selection3__c;
                tInfo.department_selection4__c = ttc.tender_selection4__c;
                // DB202406382590 招标项目：不应标申请里增加涉及科室 start
                }else{
                    tInfo.notBidsSelection__c = addSplicingOptions(tInfoMap.get(tenderId).notBidsSelection__c,ttc.notBidsSelection__c);
                    tInfo.notBidsSelection1__c = addSplicingOptions(tInfoMap.get(tenderId).notBidsSelection1__c,ttc.notBidsSelection1__c);
                    tInfo.notBidsSelection2__c = addSplicingOptions(tInfoMap.get(tenderId).notBidsSelection2__c,ttc.notBidsSelection2__c);
                    tInfo.notBidsSelection3__c = addSplicingOptions(tInfoMap.get(tenderId).notBidsSelection3__c,ttc.notBidsSelection3__c);
                    tInfo.notBidsSelection4__c = addSplicingOptions(tInfoMap.get(tenderId).notBidsSelection4__c,ttc.notBidsSelection4__c);
                    
                    if(tInfoMap.get(tenderId).NotBidApprovalStatus__c != '批准'){
                        tInfo.NotBidApprovalStatus__c = ttc.status__c;
                        // tInfo.IsBid__c = ttc.IsBid__c;
                        tInfo.irresponsibleReason__c = ttc.irresponsibleReason__c;
                        tInfo.irresponsibleReasonOther__c = ttc.irresponsibleReasonOther__c;
                        tInfo.proInvolvedManual__c = ttc.proInvolvedManual__c;
                        tInfo.proInvolved__c = ttc.proInvolved__c;
                        tInfo.RepsExtraContent__c = ttc.RepsExtraContent__c;
                        tInfo.irresponseApprovalTime__c = System.now();
                    }
                }
                // DB202406382590 招标项目：不应标申请里增加涉及科室 end
                tInfoList.add(tInfo);
            }
            // DB202406382590 招标项目：不应标申请里增加涉及科室 start
            else if((ttc.status__c != oldMap.get(ttc.Id).status__c)){
                Tender_information__c tInfo = new Tender_information__c();
                tInfo.Id = ttc.TenderInfo__c;
                if(ttc.RecordTypeId == rectCo[0].Id && tInfoMap.get(ttc.TenderInfo__c).NotBidApprovalStatus__c != '批准'){
                    tInfo.NotBidApprovalStatus__c = ttc.status__c == '审批中' ? '申请中' : ttc.status__c;
                    // tInfo.IsBid__c = ttc.IsBid__c;
                    tInfoList.add(tInfo);
                }
            }
            // DB202406382590 招标项目：不应标申请里增加涉及科室 end

        }
        System.debug('dataWriteBackToTender'+ tInfoList);
        if (tInfoList.size() > 0) {
            // chenjingwu 20250304 start
            StaticParameter.EscapeOtherUpdateTenOwner = false;
            StaticParameter.EscapeTenderInformationUpdate = false;
            update tInfoList;
            StaticParameter.EscapeOtherUpdateTenOwner = true;
            StaticParameter.EscapeTenderInformationUpdate = true;
            // chenjingwu 20250304 end
        }
        
    }
    // DB202406382590 招标项目：不应标申请里增加涉及科室 start
    //stroption 是被追加赋值的字段
    //addoption 是需要追加的字段值
    public String addSplicingOptions(String stroption,String addoption) {
        List<String> options;
        if(stroption == null || stroption == ''){
            return addoption;
        }
        if(addoption == null || addoption == ''){
            return stroption;
        }else{
            options = addoption.split(';');
        }
        String  selectedOptionIds = stroption;
        for(String option : options){
            if(!stroption.contains(option)){
                selectedOptionIds = selectedOptionIds + ';' + option;
            }
        }
        return selectedOptionIds;
    }
    // DB202406382590 招标项目：不应标申请里增加涉及科室 end

}