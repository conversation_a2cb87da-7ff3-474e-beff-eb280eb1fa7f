({
	doInit : function(component, event, helper) {
        let rid = component.get('v.recordId');
        let pid = null;
        if(!rid){
            pid = window.location.href.replace("https://","").split("/")[4];
        }
        let isClone = component.get('v.isClone');
        if(!rid || isClone){
            component.set('v.title',$A.get("$Label.c.New")+'.客户人员');
        }else{
            component.set('v.title',$A.get("$Label.c.Edit")+'.客户人员');
        }
        
        let record_type_id = null;
        let pageref = component.get("v.pageReference")
        if(!record_type_id && pageref){
           record_type_id = pageref.state.recordTypeId
        }
        component.set("v.showSpinner", true);
        helper.CallBackAction(component,'Init',{
            rid : rid,
            pid : pid,
            //rid : component.get('v.recordId'),
            record_type_id : record_type_id,
        },function(data){
            component.set("v.showSpinner", false);
            
            
            if(data.getState() == "SUCCESS"){
                var rv = data.getReturnValue();
            	console.log(rv);
                if(rv.IsSuccess){
					let layout = JSON.parse(rv.Data.layout);
                    let fields = rv.Data.fields;
                    let staticResource = JSON.parse(rv.Data.staticResource)
                    let section_names = [];
                    section_names = layout.map(s=>s.label);
                    component.set('v.section_names',section_names);

                    let m = {};
                    for(let f of staticResource.PIDetails){
                        m[f.SF_Field_API_Name__c] = f;
                    }
                    component.set('v.pi_fields_map',m);

                    

                    if(rv.Data && rv.Data.data && rv.Data.data.AWS_Data_Id__c){
                        helper.AwsGet(staticResource.queryUrl, {
                            dataId : rv.Data.data.AWS_Data_Id__c
                        }, function(data){
                            console.log('data = ' + data);
                            
                            for(let f of staticResource.PIDetails){
                                
                                if(data.object && data.object.hasOwnProperty(f.AWS_Field_API__c)){
                                    rv.Data.data[f.SF_Field_API_Name__c] = data.object[f.AWS_Field_API__c];
                                    if(data.object.hasOwnProperty(f.AWS_Encrypted_Field_API__c) && data.object[f.AWS_Encrypted_Field_API__c]){
                                        rv.Data.data[f.SF_Field_Encrypted_API__c] = data.object[f.AWS_Encrypted_Field_API__c];
                                    }
                                }
                            }
                            
                            for(let s of layout){
                                for(let c of s.layoutColumns){
                                    for(let item of c.layoutItems){
                                        console.log('item.field = ' + item.field);
                                        if(rv.Data.data.hasOwnProperty(item.field)){
                                            item.value = rv.Data.data[item.field];
                                        }
                                        
                                        if(fields.hasOwnProperty(item.field)){
                                            item.fi = fields[item.field];
                                        }
                                    }
                                }
                            }

                    		component.set('v.record_data',rv.Data.data);
                            component.set('v.layout',layout);
                        }, staticResource.token);
                    }else{
                        
                        for(let s of layout){
                            for(let c of s.layoutColumns){
                                for(let item of c.layoutItems){
                                    if(rv.Data && fields.hasOwnProperty(item.field) && fields[item.field].References && fields[item.field].References.map(m=>m.value).indexOf(rv.Data.pidType) > -1){
                                        item.value = pid;
                                    }
                                }
                            }
                        }
                        component.set('v.layout',layout);
                    }
                    
                    
                    component.set('v.fields',fields);
                    component.set('v.staticResource',staticResource);
                }else{
                    helper.ShowToast({
                        "message" : rv.Message,
                        "type" : "error"
                    });
                }
                
            }else{ 
                helper.ShowToast({
                    "message" : "Init error",
                    "type" : "error"
                });
                
            }
        });
	},
    saveClick : function(component, event, helper){
        
        let staticResource = component.get('v.staticResource');
        let record_id = component.get('v.recordId');
        let record_type_id = component.get('v.recordTypeId');
        
        let isClone = component.get('v.isClone');
        let url = staticResource.newUrl;
        let payloadPi = {};
        if (record_id && !isClone) {
            url = staticResource.updateUrl;
            payloadPi['dataId'] = component.get('v.record_data').AWS_Data_Id__c;
        }
        let layout = component.get('v.layout');
        
        let pi_fields_map = component.get('v.pi_fields_map');
        
        let hospitalId = '';
        for(let s of layout){
            for(let c of s.layoutColumns){                
                for(let item of c.layoutItems){
                    if(item.field == 'Agency_Hospital__c'){
                        hospitalId = item.value;
                    }
                    if(pi_fields_map.hasOwnProperty(item.field)){
                        
                        payloadPi[pi_fields_map[item.field].AWS_Field_API__c] = item.value;
                    }

                    if (item.behavior == "Required" && !item.value ) {
                        let fs = component.find("field");
                        for(let fi in fs){
                            let f = fs[fi];
                            if(!f.get('v.value')){
                                f.reset();
                            }
                        }
                        return;
                    }
                }
            }
        }
        
        component.set("v.showSpinner", true);
        //zhj MEBG新方案改造 2022-11-29 start
        debugger
        let hospitalName = '';
        //调用后端searchAgencyDataId方法查询出医院下面所有客户人员dataid
        helper.CallBackAction(component,'searchAgencyDataId',{
            hospitalId : hospitalId
        },function(data){
            if(data.getState() == "SUCCESS"){
                var data = data.getReturnValue();
                if(data.IsSuccess == true){
                    let agencyContactIds = ''
                    if(data.Message == '' && data.Data && data.Data.length > 0){
                        hospitalName = data.Data[0].Agency_Hospital__r.Name;
                        for(var i=0;i<data.Data.length;i++){
                            if(data.Data[i].AWS_Data_Id__c)
                                agencyContactIds += ','+data.Data[i].AWS_Data_Id__c;
                        }
                        agencyContactIds = agencyContactIds.substring(1);
                        payloadPi['agencyContactIds'] = agencyContactIds;
                    }else{
                        payloadPi['agencyContactIds'] = agencyContactIds;
                    }
                    helper.AwsPost(url+'V2', [payloadPi], function(result){
                        //如果返回是129表示重复了，报错
                        if(result.status == '129'){
                            helper.ShowToast({
                                "message" : '该客户人员名字已存在，在'+hospitalName+'医院，请修改',
                                "type" : "error"
                            });
                            component.set("v.showSpinner", false);
                            return
                        }
    
                        let obj = result.object[0];
                        let data = {};
                        if(record_id && !isClone){
                            data.Id = record_id;
                        }else{
                            data.AWS_Data_Id__c = obj.dataId;
                        }
                        for(let s of layout){
                            for(let c of s.layoutColumns){                
                                for(let item of c.layoutItems){
                                    if(item.field && item.behavior != "Readonly"){
                                        if(pi_fields_map.hasOwnProperty(item.field)){
                                            data[item.field] = obj[pi_fields_map[item.field].AWS_Field_API__c];
                                            //data[pi_fields_map[item.field].SF_Field_Encrypted_API__c] = obj[pi_fields_map[item.field].AWS_Encrypted_Field_API__c];
                                        }else{
                                            data[item.field] = item.value;
                                        }
                                    }
                                    
                                }
                            }
                        }
                        
                        
                        $A.getCallback(function(){
                            helper.CallBackAction(component,'Save',{
                                data : data,
                                transId : result.txId
                            },function(data){
                                component.set("v.showSpinner", false);
                                if(data.getState() == "SUCCESS"){
                                    var rv = data.getReturnValue();
                                    console.log(rv);
                                    var sfId = null;
                                    if(rv.IsSuccess){
                                        sfId = rv.Data.recordId;
                                    }
                                    
                                    helper.AwsPost(staticResource.transactionUrl,{
                                        txId: result.txId,
                                        sfRecordId:sfId,
                                        isSuccess: rv.IsSuccess ? 1 : 0
                                    },function(data){
                                        if(rv.IsSuccess){
                                            helper.ShowToast({
                                                "message" : "成功",
                                                "type" : "success"
                                            });
                                            if (record_id && !isClone){
                                                
                                                helper.closeAgencyContact(component);
                                                //$A.get('e.force:refreshView').fire();
                                            }else{
                                                var sObjectEvent = $A.get("e.force:navigateToSObject");
                                                sObjectEvent.setParams({
                                                    "recordId": sfId
                                                })
                                                sObjectEvent.fire();
                                            }
                                        }else{
                                            helper.ShowToast({
                                                "message" : rv.Message,
                                                "type" : "error"
                                            });
                                        }
                                    },staticResource.token);
                                    
                                    
                                }else{
                                    
                                    helper.ShowToast({
                                        "message" : "Init error",
                                        "type" : "error"
                                    });
                                    
                                }
                            });
                        })();
                        
                    }, staticResource.token);
                }else{
                    helper.ShowToast({
                        "message" : data.message,
                        "type" : "error"
                    });
                    component.set("v.showSpinner", false);
                }
            }else{
                helper.ShowToast({
                    "message" : 'searchAgency失败',
                    "type" : "error"
                });
                component.set("v.showSpinner", false);
            }
        });
        //zhj MEBG新方案改造 2022-11-29 end

        // helper.AwsPost(url, [payloadPi], function(result){
            
        //     let obj = result.object[0];
        //     let data = {};
        //     if(record_id && !isClone){
        //         data.Id = record_id;
        //     }else{
        //         data.AWS_Data_Id__c = obj.dataId;
        //     }
        //     for(let s of layout){
        //         for(let c of s.layoutColumns){                
        //             for(let item of c.layoutItems){
        //                 if(item.field && item.behavior != "Readonly"){
        //                     if(pi_fields_map.hasOwnProperty(item.field)){
        //                         data[item.field] = obj[pi_fields_map[item.field].AWS_Field_API__c];
        //                         data[pi_fields_map[item.field].SF_Field_Encrypted_API__c] = obj[pi_fields_map[item.field].AWS_Encrypted_Field_API__c];
        //                     }else{
        //                         data[item.field] = item.value;
        //                     }
        //                 }
                        
        //             }
        //         }
        //     }
            
            
        //     $A.getCallback(function(){
        //         helper.CallBackAction(component,'Save',{
        //             data : data,
        //             transId : result.txId
        //         },function(data){
        //             component.set("v.showSpinner", false);
        //             if(data.getState() == "SUCCESS"){
        //                 var rv = data.getReturnValue();
        //                 console.log(rv);
        //                 var sfId = null;
        //                 if(rv.IsSuccess){
        //                     sfId = rv.Data.recordId;
        //                 }
                        
        //                 helper.AwsPost(staticResource.transactionUrl,{
        //                     txId: result.txId,
        //                     sfRecordId:sfId,
        //                     isSuccess: rv.IsSuccess ? 1 : 0
        //                 },function(data){
        //                     if(rv.IsSuccess){
        //                         helper.ShowToast({
        //                             "message" : "成功",
        //                             "type" : "success"
        //                         });
                                
        //                         if (record_id && !isClone){
                                    
        //                             $A.get("e.force:closeQuickAction").fire();
        //                             $A.get('e.force:refreshView').fire();
        //                         }else{
        //                             var sObjectEvent = $A.get("e.force:navigateToSObject");
        //                             sObjectEvent.setParams({
        //                                 "recordId": sfId
        //                             })
        //                             sObjectEvent.fire();
        //                         }
        //                     }else{
        //                         helper.ShowToast({
        //                             "message" : rv.Message,
        //                             "type" : "error"
        //                         });
        //                     }
        //                 },staticResource.token);
                        
                        
        //             }else{
                        
        //                 helper.ShowToast({
        //                     "message" : "Init error",
        //                     "type" : "error"
        //                 });
                        
        //             }
        //         });
        //     })();
            
        // }, staticResource.token);
    },
    cancelClick : function(component, event, helper){
        $A.get("e.force:closeQuickAction").fire();
        //document.getElementById('NewAgencyContact').remove();
        //component.set('v.showNewAgencyContact', false);
    },
    
    scriptsLoaded : function(component, event, helper){}
})