({
    doInit : function(component, event, helper) {
        console.log('zhj 新方案weeklyReport');
        if (window.location.href.endsWith("weekly-report")) {
            helper.doinit(component, event, helper);
        } else {
            helper.hideCmp(component, event, helper);
        }
        //helper.set_aws_url(component,'Agency_Contact__c'); //deloitte-zhj 2023-09-14   //deloitte-zhj 20231127 PIPL还原
        component.set("v.showErrorInfo", false);
        // add by Link : 2024-1-2 PIPL 客户人员选取
        component.set("v.newContactFlag", 0);
    },

    //  DTT-亚楠 20241025 DB202409327066 DAMS系统界面优化需求 Start 
    // createAopp : function(component, event, helper) {
    //     var addRecordEvent = $A.get('e.force:createRecord');
    //     addRecordEvent.setParams({
    //         entityApiName: 'Agency_Opportunity__c',
    //         recordTypeId: $A.get("$Label.c.weeklyReport") //niwu add  012100000006KW7 =weeklyReport 
    //     });
    //     addRecordEvent.fire();
    //     component.set("v.showErrorInfo", false);
    // },
    showNewAgency: function (component, event, helper) {
        //do something 
        console.log('showNewAgency');
        component.set("v.recordTypeId", $A.get("$Label.c.weeklyReport"));
        var accId = component.get('v.data.Agency_Hospital__c');
        var department = component.find('select_department').get('v.value');
        component.set("v.agencyHospitalLinkId", accId);
        component.set("v.departmentName", department);
        component.set("v.showNewAgencyOpportunity", true);
    },
    closeAgencyOpportunity: function (component, event, helper) {
        var message = event.getParam("showNewAgencyOpportunity");
        var agencyOpportunityId = event.getParam("agencyOpportunityId");
        component.set("v.showNewAgencyOpportunity", message);
        console.log('agencyOpportunityId: ' + agencyOpportunityId);
        component.set('v.data.Opportunity__c', agencyOpportunityId);
    //  DTT-亚楠 20241025 DB202409327066 DAMS系统界面优化需求 End 
    },
    
    new_report : function(component, event, helper) {
        component.find('save_button').set('v.label', '保存并新建');
        component.set('v.data.Report_Date__c', '');
        helper.new_report(component, event, helper);
    },
    
    copy_button : function(component, event, helper) {
        component.find('save_button').set('v.label', '保存并新建');
        helper.copy_button(component, event, helper);
    },
    
    change_report_radio : function(component, event, helper) {
        helper.change_report_radio(component, event, helper);
    },
    
    edit_button : function(component, event, helper) {
        component.find('save_button').set('v.label', '保存');
        helper.edit_button(component, event, helper);
    },
    
    createCon : function(component, event, helper) {
		helper.createCon(component, event, helper);
    },

    showRequiredFields: function(component, event, helper){
        $A.util.removeClass(component.find("newOpportunityField"), "none");
        $A.util.removeClass(component.find("newOpportunityField"), "none");
    },
    
    handleSuccess : function(component, event, helper) {
        helper.handleSuccess(component, event, helper);
    },
    
    handleSubmit : function(component, event, helper) {
        /*
        var params = event.getParams();
        params.fields["Agency_Hospital__c"] = component.get('v.hospitalLinkId');
        console.log(params);
        event.setParams(params);
        */
        event.preventDefault(); // stop form submission
        // vivek 添加验证 start
        // helper.handleFormSubmit(component);
        var showValidationError = false;
        var fields = component.find("newOpportunityField");
        var vaildationFailReason = '';
        // var vaildationFailReason2 = '';
        // var currentDate = new Date().toJSON().slice(0,10);
        
        // PIPL update Yin Mingjie 21/02/2022 start
        let agencyReport = Object.create(null);
        // PIPL update Yin Mingjie 21/02/2022 end

        fields.forEach(function (field) {
            if(field.get("v.fieldName") === 'Type__c' && $A.util.isEmpty(field.get("v.value"))){
                showValidationError = true;
                vaildationFailReason = "分类不能为空！";
            }else if(field.get("v.fieldName") === 'Doctor_Division1__c' && $A.util.isEmpty(field.get("v.value"))){
                showValidationError = true;
                if(vaildationFailReason != ''){
                    vaildationFailReason += "医生区分(职务)不能为空！";
                }else{
                    vaildationFailReason = "医生区分(职务)不能为空！";
                }
            }
            // PIPL update Yin Mingjie 21/02/2022 start
            if(field.get("v.fieldName") === 'Name'){
                agencyReport['name'] = field.get("v.value");
            }else if(field.get("v.fieldName") === 'Type__c'){
                agencyReport['type'] = field.get("v.value");
            }else if(field.get("v.fieldName") === 'Doctor_Division1__c'){
                agencyReport['doctorDivision1'] = field.get("v.value");
            }
            // PIPL update Yin Mingjie 21/02/2022 end
        });
         
        if (!showValidationError) {
            // PIPL update Yin Mingjie 21/02/2022 start
            /*
            var eventFields = event.getParam("fields");
            eventFields["Agency_Hospital__c"] = component.get('v.hospitalLinkId');
            component.find('recordEditForm').submit(eventFields);
            */

            var agencyHospitalid = component.get('v.hospitalLinkId');
            //zhj MEBG新方案改造 2022-11-29 start
            debugger
            let hospitalName = '';
            //调用后端searchAgencyDataId方法查询出医院下面所有客户人员dataid
            helper.CallBackAction(component,'searchAgencyDataId',{
                hospitalId : agencyHospitalid
            },function(data){
                if(data.getState() == "SUCCESS"){
                    var data = data.getReturnValue();
                    if(data.IsSuccess == true){
                        //deloitte-zhj 20231116 PIPL还原 start
                        // let agencyContactIds = ''
                        // if(data.Message == '' && data.Data && data.Data.length > 0){
                        //     hospitalName = data.Data[0].Agency_Hospital__r.Name;
                        //     for(var i=0;i<data.Data.length;i++){
                        //         if(data.Data[i].AWS_Data_Id__c)
                        //             agencyContactIds += ','+data.Data[i].AWS_Data_Id__c;
                        //     }
                        //     agencyContactIds = agencyContactIds.substring(1);
                        //     agencyReport['agencyContactIds'] = agencyContactIds;
                        // }else{
                        //     agencyReport['agencyContactIds'] = agencyContactIds;
                        // }
                        // var arr = new Array();
                        // arr.push(agencyReport);
                        // var requestData = JSON.stringify(arr);
                        // // helper.set_aws_url(component,data,agencyHospitalid);

                        // var token = component.get('v.AWStoken');
                        // var newUrl = component.get('v.AWSinsert') + 'V2';
                        
                        // component.set('v.loginEdit',true);
                        // helper.insert_agencycontact(component,token,newUrl,requestData,agencyHospitalid,helper,hospitalName);
                        component.set('v.loginEdit', true);
                        helper.insert_agencycontact(component, agencyReport, agencyHospitalid, helper);
                        //deloitte-zhj 20231116 PIPL还原 end
                    }else{
                        helper.ShowToast({
                            "message" : data.message,
                            "type" : "error"
                        });
                    }
                }else{
                    helper.ShowToast({
                        "message" : 'searchAgency失败',
                        "type" : "error"
                    });
                }
            })
            //zhj MEBG新方案改造 2022-11-29 end
            // var arr = new Array();
            // arr.push(agencyReport);
            // var data = JSON.stringify(arr);
            // // helper.set_aws_url(component,data,agencyHospitalid);

            // var token = component.get('v.AWStoken');
            // var newUrl = component.get('v.AWSinsert');
            
            // component.set('v.login',true);
            // helper.insert_agencycontact(component,token,newUrl,data,agencyHospitalid,helper);

            // PIPL update Yin Mingjie 21/02/2022 end
        }else{
            component.find('OppMessage').setError(vaildationFailReason);
        }
        // var eventFields = event.getParam("fields");
        // eventFields["Agency_Hospital__c"] = component.get('v.hospitalLinkId');
        // component.find('recordEditForm').submit(eventFields);
        // vivek 添加验证 end
    },
    
    createCancel : function(component, event, helper) {
        helper.createCancel(component, event, helper);
    },
    
    yes_button : function(component, event, helper) {
        component.set('v.confirm_status', 1);
        helper.close_confirm(component, component.get('v.modal_confirm_title'), component.get('v.modal_confirm_text'));
    },

    no_button : function(component, event, helper) {
        component.set('v.confirm_status', 2);
        helper.close_confirm(component, component.get('v.modal_confirm_title'), component.get('v.modal_confirm_text'));
    },
    
    toggle_report : function(component, event, helper) {
        helper.toggle_report(component);
    },

    save_report : function(component, event, helper) {
        helper.save_report(component, event, helper);
    },
    
    select_date_change : function(component, event, helper) {
        helper.select_date_change(component, event, helper);
    },
    
    select_agency_change : function(component, event, helper) {
        helper.select_agency_change(component, event, helper);
    },
    
    hosChange : function(component, event, helper) {
        console.log('enter hosChange');
		var hospital_name = event.getParam("value");
        // DTT-亚楠 20240605 去除医院空格 Start
        var accName = component.get("v.hospital");
		// if (hospital_name.match(/(\S+\s)+/)) {
		if (hospital_name.length != 0 && hospital_name.charAt(hospital_name.length - 1).trim() === '') {
        // DTT-亚楠 20240605 去除医院空格 End
            console.log('helper.searchHos');
			helper.searchHos(component, event, helper);
		} else {
            console.log('helper.hideSearch');
			helper.hideSearch(component, event, helper);
		}
    },
    
    selectHos : function(component, event, helper) {
        console.log('enter selectHos');
		helper.selectHos(component, event, helper);
        helper.setOpportunity_cfilter(component);
        // add by Link : 2024-1-2 PIPL 客户人员选取
        helper.setContact_cfilter(component);
    },
    select_department : function(component, event, helper) {
        // DTT-亚楠 20241016 客户人员检索优化 Start
        component.set('v.contactValue', '');
        // DTT-亚楠 20241016 客户人员检索优化 End
        helper.select_department(component, event, helper);
        helper.setOpportunity_cfilter(component);
    },
    select_purpose_type : function(component, event, helper) {
        helper.select_purpose_type(component, event, helper);
    },
    
    doctor_change : function(component, event, helper) {
        helper.doctor_change(component, event, helper);
    },
    
    opportunityChange : function(component, event, helper) {
        helper.opportunityChange(component, event, helper);
    },

    // add by Link : 2024-1-2 PIPL 客户人员选取
    contactValueChange: function(component, event, helper) {
        helper.contactValueChange(component, event, helper);
    },
    
    productcategoryChange1 : function(component, event, helper) {
        helper.productcategoryChange1(component, event, helper);
    },
    productcategoryChange2 : function(component, event, helper) {
        helper.productcategoryChange2(component, event, helper);
    },
    productcategoryChange3 : function(component, event, helper) {
        helper.productcategoryChange3(component, event, helper);
    },
    stageNameChange : function(component, event, helper) {
        helper.stageNameChange(component, event, helper);
    },
    onDragOver : function(component, event, helper) {
        event.preventDefault();
    },
    
    onDrop : function(component, event, helper) {
        event.stopPropagation();
        event.preventDefault();
        event.dataTransfer.dropEffect='copy';
        var files=event.dataTransfer.files;
        helper.readFile(component,helper,files[0]);
    },
    CreateRecord : function(component, event, helper){
        var files = event.getSource().get("v.files");
        // var fileInput = component.find("file").getElement();
        // var file = fileInput.files[0];
        helper.readFile(component,helper,files[0]);
        component.set("v.showErrorInfo", false);
    },

    processFileContent : function(component,event,helper){
        helper.saveRecords(component,event,helper);
        component.set("v.showErrorInfo", false);
    },
    
    cancel : function(component,event,helper){
        component.set("v.showMain",true);
    },

    import : function(component,event,helper){
        // component.find('file').click();
        // console.log('lll'+component.find('file'));
        helper.showImport(component);
    },
    exportDate : function(component,event,helper){
        var stockData = component.get("v.reports_date");
        console.log('导出数据'+stockData); 
        var csv = helper.convertArrayOfObjectsToCSV(component,stockData);
        if (csv == null){return;} 

        // ####--code for create a temp. <a> html tag [link tag] for download the CSV file--####     
        var universalBOM = "\uFEFF";
        var hiddenElement = document.createElement('a');
        hiddenElement.href = 'data:text/csv;charset=utf-8,' + encodeURI(universalBOM+csv);
        hiddenElement.target = '_self'; // 
        hiddenElement.download = 'ExportData.csv';  // CSV file Name* you can change it.[only name not .csv] 
        document.body.appendChild(hiddenElement); // Required for FireFox browser
        hiddenElement.click(); // using click() js function to download csv file
        // helper.showExport(component);
        
    },

    export_condition : function(component,event,helper){
        helper.showExport(component);
    },

    select_repores_date : function(component,event,helper){
        helper.select_repores_date(component,event,helper);
        // var stockData = component.get("v.reports_date");
        // console.log('查出的数据'+stockData);
        // helper.showExportDate(component,stockData);
        // this.export(component,event,helper);
    },
    export : function(component,event,helper){
        console.log('进入export');
        var stockData = component.get("v.reports_date");
        console.log('查出的数据'+stockData);
        // helper.showExportDate(component,stockData);
        // helper.showExport(component);
    },
    close_import : function(component,event,helper){
        component.set("v.showMain",true);
        helper.close_import(component);
    },
    close_export : function(component,event,helper){
        component.set("v.showMain",true);
        helper.close_export(component);
    },
    exportErrorInfo: function(component,event,helper){
        helper.exportErrorInfoHelper(component);
    },
    //deloitte-zhj 2023/07/18 防止选择***** start
    avoidPI : function(component, event, helper) {
        setTimeout(function() {
            let doctorDivision1 = component.find('doctorDivision1');
            let doctorDivision1Value = doctorDivision1.get('v.value');
            console.log('doctorDivision1Value = ' + doctorDivision1Value);
            if (doctorDivision1Value == '*****') {
                doctorDivision1.set('v.value', '');
                helper.warning('不能选择*****！');
            }
        }, 0);
    },
    //end
})