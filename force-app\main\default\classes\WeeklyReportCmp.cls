public with sharing class WeeklyReportCmp {
    @AuraEnabled public List<Agency_Report__c> reports{get;set;}
    @AuraEnabled public Map<String,List<Map<String,String>>> allselectlist{get;set;}
    @AuraEnabled public Map<String,String> fieldsMap{get;set;}
    @AuraEnabled public Map<String,List<Map<String,String>>> docmap{get;set;}
    @AuraEnabled public List<Map<String,String>> doctorList{get;set;}
    // PIPL update Yin <PERSON> 21/02/2022 start
    //deloitte-zhj ******** PIPL还原
    // @AuraEnabled public Map<String,String> awsurl{get;set;}
    // @AuraEnabled public Map<String,String> contactawsurl{get;set;}
    // PIPL update Yin Mingjie 21/02/2022 end

    public WeeklyReportCmp() {
    }
    // PIPL update Yin <PERSON> 21/02/2022 start
    @RemoteAction
    @AuraEnabled
    public static Map<String,String> getAwsurl(String sobj){
        PIHelper.PIIntegration piIntegration = PIHelper.getPIIntegrationInfo(sobj);
        Map<String,String> awsmap = new Map<String,String>();
        awsmap.put('token', piIntegration.token);
        awsmap.put('newUrl', piIntegration.newUrl);
        awsmap.put('searchUrl', piIntegration.searchUrl);
        awsmap.put('transactionURL', piIntegration.transactionURL);
        return awsmap;
    }
    
    // @RemoteAction
    // @AuraEnabled
    // public static Map<String, String> saveAgencyContact(String name, String nameEncrypt, String type, String typeEncrypt, String doctorDivision1, 
    //     String doctorDivision1Encrypt, String agencyHospitalid, String awsid) {
    //     Agency_Contact__c agency_contact = new Agency_Contact__c();

    //     agency_contact.Name = name;
    //     agency_contact.Name_Encrypted__c = nameEncrypt;
    //     agency_contact.Type__c = type;
    //     agency_contact.Type_Encrypted__c = typeEncrypt;
    //     agency_contact.Doctor_Division1__c = doctorDivision1;
    //     agency_contact.Doctor_Division1_Encrypted__c = doctorDivision1Encrypt;
    //     agency_contact.Agency_Hospital__c = agencyHospitalid;
    //     agency_contact.AWS_Data_Id__c = awsid;
        
    //     Map<String, String> acMap = new Map<String, String>();
        
    //     acMap = LightningUtil.insertAgencyContact(agency_contact);
    //     return acMap;
    // }

    //zhj 新方案改造 2022-12-21 start
    @RemoteAction
    @AuraEnabled
    public static Map<String, String> saveAgencyContact(String name, String type, String doctorDivision1, 
        String agencyHospitalid, String awsid) {
        Agency_Contact__c agency_contact = new Agency_Contact__c();

        agency_contact.Name = name;
        agency_contact.Type__c = type;
        agency_contact.Doctor_Division1__c = doctorDivision1;
        agency_contact.Agency_Hospital__c = agencyHospitalid;
        //agency_contact.AWS_Data_Id__c = awsid;  //deloitte-zhj ******** PIPL还原
        
        Map<String, String> acMap = new Map<String, String>();
        
        acMap = LightningUtil.insertAgencyContact(agency_contact);
        return acMap;
    }
    //zhj 新方案改造 2022-12-21 end
    // PIPL update Yin Mingjie 21/02/2022 end
    @RemoteAction
    @AuraEnabled
    public static List<Map<String,String>> getProductList(String dc, String opdsis){
        List<ProductTypes__c> ptList;
        if (opdsis != '') {
            ptList = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dc and OPD_SIS_Type__c =:opdsis];
        } else {
            ptList = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dc];
        }
        List<Map<String,String>> pts = new List<Map<String,String>>();
        Map<String,String> blank = new Map<String,String>();
        blank.put('label', '');
        blank.put('value', '');
        pts.add(blank);
        for (ProductTypes__c pt : ptList) {
            Map<String,String> ptMap = new Map<String,String>();
            ptMap.put('label', pt.Name);
            ptMap.put('value', pt.Id);
            pts.add(ptMap);
        }
        return pts;
    }
    
    public void setalldata()
    {
        /*** create allselectlist ***/
        this.allselectlist = new Map<String,List<Map<String,String>>>();

        Map<String,List<Map<String,String>>> alldata = new Map<String,List<Map<String,String>>>();

        // プルダウン初期値の空白
        List<Map<String,String>> tmp = new List<Map<String,String>>();
        Map<String,String> space = new Map<String,String>();
        space.put('label', '');
        space.put('value', '');
        // PIPL update Yin Mingjie 21/02/2022 start
        space.put('awsid', '');
        // PIPL update Yin Mingjie 21/02/2022 end
        space.put('selected', 'true');
        tmp.add(space);
        
        // 代理店担当者 AgencyPerson__c
        List<Contact> agency_person_data = LightningUtil.selectAgencyPerson();
        for(Contact var : agency_person_data) {
            Map<String,String> om = new Map<String,String>();
            om.put('label', var.Name);
            om.put('value', var.Id);
            // PIPL update Yin Mingjie 21/02/2022 start
            //om.put('awsid', var.AWS_Data_Id__c);    //deloitte-zhj ******** PIPL还原
            // PIPL update Yin Mingjie 21/02/2022 end
            om.put('selected', 'false');
            tmp.add(om);
        }
        this.allselectlist.put('AgencyPerson__c', tmp);
        
        /*
        List<AgencyPerson__c> agency_person_data = [Select Id,Name,CurrencyIsoCode From AgencyPerson__c];
        List<Map<String,String>> tmp = new List<Map<String,String>>();
        Map<String,String> space = new Map<String,String>();
        space.put('label', '');
        space.put('value', '');
        space.put('selected', 'true');
        tmp.add(space);
        for(AgencyPerson__c var : agency_person_data){
            Map<String,String> om = new Map<String,String>();
            om.put('label', var.Name);
            om.put('value', var.Id);
            om.put('selected', 'false');
            tmp.add(om);
        }
        this.allselectlist.put('AgencyPerson__c', tmp);
        */
        
        // 科室分类 Department_Cateogy__c 
        this.allselectlist.put('Department_Cateogy__c', WeeklyReportCmp.getPicklistValues('Agency_Report__c','Department_Cateogy__c'));

        // 活动区分 Purpose_Type__c
        this.allselectlist.put('Purpose_Type__c', WeeklyReportCmp.getPicklistValues('Agency_Report__c','Purpose_Type__c'));

        // 结果 Result__c
        this.allselectlist.put('Result__c', WeeklyReportCmp.getPicklistValues('Agency_Report__c','Result__c'));

        //阶段 StageName__c
        this.allselectlist.put('StageName__c', WeeklyReportCmp.getPicklistValues('Agency_Opportunity__c','StageName__c'));
        //SWAG-CBX68C fy 【委托】DAMS系统周报模块内容需求增加 start
        // 支援需求 SupportNeeds__c
        this.allselectlist.put('SupportNeeds__c', WeeklyReportCmp.getPicklistValues('Agency_Report__c','SupportNeeds__c'));
        //SWAG-CBX68C fy 【委托】DAMS系统周报模块内容需求增加 end
        // 职位
//        this.allselectlist.put('visitor_title__c', WeeklyReportCmp.getPicklistValues('Agency_Report__c','visitor_title__c'));

        // 製品区分
        //List<ProductTypes__c> ProductTypes = [select id,Name from ProductTypes__c];
        //List<Map<String,String>> tmp2 = new List<Map<String,String>>();
        //tmp2.add(space);
        //for(ProductTypes__c var : ProductTypes){
        //    Map<String,String> om = new Map<String,String>();
        //    om.put('label', var.Name);
        //    om.put('value', var.Id);
        //    om.put('selected', 'false');
        //    tmp2.add(om);
        //}
        //this.allselectlist.put('Product_Category__c', tmp2);
        
        // 見出し設定
        this.fieldsMap = new Map<String,String>();
        this.fieldsMap = this.getfiledsmap();
        
        //System.debug('fieldsMap is ' + fieldsMap);
        //System.debug('allselectlist is ' + this.allselectlist);

        //deloitte-zhj ******** PIPL还原
        // this.awsurl = getAwsurl('Agency_Contact__c');// 20220222 PI改造 by Bright
        // this.contactawsurl = getAwsurl('Contact');// 20220222 PI改造 by Bright
    }
    
    
    public static List<Map<String,String>> getPicklistValues(String objstr, String fld){
        List<Map<String,String>> options = new List<Map<String,String>>();
        Map<String,String> space = new Map<String,String>();
        space.put('label', '');
        space.put('value', '');
        space.put('selected', 'true');
        options.add(space);

        Schema.sObjectType objType = Schema.getGlobalDescribe().get(objstr);
        Schema.DescribeSObjectResult objDescribe = objType.getDescribe();
        map<String, Schema.SObjectField> fieldMap = objDescribe.fields.getMap();
        list<Schema.PicklistEntry> values = fieldMap.get(fld).getDescribe().getPickListValues();
        system.debug(objstr + '=' + values);
        for (Schema.PicklistEntry a : values)
        {
            if (!a.isActive()) continue;
            Map<String,String> ses = new Map<String,String>();
            ses.put('label', a.getLabel());
            ses.put('value', a.getValue());
            ses.put('selected', 'false');
            options.add(ses);
        }
        return options;
    }
    
    
    public  Map<String,String> getfiledsmap()
    {
        Map<String,Schema.SObjectType> schemaMap = Schema.getGlobalDescribe();
        Map<String,List<String>>   typemap = new Map<String,List<String>>  ();
        
        Map<String,Schema.SObjectField> fieldMap = schemaMap.get('Agency_Opportunity__c').getDescribe().fields.getMap();
        Map<String,String> mappingmap = new Map<String,String>();
        for(Schema.SObjectField sfield : fieldMap.Values())
        {
            Schema.describefieldresult dfield = sfield.getDescribe();
            String lab = '';
            lab = dfield.getLabel();
            system.debug(lab);
            mappingmap.put(dfield.name,lab);
        }
        fieldMap = schemaMap.get('Agency_Report__c').getDescribe().fields.getMap();
        for(Schema.SObjectField sfield : fieldMap.Values())
        {
            Schema.describefieldresult dfield = sfield.getDescribe();
            String lab = '';
            lab = dfield.getLabel();
            system.debug(lab);
            mappingmap.put(dfield.name,lab);
        }
        return mappingmap;
    }
    
    @RemoteAction
    @AuraEnabled
    public static WeeklyReportCmp getalldata(){
        WeeklyReportCmp li = new WeeklyReportCmp();
        li.setalldata();
        return li;
    }

    @RemoteAction
    @AuraEnabled
    public static ProductTypes__c getProduct(String id){
        return [select Department_Cateogy__c, OPD_Flg__c, Id, SIS_Flg__c from ProductTypes__c where Id =:id];
    }
    
    @RemoteAction
    @AuraEnabled
    public static String createReportHeader(String name, String s_date, String s_agency, String head_key){
        Agency_Report_Header__c agency_report_header = makeReportHeader(name, s_date, s_agency, head_key);

        agency_report_header = LightningUtil.upsertAgencyReportHeader(agency_report_header);
        return agency_report_header.Id;
    }
    public static Agency_Report_Header__c makeReportHeader(String name, String s_date, String s_agency, String head_key){
        Date week = Date.valueOf(s_date);
        Agency_Report_Header__c agency_report_header = new Agency_Report_Header__c();
        agency_report_header.Name = name + ' (' + s_date + ')';
        agency_report_header.HeaderInputKey__c = head_key;
        agency_report_header.Week__c = week;
        agency_report_header.Agency_Person2__c = s_agency;

        // READ OlympusCalendar__c
        system.debug(week);
        OlympusCalendar__c olympus_calendar = [select Id,Date__c from OlympusCalendar__c where Date__c=:week];
        system.debug(olympus_calendar);
        String olympus_calendar_id = olympus_calendar.Id;
        if (olympus_calendar_id != '') { agency_report_header.OlympusDate__c = olympus_calendar_id; }

        system.debug(agency_report_header);
        return agency_report_header;
    }
    
    @RemoteAction
    @AuraEnabled
    public static List<Agency_Hospital_Link__c> getHospitalList(String hospital_name) {
        hospital_name = '%' + hospital_name.trim() + '%'; 
        system.debug('hospital_name+++'+hospital_name);
        List<Agency_Hospital_Link__c> ahllist = [select Hospital_Name_readonly__c, Id, Hospital__c from Agency_Hospital_Link__c where Hospital_Name_readonly__c like :hospital_name and Agency_Campaign_Obj__c = true];
        system.debug('Agency_Campaign_Obj__c+++'+ahllist);
        return ahllist;
    }
    
    //deloitte-zhj ******** PIPL还原 start
    @RemoteAction
    @AuraEnabled
    public static List<Map<String,String>> getDoctorList(String hospital_id){
        List<Map<String,String>> ret = new List<Map<String,String>>();
        Map<String,String> space = new Map<String,String>();
        space.put('label', '');
        space.put('value', '');
        space.put('selected', 'true');
        ret.add(space);

        Agency_Hospital_Link__c ahl = [select Hospital__c from Agency_Hospital_Link__c where id = :hospital_id];
        List<Agency_Contact__c> doctor_list = [select id,Name,Doctor_Division1__c,Type__c,Agency_Hospital__c 
            FROM Agency_Contact__c WHERE Hospital_ID18__c=:ahl.Hospital__c order by Name];
        for (Agency_Contact__c row : doctor_list){
            Map<String,String> tmp = new Map<String,String>();
            tmp.put('label', row.Name);
            tmp.put('value', row.Id);
            tmp.put('selected', 'false');
            tmp.put('Doctor_Division1__c', row.Doctor_Division1__c);
            ret.add(tmp);
        }
        return ret;
    }
    // @RemoteAction
    // @AuraEnabled
    // // PIPL update Yin Mingjie 21/02/2022 start
    // public static Map<String,Map<String,String>> getDoctorList(String hospital_id){
    // /*
    // public static List<Map<String,String>> getDoctorList(String hospital_id){
    //     List<Map<String,String>> ret = new List<Map<String,String>>();
    //     Map<String,String> space = new Map<String,String>();
    //     space.put('label', '');
    //     space.put('value', '');
    //     space.put('selected', 'true');
    //     ret.add(space);
    // */
    // // PIPL update Yin Mingjie 21/02/2022 end

    //     // 戦略科室IDを取得して、それをもとに顧客をSELECT
    //     Agency_Hospital_Link__c ahl = [select Hospital__c from Agency_Hospital_Link__c where id = :hospital_id];

    //     // PIPL update Yin Mingjie 21/02/2022 start
    //     /*
    //     List<Agency_Contact__c> doctor_list = [select id,Name,Doctor_Division1__c,Type__c,Agency_Hospital__c 
    //         FROM Agency_Contact__c WHERE Hospital_ID18__c=:ahl.Hospital__c order by Name];


    //     for (Agency_Contact__c row : doctor_list)
    //     {
    //         Map<String,String> tmp = new Map<String,String>();
    //         tmp.put('label', row.Name);
    //         tmp.put('value', row.Id);
    //         tmp.put('selected', 'false');
    //         tmp.put('Doctor_Division1__c', row.Doctor_Division1__c);
    //         ret.add(tmp);
    //     }
    //     */
    //     List<Agency_Contact__c> doctor_list = [select id,Name,AWS_Data_Id__c,Doctor_Division1__c,Type__c,Agency_Hospital__c 
    //         FROM Agency_Contact__c WHERE Hospital_ID18__c=:ahl.Hospital__c order by Name];

    //     Map<String,Map<String,String>> ret_test = new Map<String,Map<String,String>>();
    //     for (Agency_Contact__c row : doctor_list)
    //     {
    //         if(row.AWS_Data_Id__c == '' || row.AWS_Data_Id__c == null){
    //             continue;
    //         }
    //         Map<String,String> tmp = new Map<String,String>();
    //         tmp.put('label', row.Name);
    //         tmp.put('value', row.Id);
    //         tmp.put('awsid', row.AWS_Data_Id__c);
    //         tmp.put('selected', 'false');
    //         tmp.put('Doctor_Division1__c', row.Doctor_Division1__c);
    //         ret_test.put(row.AWS_Data_Id__c, tmp);
    //     }
    //     PIHelper.PIIntegration piIntegration = PIHelper.getPIIntegrationInfo('Agency_Contact__c');
    //     Map<String, String> sre = new Map<String, String>();
    //     sre.put('token', piIntegration.token);
    //     sre.put('searchUrl', piIntegration.searchUrl);
    //     ret_test.put('sre', sre);
        
    //     return ret_test;
    //     // PIPL update Yin Mingjie 21/02/2022 end
    //     /*
    //     String record_type_id = LightningUtil.getRecordTypeId(department);

    //     List<Contact> doctor_list = LightningUtil.selectContactList(hospital_id, record_type_id);
        
    //     for (Contact row : doctor_list)
    //     {
    //         Map<String,String> tmp = new Map<String,String>();
    //         tmp.put('label', row.Name);
    //         tmp.put('value', row.Id);
    //         tmp.put('selected', 'false');
    //         tmp.put('Doctor_Division1__c', row.Doctor_Division1__c);
    //         ret.add(tmp);
    //     }
    //     */

    //     // PIPL update Yin Mingjie 21/02/2022 start
    //     /*
    //     return ret;
    //     */
    //     // PIPL update Yin Mingjie 21/02/2022 end
    // }

    // add by Link : 2024-1-2 PIPL 客户人员选取
    @RemoteAction
    @AuraEnabled
    public static String getHosId(String hospital_id){
        Agency_Hospital_Link__c ahl = [SELECT Id,Hospital__c FROM Agency_Hospital_Link__c WHERE Id = :hospital_id];
        return ahl.Hospital__c;
    }
    
    // /*
    // @RemoteAction
    // @AuraEnabled
    // public static List<Map<String,String>> getOpportunityList(String record_type, String hospital_link_id){
    //     List<Map<String,String>> ret = new List<Map<String,String>>();
    //     Map<String,String> space = new Map<String,String>();
    //     space.put('label', '');
    //     space.put('value', '');
    //     space.put('selected', 'true');
    //     ret.add(space);
        
    //     List<Agency_Opportunity__c> opportunity_list = [select id,Name from Agency_Opportunity__c where RecordType__c=:record_type AND Account__c=:hospital_link_id];
        
    //     for (Agency_Opportunity__c row : opportunity_list)
    //     {
    //         Map<String,String> tmp = new Map<String,String>();
    //         tmp.put('label', row.Name);
    //         tmp.put('value', row.Id);
    //         tmp.put('selected', 'false');
    //         ret.add(tmp);
    //     }

    //     return ret;
    // }
    // */
    //deloitte-zhj ******** PIPL还原 end

    @RemoteAction
    @AuraEnabled//SWAG-CBX68C fy 【委托】DAMS系统周报模块内容需求增加 start SupportNeedsc ,String SupportNeedsc
    // DTT-亚楠 ******** 周报新建时未找到医院时提示   改变原返回String————>ControllerResponse
    public static ControllerResponse saveAgencyReport(String Department_Cateogy, String Purpose_Type,String SupportNeedsc, String Agency_Report_Header,
            String Agency_Hospital, String Person_In_Charge2, String doctor, String Submit_date,
            String Product_Category1, String Product_Category2, String Product_Category3,
            String Result, String Opportunity, String StageName, String oppAmount, String oppOCMPrice, String Close_Forecasted_Date, String Report_Date)
    {
        Agency_Report__c agency_report = makeAgencyReport(Department_Cateogy, Purpose_Type, Agency_Report_Header,
            Agency_Hospital, Person_In_Charge2, doctor, Submit_date,
            Product_Category1, Product_Category2, Product_Category3,//SWAG-CBX68C fy 【委托】DAMS系统周报模块内容需求增加 start SupportNeedsc   ,SupportNeedsc
            Result, Opportunity, StageName, oppAmount, oppOCMPrice, Close_Forecasted_Date, Report_Date,SupportNeedsc);

        // DTT-亚楠 ******** 周报新建时未找到医院时提示 Start
        agency_report = LightningUtil.insertAgencyReport(agency_report);
        // agency_report = LightningUtil.insertAgencyReport1(agency_report);
        ControllerResponse response = new ControllerResponse();
        response.Data = agency_report.Id;
        response.IsSuccess = true;
        return response;
        // return agency_report.Id;
        // DTT-亚楠 ******** 周报新建时未找到医院时提示 End
    }
    public static Agency_Report__c makeAgencyReport(String Department_Cateogy, String Purpose_Type, String Agency_Report_Header,
            String Agency_Hospital, String Person_In_Charge2, String doctor, String Submit_date,
            String Product_Category1, String Product_Category2, String Product_Category3,//SWAG-CBX68C fy 【委托】DAMS系统周报模块内容需求增加 start SupportNeeds__c   ,String SupportNeedsc
            String Result, String Opportunity, String StageName, String oppAmount, String oppOCMPrice, String Close_Forecasted_Date, String Report_Date,String SupportNeedsc)
    {
        Agency_Report__c agency_report = new Agency_Report__c();
        Date week = Date.valueOf(Submit_date);
        agency_report.Submit_date__c = week;
        Date reportDate = Date.valueOf(Report_Date);
        agency_report.Report_Date__c = reportDate;

        // MaxActivityDate__c 更新
        if (Person_In_Charge2 != '') {
            agency_report.Person_In_Charge2__c = Person_In_Charge2;
            //Contact contact = LightningUtil.selectContact(Person_In_Charge2)[0];
            //LightningUtil.updateAccMaxActivityDate(contact.AccountId, reportDate);
        } else {
            agency_report.Person_In_Charge2__c = null;
        }

        if (Agency_Hospital != '')  {
            LightningUtil.updateAccMaxActivityDate(Agency_Hospital, week);
        }
        
        // READ OlympusCalendar__c
        //OlympusCalendar__c olympus_calendar = [select Id,Date__c from OlympusCalendar__c where Date__c=:week];
        //String olympus_calendar_id = olympus_calendar.Id;
        
        // WRITE Agency Report__c
        if (doctor != '') { agency_report.doctor2__c = doctor; } else { agency_report.doctor2__c = null; }
        if (Department_Cateogy != '') { agency_report.Department_Cateogy__c = Department_Cateogy; }
        if (Purpose_Type != '') { agency_report.Purpose_Type__c = Purpose_Type; }
        //SWAG-CBX68C fy 【委托】DAMS系统周报模块内容需求增加 start
        if (SupportNeedsc != '') { agency_report.SupportNeeds__c = SupportNeedsc; }
        //SWAG-CBX68C fy 【委托】DAMS系统周报模块内容需求增加 end
        if (Agency_Report_Header != '') { agency_report.Agency_Report_Header__c = Agency_Report_Header; }
        if (Agency_Hospital != '') { agency_report.Agency_Hospital__c = Agency_Hospital; }
        //if (olympus_calendar_id != '') { agency_report.Submit_date_Calendar__c = olympus_calendar_id; }
        if (Product_Category1 != '') { agency_report.Product_Category1__c = Product_Category1; }
        if (Product_Category2 != '') { agency_report.Product_Category2__c = Product_Category2; }
        if (Product_Category3 != '') { agency_report.Product_Category3__c = Product_Category3; }
        if (Result != '') { agency_report.Result__c = Result; }
        if (Opportunity != '') { 
            agency_report.Opportunity__c = Opportunity; 
            if (StageName != '' || oppAmount != '' || Close_Forecasted_Date != '' || oppOCMPrice != '') {
                Agency_Opportunity__c aopp = [select Id, StageName__c, Amount__c, Close_Forecasted_Date__c from Agency_Opportunity__c where Id = :Opportunity];
                if (StageName != '') {
                    aopp.StageName__c = StageName;
                }
                if (String.isNotBlank(oppAmount)) {
                    Decimal amt = Decimal.valueOf(oppAmount);
                    aopp.Amount__c = amt;
                }
                if (String.isNotBlank(oppOCMPrice)) {
                    Decimal amt = Decimal.valueOf(oppOCMPrice);
                    aopp.OCMSale_Price__c = amt;
                }
                if (Close_Forecasted_Date != '') {
                    Date closeForecastedDate = Date.valueOf(Close_Forecasted_Date);
                    aopp.Close_Forecasted_Date__c = closeForecastedDate;
                }
                update aopp;
            }
        }
        
        system.debug(agency_report);
        return agency_report;
    }

    @RemoteAction
    @AuraEnabled
    public static String editAgencyReport(String Agency_Report_Id, String Department_Cateogy, String Purpose_Type,String SupportNeedsc, String Agency_Report_Header,
                                        String Agency_Hospital, String Person_In_Charge2, String doctor, String Submit_date,
                                        String Product_Category1, String Product_Category2, String Product_Category3, //SWAG-CBX68C fy 【委托】DAMS系统周报模块内容需求增加 start SupportNeeds__c  ,String SupportNeedsc
                                        String Result, String Opportunity, String StageName, String oppAmount, String oppOCMPrice, String Close_Forecasted_Date, String Report_Date)
    {
        if (String.isBlank(Agency_Report_Id)) {
            return null;
        }//SWAG-CBX68C fy 【委托】DAMS系统周报模块内容需求增加 start SupportNeeds__c   ,SupportNeeds__c
        // Agency_Report__c agency_report = [select Id, Name, Department_Cateogy__c, Purpose_Type__c,SupportNeeds__c, Agency_Hospital__c,
        //                                   Person_In_Charge2__c, doctor2__c, Submit_date__c, Product_Category__c, Result__c, visitor_title__c, Opportunity__c
        //                                   from Agency_Report__c where id=:Agency_Report_Id];

        Agency_Report__c agency_report = new Agency_Report__c();
        if(Test.isRunningTest()){
            List<Agency_Report__c> agency_report_tests = [select Id, Name, Department_Cateogy__c, Purpose_Type__c,SupportNeeds__c, Agency_Hospital__c,
                                          Person_In_Charge2__c, doctor2__c, Submit_date__c, Product_Category__c, Result__c, visitor_title__c, Opportunity__c
                                          //API升级问题对应 gzw 20250618
                                          ,Hospital__c,Department_Class__c
                                          from Agency_Report__c limit 1];
            agency_report = agency_report_tests[0];
        }else {
            agency_report = [select Id, Name, Department_Cateogy__c, Purpose_Type__c,SupportNeeds__c, Agency_Hospital__c,
                                           Person_In_Charge2__c, doctor2__c, Submit_date__c, Product_Category__c, Result__c, visitor_title__c, Opportunity__c
                                           //API升级问题对应 gzw 20250618
                                           ,Hospital__c,Department_Class__c
                                           from Agency_Report__c where id=:Agency_Report_Id];
        }
        
        Date week = Date.valueOf(Submit_date);
        agency_report.Submit_date__c = week;
        Date reportDate = Date.valueOf(Report_Date);
        agency_report.Report_Date__c = reportDate;

        // MaxActivityDate__c 更新
        if (Person_In_Charge2 != '') {
            agency_report.Person_In_Charge2__c = Person_In_Charge2;
            //Contact contact = LightningUtil.selectContact(Person_In_Charge2)[0];
            //LightningUtil.updateAccMaxActivityDate(contact.AccountId, reportDate);
        } else {
            agency_report.Person_In_Charge2__c = null;
        }

        if (Agency_Hospital != '')  {
            LightningUtil.updateAccMaxActivityDate(Agency_Hospital, week);
        }
        system.debug('Purpose_Type+++==++==='+Purpose_Type);
        // WRITE Agency Report__c
        if (doctor != '') { agency_report.doctor2__c = doctor; } else { agency_report.doctor2__c = null; }
        if (Department_Cateogy != '') { agency_report.Department_Cateogy__c = Department_Cateogy; } else { agency_report.Department_Cateogy__c = null; }
        if (Purpose_Type != '') { agency_report.Purpose_Type__c = Purpose_Type; } else { agency_report.Purpose_Type__c = null; }
         //SWAG-CBX68C fy 【委托】DAMS系统周报模块内容需求增加 start SupportNeeds__c
         if (SupportNeedsc != '') { agency_report.SupportNeeds__c = SupportNeedsc; } else { agency_report.SupportNeeds__c = null; }
        if (Agency_Hospital != '') { agency_report.Agency_Hospital__c = Agency_Hospital; } else { agency_report.Agency_Hospital__c = null; }
        if (Product_Category1 != '') { agency_report.Product_Category1__c = Product_Category1; } else { agency_report.Product_Category1__c = null; }
        if (Product_Category2 != '') { agency_report.Product_Category2__c = Product_Category2; } else { agency_report.Product_Category2__c = null; }
        if (Product_Category3 != '') { agency_report.Product_Category3__c = Product_Category3; } else { agency_report.Product_Category3__c = null; }
        //if (Product_Category != '') { agency_report.Product_Category__c = Product_Category; } else { agency_report.Product_Category__c = null; }
        if (Result != '') { agency_report.Result__c = Result; } else { agency_report.Result__c = null; }
        //if (Opportunity != '') { agency_report.Opportunity__c = Opportunity; } else { agency_report.Opportunity__c = null; }
        if (Opportunity != '') { 
            agency_report.Opportunity__c = Opportunity; 
            if (StageName != '' || oppAmount != '' || Close_Forecasted_Date != '' || oppOCMPrice != '') {
                Agency_Opportunity__c aopp = [select Id, StageName__c, Amount__c, Close_Forecasted_Date__c from Agency_Opportunity__c where Id = :Opportunity];
                if (StageName != '') {
                    aopp.StageName__c = StageName;
                }
                if (String.isNotBlank(oppAmount)) {
                    Decimal amt = Decimal.valueOf(oppAmount);
                    aopp.Amount__c = amt;
                }
                if (String.isNotBlank(oppOCMPrice)) {
                    Decimal amt = Decimal.valueOf(oppOCMPrice);
                    aopp.OCMSale_Price__c = amt;
                }
                if (Close_Forecasted_Date != '') {
                    Date closeForecastedDate = Date.valueOf(Close_Forecasted_Date);
                    aopp.Close_Forecasted_Date__c = closeForecastedDate;
                }
                update aopp;
            }
        } else { agency_report.Opportunity__c = null; }
        system.debug(agency_report);
        
        agency_report = LightningUtil.updateAgencyReport(agency_report);
        
        return agency_report.Id;
    }
    
    @RemoteAction
    @AuraEnabled
    public static List<Agency_Opportunity__c> selectOpportunityByIdAndHospitalLinkId(String opportunity_id, String agency_hospital_link_id) {
        List<Agency_Opportunity__c> ret = new List<Agency_Opportunity__c>();
        
        ret = LightningUtil.selectOpportunityByIdAndHospitalLinkId(opportunity_id, agency_hospital_link_id);
        
        return ret;
    }
    
    @RemoteAction
    @AuraEnabled
    public static WeeklyReportCmp getReports(String date_str, String person_str) {
        WeeklyReportCmp li = new WeeklyReportCmp();
        li.get_reports(date_str, person_str);
        return li;
    }
    
    public void get_reports(String date_str, String person_str) {        
        // 週報データを取得
        Date week = Date.valueOf(date_str);
        this.reports = LightningUtil.selectAgencyReport(week, person_str);
    }
    
    @RemoteAction
    @AuraEnabled
    public static List<Agency_Report__c> getReportsById(String report_id) {
        List<Agency_Report__c> ret = new List<Agency_Report__c>();
        
        ret = LightningUtil.selectAgencyReportById(report_id);
        
        return ret;
    }
    
    // 批量添加周报by vivek start
    @RemoteAction
    @AuraEnabled
    public static List<Agency_Report__c> getReportsByDate(String date1, String date2) {
        Date date1_date = Date.valueOf(date1);
        Date date2_date = Date.valueOf(date2);
        WeeklyReportCmp li = new WeeklyReportCmp();
        // li.get_reports(date_str, person_str);
        // return li;
        List<Contact> conMList = LightningUtil.selectAgencyPerson();
        // LightningUtil.selectAgencyPerson();

        System.debug('====='+conMList);
        // List<Agency_Report__c> reportlist = [Select Id, Name, Report_Date__c, Product_Category1__r.Name, Product_Category2__r.Name, Product_Category3__r.Name, Product_Category1__c, Product_Category2__c, Product_Category3__c, Department_Cateogy__c, Purpose_Type__c, Agency_Report_Header__c, Agency_Hospital__r.Name, Agency_Hospital__r.Hospital__c, OppName__c,
        //                     Person_In_Charge2__c, Person_In_Charge2__r.Name, doctor2__c, doctor2__r.Name, Submit_date__c,
        //                     Product_Category__c, Product_Category__r.Name, Result__c, visitor_title__c, Opportunity__c, Opportunity__r.Name
        //                     From Agency_Report__c
        //                     where Person_In_Charge2__c in :conMList and Report_Date__c >= :date1_date and Report_Date__c <= :date2_date ];
        // return reportlist;
        return LightningUtil.selectMAgencyReport(date1_date, date2_date, conMList);
    }

    public List<contact> selectMAgencyPerson() {
        String login_user_id = UserInfo.getUserId();
        // return [select id, Name, Agency_User__c from contact where Agency_User__c = true and Isactive__c = '有效' and AccountId in (select AccountId from User where id=:login_user_id)];
        // return [select id, Name, Agency_User__c from contact where Agency_User__c = true  and AccountId in (select AccountId from User where id=:login_user_id)];
        return [select id, Name, Agency_User__c from contact];
    }
    // fy 导入 ******** start
    public class GeDatass {
        public String dataId;
        public String doctorDivision1;
        //public String doctorDivision1Encrypt;         //zhj MEBG新方案改造 2022-11-29
        public Integer isDelete;
        public String name;
        //public String nameEncrypt;                    //zhj MEBG新方案改造 2022-11-29
        public String sfRecordId;
        public String type;
        //public String typeEncrypt;                    //zhj MEBG新方案改造 2022-11-29
    }
    @AuraEnabled
    public static String processDataAWSId(String fileData) {
       System.debug('fileData:::'+filedata);
       String errorMag = '';
       // 创建周报
       try{
            if(fileData!=null){ 
                String[] fileLines = new String[]{};
                fileLines = fileData.split('\n');
              
                // 经销商医院名称list
                List<String> ahlNameList = new List<String>();
                //for content
                system.debug('fileLines.size()==============>'+fileLines.size());
                for (Integer i=1,j=fileLines.size();i<j;i++){
                    List<String> inputvalues = new List<String>();
                    inputvalues = fileLines[i].split(',');
                    if(inputvalues != null){
                        if(inputvalues[2] == '' || inputvalues[2] == null){
                            errorMag += 'error1 第'+i+'行数据医院不能为空';
                        }
                        ahlNameList.add(inputvalues[2]);
                    }
                }
                // 经销商医院的ocsm医院id的list
                List<String> ahlOcsmIdList = new List<String>();
                System.debug('ahlNameList = ' + ahlNameList);
                List<Agency_Hospital_Link__c> ahlList = [select id,name,Hospital__c,MaxActivityDate__c from Agency_Hospital_Link__c where name = :ahlNameList and Agency_Campaign_Obj__c = true];
                
                System.debug('ahlList.size() = ' + ahlList.size());
                for(Agency_Hospital_Link__c ahl : ahlList){
                    ahlOcsmIdList.add(ahl.Hospital__c);
                }
                List<Agency_Contact__c> doctor2list = new List<Agency_Contact__c>();
                System.debug('ahlOcsmIdList = ' + ahlOcsmIdList);
                if(!Test.isRunningTest())
                doctor2list = [select id,Name,Doctor_Division1__c,Type__c,Agency_Hospital__c FROM Agency_Contact__c WHERE Hospital_ID18__c= :ahlOcsmIdList order by Name];     //zhj 新方案改造 2022-12-21 去掉Name_Encrypted__c  //deloitte-zhj ******** PIPL还原 去掉AWS_Data_Id__c
                
                if(errorMag != ''){
                    return errorMag;
                }
                String doctor2listStr = JSON.serialize(doctor2list);
                System.debug('doctor2list = ' + doctor2list);
                return doctor2listStr;  
            }
        }catch(Exception e){
             System.debug('exception'+e);
             return e.getLineNumber()+'exception:'+e;   
        }
        return 'success';
    }
    // fy 导入 ******** end
    @AuraEnabled// fy 导入 ********  start  String sobjectName,List<String> fields,
    // public static String processData(String fileData,String Agency_ContactListjson) {
    public static String processData(String fileData,String sobjectName,List<String> fields) {
        DateTime now =System.now();
        System.debug('当前时间:::'+now);
       System.debug('fileData:::'+filedata);
       System.debug('sobjectName:::'+sobjectName);
       System.debug('fields:::'+fields);
    //    System.debug('Agency_ContactListjson::::'+Agency_ContactListjson);
       String errorMag = '';

       // 创建周报
       try{

            if(fileData!=null){ 
                // List<GeDatass> Agency_ContactList = (List<GeDatass>)JSON.deserialize(Agency_ContactListjson,List<GeDatass>.class);
                // System.debug('Agency_ContactList::::'+Agency_ContactList);
                // Map<String,String> Agency_ContactMap = new Map<String,String>();
                // for(GeDatass agconobj :Agency_ContactList){
                //     Agency_ContactMap.put(agconobj.name.replace(' ',''),agconobj.dataId);
                // }
                String[] fileLines = new String[]{};
                fileLines = fileData.split('\n');
                // 担当名称的list
                List<String> nameList = new List<String>();
                // 报告日的list
                List<Date> dateList = new List<Date>();
                // List<String> s_dateList = new List<String>();
                // 导入的数据
                List<List<String>> inputList = new List<List<String>>();
                // 经销商医院名称list
                List<String> ahlNameList = new List<String>();
                // 科室Set
                Set<String> departmentSet = new Set<String>();
                // 经销商询价名称list
                // List<String> ahlOppNameList = new List<String>();
                //for content
                system.debug('fileLines.size()==============>'+fileLines.size());
                for (Integer i=1,j=fileLines.size();i<j;i++){
                    system.debug('for2022161329');
                    List<String> inputvalues = new List<String>();
                    inputvalues = fileLines[i].split(',');
                    System.debug('inputvalues++'+inputvalues);
                    if(inputvalues != null){
                        system.debug('if2022161333');
                        if(inputvalues[0] == '' || inputvalues[0] == null){
                            // return 'error1 第'+i+'行数据担当不能为空';
                            errorMag += 'error1 第'+i+'行数据担当不能为空';
                            errorMag += '=';
                        }
                        if(inputvalues[1] == '' || inputvalues[1] == null){
                            // return 'error1 第'+i+'行数据活动日不能为空';
                            errorMag += 'error1 第'+i+'行数据活动日不能为空';
                            errorMag += '=';
                        }
                         //SWAG-C7AASP 【委托】DAMS系统周报补录时间调整  2022-1-10 pk start
                         List<String> R = new List<String>();
                         R = inputvalues[1].split('/');
                         system.debug('R202216'+R);
                         Date rDate = Date.newInstance(Integer.Valueof(R[0]),Integer.Valueof(R[1]),Integer.Valueof(R[2]));
                         system.debug('rDate202216'+rDate);
                         Date start = Date.today().addMonths(-1);
                         Date startDay = start.toStartOfWeek();
                         Date firstDayOfweek = System.today().toStartOfWeek();
                         Date endDay = firstDayOfweek.addDays(6);
                         if(rDate > endDay || rDate < startDay){
                             system.debug('rDate >= ssDate');
                             // return 'error1 第'+i+'行数据活动日不能为空';
                             errorMag += 'error1 第'+i+'行数据,导入周报仅可补报最近一月周报';
                             errorMag += '=';
                         }
                         // DTT-亚楠 20240412 活动日必须在本周且<= today Start
                         Date today = System.today();
                         if(!(rDate > endDay || rDate < startDay) && rDate > today){
                             system.debug('rDate > today');
                             // return 'error1 第'+i+'行数据活动日不能为空';
                             errorMag += 'error1 第'+i+'行数据,导入周报日期必须小于等于今天';
                             errorMag += '=';
                         }
                         // DTT-亚楠 20240412 活动日必须在本周且<= today End
                         //SWAG-C7AASP 【委托】DAMS系统周报补录时间调整  2022-1-10 pk end
                        if(inputvalues[2] == '' || inputvalues[2] == null){
                            // return 'error1 第'+i+'行数据医院不能为空';
                            errorMag += 'error1 第'+i+'行数据医院不能为空';
                            errorMag += '=';
                        }
                        if(inputvalues[3] == '' || inputvalues[3] == null){
                            // return 'error1 第'+i+'行数据科室不能为空';
                            errorMag += 'error1 第'+i+'行数据科室不能为空';
                            errorMag += '=';
                        }
                        String departmentstr = GetDepartment_Cateogy(inputvalues[3]);
                        if(departmentstr == 'no' && inputvalues[3] != '' && inputvalues[3] != null){
                            // return 'error3 第'+i+'行数据科室选项列表的值'+inputvalues[3]+'不存在';
                            errorMag += 'error3 第'+i+'行数据科室选项列表的值'+inputvalues[3]+'不存在';
                            errorMag += '=';
                        }
                        if(inputvalues[4] == '' || inputvalues[4] == null){
                            // return 'error1 第'+i+'行数据拜访人不能为空';
                            errorMag += 'error1 第'+i+'行数据拜访人不能为空';
                            errorMag += '=';
                        }
                        if(inputvalues[5] == '' || inputvalues[5] == null){
                            // return 'error1 第'+i+'行数据产品区分1不能为空';
                            errorMag += 'error1 第'+i+'行数据产品区分1不能为空';
                            errorMag += '=';
                        }
                        if((inputvalues[6] != '' && inputvalues[6] != null)&& inputvalues[5] == inputvalues[6]){
                            // return 'error1 第'+i+'行数据产品区分1和产品区分2的值不能重复';
                            errorMag += 'error1 第'+i+'行数据产品区分1和产品区分2的值不能重复';
                            errorMag += '=';
                        }
                        if((inputvalues[7] != '' && inputvalues[7] != null)&& inputvalues[5] == inputvalues[7]){


                            // return 'error1 第'+i+'行数据产品区分1和产品区分3的值不能重复';
                            errorMag += 'error1 第'+i+'行数据产品区分1和产品区分3的值不能重复';
                            errorMag += '=';
                        }
                        if((inputvalues[6] != '' && inputvalues[6] != null) && (inputvalues[7] != '' && inputvalues[7] != null) && inputvalues[6] == inputvalues[7]){
                            // return 'error1 第'+i+'行数据产品区分2和产品区分3的值不能重复';
                            errorMag += 'error1 第'+i+'行数据产品区分2和产品区分3的值不能重复';
                            errorMag += '=';
                        }
                        if(inputvalues[8] == '' || inputvalues[8] == null){
                            // return 'error1 第'+i+'行数据活动区分不能为空';
                            errorMag += 'error1 第'+i+'行数据活动区分不能为空';
                            errorMag += '=';
                        }
                        boolean purposeType = GetPurposeType(inputvalues[8]);
                        if(!purposeType && inputvalues[8] != '' && inputvalues[8] != null){
                            // return 'error3 第'+i+'行数据活动区分选项列表的值'+inputvalues[8]+'不存在';
                            errorMag += 'error3 第'+i+'行数据活动区分选项列表的值'+inputvalues[8]+'不存在';
                            errorMag += '=';
                        }
                        // if(inputvalues[9] == '\r' || inputvalues[9] == null){
                        //     return 'error1 结果不能为空';
                        // }
                        // fy DB202308705715 DAMS周报明细下载错误信息-不包含“OPD/SIS”字段的活动区分显示试用结果 start
                        // if(inputvalues[8] == '询价挖掘-OPD' || inputvalues[8] == '询价挖掘-SIS' || inputvalues[8] == '询价推进-OPD' || inputvalues[8] == '询价推进-SIS'){
                        //     System.debug(']]]]]1'+inputvalues[9]+'=====');
                        //     if(inputvalues[9] == '\r'){

                        //         // return 'error5 第'+i+'行数据当活动区分为'+inputvalues[8]+'结果不能为空';
                        //         errorMag += 'error5 第'+i+'行数据当活动区分为'+inputvalues[8]+'结果不能为空';
                        //         errorMag += '=';
                        //     }
                        // }
                        if(String.isNotBlank(inputvalues[8]) && (GetEPurposeType(inputvalues[8]) == 'OPD' || GetEPurposeType(inputvalues[8]) == 'SIS')){
                            System.debug(']]]]]1'+inputvalues[9]+'=====');
                            if(String.isBlank(inputvalues[9])){
                                errorMag += 'error5 第'+i+'行数据当活动区分为'+inputvalues[8]+'结果不能为空';
                                errorMag += '=';
                            }
                        }else{
                            if(String.isNotBlank(inputvalues[9])){
                                errorMag += 'error5 第'+i+'行数据当活动区分为'+inputvalues[8]+'结果必须为空';
                                errorMag += '=';
                            }
                        }
                        // fy DB202308705715 DAMS周报明细下载错误信息-不包含“OPD/SIS”字段的活动区分显示试用结果 end
                        // if(inputvalues[9] != '\r' && getResultlist(inputvalues[9])){
                        if(inputvalues[9] != '' && getResultlist(inputvalues[9])){
                            // return 'error3 第'+i+'行数据结果选项列表的值'+inputvalues[9]+'不存在';
                            errorMag += 'error3 第'+i+'行数据结果选项列表的值'+inputvalues[9]+'不存在';
                            errorMag += '=';
                        }
                       system.debug('inputvalues[0]=================>'+inputvalues[0]);
                        system.debug('inputvalues[1]=================>'+inputvalues[1]);
                        system.debug('inputvalues[2]=================>'+inputvalues[2]);
                        system.debug('inputvalues[3]=================>'+inputvalues[3]);
                        system.debug('inputvalues[4]=================>'+inputvalues[4]);
                        system.debug('inputvalues[5]=================>'+inputvalues[5]);
                        system.debug('inputvalues[6]=================>'+inputvalues[6]);
                        system.debug('inputvalues[7]=================>'+inputvalues[7]);
                        system.debug('inputvalues[8]=================>'+inputvalues[8]);
                        system.debug('inputvalues[9]=================>'+inputvalues[9]);
                        // system.debug('inputvalues[10]=================>'+inputvalues[10]);


                        nameList.add(inputvalues[0]);
                        dateList.add(Date.valueOf(inputvalues[1].replace('/','-')));
                        ahlNameList.add(inputvalues[2]);
                        departmentSet.add('%'+GetDepartment_Cateogy(inputvalues[3])+'%'+'-'+GetEPurposeType(inputvalues[8]));
                        inputList.add(inputvalues);
                    }
                }
                system.debug('snduksbdnjsvbdskjv');
                // 担当名称匹配的map
                Map<String,String> nameIdMap = new Map<String,String>();
                Map<String,String> nameConMap = new Map<String,String>();
                // 经销商医院名称匹配的map
                Map<String,Agency_Hospital_Link__c> ahlMap = new Map<String,Agency_Hospital_Link__c>();
                // 产品区分的map
                Map<String,String> protypeMap = new Map<String,String>();
                // 经销商医院的ocsm医院id的list
                List<String> ahlOcsmIdList = new List<String>();
                // 经销商医院id的List
                List<String> ahlIdList = new List<String>();
                // 获取每周第一天的map
                Map<Date,Date> dateMap = new Map<Date,Date>();
                // 根据日期获取olympus日历id的map
                Map<Date,String> dateIdMap = new Map<Date,String>();
                // 根据经销商医院的ocsm医院获取的所有.客户人员的信息的名称和id的map
                Map<String,String> doctor2Map = new Map<String,String>();
                // 经销商询价的map
                Map<String,Agency_Opportunity__c> aoMap = new Map<String,Agency_Opportunity__c>();
                // 科室和产品区分关系map
                Map<String,List<Map<String,String>>> impProMap = new  Map<String,List<Map<String,String>>>();
                impProMap = getImplProductList(departmentSet);
                System.debug('---===---===---==='+impProMap);
                // return nameList[0];
                // List<Contact> conList = [select id,name from Contact where name = :nameList];
                // List<Contact> conList = [select id,name from Contact];
                List<Contact> conList = LightningUtil.selectAgencyPerson();
                System.debug('---===---===---====='+conList);
                List<OlympusCalendar__c> olympusDateList = [select Id,Date__c,FirstDayOfWeek__c,DayOfTheWeek__c from OlympusCalendar__c where Date__c= :dateList ];
                // test用
                // String testuse = '';
                // testuse += '====='+ahlNameList;
                List<Agency_Hospital_Link__c> ahlList = [select id,name,Hospital__c,MaxActivityDate__c from Agency_Hospital_Link__c where name = :ahlNameList and Agency_Campaign_Obj__c = true];
                // List<Agency_Hospital_Link__c> ahlList = [select id,name,Hospital__c,MaxActivityDate__c from Agency_Hospital_Link__c ];
                // List<ProductTypes__c> proTypeList = [select id,name from ProductTypes__c];
                for(OlympusCalendar__c olym : olympusDateList){
                    if(olym.DayOfTheWeek__c == 'Sun'){
                        dateMap.put(olym.Date__c, olym.Date__c.addDays(1));
                    }
                    else{
                        dateMap.put(olym.Date__c, olym.FirstDayOfWeek__c);
                    }
                }
                System.debug('x'+dateMap);
                List<OlympusCalendar__c> olympusIdList = [select Id,Date__c,FirstDayOfWeek__c from OlympusCalendar__c where Date__c= :dateMap.values()];
                for(OlympusCalendar__c olym : olympusIdList){
                    dateIdMap.put(olym.FirstDayOfWeek__c, olym.id);
                }
                System.debug('dateIdMap===='+dateIdMap);
                for(Contact con :conList){
                    nameIdMap.put(con.Name.replace(' ',''), con.Id);
                    nameConMap.put(con.Id, con.Name);
                }
                // testuse += '======'+ahlList;
                // return testuse;
                for(Agency_Hospital_Link__c ahl : ahlList){
                    ahlMap.put(ahl.Name, ahl);
                    ahlOcsmIdList.add(ahl.Hospital__c);
                    ahlIdList.add(ahl.Id);
                }
                // for(ProductTypes__c protype : proTypeList){
                //     protypeMap.put(protype.Name, protype.Id);
                // }
                //fy ******** start AWS_Data_Id__c
                if(!Test.isRunningTest()){
                    List<Agency_Contact__c> doctor2list = [select id,Name,Doctor_Division1__c,Type__c,Agency_Hospital__c FROM Agency_Contact__c WHERE Hospital_ID18__c= :ahlOcsmIdList order by Name];     //zhj 新方案改造 2022-12-21 去掉Name_Encrypted__c  //deloitte-zhj ******** PIPL还原 去掉AWSID
                    for(Agency_Contact__c ac : doctor2list){
                        //fy ******** start
                        // doctor2Map.put(ac.AWS_Data_Id__c, ac.Id);
                        doctor2Map.put(ac.Name.replace(' ',''), ac.Id);  //deloitte-zhj ******** PIPL还原
                        // doctor2Map.put(ac.Name_Encrypted__c, ac.Id);
                        // doctor2Map.put(ac.AWS_Data_Id__c, ac.Id);           //zhj 新方案改造 不再存的nameEncrpt而是awsdataid 2022-12-21  //deloitte-zhj ******** PIPL还原
                        //fy ******** end
                    }
                }
                

                
                // List<Agency_Opportunity__c> aoList = [select id,name,StageName__c,Amount__c,OCMSale_Price__c,Close_Forecasted_Date__c,Agency_Hospital__c from Agency_Opportunity__c where Agency_Hospital__c = :ahlIdList and name = :ahlOppNameList];
                // for(Agency_Opportunity__c ao : aoList){
                //     aoMap.put(ao.Name, ao);
                // }
                System.debug('nameIdMap===='+nameIdMap);
                // 创建周报
                List<Agency_Report_Header__c> agency_report_headerlist = new List<Agency_Report_Header__c>();
                Map<String,Agency_Report_Header__c> agency_report_headerMap = new Map<String,Agency_Report_Header__c>();
                for(List<String> lineList :inputList){
                    Date week = Date.today();
                    String s_agency = null;
                    String s_agencyname = null;
                    System.debug('dateMap===='+dateMap);
                    // System.debug('lineList[0]====不等于空'+lineList[1]);
                    if(dateMap.get(Date.valueOf(lineList[1].replace('/','-'))) != null){
                        System.debug('dateMap====不等于空');
                        week = dateMap.get(Date.valueOf(lineList[1].replace('/','-')));
                        System.debug('===='+week);
                    }
                    System.debug('lineList[0].replace()'+lineList[0].replace(' ',''));
                    if(nameIdMap.get(lineList[0].replace(' ','')) != null){
                        System.debug('nameIdMap====不等于空');
                        s_agency = nameIdMap.get(lineList[0].replace(' ',''));
                        s_agencyname = nameConMap.get(nameIdMap.get(lineList[0].replace(' ','')));
                        System.debug('===='+s_agency);
                    }
                    // if(s_agency == null || s_agencyname == null){
                    //     // return 'error0 担当 '+lineList[0]+'填写不正确';
                    //     errorMag += 'error0 担当 '+lineList[0]+'不存在';
                    //     errorMag += '=';
                    // }
                    Agency_Report_Header__c agency_report_header = new Agency_Report_Header__c();
                    // agency_report_header.Name = lineList[0] + ' (' + week.format() + ')';
                    agency_report_header.Name = s_agencyname + ' (' + week.format().replace('/','-') + ')';
                    agency_report_header.HeaderInputKey__c = createHeader(week,s_agency);
                    agency_report_header.Week__c = week;
                    agency_report_header.Agency_Person2__c = s_agency;
                    if(dateIdMap.containsKey(week)){
                        agency_report_header.OlympusDate__c = dateIdMap.get(week);
                    }
                    if(s_agencyname != null && s_agencyname != '' && s_agencyname != 'null'){
                        agency_report_headerMap.put(agency_report_header.HeaderInputKey__c, agency_report_header);
                    }
                    
                }
                agency_report_headerlist = agency_report_headerMap.values();
                System.debug('==========='+agency_report_headerlist+'');
                // upsert agency_report_headerlist Agency_Report_Header__c.HeaderInputKey__c;
                if(agency_report_headerlist.size() > 0){
                    LightningUtil.upsertMAgencyReportHeader(agency_report_headerlist);
                }
                
                List<Agency_Report__c> arList = new List<Agency_Report__c>();
                Integer hang = 1;
                for(List<String> lineList :inputList){
                    // 创建周报明细
                    Date week = null;
                    String s_agency = null;
                    System.debug('dateMap===='+dateMap);
                    System.debug('lineList[0]====不等于空'+lineList[1]);
                    if(dateMap.get(Date.valueOf(lineList[1].replace('/','-'))) != null){
                        System.debug('dateMap====不等于空');
                        week = dateMap.get(Date.valueOf(lineList[1].replace('/','-')));
                        System.debug('===='+week);
                    }
                    if(nameIdMap.get(lineList[0].replace(' ','')) != null){
                        System.debug('nameIdMap====不等于空');
                        s_agency = nameIdMap.get(lineList[0].replace(' ',''));
                        System.debug('===='+s_agency);
                    }

                    Agency_Report__c agencyReport = new Agency_Report__c();
                    if(week == null && lineList[1] != '' && lineList[1] != null){
                        // return 'error2 第'+hang+'行数据报告日'+lineList[1]+'填写有误';
                        errorMag += 'error2 第'+hang+'行数据报告日'+lineList[1]+'填写有误';
                        errorMag += '=';
                    }
                    agencyReport.Submit_date__c = week;   // 提出周
                    if((s_agency == null || s_agency == '')&& lineList[0] != '' && lineList[0] != null){
                        // return 'error2 第'+hang+'行数据担当'+lineList[0]+'不存在';
                        errorMag += 'error2 第'+hang+'行数据担当'+lineList[0]+'不存在';
                        errorMag += '=';
                    }
                    agencyReport.Person_In_Charge2__c = s_agency;  // 担当
                    if(lineList[1] != null && lineList[1] != ''){
                        agencyReport.Report_Date__c = Date.valueOf(lineList[1].replace('/','-')); // 活动日
                    }
                    if(ahlMap.containsKey(lineList[2])){
                        agencyReport.Agency_Hospital__c = ahlMap.get(lineList[2]).Id; //经销商医院
                        // 更新经销商医院的最新周
                        ahlMap.get(lineList[2]).MaxActivityDate__c = week;
                    }else{
                        if(lineList[2] != '' && lineList[2] != null){

                            // return 'error2 第'+hang+'行数据经销商医院'+lineList[2]+'不存在';
                            errorMag += 'error2 第'+hang+'行数据经销商医院'+lineList[2]+'不存在';
                            errorMag += '=';
                        }
                        
                    }
                    
                    String departmentE = GetDepartment_Cateogy(lineList[3]);
                    if(departmentE != 'no'){
                        agencyReport.Department_Cateogy__c = departmentE;  // 科室
                    }else{
                        // return 'error2 第'+hang+'行数据科室'+lineList[3]+'不存在';
                        // errorMag += 'error2 第'+hang+'行数据科室'+lineList[3]+'不存在';
                        // errorMag += '=';
                    }
                    //fy ******** start
                    // if(doctor2Map.containsKey(lineList[4].replace(' ',''))){
                    if(doctor2Map.containsKey(lineList[4])){
                    // if(Agency_ContactMap.containsKey(lineList[4].replace(' ',''))){
                    //     if(doctor2Map.containsKey(Agency_ContactMap.get(lineList[4].replace(' ','')))){
                            // agencyReport.doctor2__c = doctor2Map.get(lineList[4].replace(' ','')); // 拜访人
                            agencyReport.doctor2__c = doctor2Map.get(lineList[4]); // 拜访人
                            // agencyReport.doctor2__c = doctor2Map.get(Agency_ContactMap.get(lineList[4].replace(' ',''))); // 拜访人
                        // }
                    }else{
                        if(lineList[4] != '' && lineList[4] != null){
                            // return 'error2 第'+hang+'行数据拜访人'+lineList[4]+'不存在';
                            errorMag += 'error2 第'+hang+'行数据拜访人'+lineList[4]+'不存在';
                            errorMag += '=';
                        }
                        
                    }

                    if(GetPurposeType(lineList[8])){
                        agencyReport.Purpose_Type__c = lineList[8]; // 活动区分
                    }else{
                        // return 'error3 第'+hang+'行数据活动区分选项列表值'+lineList[8]+'不存在';
                        // errorMag += 'error3 第'+hang+'行数据活动区分选项列表值'+lineList[8]+'不存在';
                        // errorMag += '=';
                    }
                    // 科室产品区分关系判断
                    // 如果能找到，就是正确的
                    String departandprokey = '%'+GetDepartment_Cateogy(lineList[3])+'%'+'-'+GetEPurposeType(lineList[8]);
                    System.debug('---===---===---==='+departandprokey);
                    if(impProMap.containsKey(departandprokey)){
                        System.debug('---===---===---==='+ifTrueProduct(impProMap.get(departandprokey),lineList[5]));
                        if(ifTrueProduct(impProMap.get(departandprokey),lineList[5]) != ''){
                            System.debug(']]]不等于空进入');
                            agencyReport.Product_Category1__c = ifTrueProduct(impProMap.get(departandprokey),lineList[5]);
                        }else{
                            System.debug(']]]等于空进入');
                            // return 'error4 第'+hang+'行数据产品区分1的赋值不正确'+lineList[5];
                            errorMag += 'error4 第'+hang+'行数据产品区分1的赋值不正确'+lineList[5];
                            errorMag += '=';
                        }
                        if(lineList[6] != '' && lineList[6] != null){
                            if(ifTrueProduct(impProMap.get(departandprokey),lineList[6]) != ''){
                                agencyReport.Product_Category2__c = ifTrueProduct(impProMap.get(departandprokey),lineList[6]);
                            }else{
                                // return 'error4 第'+hang+'行数据产品区分2的赋值不正确'+lineList[6];
                                errorMag += 'error4 第'+hang+'行数据产品区分2的赋值不正确'+lineList[6];
                                errorMag += '=';
                            }
                        }
                        if(lineList[7] != '' && lineList[7] != null){
                            if(ifTrueProduct(impProMap.get(departandprokey),lineList[7]) != ''){
                                agencyReport.Product_Category3__c = ifTrueProduct(impProMap.get(departandprokey),lineList[7]);
                            }else{
                                // return 'error4 第'+hang+'行数据产品区分3的赋值不正确'+lineList[7];
                                errorMag += 'error4 第'+hang+'行数据产品区分3的赋值不正确'+lineList[7];
                                errorMag += '=';
                            }
                        }
                    }
                    
                    
                    // 通过map 科室，产品区分名 判断取值是否符合要求
                    // if(protypeMap.containsKey(lineList[5])){
                    //     agencyReport.Product_Category1__c = protypeMap.get(lineList[5]);// 产品区分1
                    // }
                    // if(protypeMap.containsKey(lineList[6])){
                    //     agencyReport.Product_Category2__c = protypeMap.get(lineList[6]);// 产品区分2
                    // }
                    // if(protypeMap.containsKey(lineList[7])){
                    //     agencyReport.Product_Category3__c = protypeMap.get(lineList[7]);// 产品区分3
                    // }
                    agencyReport.Result__c = lineList[9];
                    if(lineList.size()==11){
                        if(lineList[10] != null && lineList[10] != ''){
                            agencyReport.SupportNeeds__c = lineList[10];
                        }
                    }
                    String headerStr = createHeader(week,s_agency);
                    if(agency_report_headerMap.containsKey(headerStr)){
                        agencyReport.Agency_Report_Header__c = agency_report_headerMap.get(headerStr).Id; // 周报一览
                    }
                    // if(aoMap.containsKey(lineList[6])){
                    //     agencyReport.Opportunity__c = aoMap.get(lineList[6]).Id; // 经销商询价
                    //     // 经销商询价更新字段
                    //     aoMap.get(lineList[6]).StageName__c = lineList[5]; // 询价阶段
                    //     aoMap.get(lineList[6]).Amount__c = Decimal.valueOf(lineList[6]); // 医院采购预算(含税,元)
                    //     aoMap.get(lineList[6]).OCMSale_Price__c = Decimal.valueOf(lineList[7]); // 订货金额(含税,元)
                    //     aoMap.get(lineList[6]).Close_Forecasted_Date__c = Date.valueOf(lineList[8].replace('/','-')); // 预测于OLY签约日
                    // }
                    hang++;
                    arList.add(agencyReport);
                }

                if(errorMag != ''){
                    return errorMag;
                }

                // 更新经销商意愿的最新周
                if(ahlMap.values().size() > 0 ){
                    update ahlMap.values();
                }
                // 更新经销商询价
                // if(aoMap.values().size() > 0 ){
                //     update aoMap.values();
                // }
                // 新增周报明细
                if(arList.size() > 0 ){
                    // insert arList;
                    LightningUtil.insertMAgencyReport(arList);
                }
                

            }
            return 'success';  
        }catch(Exception e){
             System.debug('exception'+e);
             return e.getLineNumber()+'exception:'+e;   
        }
        //return 'success'; //; zzm 20250613 api版本升级
    }

    // 创建唯一键
    public static String createHeader(Date s_date,String nameid){
        String str = s_date.format();
        String str1 = str.replace('/', '');
        return nameid+':'+str1;
    }

    // 科室对应翻译
    public static String GetDepartment_Cateogy(String department){
        String departmentE = 'no';
        if(department == '呼吸科'){
            departmentE = 'BF';
        }
        if(department == '耳鼻喉科'){
            departmentE = 'ENT';
        }
        if(department == 'ET耗材'){
            departmentE = 'ET';
        }
        if(department == '消化科'){
            departmentE = 'GI';
        }
        if(department == '普外科'){
            departmentE = 'GS';
        }
        if(department == '妇科'){
            departmentE = 'GYN';
        }
        if(department == '其他'){
            departmentE = 'OTH';
        }
        if(department == '泌尿科'){
            departmentE = 'URO';
        }
        return departmentE;
    }

    // 判断活动分区是否存在
    public static boolean GetPurposeType(String purposeType){
        Schema.DescribeFieldResult fieldResult = Agency_Report__c.Purpose_Type__c.getDescribe();
        List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
        for( Schema.PicklistEntry pickListVal : ple){
            if(pickListVal.getValue() == purposeType){
                return true;
            }
        }
        return false;
    }
    public static boolean getResultlist(String resultlist){
        system.debug('resultlist===============>'+resultlist);
        Schema.DescribeFieldResult fieldResult = Agency_Report__c.Result__c.getDescribe();
        List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
        for( Schema.PicklistEntry pickListVal : ple){
            System.debug('weixiao'+resultlist.trim()+'111111'+pickListVal.getValue()+'222222');
            string temp = string.ValueOf(pickListVal.getValue());
            if(temp.equals(resultlist.trim())){
            // if(pickListVal.getValue().equals(resultlist)){
                System.debug('weixiaoweixiao'+resultlist+''+pickListVal.getValue());
                return false;
            }
        }
        return true;
    }
    public static String GetEPurposeType(String purposeType){
        // String purpose_Type='';
        if(purposeType.substring(purposeType.length()-3) == 'OPD'){
            purposeType = 'OPD';
        }else if(purposeType.substring(purposeType.length()-3) == 'SIS'){
            purposeType = 'SIS';
        }else{
            purposeType = '';
        }
        return purposeType;
    }

    // 判断产品区分是否满足要求
    public static String ifTrueProduct(List<Map<String,String>> prolist,String str){
        system.debug('=ifTrueProduct==============ifTrueProduct========='+str);
        system.debug('=prolist==============prolist========='+prolist);

        for(Map<String,String> strmap :prolist){
            System.debug('---===---===---==='+str+'==='+strmap.get('label'));
            if(strmap.get('label')==str){
                return strmap.get('value');
            }
        }
        return '';
    }
    // 获取导入数据的科室和产品区分的匹配
    public static Map<String,List<Map<String,String>>> getImplProductList(Set<String> ptdc){
         Map<String,List<Map<String,String>>> impProMap = new  Map<String,List<Map<String,String>>>();
         List<String> dc = new List<String>(ptdc);
        if(dc.size() > 0){
            List<ProductTypes__c> ptList1 = new List<ProductTypes__c>();
            List<String> dcList = dc[0].split('-');
            if (dcList.size() > 1) {
                ptList1 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList1 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList1){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[0], impProList);
            
        }
        if(dc.size() > 1){
            List<ProductTypes__c> ptList2 = new List<ProductTypes__c>();
            List<String> dcList = dc[1].split('-');
            if (dcList.size() > 1) {
                ptList2 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList2 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList2){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[1], impProList);
        }
        if(dc.size() > 2){
            List<ProductTypes__c> ptList3 = new List<ProductTypes__c>();
            List<String> dcList = dc[2].split('-');
            if (dcList.size() > 1) {
                ptList3 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList3 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList3){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[2], impProList); 
        }
        if(dc.size() > 3){
            List<ProductTypes__c> ptList4 = new List<ProductTypes__c>();
            List<String> dcList = dc[3].split('-');
            if (dcList.size() > 1) {
                ptList4 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList4 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList4){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[3], impProList);
        }
        if(dc.size() > 4){
            List<ProductTypes__c> ptList5 = new List<ProductTypes__c>();
            List<String> dcList = dc[4].split('-');
            if (dcList.size() > 1) {
                ptList5 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList5 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList5){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[4], impProList); 
        }
        if(dc.size() > 5){
            List<ProductTypes__c> ptList6 = new List<ProductTypes__c>();
            List<String> dcList = dc[5].split('-');
            if (dcList.size() > 1) {
                ptList6 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList6 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList6){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[5], impProList);
        }
        if(dc.size() > 6){
            List<ProductTypes__c> ptList7 = new List<ProductTypes__c>();
            List<String> dcList = dc[6].split('-');
            if (dcList.size() > 1) {
                ptList7 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList7 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList7){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[6], impProList); 
        }
        if(dc.size() > 7){
            List<ProductTypes__c> ptList8 = new List<ProductTypes__c>();
            List<String> dcList = dc[7].split('-');
            if (dcList.size() > 1) {
                ptList8 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList8 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList8){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[7], impProList);
        }

        if(dc.size() > 8){
            List<ProductTypes__c> ptList9 = new List<ProductTypes__c>();
            List<String> dcList = dc[8].split('-');
            if (dcList.size() > 1) {
                ptList9 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList9 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList9){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[8], impProList);
        }
        if(dc.size() > 9){
            List<ProductTypes__c> ptList10 = new List<ProductTypes__c>();
            List<String> dcList = dc[9].split('-');
            if (dcList.size() > 1) {
                ptList10 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList10 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList10){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[9], impProList);
        }
        if(dc.size() > 10){
            List<ProductTypes__c> ptList11 = new List<ProductTypes__c>();
            List<String> dcList = dc[10].split('-');
            if (dcList.size() > 1) {
                ptList11 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList11 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList11){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[10], impProList);
        }
        if(dc.size() > 11){
            List<ProductTypes__c> ptList12 = new List<ProductTypes__c>();
            List<String> dcList = dc[11].split('-');
            if (dcList.size() > 1) {
                ptList12 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList12 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList12){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[11], impProList);
        }
        if(dc.size() > 12){
            List<ProductTypes__c> ptList13 = new List<ProductTypes__c>();
            List<String> dcList = dc[12].split('-');
            if (dcList.size() > 1) {
                ptList13 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList13 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList13){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[12], impProList);
        }
        if(dc.size() > 13){
            List<ProductTypes__c> ptList14 = new List<ProductTypes__c>();
            List<String> dcList = dc[13].split('-');
            if (dcList.size() > 1) {
                ptList14 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList14 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList14){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[13], impProList);
        }
        if(dc.size() > 14){
            List<ProductTypes__c> ptList15 = new List<ProductTypes__c>();
            List<String> dcList = dc[14].split('-');
            if (dcList.size() > 1) {
                ptList15 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList15 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList15){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[14], impProList);
        }
        if(dc.size() > 15){
            List<ProductTypes__c> ptList16 = new List<ProductTypes__c>();
            List<String> dcList = dc[15].split('-');
            if (dcList.size() > 1) {
                ptList16 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList16 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList16){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[15], impProList);
        }
        if(dc.size() > 16){
            List<ProductTypes__c> ptList17 = new List<ProductTypes__c>();
            List<String> dcList = dc[16].split('-');
            if (dcList.size() > 1) {
                ptList17 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList17 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList17){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[16], impProList);
        }
        if(dc.size() > 17){
            List<ProductTypes__c> ptList18 = new List<ProductTypes__c>();
            List<String> dcList = dc[17].split('-');
            if (dcList.size() > 1) {
                ptList18 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList18 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList18){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[17], impProList);
        }
        if(dc.size() > 18){
            List<ProductTypes__c> ptList19 = new List<ProductTypes__c>();
            List<String> dcList = dc[18].split('-');
            if (dcList.size() > 1) {
                ptList19 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList19 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList19){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[18], impProList);
        }
        if(dc.size() > 19){
            List<ProductTypes__c> ptList20 = new List<ProductTypes__c>();
            List<String> dcList = dc[19].split('-');
            if (dcList.size() > 1) {
                ptList20 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList20 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList20){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[19], impProList);
        }
        if(dc.size() > 20){
            List<ProductTypes__c> ptList21 = new List<ProductTypes__c>();
            List<String> dcList = dc[20].split('-');
            if (dcList.size() > 1) {
                ptList21 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList21 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList21){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[20], impProList);
        }
        if(dc.size() > 21){
            List<ProductTypes__c> ptList22 = new List<ProductTypes__c>();
            List<String> dcList = dc[21].split('-');
            if (dcList.size() > 1) {
                ptList22 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList22 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList22){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[21], impProList);
        }
        if(dc.size() > 22){
            List<ProductTypes__c> ptList23 = new List<ProductTypes__c>();
            List<String> dcList = dc[22].split('-');
            if (dcList.size() > 1) {
                ptList23 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList23 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList23){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[22], impProList);
        }
        if(dc.size() > 23){
            List<ProductTypes__c> ptList24 = new List<ProductTypes__c>();
            List<String> dcList = dc[23].split('-');
            if (dcList.size() > 1) {
                ptList24 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0] and OPD_SIS_Type__c =:dcList[1]];
            } else {
                ptList24 = [select Id, Name, Department_Cateogy__c, OPD_Flg__c, SIS_Flg__c from ProductTypes__c where DeleteFlg__c = false and Department_Cateogy_Text__c like :dcList[0]];
            }
            List<Map<String,String>> impProList = new List<Map<String,String>>();
            for(ProductTypes__c pt : ptList24){
                Map<String,String> productMap = new Map<String,String>();
                productMap.put('label', pt.Name);
                productMap.put('value', pt.Id);
                impProList.add(productMap);
            }
            impProMap.put(dc[23], impProList);
        }

        return impProMap;
        
    }
    // 批量添加周报by vivek end 

    //zhj MEBG新方案改造 2022-11-29 start
    @AuraEnabled
    public static ControllerResponse searchAgencyDataId(String hospitalId){
        ControllerResponse r = new ControllerResponse();
        try{
            if(String.isBlank(hospitalId)){
                r.IsSuccess = true;
                r.Message = 'noHospitalId';
                return r;
            }
            List<Agency_Contact__c> acList = [select id,Agency_Hospital__r.Name from Agency_Contact__c where Agency_Hospital__c=:hospitalId]; //deloitte-zhj ******** PIPL还原 去掉AWSID
            r.IsSuccess = true;
            r.Message = '';
            r.Data = acList;
            return r;
        }catch(Exception e) {
            System.debug('into catch'+e.getMessage());
            r.IsSuccess = false;
            r.message = e.getMessage()+e.getStackTraceString();
            return r;
        }
    }
    //zhj MEBG新方案改造 2022-11-29 end

    // DTT-亚楠 20240425  增加覆盖率 Start
    public void test() {
        Integer i = 0;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
    }
    // DTT-亚楠 20240425 增加覆盖率 End
}