global class UpOpportunityTradeBatch implements Database.Batchable<sObject> {
   
   String query;

   Boolean IsNeedExecute = false; // 2021-03-05  mzy  WLIG-BYHD79  SFDC环境batch合并调查  是否符合执行条件
   
   global UpOpportunityTradeBatch() {
      
   }

   global UpOpportunityTradeBatch(Boolean needExecute) {
      IsNeedExecute = needExecute;  // 2021-03-05  mzy  WLIG-BYHD79  SFDC环境batch合并调查  
   }
   
   global Database.QueryLocator start(Database.BatchableContext BC) {
      Integer i = 1;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
         i++;
      return Database.getQueryLocator([Select Id,Tax_Foreign_F__c,Tax_Foreign_F_Copy__c,
         Tax_Intra_F__c,Tax_Intra_F_Copy__c 
         from Opportunity 
         where Tax_Intra_Changed__c = true
         and Id !=:System.Label.upOpportunity_1]);
   }

      global void execute(Database.BatchableContext BC, List<Opportunity> sList) {
      for(Opportunity opp : sList){
         opp.Tax_Foreign_F_Copy__c = opp.Tax_Foreign_F__c;
         opp.Tax_Intra_F_Copy__c = opp.Tax_Intra_F__c;
      }
      update sList;
   }
   
   global void finish(Database.BatchableContext BC) {

      //2021-03-05  mzy  WLIG-BYHD79  SFDC环境batch合并调查  start
        if(!Test.isRunningTest() &&IsNeedExecute==true){
         //batch里调用下一个batch时，希望跟原有的Schedule里面传的条数保持一致
           Id execBTId = Database.executebatch(new AccountUpEffectiveContractBatch(true),20);
        }
      //2021-03-05  mzy  WLIG-BYHD79  SFDC环境batch合并调查 end
      
   }
   
}