({
	CallBackAction  : function(component,action_name,para,callback) {
		var action = component.get("c." + action_name.trimStart().replace("c.",""));
        if(para){
            action.setParams(para);
        }
        if(callback){
            action.setCallback(this,function(data){
                callback(data);
            });
        }
        
        $A.enqueueAction(action);
	},
    ShowToast : function(paras){
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams(paras);
        toastEvent.fire();
    },
    AwsPost : function(postURL, data ,callback,token){
        let payloadForNewPI = '';
        if(typeof(data) == 'string'){
            payloadForNewPI = data;
        }else{
            payloadForNewPI = JSON.stringify(data);
        }
        
        AWSService.post(postURL,payloadForNewPI,function(result) {
            //this.insertCalloutLog(this.insertModule,postURL,JSON.stringify(payloadForNewPI),JSON.stringify(result),this.successStatus);
            if(callback) callback(result);
        },token);
    },
    AwsGet : function(url, data ,callback,token){
        
        if(typeof(data) == 'string'){
            url += data;
        }else{
            let i = 0;
            for(let p in data){
                url += (i++) ? '&' : '?';
                url += p + '=' + data[p];
            }
        }
        
        AWSService.get(url,function(result){
            //this.insertCalloutLog(this.insertModule,postURL,JSON.stringify(payloadForNewPI),JSON.stringify(result),this.successStatus);
            if(callback) callback(result);
        },token);
    },
    closeAgencyContact : function(component){
        var cmpEvent = component.getEvent("NewAgencyContactEvent");
        cmpEvent.setParams({
            "showNewAgencyContact":false});
        cmpEvent.fire();
    }
})