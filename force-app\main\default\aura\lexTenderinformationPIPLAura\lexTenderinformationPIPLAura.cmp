<aura:component
    implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickActionWithoutHeader,lightning:actionOverride"
    access="global">
        <aura:html tag="style">
            .slds-modal__container{
            max-width: 65rem !important;
            width:100% !important;
            }
            .cuf-content {
            padding: 0 0rem !important;
            }
            .slds-p-around--medium {
            padding: 0rem !important;
            }
            .slds-modal__content{
            overflow-y:auto !important;
            height:unset !important;
            max-height:unset !important;
            min-height:8rem;
            }
            .slds-tile_board{
            padding:20px 40px;
            }
        </aura:html>
    <c:lexNewAndEditTenderinformationPIPL onclose="{!c.closeModal}" onrefresh="{!c.refreshView}" recordId="{!v.recordId}" />
</aura:component>