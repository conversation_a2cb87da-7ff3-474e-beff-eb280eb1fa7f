@isTest
public with sharing class UpdateUserTextColScheduleTest {
    
    static testMethod void testExecute() {
        String loginId = UserInfo.getUserId();
        Date toDate = Date.today();
        Date mon1stDate = Date.newInstance(toDate.year(), toDate.month(), 1);
        Integer SSBacth_Execute_Day = Integer.valueOf(System.Label.SSBacth_Execute_Day);
        List<OlympusCalendar__c> ocList = new List<OlympusCalendar__c>();
        for (Integer i=0; i<=SSBacth_Execute_Day; i++) {
            OlympusCalendar__c oc = new OlympusCalendar__c(Date__c = mon1stDate.addDays(i), ChangeToWorkday__c=true);
            ocList.add(oc);
        }
        insert ocList;

        // This test runs a scheduled job at midnight Sept. 3rd. 2022
        String CRON_EXP = '0 0 0 3 9 ? 2022';
        new UpdateUserTextColSchedule();

        System.Test.startTest();
        // Schedule the test job
        String jobId = system.schedule('UpdateUserTextColScheduleTest', CRON_EXP, new UpdateUserTextColSchedule(loginId));
        // Get the information from the CronTrigger API object
        CronTrigger ct = [SELECT Id, CronExpression, TimesTriggered, NextFireTime FROM CronTrigger WHERE id = :jobId];
        // Verify the expressions are the same
        System.assertEquals(CRON_EXP, ct.CronExpression);
        // Verify the job has not run
        System.assertEquals(0, ct.TimesTriggered);
        // Verify the next time the job will run
        System.assertEquals('2022-09-03 00:00:00', String.valueOf(ct.NextFireTime));
        System.Test.StopTest();
    }
}