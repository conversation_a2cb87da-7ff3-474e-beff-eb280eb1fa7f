public with sharing class Xin_SearchVisitorPlace_Training {
    public List<Account> results {get; private set;}
    public void search() {
        this.results = new List<Account>();
        
        // 検索条件を用意
        String query = System.currentPageReference().getParameters().get('q');
        
        String reporterState = System.currentPageReference().getParameters().get('r');
        
        if(query == null || query == '' || query.length() < 3) {
            return;
        }
        
        String nameCondition = '%' + String.escapeSingleQuotes(query.replaceAll('%', '')) + '%';
        system.debug('cond=' + nameCondition);
        
        // 検索
        this.results = [select Id, Name from Account where Is_Active__c != '無効' and Name like :nameCondition and RecordType.DeveloperName = 'Training'  order by Name limit 30];
    }
}