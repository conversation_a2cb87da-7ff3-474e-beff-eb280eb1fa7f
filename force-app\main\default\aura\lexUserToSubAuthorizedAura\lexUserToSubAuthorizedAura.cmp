<aura:component implements="flexipage:availableForAllPageTypes,force:lightningQuickActionWithoutHeader,force:hasRecordId,lightning:actionOverride,lightning:isUrlAddressable" access="global"> 
    <aura:attribute name="userId" type="String" />
    <aura:attribute name="userName" type="String" />
    <aura:attribute name="Id" type="String" />
    <aura:attribute name="Name" type="String" />
    <aura:handler name="init" value="{!this}"  action="{!c.init}" />
    <!-- <aura:handler name="render" value="{!this}"  action="{!c.closeModal}" /> -->
    <aura:attribute name="isDoneRendering" type="Boolean" default="false"/>
    <div class="exampleHolder">

        <c:lexUserToSubAuthorized userId="{!v.Id}" userName="{!v.Name}"   />
        
    </div>
</aura:component>