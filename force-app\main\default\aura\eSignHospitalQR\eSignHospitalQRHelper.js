({
    rerender  :function(cmp,event){

        // 创建Base64对象
        
    	if(cmp.get("v.isJqueryLoaded") && cmp.get("v.needToProcessReRenderLogic")) {
             //实例化二维码并调整二维码大小
            var DNName = cmp.get('v.DNName')
            //base64转码
            var encode = window.btoa(DNName);
    	    var qrcode = new QRCode(document.getElementById("qrcode"), {
    	        width: 180,
    	        height: 180,
                colorDark : "#000000",
                colorLight : "#ffffff",
    	        correctLevel: QRCode.CorrectLevel.L
    	    });

    	    //生成二维码
    	    function makeCode() {
    	        //qrcode.clear();
                
                qrcode.makeCode($A.get('$Label.c.extensionUrl')+encode);
                // qrcode.makeCode("https://ocm.force.com/eSignSystem/s/EsignDataEntry?DNName="+encode);
    	        //qrcode.makeCode("https://stagefull-ocm.cs113.force.com/eSignSystem/s/EsignDataEntry?DNName="+encode);
    	    };

    	    //调用生成二维码方法
    	    makeCode();
            }
        	
    //         var DNName = '{!eSignForm__c.DNNameNo0__c}';
    // var profileID = '{!$Profile.Id}';
    // console.log('profileID:'+profileID);
    // if( profileID == '00e10000000dE0KAAU' || profileID == '00e10000000Y3o5AAC'){
    //     var qrcode = new QRCode(document.getElementById("qrcode"), {
    //         text: " https://stagefull-ocm.cs113.force.com/eSignSystem/s/EsignDataEntry?DNName="+DNName,
    //         width: 200,
    //         height: 200,
    //         colorDark : "#000000",
    //         colorLight : "#ffffff",
    //         correctLevel : QRCode.CorrectLevel.H
    //     });
    // }
        	// var qrcode = document.getElementById("qrcode");
        	// //制作DOM
            	// var qrcode = new QRCode(document.getElementById("qrcode"), {
             //        text: "	https://stagefull-ocm.cs113.force.com/eSignSystem/s/EsignDataEntry",
             //        width: 290,
             //        height: 290,
             //        colorDark : "#000000",
             //        colorLight : "#ffffff",
             //        correctLevel : QRCode.CorrectLevel.H
             //    });

    
    }
})