@isTest
private class TransferApplySelectDetailControllerTest {

     static void setupTestData1() {

        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        Oly_TriggerHandler.bypass(ContactTriggerHandler.class.getName());
        Oly_TriggerHandler.bypass(AgencyHospitalHandler.class.getName());

        TransferApply__c raObj = new TransferApply__c();
        TransferApply__c raObj2 = new TransferApply__c();
        // 备品配套明细
        Fixture_Set_Detail__c fsdObjA1 = new Fixture_Set_Detail__c();
        Fixture_Set_Detail__c fsdObjA2 = new Fixture_Set_Detail__c();
        Fixture_Set_Detail__c fsdObjA3 = new Fixture_Set_Detail__c();

        TransferApplySummary__c raesObj = new TransferApplySummary__c();
        TransferApplySummary__c raesObj2 = new TransferApplySummary__c();

        Asset asset1 = new Asset(Asset_Owner__c = 'Olympus');
        Asset asset2 = new Asset(Asset_Owner__c = 'Olympus');
        Asset asset3 = new Asset(Asset_Owner__c = 'Olympus');

        System.runAs(new User(Id = Userinfo.getUserId())) {
            StaticParameter.EscapeNFM001AgencyContractTrigger = true;
            StaticParameter.EscapeNFM001Trigger = true;
            Oly_TriggerHandler.bypass(ContactTriggerHandler.class.getName());
            Oly_TriggerHandler.bypass(AgencyHospitalHandler.class.getName());
            // システム管理者
            User user = new User(Test_staff__c = true);
            user.LastName = '_サンブリッジ';
            // user.FirstName = 'う';
            user.Alias = 'う';
            user.Email = '<EMAIL>';
            user.Username = '<EMAIL>';
            user.CommunityNickname = 'う';
            user.IsActive = true;
            user.EmailEncodingKey = 'ISO-2022-JP';
            user.TimeZoneSidKey = 'Asia/Tokyo';
            user.LocaleSidKey = 'ja_JP';
            user.LanguageLocaleKey = 'ja';
            user.ProfileId = System.Label.ProfileId_SystemAdmin;
            user.Province__c = '北京';
            user.Dept__c = '医疗华北营业本部';
            user.Use_Start_Date__c = Date.today().addMonths(-6);
            insert user;

            // 省
            Address_Level__c al = new Address_Level__c();
            al.Name = '東京';
            al.Level1_Code__c = 'CN-99';
            al.Level1_Sys_No__c = '999999';
            insert al;
            // 市
            Address_Level2__c al2 = new Address_Level2__c();
            al2.Level1_Code__c = 'CN-99';
            al2.Level1_Sys_No__c = '999999';
            al2.Level1_Name__c = '東京';
            al2.Name = '渋谷区';
            al2.Level2_Code__c = 'CN-9999';
            al2.Level2_Sys_No__c = '9999999';
            al2.Address_Level__c = al.id;
            insert al2;

            // 病院を作る
            Account hospital = new Account();
            hospital.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'HP'].id;
            hospital.Name = 'test hospital';
            hospital.Is_Active__c = '有効';
            hospital.Attribute_Type__c = '卫生部';
            hospital.Speciality_Type__c = '综合医院';
            hospital.Grade__c = '一级';
            hospital.OCM_Category__c = 'SLTV';
            hospital.Is_Medical__c = '医疗机构';
            hospital.State_Master__c = al.id;
            hospital.City_Master__c = al2.id;
            hospital.Town__c = '东京';
            insert hospital;

            StaticParameter.EscapeAccountTrigger = true;
            // 戦略科室を得る
            Account[] strategicDep = [SELECT ID, Name FROM Account WHERE parentId = :hospital.Id AND recordType.DeveloperName = 'Department_Class_OTH'];
            // 診療科を作る
            Account dep = new Account();
            dep.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'Department_OTH'].id;
            dep.Name = 'test dep1';
            dep.AgentCode_Ext__c = System.Label.Account_Asset_FJZ;
            dep.ParentId = strategicDep[0].Id;
            dep.Department_Class__c = strategicDep[0].Id;
            dep.Hospital__c = hospital.Id;

            Account dep1 = new Account();
            dep1.recordtypeId = dep.recordtypeId;
            dep1.Name = 'test dep1';
            dep1.AgentCode_Ext__c = System.Label.Account_Asset;
            dep1.ParentId = strategicDep[0].Id;
            dep1.Department_Class__c = strategicDep[0].Id;
            dep1.Hospital__c = hospital.Id;

            insert new Account[]{dep, dep1};


            Contact contact2 = new Contact();
            contact2.AccountId = dep.Id;
            // contact2.FirstName = '責任者';
            contact2.LastName = 'test1经销商';
            insert contact2;

            // 产品
            Product2 pro1 = new Product2(Name='CLH-250:内窥镜冷光源',IsActive=true,Family='GI',
                    Fixture_Model_No__c='CLH-250',Serial_Lot_No__c='S/N tracing',
                    Fixture_Model_No_T__c = 'CLH-250', Asset_Model_No__c = 'Pro1',
                    ProductCode_Ext__c='4604362',Manual_Entry__c=false);

            Product2 pro2 = new Product2(Name='电源线',IsActive=true,Family='GI',
                    Fixture_Model_No__c='电源线',Serial_Lot_No__c='Lot tracing',
                    Fixture_Model_No_T__c = '电源线', Asset_Model_No__c = 'Pro2',
                    ProductCode_Ext__c='BP900003',Manual_Entry__c=false);
            Product2 pro3 = new Product2(Name='MAJ-1933:数字调光电缆',IsActive=true,Family='GI',
                    Fixture_Model_No__c='MAJ-1933',Serial_Lot_No__c='Lot tracing',
                    Fixture_Model_No_T__c = 'MAJ-1933', Asset_Model_No__c = 'Pro3',
                    ProductCode_Ext__c='N3647100',Manual_Entry__c=false);
            insert new Product2[] {pro1, pro2, pro3};

            //备品借出申请
            raObj.Name = 'testra';
            raObj.OwnerId = user.Id;
            raObj.From_Location__c = '北京 备品中心';
            raObj.Contact_Person__c = Userinfo.getUserId();
            Map<String,Schema.RecordTypeInfo>  DEVELOPERNAMEMAP  = Schema.SObjectType.TransferApply__c.getRecordTypeInfosByDeveloperName();
            raObj.RecordTypeId = DEVELOPERNAMEMAP.get('CenterToCenter').getRecordTypeId();
            raObj.Destination_location__c = '上海 备品中心';

            insert raObj;
             //保有设备
            asset1.RecordTypeId = System.Label.Asset_RecordType;
            asset1.SerialNumber = 'T1';
            asset1.Name = 'CLH-250:内窥镜冷光源';
            asset1.AccountId = dep.Id;
            asset1.Department_Class__c = strategicDep[0].Id;
            asset1.Hospital__c = hospital.Id;
            asset1.Product2Id = pro1.Id;
            asset1.Quantity = 1;
            asset1.Status = '使用中';
            asset1.Manage_type__c = '个体管理';
            asset1.Loaner_accsessary__c = false;
            asset1.Out_of_wh__c = 0;
            asset1.Salesdepartment__c = '0.备品中心';
            asset1.Internal_asset_location__c = '北京 备品中心';
            asset1.Product_category__c = 'GI';
            asset1.Equipment_Type__c = '产品试用';
            asset1.SalesProvince__c = '北京';
            asset1.CompanyOfEquipment__c = '北京';
            asset1.Internal_Asset_number__c = '0001';
            asset1.WH_location__c = '货架号3';
            asset1.AssetManageConfirm__c = true;
            asset1.EquipmentSet_Managment_Code__c = '123';
            asset1.Asset_loaner_category__c = '固定资产';

            asset2.RecordTypeId = System.Label.Asset_RecordType;
            asset2.SerialNumber = 'T2';
            asset2.Name = '电源线';
            asset2.AccountId = dep.Id;
            asset2.Department_Class__c = strategicDep[0].Id;
            asset2.Hospital__c = hospital.Id;
            asset2.Product2Id = pro2.Id;
            asset2.Quantity = 10;
            asset2.Status = '使用中';
            asset2.Manage_type__c = '数量管理';
            asset2.Loaner_accsessary__c = true;
            asset2.Out_of_wh__c = 0;
            asset2.Salesdepartment__c = '0.备品中心';
            asset2.Internal_asset_location__c = '北京 备品中心';
            asset2.Product_category__c = 'GI';
            asset2.Equipment_Type__c = '产品试用';
            asset2.SalesProvince__c = '北京';
            asset2.CompanyOfEquipment__c = '北京';
            asset2.Internal_Asset_number__c = '0002';
            asset2.WH_location__c = '货架号2';
            asset2.Asset_loaner_category__c = '固定资产';

            asset3.RecordTypeId = System.Label.Asset_RecordType;
            asset3.SerialNumber = 'UK-19-2401685';
            asset3.Name = 'MAJ-1933:数字调光电缆';
            asset3.AccountId = dep.Id;
            asset3.Department_Class__c = strategicDep[0].Id;
            asset3.Hospital__c = hospital.Id;
            asset3.Product2Id = pro3.Id;
            asset3.Quantity = 5;
            asset3.Status = '使用中';
            asset3.Manage_type__c = '数量管理';
            asset3.Loaner_accsessary__c = true;
            asset3.Out_of_wh__c = 0;
            asset3.Salesdepartment__c = '0.备品中心';
            asset3.Internal_asset_location__c = '北京 备品中心';
            asset3.Product_category__c = 'GI';
            asset3.Equipment_Type__c = '产品试用';
            asset3.SalesProvince__c = '北京';
            asset3.CompanyOfEquipment__c = '北京';
            asset3.Internal_Asset_number__c = '0003';
            asset3.WH_location__c = '货架号1';
            asset3.Asset_loaner_category__c = '固定资产';

            insert new Asset[] {asset1, asset2, asset3};

            // 备品一对一Link表
            Fixture_OneToOne_Link__c foLink1 = new Fixture_OneToOne_Link__c();
            foLink1.Main_Asset__c = asset1.Id;
            foLink1.Accessory_Asset__c = asset2.Id;
            foLink1.Quantity__c = 1;

            Fixture_OneToOne_Link__c foLink2 = new Fixture_OneToOne_Link__c();
            foLink2.Main_Asset__c = asset1.Id;
            foLink2.Accessory_Asset__c = asset3.Id;
            foLink2.Quantity__c = 1;

            insert new Fixture_OneToOne_Link__c[] {foLink1, foLink2};

            // 备品配套
            Fixture_Set__c fsObj1 = new Fixture_Set__c();
            fsObj1.Name = 'set1';
            fsObj1.Fixture_Set_Body_Model_No__c = 'modelNo1';
            fsObj1.Loaner_name__c = 'name1';
            insert fsObj1;

            // 备品配套明细
            fsdObjA1.Name = '备品配套明细名1';
            fsdObjA1.Name_CHN_Created__c = '中文名称1';
            fsdObjA1.Product2__c = pro1.Id;
            fsdObjA1.Fixture_Set__c = fsObj1.Id;
            fsdObjA1.Is_Body__c = true;
            fsdObjA1.Is_Optional__c = false;
            fsdObjA1.UniqueKey__c = fsObj1.Id + ':' + pro1.Id;
            fsdObjA1.SortInt__c = 1;
            fsdObjA1.Quantity__c = 1;

            fsdObjA2.Name = '备品配套明细名2';
            fsdObjA2.Name_CHN_Created__c = '中文名称2';
            fsdObjA2.Product2__c = pro2.Id;
            fsdObjA2.Fixture_Set__c = fsObj1.Id;
            fsdObjA2.Is_Body__c = false;
            fsdObjA2.Is_Optional__c = true;
            fsdObjA2.UniqueKey__c = fsObj1.Id + ':' + pro2.Id;
            fsdObjA2.SortInt__c = 2;
            fsdObjA2.Quantity__c = 1;
            fsdObjA2.Is_OneToOne__c = true;

            fsdObjA3.Name = '备品配套明细名3';
            fsdObjA3.Name_CHN_Created__c = '中文名称3';
            fsdObjA3.Product2__c = pro3.Id;
            fsdObjA3.Fixture_Set__c = fsObj1.Id;
            fsdObjA3.Is_Body__c = false;
            fsdObjA3.Is_Optional__c = true;
            fsdObjA3.UniqueKey__c = fsObj1.Id + ':' + pro3.Id;
            fsdObjA3.SortInt__c = 3;
            fsdObjA3.Quantity__c = 1;
            fsdObjA3.Is_OneToOne__c = true;

            insert new Fixture_Set_Detail__c[] {fsdObjA1, fsdObjA2, fsdObjA3};

            // 借出备品配套一览
            raesObj.TransferApply__c = raObj.Id;
            raesObj.Fixture_Set__c = fsObj1.Id;
            raesObj.Cancel_Select__c = false;
            raesObj.IndexFromUniqueKey__c = 1;
            insert raesObj;

        }
        //调拨申请一览明细
        TransferApplyDetail__c raesdObj1 = new TransferApplyDetail__c();
        TransferApplyDetail__c raesdObj2 = new TransferApplyDetail__c();
        TransferApplyDetail__c raesdObj3 = new TransferApplyDetail__c();

        System.runAs(new User(Id = Userinfo.getUserId())) {
            // 借出备品配套一览明细
            raesdObj1.TransferApply__c = raObj.Id;
            raesdObj1.Fixture_Set_Detail__c = fsdObjA1.Id;
            raesdObj1.TransferApplySummary__c = raesObj.Id;
            raesdObj1.IndexFromUniqueKey__c = 1;
            raesdObj1.FSD_OneToOneAccessory_Cnt__c = 2;
            raesdObj1.ApplyPersonAppended__c = false;
            raesdObj1.TransferCount__c = 1;
            raesdObj1.FSD_SortInt__c = 01;

            raesdObj2.TransferApply__c = raObj.Id;
            raesdObj2.Fixture_Set_Detail__c = fsdObjA2.Id;
            raesdObj2.TransferApplySummary__c = raesObj.Id;
            raesdObj2.IndexFromUniqueKey__c = 3;
            raesdObj2.FSD_OneToOneAccessory_Cnt__c = 2;
            raesdObj2.ApplyPersonAppended__c = false;
            raesdObj2.TransferCount__c = 1;
            raesdObj2.FSD_SortInt__c = 02;

            raesdObj3.TransferApply__c = raObj.Id;
            raesdObj3.Fixture_Set_Detail__c = fsdObjA3.Id;
            raesdObj3.TransferApplySummary__c = raesObj.Id;
            raesdObj3.IndexFromUniqueKey__c = 3;
            raesdObj3.FSD_OneToOneAccessory_Cnt__c = 2;
            raesdObj3.ApplyPersonAppended__c = false;
            raesdObj3.FSD_SortInt__c = 03;

            insert new TransferApplyDetail__c[] {raesdObj1,raesdObj2,raesdObj3};
        }
        System.runAs(new User(Id = Userinfo.getUserId())) {
            // 申请单
            raObj.Status__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
            raObj.Status_Text__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());

            update raObj;

            // 申请者收货操作
            raesObj.Received_Confirm__c = 'OK';
            raesObj.Loaner_received_time__c = Datetime.now();
            update raesObj;

            FixtureDeliverySlip__c fdsObj = new FixtureDeliverySlip__c();
            fdsObj.Name = '00001';
            fdsObj.DeliveryCompany__c = '利讯';
            fdsObj.Distributor_method__c = '陆运';
            fdsObj.DeliveryType__c = '发货';
            fdsObj.Shippment_loaner_time__c = System.now();
            insert fdsObj;

            FixtureDeliverySlip__c fdsObj2 = new FixtureDeliverySlip__c();
            fdsObj2.Name = 'abcd';
            fdsObj2.DeliveryCompany__c = '利讯';
            fdsObj2.Distributor_method__c = '陆运';
            fdsObj2.DeliveryType__c = '回寄';
            fdsObj2.Wh_Staff__c = Userinfo.getUserId();
            insert fdsObj2;

            // 出库前点检操作
            raesdObj1.Asset__c = asset1.Id;
            raesdObj1.Main_OneToOne__c = true;
            raesdObj1.Loaner_accsessary__c = false;
            raesdObj1.FSD_Name_CHN__c = 'name01';
            raesdObj1.Shipment_Status_Text__c = FixtureUtil.raesdStatusMap.get(FixtureUtil.HistoryStatus.Yi_Fen_Pei.ordinal());

            raesdObj2.Asset__c = asset2.Id;
            raesdObj2.Loaner_accsessary__c = true;
            raesdObj2.OneToOneAccessory__c = true;
            raesdObj2.FSD_Name_CHN__c = 'name02';
            raesdObj2.Shipment_Status_Text__c = FixtureUtil.raesdStatusMap.get(FixtureUtil.HistoryStatus.Yi_Fen_Pei.ordinal());

            raesdObj3.Asset__c = asset3.Id;
            raesdObj3.OneToOneAccessory__c = true;
            raesdObj3.Loaner_accsessary__c = true;
            raesdObj3.FSD_Name_CHN__c = 'name03';
            raesdObj3.Shipment_Status_Text__c = FixtureUtil.raesdStatusMap.get(FixtureUtil.HistoryStatus.Yi_Fen_Pei.ordinal());

            raesdObj1.Shipment_request_time2__c = Date.toDay();
            raesdObj1.StockDown__c = true;
            raesdObj1.StockDown_time__c = Date.toDay();
            raesdObj1.StockDown_staff__c = Userinfo.getUserId();
            raesdObj1.Shipment_Status_Text__c = FixtureUtil.raesdStatusMap.get(FixtureUtil.HistoryStatus.Yi_Xia_Jia.ordinal());

            raesdObj2.Shipment_request_time2__c = Date.toDay();
            raesdObj2.StockDown__c = true;
            raesdObj2.StockDown_time__c = Date.toDay();
            raesdObj2.StockDown_staff__c = Userinfo.getUserId();
            raesdObj2.Shipment_Status_Text__c = FixtureUtil.raesdStatusMap.get(FixtureUtil.HistoryStatus.Yi_Xia_Jia.ordinal());

            raesdObj3.Shipment_request_time2__c = Date.toDay();
            raesdObj3.StockDown__c = true;
            raesdObj3.StockDown_time__c = Date.toDay();
            raesdObj3.StockDown_staff__c = Userinfo.getUserId();
            raesdObj3.Shipment_Status_Text__c = FixtureUtil.raesdStatusMap.get(FixtureUtil.HistoryStatus.Yi_Xia_Jia.ordinal());

            raesdObj1.Inspection_result__c = 'OK';
            raesdObj1.Pre_inspection_time__c = System.now();
            raesdObj1.Inspection_staff__c = Userinfo.getUserId();

            raesdObj2.Inspection_result__c = 'OK';
            raesdObj2.Pre_inspection_time__c = System.now();
            raesdObj2.Inspection_staff__c = Userinfo.getUserId();

            raesdObj3.Inspection_result__c = 'OK';
            raesdObj3.Pre_inspection_time__c = System.now();
            raesdObj3.Inspection_staff__c = Userinfo.getUserId();

            // 发货操作
            raesdObj1.DeliverySlip__c = fdsObj.Id;
            raesdObj2.DeliverySlip__c = fdsObj.Id;
            raesdObj3.DeliverySlip__c = fdsObj.Id;

            update new TransferApplyDetail__c[] {raesdObj1, raesdObj2,raesdObj3};

            // 分配后修改asset里面的数据
            asset1.Last_Reserve_TAES_Detail__c = raesdObj1.Id;
            asset1.TransferFrozenQuantity__c = 1;
            asset2.Last_Reserve_TAES_Detail__c = raesdObj2.Id;
            asset2.TransferFrozenQuantity__c = 1;
            asset3.Last_Reserve_TAES_Detail__c = null;
            asset3.TransferFrozenQuantity__c = 2;
            update new Asset[] {asset1,asset2,asset3};
        }
    }



private static User user1 ;

    // 已分配的数据节点
    static Id setupTestData2() {
        // OLY_OCM-643 追加ControllerUtil.EscapeNFM001Trigger
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        StaticParameter.EscapeAccountTrigger = true;
        ControllerUtil.EscapeNFM001Trigger = true;

        Rental_Apply__c raObj = new Rental_Apply__c();
        Fixture_Set_Detail__c fsdObjA1 = new Fixture_Set_Detail__c();
        Fixture_Set_Detail__c fsdObjA2 = new Fixture_Set_Detail__c();

        Rental_Apply_Equipment_Set__c raesObj = new Rental_Apply_Equipment_Set__c();
        Asset asset1 = new Asset(Asset_Owner__c = 'Olympus');
        Asset asset2 = new Asset(Asset_Owner__c = 'Olympus');

        Fixture_OneToOne_Link__c foLink1 = new Fixture_OneToOne_Link__c();

        System.runAs(new User(Id = Userinfo.getUserId())) {
            StaticParameter.EscapeNFM001AgencyContractTrigger = true;
            StaticParameter.EscapeNFM001Trigger = true;
            StaticParameter.EscapeAccountTrigger = true;
            ControllerUtil.EscapeNFM001Trigger = true;

            Oly_TriggerHandler.bypass(ContactTriggerHandler.class.getName());
            Oly_TriggerHandler.bypass(AgencyHospitalHandler.class.getName());
            // 省
            Address_Level__c al = new Address_Level__c();
            al.Name = '東京';
            al.Level1_Code__c = 'CN-99';
            al.Level1_Sys_No__c = '999999';
            insert al;
            // 市
            Address_Level2__c al2 = new Address_Level2__c();
            al2.Level1_Code__c = 'CN-99';
            al2.Level1_Sys_No__c = '999999';
            al2.Level1_Name__c = '東京';
            al2.Name = '渋谷区';
            al2.Level2_Code__c = 'CN-9999';
            al2.Level2_Sys_No__c = '9999999';
            al2.Address_Level__c = al.id;
            insert al2;
            // 病院を作る
            Account hospital = new Account();
            hospital.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'HP'].id;
            hospital.Name = 'test hospital';
            hospital.Is_Active__c = '有効';
            hospital.Attribute_Type__c = '卫生部';
            hospital.Speciality_Type__c = '综合医院';
            hospital.Grade__c = '一级';
            hospital.OCM_Category__c = 'SLTV';
            hospital.Is_Medical__c = '医疗机构';
            hospital.State_Master__c = al.id;
            hospital.City_Master__c = al2.id;
            hospital.Town__c = '东京';
            insert hospital;

            // 戦略科室を得る
            Account strategicDep = new Account();
            strategicDep.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'Department_Class_OTH'].id;
            strategicDep.Name = 'Olympus社内 其他';
            strategicDep.Department_Class_Label__c = '其他';
            strategicDep.Hospital__c = hospital.Id;
            strategicDep.ParentId = hospital.Id;
            insert strategicDep;

            // 診療科を作る
            Account dep = new Account();
            dep.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'Department_OTH'].id;
            dep.Name = 'test dep';
            dep.AgentCode_Ext__c = System.Label.Account_Asset_FJZ;
            dep.ParentId = strategicDep.Id;
            dep.Department_Class__c = strategicDep.Id;
            dep.Hospital__c = hospital.Id;
            insert dep;

            Contact contact2 = new Contact();
            contact2.AccountId = dep.Id;
            // contact2.FirstName = '責任者';
            contact2.LastName = 'test1经销商';
            insert contact2;

            // 产品
            Product2 pro1 = new Product2(Name='name01',IsActive=true,Family='GI',
                    Fixture_Model_No__c='n01',Serial_Lot_No__c='S/N tracing',
                    Fixture_Model_No_T__c = 'n01', Asset_Model_No__c = 'Pro1',
                    ProductCode_Ext__c='pc01',Manual_Entry__c=false);
            Product2 pro2 = new Product2(Name='name02',IsActive=true,Family='GI',
                    Fixture_Model_No__c='n02',Serial_Lot_No__c='Lot tracing',
                    Fixture_Model_No_T__c = 'n02', Asset_Model_No__c = 'Pro2',
                    ProductCode_Ext__c='pc02',Manual_Entry__c=false);

            insert new Product2[] {pro1, pro2};

            //备品借出申请
            raObj.Name = 'testra';
            raObj.Product_category__c = 'GI';
            raObj.Demo_purpose1__c = '产品试用';
            raObj.demo_purpose2__c = FixtureUtil.raDemo_purpose2MAP.get('shiyongwuxunjia');
            raObj.direct_send__c = '医疗机构';
            raObj.Loaner_received_staff__c = '王五';
            raObj.Loaner_received_staff_phone__c = '110';
            raObj.direct_shippment_address__c = '北京市';
            raObj.Hospital__c = hospital.Id;
            raObj.Strategic_dept__c = strategicDep.Id;
            raObj.Account__c = dep.Id;
            raObj.Request_shipping_day__c = Date.toDay();  // 希望到货日
            raObj.Hope_Lonaer_date_Num__c = 1;  // 希望借用天数
            raObj.Status__c = '草案中';
            raObj.Phone_number__c = '**********';
            raObj.Loaner_medical_Staff__c = contact2.Id;
            insert raObj;


            // 保有设备
            asset1.RecordTypeId = System.Label.Asset_RecordType;
            asset1.SerialNumber = 'asset1';
            asset1.Name = 'asset1';
            asset1.AccountId = dep.Id;
            asset1.Department_Class__c = strategicDep.Id;
            asset1.Hospital__c = hospital.Id;
            asset1.Product2Id = pro1.Id;
            asset1.Quantity = 1;
            asset1.Status = '不明';
            asset1.Manage_type__c = '个体管理';
            asset1.Loaner_accsessary__c = false;
            asset1.Out_of_wh__c = 0;
            asset1.Salesdepartment__c = '1.华北营业本部';
            asset1.Internal_asset_location__c = '北京 备品中心';
            asset1.Product_category__c = 'GI';
            asset1.Equipment_Type__c = '产品试用';
            asset1.SalesProvince__c = '北京';
            asset1.CompanyOfEquipment__c = '北京';
            asset1.Internal_Asset_number__c = '0001';

            asset2.RecordTypeId = System.Label.Asset_RecordType;
            asset2.SerialNumber = 'asset2';
            asset2.Name = 'asset2';
            asset2.AccountId = dep.Id;
            asset2.Department_Class__c = strategicDep.Id;
            asset2.Hospital__c = hospital.Id;
            asset2.Product2Id = pro2.Id;
            asset2.Quantity = 100;
            asset2.Status = '不明';
            asset2.Manage_type__c = '数量管理';
            asset2.Loaner_accsessary__c = true;
            asset2.Out_of_wh__c = 0;
            asset2.Salesdepartment__c = '1.华北营业本部';
            asset2.Internal_asset_location__c = '北京 备品中心';
            asset2.Product_category__c = 'GI';
            asset2.Equipment_Type__c = '产品试用';
            asset2.SalesProvince__c = '北京';
            asset2.CompanyOfEquipment__c = '北京';
            asset2.Internal_Asset_number__c = '0002';

            insert new Asset[] {asset1, asset2};

            // 备品一对一Link表
            foLink1.Main_Asset__c = asset1.Id;
            foLink1.Accessory_Asset__c = asset2.Id;
            foLink1.Quantity__c = 1;
            foLink1.Select_Accessory_Asset_Cnt__c = 1;

            insert new Fixture_OneToOne_Link__c[] {foLink1};

            // 备品配套
            Fixture_Set__c fsObj1 = new Fixture_Set__c();
            fsObj1.Name = 'set1';
            fsObj1.Fixture_Set_Body_Model_No__c = 'modelNo1';
            fsObj1.Loaner_name__c = 'name1';
            insert fsObj1;

            // 备品配套明细
            fsdObjA1.Name = '备品配套明细名1';
            fsdObjA1.Name_CHN_Created__c = '中文名称1';
            fsdObjA1.Product2__c = pro1.Id;
            fsdObjA1.Fixture_Set__c = fsObj1.Id;
            fsdObjA1.Is_Body__c = true;
            fsdObjA1.Is_Optional__c = false;
            fsdObjA1.UniqueKey__c = fsObj1.Id + ':' + pro1.Id;
            fsdObjA1.SortInt__c = 1;

            fsdObjA2.Name = '备品配套明细名2';
            fsdObjA2.Name_CHN_Created__c = '中文名称2';
            fsdObjA2.Product2__c = pro2.Id;
            fsdObjA2.Fixture_Set__c = fsObj1.Id;
            fsdObjA2.Is_Body__c = false;
            fsdObjA2.Is_Optional__c = true;
            fsdObjA2.UniqueKey__c = fsObj1.Id + ':' + pro2.Id;
            fsdObjA2.SortInt__c = 2;

            insert new Fixture_Set_Detail__c[] {fsdObjA1,fsdObjA2};

            // 借出备品配套一览
            raesObj.Rental_Apply__c = raObj.Id;
            raesObj.Fixture_Set__c = fsObj1.Id;
            raesObj.Cancel_Select__c = false;
            raesObj.Rental_Start_Date__c = Date.toDay();
            raesObj.Rental_End_Date__c = Date.toDay();
            raesObj.IndexFromUniqueKey__c = 1;
            raesObj.UniqueKey__c = '1:'+ fsObj1.Id + ':1';
            insert raesObj;
        }
        Rental_Apply_Equipment_Set_Detail__c raesdObj1 = new Rental_Apply_Equipment_Set_Detail__c();
        Rental_Apply_Equipment_Set_Detail__c raesdObj2 = new Rental_Apply_Equipment_Set_Detail__c();

        System.runAs(new User(Id = Userinfo.getUserId())) {

            // 借出备品配套一览明细
            raesdObj1.Rental_Apply__c = raObj.Id;
            raesdObj1.Fixture_Set_Detail__c = fsdObjA1.Id;
            raesdObj1.Rental_Num__c = 1;
            raesdObj1.Queue_Number__c = null;
            raesdObj1.Is_Body__c = true;
            raesdObj1.Rental_Apply_Equipment_Set__c = raesObj.Id;
            raesdObj1.IndexFromUniqueKey__c = 1;
            raesdObj1.UniqueKey__c = '1:'+ raesObj.Id + ':' + fsdObjA1.Id + ':1';
            raesdObj1.FSD_OneToOneAccessory_Cnt__c = 1;
            raesdObj1.FSD_Is_Optional__c = false;
            raesdObj1.FSD_Is_OneToOne__c = true;
            raesdObj1.ApplyPersonAppended__c = false;
            raesdObj1.Fixture_OneToOne_Link_Id__c = foLink1.Id;
            raesdObj1.FSD_Fixture_Model_No__c = 'n01';
            raesdObj1.Fixture_Model_No_text__c = 'n01';
            raesdObj1.Salesdepartment_before__c = '1.华北营业本部';
            raesdObj1.Internal_asset_location_before__c = '北京 备品中心';
            raesdObj1.Product_category_text__c = 'GI';
            raesdObj1.Equipment_Type_text__c = '产品试用';

            // 借出备品配套一览明细
            raesdObj2.Rental_Apply__c = raObj.Id;
            raesdObj2.Fixture_Set_Detail__c = fsdObjA2.Id;
            raesdObj2.Rental_Num__c = 2;
            raesdObj2.Queue_Number__c = null;
            raesdObj2.Is_Body__c = false;
            raesdObj2.Rental_Apply_Equipment_Set__c = raesObj.Id;
            raesdObj2.IndexFromUniqueKey__c = 2;
            raesdObj2.UniqueKey__c = '1:'+ raesObj.Id + ':' + fsdObjA2.Id + ':2';
            raesdObj2.FSD_OneToOneAccessory_Cnt__c = 2;
            raesdObj2.FSD_Is_Optional__c = true;
            raesdObj2.FSD_Is_OneToOne__c = true;
            raesdObj2.ApplyPersonAppended__c = false;
            raesdObj2.Cancel_Select__c = false;
            raesdObj2.Fixture_OneToOne_Link_Id__c = foLink1.Id;
            raesdObj2.FSD_Fixture_Model_No__c = 'n04';
            raesdObj2.Fixture_Model_No_text__c = 'n04';
            raesdObj2.Salesdepartment_before__c = '1.华北营业本部';
            raesdObj2.Internal_asset_location_before__c = '北京 备品中心';
            raesdObj2.Product_category_text__c = 'GI';
            raesdObj2.Equipment_Type_text__c = '产品试用';


            insert new Rental_Apply_Equipment_Set_Detail__c[] {raesdObj1,raesdObj2};

        }
        System.runAs(new User(Id = Userinfo.getUserId())) {
            // 申请单
            raObj.Status__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
            raObj.Status_Text__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
            raObj.NotWatch_RA_Status__c = true;
            update raObj;

            // 借出备品配套一览明细
            raesdObj1.Select_Time__c = Date.toDay();
            raesdObj1.Asset__c = asset1.Id;
            raesdObj1.Loaner_accsessary__c = false;
            raesdObj1.FSD_Name_CHN__c = 'name01';
            raesdObj1.Lost_item_giveup__c = true;

            raesdObj2.Select_Time__c = Date.toDay();
            raesdObj2.Asset__c = asset2.Id;
            raesdObj2.Loaner_accsessary__c = true;
            raesdObj2.FSD_Name_CHN__c = 'name02';

            update new Rental_Apply_Equipment_Set_Detail__c[] {raesdObj1,raesdObj2};

            raesObj.First_RAESD__c = raesdObj1.Id;
            update raesObj;

        }
        return raObj.Id;
    }

    static void setupTestData3() {

        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        Oly_TriggerHandler.bypass(ContactTriggerHandler.class.getName());
        Oly_TriggerHandler.bypass(AgencyHospitalHandler.class.getName());

        TransferApply__c raObj = new TransferApply__c();
        // 备品配套明细
        Fixture_Set_Detail__c fsdObjA1 = new Fixture_Set_Detail__c();
        Fixture_Set_Detail__c fsdObjA2 = new Fixture_Set_Detail__c();
        Fixture_Set_Detail__c fsdObjA3 = new Fixture_Set_Detail__c();
        Fixture_Set_Detail__c fsdObjA4 = new Fixture_Set_Detail__c();
        TransferApplySummary__c raesObj = new TransferApplySummary__c();

        Asset asset1 = new Asset(Asset_Owner__c = 'Olympus');
        Asset asset2 = new Asset(Asset_Owner__c = 'Olympus');
        Asset asset3 = new Asset(Asset_Owner__c = 'Olympus');
        Asset asset4 = new Asset(Asset_Owner__c = 'Olympus');
        Fixture_OneToOne_Link__c foLink1 = new Fixture_OneToOne_Link__c();
        Fixture_OneToOne_Link__c foLink2 = new Fixture_OneToOne_Link__c();
        Asset_EquipmentSet_Managment_Code__c aemCode = new Asset_EquipmentSet_Managment_Code__c();// ******** ljh add 管理编码
        Asset_EquipmentSet_Managment_Code__c aemCode1 = new Asset_EquipmentSet_Managment_Code__c();// ******** ljh add 管理编码
        Asset_EquipmentSet_Managment_Code__c aemCode2 = new Asset_EquipmentSet_Managment_Code__c();// ******** ljh add 管理编码
        // MIXED_DML_OPERATION, DML operation on setup object is not permitted Error
        System.runAs(new User(Id = Userinfo.getUserId())) {
            StaticParameter.EscapeNFM001AgencyContractTrigger = true;
            StaticParameter.EscapeNFM001Trigger = true;
            Oly_TriggerHandler.bypass(ContactTriggerHandler.class.getName());
            Oly_TriggerHandler.bypass(AgencyHospitalHandler.class.getName());
            // システム管理者
            User user = new User(Test_staff__c = true);
            user.LastName = '_サンブリッジ';
            // user.FirstName = 'う';
            user.Alias = 'う';
            user.Email = '<EMAIL>';
            user.Username = '<EMAIL>';
            user.CommunityNickname = 'う';
            user.IsActive = true;
            user.EmailEncodingKey = 'ISO-2022-JP';
            user.TimeZoneSidKey = 'Asia/Tokyo';
            user.LocaleSidKey = 'ja_JP';
            user.LanguageLocaleKey = 'ja';
            user.ProfileId = System.Label.ProfileId_SystemAdmin;
            user.Province__c = '北京';
            user.Dept__c = '医疗华北营业本部';
            user.Use_Start_Date__c = Date.today().addMonths(-6);
            insert user;

            // 省
            Address_Level__c al = new Address_Level__c();
            al.Name = '東京';
            al.Level1_Code__c = 'CN-99';
            al.Level1_Sys_No__c = '999999';
            insert al;
            // 市
            Address_Level2__c al2 = new Address_Level2__c();
            al2.Level1_Code__c = 'CN-99';
            al2.Level1_Sys_No__c = '999999';
            al2.Level1_Name__c = '東京';
            al2.Name = '渋谷区';
            al2.Level2_Code__c = 'CN-9999';
            al2.Level2_Sys_No__c = '9999999';
            al2.Address_Level__c = al.id;
            insert al2;

            // 病院を作る
            Account hospital = new Account();
            hospital.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'HP'].id;
            hospital.Name = 'test hospital';
            hospital.Is_Active__c = '有効';
            hospital.Attribute_Type__c = '卫生部';
            hospital.Speciality_Type__c = '综合医院';
            hospital.Grade__c = '一级';
            hospital.OCM_Category__c = 'SLTV';
            hospital.Is_Medical__c = '医疗机构';
            hospital.State_Master__c = al.id;
            hospital.City_Master__c = al2.id;
            hospital.Town__c = '东京';
            insert hospital;

            StaticParameter.EscapeAccountTrigger = true;
            // 戦略科室を得る
            Account[] strategicDep = [SELECT ID, Name FROM Account WHERE parentId = :hospital.Id AND recordType.DeveloperName = 'Department_Class_OTH'];
            // 診療科を作る
            Account dep = new Account();
            dep.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'Department_OTH'].id;
            dep.Name = 'test dep1';
            dep.AgentCode_Ext__c = System.Label.Account_Asset_FJZ;
            dep.ParentId = strategicDep[0].Id;
            dep.Department_Class__c = strategicDep[0].Id;
            dep.Hospital__c = hospital.Id;

            Account dep1 = new Account();
            dep1.recordtypeId = dep.recordtypeId;
            dep1.Name = 'test dep1';
            dep1.AgentCode_Ext__c = System.Label.Account_Asset;
            dep1.ParentId = strategicDep[0].Id;
            dep1.Department_Class__c = strategicDep[0].Id;
            dep1.Hospital__c = hospital.Id;

            insert new Account[]{dep, dep1};

            Contact contact2 = new Contact();
            contact2.AccountId = dep.Id;
            // contact2.FirstName = '責任者';
            contact2.LastName = 'test1经销商';
            insert contact2;

            // 产品
            Product2 pro1 = new Product2(Name='CLH-250:内窥镜冷光源',IsActive=true,Family='GI',
                    Fixture_Model_No__c='CLH-250',Serial_Lot_No__c='S/N tracing',
                    Fixture_Model_No_T__c = 'CLH-250', Asset_Model_No__c = 'Pro1',
                    ProductCode_Ext__c='4604362',Manual_Entry__c=false);
            Product2 pro2 = new Product2(Name='电源线',IsActive=true,Family='GI',
                    Fixture_Model_No__c='电源线',Serial_Lot_No__c='Lot tracing',
                    Fixture_Model_No_T__c = '电源线', Asset_Model_No__c = 'Pro2',
                    ProductCode_Ext__c='BP900003',Manual_Entry__c=false);
            Product2 pro3 = new Product2(Name='MAJ-1933:数字调光电缆',IsActive=true,Family='GI',
                    Fixture_Model_No__c='MAJ-1933',Serial_Lot_No__c='Lot tracing',
                    Fixture_Model_No_T__c = 'MAJ-1933', Asset_Model_No__c = 'Pro3',
                    ProductCode_Ext__c='N3647100',Manual_Entry__c=false);
            Product2 pro4 = new Product2(Name='name04',IsActive=true,Family='GI',
                    Fixture_Model_No__c='n04',Serial_Lot_No__c='Lot tracing',
                    Fixture_Model_No_T__c = 'n04', Asset_Model_No__c = 'Pro4',
                    ProductCode_Ext__c='pc04',Manual_Entry__c=false);
            insert new Product2[] {pro1, pro2, pro3, pro4};

            // ******** ljh add 管理编码 start
            aemCode.keyName__c ='!检测用备品;4.华东营业本部;0系;北/上/广 备品中心';
            aemCode.key__c='NJC;4;0';
            aemCode.code__c = 'HD0';
            aemCode.isSpecial__c = false;
            aemCode.LastNo__c = 1999;  
            aemCode.MaxLastNo__c = 9999;

            aemCode1.keyName__c ='!检测用备品;4.华东营业本部;!0系;GI;北/上/广 备品中心';
            aemCode1.key__c='NJC;4;N0;GI';
            aemCode1.code__c = 'HD';
            aemCode1.isSpecial__c = true;
            aemCode1.LastNo__c = 19999;   
            aemCode1.MaxLastNo__c = 39999;

            aemCode2.keyName__c ='!检测用备品;4.华东营业本部;!0系;SP;北/上/广 备品中心';
            aemCode2.key__c='NJC;4;N0;SP';
            aemCode2.code__c = 'HD';
            aemCode2.isSpecial__c = true;
            aemCode2.LastNo__c = 49999;  
            aemCode2.MaxLastNo__c = 79999;
            insert new Asset_EquipmentSet_Managment_Code__c[] {aemCode, aemCode1, aemCode2};
            // ******** ljh add 管理编码 end
            //备品借出申请
            raObj.Name = 'testra';
            raObj.OwnerId = user.Id;
            raObj.From_Location__c = '北京 备品中心';
            raObj.Contact_Person__c = Userinfo.getUserId();

            insert raObj;

            // 保有设备
            asset1.RecordTypeId = System.Label.Asset_RecordType;
            asset1.SerialNumber = 'T1';
            asset1.Name = 'CLH-250:内窥镜冷光源';
            asset1.AccountId = dep.Id;
            asset1.Department_Class__c = strategicDep[0].Id;
            asset1.Hospital__c = hospital.Id;
            asset1.Product2Id = pro1.Id;
            asset1.Quantity = 1;
            asset1.Status = '使用中';
            asset1.Manage_type__c = '个体管理';
            asset1.Loaner_accsessary__c = false;
            asset1.Out_of_wh__c = 0;
            asset1.Salesdepartment__c = '0.备品中心';
            asset1.Internal_asset_location__c = '北京 备品中心';
            asset1.Product_category__c = 'GI';
            asset1.Equipment_Type__c = '产品试用';
            asset1.SalesProvince__c = '北京';
            asset1.CompanyOfEquipment__c = '北京';
            asset1.Internal_Asset_number__c = '0001';
            asset1.WH_location__c = '货架号1';
            asset1.AssetManageConfirm__c = true;
            asset1.Asset_loaner_category__c = '固定资产';

            asset2.RecordTypeId = System.Label.Asset_RecordType;
            asset2.SerialNumber = 'T2';
            asset2.Name = '电源线';
            asset2.AccountId = dep.Id;
            asset2.Department_Class__c = strategicDep[0].Id;
            asset2.Hospital__c = hospital.Id;
            asset2.Product2Id = pro2.Id;
            asset2.Quantity = 10;
            asset2.Status = '使用中';
            asset2.Manage_type__c = '数量管理';
            asset2.Loaner_accsessary__c = true;
            asset2.Out_of_wh__c = 0;
            asset2.Salesdepartment__c = '0.备品中心';
            asset2.Internal_asset_location__c = '北京 备品中心';
            asset2.Product_category__c = 'GI';
            asset2.Equipment_Type__c = '产品试用';
            asset2.SalesProvince__c = '北京';
            asset2.CompanyOfEquipment__c = '北京';
            asset2.Internal_Asset_number__c = '0002';
            asset2.WH_location__c = '货架号2';
            asset2.AssetManageConfirm__c = true;
            asset2.Asset_loaner_category__c = '附属品';


            asset3.RecordTypeId = System.Label.Asset_RecordType;
            asset3.SerialNumber = 'UK-19-2401685';
            asset3.Name = 'MAJ-1933:数字调光电缆';
            asset3.AccountId = dep.Id;
            asset3.Department_Class__c = strategicDep[0].Id;
            asset3.Hospital__c = hospital.Id;
            asset3.Product2Id = pro3.Id;
            asset3.Quantity = 5;
            asset3.Status = '使用中';
            asset3.Manage_type__c = '数量管理';
            asset3.Loaner_accsessary__c = true;
            asset3.Out_of_wh__c = 0;
            asset3.Salesdepartment__c = '0.备品中心';
            asset3.Internal_asset_location__c = '北京 备品中心';
            asset3.Product_category__c = 'GI';
            asset3.Equipment_Type__c = '产品试用';
            asset3.SalesProvince__c = '北京';
            asset3.CompanyOfEquipment__c = '北京';
            asset3.Internal_Asset_number__c = '0003';
            asset3.WH_location__c = '货架号3';
            asset3.AssetManageConfirm__c = true;
            asset3.Asset_loaner_category__c = '附属品';

            insert new Asset[] {asset1, asset2, asset3};

            // 备品一对一Link表
            foLink1.Main_Asset__c = asset1.Id;
            foLink1.Accessory_Asset__c = asset2.Id;
            foLink1.Quantity__c = 1;

            foLink2.Main_Asset__c = asset1.Id;
            foLink2.Accessory_Asset__c = asset3.Id;
            foLink2.Quantity__c = 1;
            insert new Fixture_OneToOne_Link__c[] {foLink1, foLink2};

            // 备品配套
            Fixture_Set__c fsObj1 = new Fixture_Set__c();
            fsObj1.Name = 'set1';
            fsObj1.Fixture_Set_Body_Model_No__c = 'modelNo1';
            fsObj1.Loaner_name__c = 'name1';
            insert fsObj1;

            // 备品配套明细
            fsdObjA1.Name = '备品配套明细名1';
            fsdObjA1.Name_CHN_Created__c = '中文名称1';
            fsdObjA1.Product2__c = pro1.Id;
            fsdObjA1.Fixture_Set__c = fsObj1.Id;
            fsdObjA1.Is_Body__c = true;
            fsdObjA1.Is_Optional__c = false;
            fsdObjA1.UniqueKey__c = fsObj1.Id + ':' + pro1.Id;
            fsdObjA1.SortInt__c = 1;
            fsdObjA1.Quantity__c = 1;

            fsdObjA2.Name = '备品配套明细名2';
            fsdObjA2.Product2__c = pro2.Id;
            fsdObjA2.Fixture_Set__c = fsObj1.Id;
            fsdObjA2.Is_Body__c = false;
            fsdObjA2.Is_Optional__c = true;
            fsdObjA2.UniqueKey__c = fsObj1.Id + ':' + pro2.Id;
            fsdObjA2.SortInt__c = 2;
            fsdObjA2.Quantity__c = 1;
            fsdObjA2.Is_OneToOne__c = true;

            fsdObjA3.Name = '备品配套明细名3';
            fsdObjA3.Name_CHN_Created__c = '中文名称3';
            fsdObjA3.Product2__c = pro3.Id;
            fsdObjA3.Fixture_Set__c = fsObj1.Id;
            fsdObjA3.Is_Body__c = false;
            fsdObjA3.Is_Optional__c = true;
            fsdObjA3.UniqueKey__c = fsObj1.Id + ':' + pro3.Id;
            fsdObjA3.SortInt__c = 3;
            fsdObjA3.Quantity__c = 2;
            fsdObjA3.Is_OneToOne__c = true;

            fsdObjA4.Name = '备品配套明细名4';
            fsdObjA4.Name_CHN_Created__c = '中文名称4';
            fsdObjA4.Product2__c = pro4.Id;
            fsdObjA4.Fixture_Set__c = fsObj1.Id;
            fsdObjA4.Is_Body__c = false;
            fsdObjA4.Is_Optional__c = true;
            fsdObjA4.UniqueKey__c = fsObj1.Id + ':' + pro4.Id;
            fsdObjA4.SortInt__c = 4;
            fsdObjA4.Quantity__c = 2;
            fsdObjA4.Is_OneToOne__c = false;
            fsdObjA4.Is_Optional__c = true;
            insert new Fixture_Set_Detail__c[] {fsdObjA1, fsdObjA2, fsdObjA3, fsdObjA4};

        }

  }

    private static User getUser() {
        if (user1 != null) {
            return user1;
        }

        String timenow = Datetime.now().format('yyyyMMddHHmmss');
        User hpOwner = new User(Test_staff__c = true, LastName = 'TestMao', 
                Alias = 'hp', CommunityNickname = 'TestMao', Email = '<EMAIL>',
                Username = 'Test' + timenow + '@sunbridge.com', IsActive = true, EmailEncodingKey = 'ISO-2022-JP',
                TimeZoneSidKey = 'Asia/Tokyo', LocaleSidKey = 'ja_JP', LanguageLocaleKey = 'ja', ProfileId = System.Label.ProfileId_SystemAdmin,
                Dept__c = '医疗华北营业本部', Province__c = '北京');
        insert hpOwner;
        return hpOwner;
    }


    // @isTest
    // public static void test_init() {
    //     setupTestData3();

    //     List<TransferApply__c> taList = [
    //     SELECT Id
    //          , Add_Approval_Status__c
    //          , Status__c
    //          , RecordType.DeveloperName
    //          , From_Location__c
    //       FROM TransferApply__c ];

    //     PageReference ref =  new PageReference('/apex/TransferApplySelectDetail?id=' + taList[0].Id);
    //     Test.setCurrentPage(ref);

    //     TransferApplySelectDetailController controller = new TransferApplySelectDetailController();
    //     CreateRelationListPagingCmpCtrl cmp = new CreateRelationListPagingCmpCtrl();

    //     Test.startTest();
    //     cmp.pageController = controller;
    //     cmp.pageController.myComponentController.init();
    //     controller.init();
    //     controller.isNeedSearchFirst = true;
    //     controller.searchOpp();
    //     Test.stopTest();

    //     System.assertEquals(5 ,controller.viewlist.size());
    //     System.debug('Success init');
    // }

    // @isTest
    // public static void test_init2() {
    //     Oly_TriggerHandler.bypass(TransferApplyHandler.class.getName());
    //      system.runAs(getUser()){

    //         Id raId = setupTestData2();

    //         TransferApply__c raObj = new TransferApply__c();
    //         raObj.Name = 'testra';
    //         raObj.OwnerId = Userinfo.getUserId();
    //         raObj.From_Location__c = '北京 备品中心';
    //         raObj.Contact_Person__c = Userinfo.getUserId();
    //         Map<String,Schema.RecordTypeInfo>  DEVELOPERNAMEMAP  = Schema.SObjectType.TransferApply__c.getRecordTypeInfosByDeveloperName();
    //         raObj.RecordTypeId = DEVELOPERNAMEMAP.get('CenterToCenter').getRecordTypeId();
    //         raObj.Destination_location__c = '上海 备品中心';
    //         insert raObj;

    //         Id taId = raObj.Id;

    //         ApexPages.currentPage().getParameters().put('raid',raid);
    //         ApexPages.currentPage().getParameters().put('newid',taId);

    //         TransferApplySelectDetailController controller = new TransferApplySelectDetailController();
    //         CreateRelationListPagingCmpCtrl cmp = new CreateRelationListPagingCmpCtrl();

    //         Test.startTest();
    //         cmp.pageController = controller;
    //         cmp.pageController.myComponentController.init();
    //         controller.init();
    //         Test.stopTest();

    //     System.assertEquals(controller.isComeFromRa ,true);
    //     System.debug('Success init');
    //  }
    // }

    @isTest
    public static void test_save() {
        setupTestData3();

                List<TransferApply__c> taList = [
                SELECT Id
                     , Add_Approval_Status__c
                     , Status__c
                     , RecordType.DeveloperName
                     , From_Location__c
                  FROM TransferApply__c ];

                PageReference ref =  new PageReference('/apex/TransferApplySelectDetail?id=' + taList[0].Id);
                Test.setCurrentPage(ref);

                TransferApplySelectDetailController controller = new TransferApplySelectDetailController();
                CreateRelationListPagingCmpCtrl cmp = new CreateRelationListPagingCmpCtrl();

                Test.startTest();
                cmp.pageController = controller;
                cmp.pageController.myComponentController.init();
                controller.init();
                controller.isNeedSearchFirst = true;
                controller.searchOpp();
                controller.viewList[0].check = true;
                controller.viewList[3].check = true;
                ((TransferApplyDetail__c) controller.viewlist[3].sobj).VF_TransferCount__c = 1;
                ((TransferApplyDetail__c) controller.viewlist[3].sobj).Manage_type__c = '个体管理';
                ((TransferApplyDetail__c) controller.viewlist[0].sobj).Salesdepartment_After__c = '4.华东营业本部';// 20210826 ljh 管理编码 add
                System.assertEquals(0, [SELECT Id FROM TransferApplySummary__c].size());
                System.assertEquals(0, [SELECT Id FROM TransferApplyDetail__c].size());//0
                controller.save();
                System.assertEquals(2, [SELECT Id FROM TransferApplySummary__c].size());//2
                System.assertEquals(4, [SELECT Id FROM TransferApplyDetail__c].size());
                Test.stopTest();

        System.debug('Success init');
    }

    @isTest
    public static void test_filter() {
        Oly_TriggerHandler.bypass(TransferApplyHandler.class.getName());
         setupTestData1();

                List<TransferApply__c> taList = [
                SELECT Id
                     , Add_Approval_Status__c
                     , Status__c
                     , RecordType.DeveloperName
                     , From_Location__c
                  FROM TransferApply__c ];

                PageReference ref =  new PageReference('/apex/TransferApplySelectDetail?id=' + taList[0].Id);
                Test.setCurrentPage(ref);

                TransferApplySelectDetailController controller = new TransferApplySelectDetailController();
                CreateRelationListPagingCmpCtrl cmp = new CreateRelationListPagingCmpCtrl();

                Test.startTest();
                cmp.pageController = controller;
                cmp.pageController.myComponentController.init();
                controller.init();
                controller.isNeedSearchFirst = true;
                controller.searchOpp();
                controller.fieldName = 'Salesdepartment__c';
                controller.operator = '==';
                controller.fieldValue = '';
                System.assertEquals(5, controller.viewlist.size());
                controller.filter();
                Test.stopTest();

        System.assertEquals(3, controller.viewlist.size());
        System.debug('Success init');
    }


    @isTest
    public static void test_filter_error() {
        Oly_TriggerHandler.bypass(TransferApplyHandler.class.getName());
          setupTestData1();

                List<TransferApply__c> taList = [
                SELECT Id
                     , Add_Approval_Status__c
                     , Status__c
                     , RecordType.DeveloperName
                     , From_Location__c
                  FROM TransferApply__c ];

                PageReference ref =  new PageReference('/apex/TransferApplySelectDetail?id=' + taList[0].Id);
                Test.setCurrentPage(ref);

                TransferApplySelectDetailController controller = new TransferApplySelectDetailController();
                CreateRelationListPagingCmpCtrl cmp = new CreateRelationListPagingCmpCtrl();


                Test.startTest();
                cmp.pageController = controller;
                cmp.pageController.myComponentController.init();
                controller.init();
                controller.isNeedSearchFirst = true;
                controller.searchOpp();
                controller.operator = '==';
                controller.fieldValue = '0.备品中心';
                controller.filter();
                Test.stopTest();

        System.assertEquals(1, controller.myComponentController.page);
        List<Apexpages.message> msgs = ApexPages.getMessages();
        System.assertEquals('请填写筛选条件', msgs[0].getDetail());
        System.debug('Success init');
    }

    @isTest
    public static void test_cancelDetail() {
          setupTestData3();

                List<TransferApply__c> taList = [
                SELECT Id
                     , Add_Approval_Status__c
                     , Status__c
                     , RecordType.DeveloperName
                     , From_Location__c
                  FROM TransferApply__c ];

                PageReference ref =  new PageReference('/apex/TransferApplySelectDetail?id=' + taList[0].Id);
                Test.setCurrentPage(ref);

                TransferApplySelectDetailController controller = new TransferApplySelectDetailController();
                CreateRelationListPagingCmpCtrl cmp = new CreateRelationListPagingCmpCtrl();
                Test.startTest();
                cmp.pageController = controller;
                cmp.pageController.myComponentController.init();
                controller.init();
                controller.isNeedSearchFirst = true;
                controller.searchOpp();
                controller.viewList[0].check = true;
                controller.save();
                System.assertEquals(1, [SELECT Id FROM TransferApplySummary__c].size());
                System.assertEquals(3, [SELECT Id FROM TransferApplyDetail__c].size());
                controller.cancelDetail();
                System.assertEquals(0, [SELECT Id FROM TransferApplySummary__c].size());
                System.assertEquals(0, [SELECT Id FROM TransferApplyDetail__c].size());
                Test.stopTest();

        System.debug('Success init');
    }

    @isTest
    public static void test_importCSVFile() {
          setupTestData3();
                List<TransferApply__c> taList = [
                SELECT Id
                     , Add_Approval_Status__c
                     , Status__c
                     , RecordType.DeveloperName
                     , From_Location__c
                  FROM TransferApply__c ];
                Asset ass = [SELECT Id FROM Asset WHERE SerialNumber = 'T1'];

                PageReference ref =  new PageReference('/apex/TransferApplySelectDetail?id=' + taList[0].Id);
                String csvStr = '保有设备,调拨数量,所在地区(本部)(调拨后),所在地区(省)(调拨后),备品分类(调拨后),备品管理编码(调拨后)';
                csvStr += '\r\n';
                csvStr += ass.Id+',1,1.华北营业本部,北京,产品试用,123132';
                ref.getParameters().put('csvData', csvStr);

                Test.setCurrentPage(ref);
                TransferApplySelectDetailController controller = new TransferApplySelectDetailController();
                CreateRelationListPagingCmpCtrl cmp = new CreateRelationListPagingCmpCtrl();

                Test.startTest();
                cmp.pageController = controller;
                cmp.pageController.myComponentController.init();
                controller.init();
                System.assertEquals(0, controller.viewlist.size());
                controller.importCSVFile();
                System.assertEquals(3, controller.viewlist.size());
                Test.stopTest();

        System.debug('Success init');
    }

    @isTest
    public static void testsearch() {
        Oly_TriggerHandler.bypass(TransferApplyHandler.class.getName());
          setupTestData1();

            List<TransferApply__c> taList = [
            SELECT Id
                 , Add_Approval_Status__c
                 , Status__c
                 , RecordType.DeveloperName
                 , From_Location__c
              FROM TransferApply__c ];

            PageReference ref =  new PageReference('/apex/TransferApplySelectDetail?id=' + taList[0].Id);
            Test.setCurrentPage(ref);

            TransferApplySelectDetailController controller = new TransferApplySelectDetailController();
            CreateRelationListPagingCmpCtrl cmp = new CreateRelationListPagingCmpCtrl();

            Test.startTest();
            cmp.pageController = controller;
            cmp.pageController.myComponentController.init();
            controller.init();
            controller.isNeedSearchFirst = true;
            controller.turnback();
            controller.savePage();
            controller.saveAndSearch();
            controller.saveAndSearchNext();
            controller.saveAndsearchPrevious();
            controller.searchNextCtrl();
            controller.searchPreviousCtrl();
            controller.clear();
            List<SelectOption> a = controller.salesdepartmentOpts;
            a = controller.salesProvinceOpts;
            a = controller.equipmentTypeOpts;
            a = controller.fieldNameOpts;
            a = controller.operatorOpts;
            controller.myComponentController.goPageInt = 1;
            controller.saveAndsearchGoPage();
            controller.searchGoPageCtrl();
            controller.keyword = 'CLH-250';
            controller.assetnumber = '1';
            controller.searchOpp();
            Test.stopTest();

        System.assertEquals(1 ,controller.myComponentController.page);
        System.assertEquals('', controller.fieldName);
        System.assertEquals('', controller.operator);
        System.assertEquals('', controller.fieldValue);
        System.debug('Success init');
    }
}