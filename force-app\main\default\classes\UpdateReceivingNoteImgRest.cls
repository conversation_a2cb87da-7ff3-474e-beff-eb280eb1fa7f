@RestResource(urlMapping='/UpdateReceivingNoteImg/*')
global without sharing class UpdateReceivingNoteImgRest {
	@HttpPost
    global static void doPost(String docId, String rnId, String libId) {
        RestResponse res = RestContext.response;
        res.addHeader('Content-Type', 'application/json');
        StaticParameter.ContentDocumentTrigger = false;
        StaticParameter.ContentDocumentLink = false;
        StaticParameter.ContentVerisonTrigger = false;
        ContentDocumentLink cdl1 = new ContentDocumentLink();
        cdl1.Visibility='AllUsers';
        cdl1.ShareType='I';
        cdl1.ContentDocumentId = docId;
        cdl1.LinkedEntityId = rnId;
        // cdl1.LinkedEntityId = libId;

        // libId = null;
        System.debug('zheli03'+libId);
        // libId = null;
        ContentDocumentLink cdl2 = new ContentDocumentLink();
        if (String.isNotBlank(libId)) {
            cdl2.Visibility='AllUsers';
            cdl2.ShareType='I';
            cdl2.ContentDocumentId = docId;
            cdl2.LinkedEntityId = libId;
        }
        System.debug('------------>' + libId);
        System.debug('------------>' + cdl2.LinkedEntityId);
        try {
            if (String.isNotBlank(libId)) {
                System.debug('zheli00');
            	insert new ContentDocumentLink[] {cdl1, cdl2};	
            } else {
                System.debug('zheli01');
                insert cdl1;
            }
		} catch ( Exception ex ) {
            res.statusCode = 200;
            String jsonResponse = '{"status": "Failure", "Message": "error when try to Link File to ReceivingNote. '+ ex +'"}';
            res.responseBody = blob.valueOf(jsonResponse);
            return;
        }
        
        res.statusCode = 200;
        String jsonResponse = '{"status": "Success", "message": {"docId": "'+ docId +'", "rnId": "'+ rnId +'", "libId": "'+ libId +'"}}';
        res.responseBody = blob.valueOf(jsonResponse);
        return;
    }
}