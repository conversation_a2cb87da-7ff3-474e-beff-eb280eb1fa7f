@isTest
private class TemperatureAndHumidityCheckTriggerTest {
    static testMethod void testMethod1() {
        Temperature_And_Humidity_Check__c TAHC1=new Temperature_And_Humidity_Check__c();
        TAHC1.Name='test01';
        TAHC1.Temperature_And_Humidity_Zone__c='A区';
        TAHC1.Temperature_Check_Result__c='OK';
        TAHC1.Humidity_Check_Result__c='OK';
        TAHC1.Internal_asset_location__c='北京d1';
        TAHC1.Inventory_Time__c='202002202';
        insert TAHC1;
    }
}