@isTest
private class UpdAccountExamineControllerTest {

	private static User getUser() {
        String timenow = Datetime.now().format('yyyyMMddHHmmss');
        User user1 = new User(Test_staff__c = true, LastName = 'TestMao', FirstName = 'TestMaoF',
                              Alias = 'hp', CommunityNickname = 'TestMao', Email = '<EMAIL>',
                              Username = 'Test' + timenow + '@sunbridge.com', IsActive = true, 
                              EmailEncodingKey = 'ISO-2022-JP',TimeZoneSidKey = 'Asia/Tokyo', 
                              LocaleSidKey = 'ja_JP', LanguageLocaleKey = 'ja',
                              ProfileId = System.Label.ProfileId_SystemAdmin,
                              Dept__c = '医疗华北营业本部', Job_Category__c = '销售服务', 
                              Province__c = '北京');

        List<Profile> p = [Select Id From Profile Where Name = '2S1_销售医院担当'];
        System.assertEquals(p.size(), 1);
        System.runAs(new User(Id = Userinfo.getUserId())) {
            insert user1;
        }
        return user1;
    }

    static testMethod void testMethod01() {
        User user = getUser();
        System.runAs(user) {

            //新建省
            Address_Level__c al = new Address_Level__c();
            al.Name = '東京';
            al.Level1_Code__c = 'CN-99';
            al.Level1_Sys_No__c = '999999';
            insert al;

            //新建客户
            RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
            
            Account acc = new Account();
            acc.RecordTypeId = rectCo.Id;
            acc.Name = 'HP test1';
            acc.Is_Active__c = '有効';
            insert acc;

            //新建 客户变更申请
            Account_Delay_Apply__c ada = new Account_Delay_Apply__c();
            ada.Hospital__c = acc.Id;
            ada.ChangeReason__c = '地址错误';
            ada.State_Master__c = al.Id;
            ada.Is_Active__c = '草案中';
            ada.Is_upload_file__c = true;
            ada.OpenWindow__c = user.Id;
            insert ada;

            String testlink = '医院变更审批_事业推进部';

	    	Approval.ProcessSubmitRequest r = new Approval.ProcessSubmitRequest();
		    r.setObjectId(ada.Id);
		    Approval.process(r);
		    String strid = r.getSubmitterId();
            //'/apex/UpdAccountExamine?AccId='+AccId+'&testlink='+testlink+'&'
		    PageReference page = new PageReference('/apex/UpdAccountExamine?AccId='+ada.Id+'&testlink='+testlink+'&');
        	System.Test.setCurrentPage(page);
        	UpdAccountExamineController controller = new UpdAccountExamineController();
        	controller.ApprovalAction = 'Approve';
        	controller.Approval();
		}
    }

    static testMethod void testMethod02() {
        User user = getUser();
        System.runAs(user) {

            //新建省
            Address_Level__c al = new Address_Level__c();
            al.Name = '東京';
            al.Level1_Code__c = 'CN-99';
            al.Level1_Sys_No__c = '999999';
            insert al;

            //新建客户
            RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
            
            Account acc = new Account();
            acc.RecordTypeId = rectCo.Id;
            acc.Name = 'HP test1';
            acc.Is_Active__c = '有効';
            insert acc;

            //新建 客户变更申请
            Account_Delay_Apply__c ada = new Account_Delay_Apply__c();
            ada.Hospital__c = acc.Id;
            ada.ChangeReason__c = '地址错误';
            ada.State_Master__c = al.Id;
            ada.Is_Active__c = '草案中';
            ada.Is_upload_file__c = true;
            ada.OpenWindow__c = user.Id;
            ada.InstitutionalType__c = '非医疗机构';
            insert ada;

            String testlink = '医院变更审批_事业推进部';

	    	Approval.ProcessSubmitRequest r = new Approval.ProcessSubmitRequest();
		    r.setObjectId(ada.Id);
		    Approval.process(r);
		    String strid = r.getSubmitterId();
            //'/apex/UpdAccountExamine?AccId='+AccId+'&testlink='+testlink+'&'
		    PageReference page = new PageReference('/apex/UpdAccountExamine?AccId='+ada.Id+'&testlink='+testlink+'&');
        	System.Test.setCurrentPage(page);
        	UpdAccountExamineController controller = new UpdAccountExamineController();
        	controller.ApprovalAction = 'Approve';
        	controller.Approval();
		}
    }

    static testMethod void testMethod03() {
        User user = getUser();
        System.runAs(user) {

            //新建省
            Address_Level__c al = new Address_Level__c();
            al.Name = '東京';
            al.Level1_Code__c = 'CN-99';
            al.Level1_Sys_No__c = '999999';
            insert al;

            //新建客户
            RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
            
            Account acc = new Account();
            acc.RecordTypeId = rectCo.Id;
            acc.Name = 'HP test1';
            acc.Is_Active__c = '有効';
            insert acc;

            //新建 客户变更申请
            Account_Delay_Apply__c ada = new Account_Delay_Apply__c();
            ada.Hospital__c = acc.Id;
            ada.ChangeReason__c = '地址错误';
            ada.State_Master__c = al.Id;
            ada.Is_Active__c = '草案中';
            ada.Is_upload_file__c = true;
            ada.OpenWindow__c = user.Id;
            ada.InstitutionalType__c = '医疗机构';
            ada.HospitalType__c = '企业集团';
            insert ada;

            String testlink = '医院变更审批_事业推进部';

	    	Approval.ProcessSubmitRequest r = new Approval.ProcessSubmitRequest();
		    r.setObjectId(ada.Id);
		    Approval.process(r);
		    String strid = r.getSubmitterId();
            //'/apex/UpdAccountExamine?AccId='+AccId+'&testlink='+testlink+'&'
		    PageReference page = new PageReference('/apex/UpdAccountExamine?AccId='+ada.Id+'&testlink='+testlink+'&');
        	System.Test.setCurrentPage(page);
        	UpdAccountExamineController controller = new UpdAccountExamineController();
        	controller.ApprovalAction = 'Approve';
        	controller.Approval();
		}
    }

    static testMethod void testMethod04() {
        User user = getUser();
        System.runAs(user) {

            //新建省
            Address_Level__c al = new Address_Level__c();
            al.Name = '東京';
            al.Level1_Code__c = 'CN-99';
            al.Level1_Sys_No__c = '999999';
            insert al;

            //新建客户
            RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
            
            Account acc = new Account();
            acc.RecordTypeId = rectCo.Id;
            acc.Name = 'HP test1';
            acc.Is_Active__c = '有効';
            insert acc;

            //新建 客户变更申请
            Account_Delay_Apply__c ada = new Account_Delay_Apply__c();
            ada.Hospital__c = acc.Id;
            ada.ChangeReason__c = '地址错误';
            ada.State_Master__c = al.Id;
            ada.Is_Active__c = '草案中';
            ada.Is_upload_file__c = true;
            ada.OpenWindow__c = user.Id;
            ada.InstitutionalType__c = '医疗机构';
            insert ada;

            String testlink = '医院变更审批_事业推进部';

	    	Approval.ProcessSubmitRequest r = new Approval.ProcessSubmitRequest();
		    r.setObjectId(ada.Id);
		    Approval.process(r);
		    String strid = r.getSubmitterId();
            //'/apex/UpdAccountExamine?AccId='+AccId+'&testlink='+testlink+'&'
		    PageReference page = new PageReference('/apex/UpdAccountExamine?AccId='+ada.Id+'&testlink='+testlink+'&');
        	System.Test.setCurrentPage(page);
        	UpdAccountExamineController controller = new UpdAccountExamineController();
        	controller.ApprovalAction = 'Approve';
        	controller.Approval();
		}
    }

    static testMethod void testMethod05() {
        User user = getUser();
        System.runAs(user) {

            //新建省
            Address_Level__c al = new Address_Level__c();
            al.Name = '東京';
            al.Level1_Code__c = 'CN-99';
            al.Level1_Sys_No__c = '999999';
            insert al;

            //新建客户
            RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
            
            Account acc = new Account();
            acc.RecordTypeId = rectCo.Id;
            acc.Name = 'HP test1';
            acc.Is_Active__c = '有効';
            insert acc;

            //新建 客户变更申请
            Account_Delay_Apply__c ada = new Account_Delay_Apply__c();
            ada.Hospital__c = acc.Id;
            ada.ChangeReason__c = '地址错误';
            ada.State_Master__c = al.Id;
            ada.Is_Active__c = '草案中';
            ada.Is_upload_file__c = true;
            ada.OpenWindow__c = user.Id;
            ada.InstitutionalType__c = '医疗机构';
            insert ada;

            String testlink = '医院变更审批_事业推进部';

	    	Approval.ProcessSubmitRequest r = new Approval.ProcessSubmitRequest();
		    r.setObjectId(ada.Id);
		    Approval.process(r);
		    String strid = r.getSubmitterId();
            //'/apex/UpdAccountExamine?AccId='+AccId+'&testlink='+testlink+'&'
		    PageReference page = new PageReference('/apex/UpdAccountExamine?AccId='+ada.Id+'&testlink='+testlink+'&');
        	System.Test.setCurrentPage(page);
        	UpdAccountExamineController controller = new UpdAccountExamineController();
        	controller.ApprovalAction = 'Reject';
        	controller.Approval();
		}
    }

    static testMethod void testMethod06() {
        User user = getUser();
        System.runAs(user) {

            //新建省
            Address_Level__c al = new Address_Level__c();
            al.Name = '東京';
            al.Level1_Code__c = 'CN-99';
            al.Level1_Sys_No__c = '999999';
            insert al;

            //新建客户
            RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
            
            Account acc = new Account();
            acc.RecordTypeId = rectCo.Id;
            acc.Name = 'HP test1';
            acc.Is_Active__c = '有効';
            insert acc;

            //新建 客户变更申请
            Account_Delay_Apply__c ada = new Account_Delay_Apply__c();
            ada.Hospital__c = acc.Id;
            ada.ChangeReason__c = '地址错误';
            ada.State_Master__c = al.Id;
            ada.Is_Active__c = '草案中';
            ada.Is_upload_file__c = true;
            ada.OpenWindow__c = user.Id;
            ada.InstitutionalType__c = '非医疗机构';
            ada.HospitalType__c = '高等院校';
            insert ada;

            String testlink = '质量法规二级部长';

	    	Approval.ProcessSubmitRequest r = new Approval.ProcessSubmitRequest();
		    r.setObjectId(ada.Id);
		    Approval.process(r);
		    String strid = r.getSubmitterId();
            //'/apex/UpdAccountExamine?AccId='+AccId+'&testlink='+testlink+'&'
		    PageReference page = new PageReference('/apex/UpdAccountExamine?AccId='+ada.Id+'&testlink='+testlink+'&');
        	System.Test.setCurrentPage(page);
        	UpdAccountExamineController controller = new UpdAccountExamineController();
        	controller.ApprovalAction = 'Approve';
        	controller.Approval();
		}
    }

    static testMethod void testMethod07() {
        User user = getUser();
        System.runAs(user) {

            //新建省
            Address_Level__c al = new Address_Level__c();
            al.Name = '東京';
            al.Level1_Code__c = 'CN-99';
            al.Level1_Sys_No__c = '999999';
            insert al;

            //新建客户
            RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
            
            Account acc = new Account();
            acc.RecordTypeId = rectCo.Id;
            acc.Name = 'HP test1';
            acc.Is_Active__c = '有効';
            insert acc;

            //新建 客户变更申请
            Account_Delay_Apply__c ada = new Account_Delay_Apply__c();
            ada.Hospital__c = acc.Id;
            ada.ChangeReason__c = '地址错误';
            ada.State_Master__c = al.Id;
            ada.Is_Active__c = '草案中';
            ada.Is_upload_file__c = true;
            ada.OpenWindow__c = user.Id;
            insert ada;

            String testlink = '经销商管理部一级审批';

	    	Approval.ProcessSubmitRequest r = new Approval.ProcessSubmitRequest();
		    r.setObjectId(ada.Id);
		    Approval.process(r);
		    String strid = r.getSubmitterId();
            //'/apex/UpdAccountExamine?AccId='+AccId+'&testlink='+testlink+'&'
		    PageReference page = new PageReference('/apex/UpdAccountExamine?AccId='+ada.Id+'&testlink='+testlink+'&');
        	System.Test.setCurrentPage(page);
        	UpdAccountExamineController controller = new UpdAccountExamineController();
        	controller.ApprovalAction = 'Approve';
        	controller.Approval();
		}
    }

    static testMethod void testMethod08() {
        User user = getUser();
        System.runAs(user) {

            //新建省
            Address_Level__c al = new Address_Level__c();
            al.Name = '東京';
            al.Level1_Code__c = 'CN-99';
            al.Level1_Sys_No__c = '999999';
            insert al;

            //新建客户
            RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
            
            Account acc = new Account();
            acc.RecordTypeId = rectCo.Id;
            acc.Name = 'HP test1';
            acc.Is_Active__c = '有効';
            insert acc;

            //新建 客户变更申请
            Account_Delay_Apply__c ada = new Account_Delay_Apply__c();
            ada.Hospital__c = acc.Id;
            ada.ChangeReason__c = '地址错误';
            ada.State_Master__c = al.Id;
            ada.Is_Active__c = '草案中';
            ada.Is_upload_file__c = true;
            ada.OpenWindow__c = user.Id;
            ada.WhetherRiskPassing__c = '否';
            insert ada;

            String testlink = '经销商管理部一级审批';

	    	Approval.ProcessSubmitRequest r = new Approval.ProcessSubmitRequest();
		    r.setObjectId(ada.Id);
		    Approval.process(r);
		    String strid = r.getSubmitterId();
            //'/apex/UpdAccountExamine?AccId='+AccId+'&testlink='+testlink+'&'
		    PageReference page = new PageReference('/apex/UpdAccountExamine?AccId='+ada.Id+'&testlink='+testlink+'&');
        	System.Test.setCurrentPage(page);
        	UpdAccountExamineController controller = new UpdAccountExamineController();
        	controller.ApprovalAction = 'Approve';
        	controller.Approval();
		}
    }

    static testMethod void testMethod09() {
        User user = getUser();
        System.runAs(user) {

            //新建省
            Address_Level__c al = new Address_Level__c();
            al.Name = '東京';
            al.Level1_Code__c = 'CN-99';
            al.Level1_Sys_No__c = '999999';
            insert al;

            //新建客户
            RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
            
            Account acc = new Account();
            acc.RecordTypeId = rectCo.Id;
            acc.Name = 'HP test1';
            acc.Is_Active__c = '有効';
            insert acc;

            //新建 客户变更申请
            Account_Delay_Apply__c ada = new Account_Delay_Apply__c();
            ada.Hospital__c = acc.Id;
            ada.ChangeReason__c = '地址错误';
            ada.State_Master__c = al.Id;
            ada.Is_Active__c = '草案中';
            ada.Is_upload_file__c = true;
            ada.OpenWindow__c = user.Id;
            ada.WhetherRiskPassing__c = '否';
            ada.InstitutionalType__c = '非医疗机构';
            ada.HospitalType__c = '企业集团';
            insert ada;

            String testlink = '经销商管理部一级审批';

	    	Approval.ProcessSubmitRequest r = new Approval.ProcessSubmitRequest();
		    r.setObjectId(ada.Id);
		    Approval.process(r);
		    String strid = r.getSubmitterId();
            //'/apex/UpdAccountExamine?AccId='+AccId+'&testlink='+testlink+'&'
		    PageReference page = new PageReference('/apex/UpdAccountExamine?AccId='+ada.Id+'&testlink='+testlink+'&');
        	System.Test.setCurrentPage(page);
        	UpdAccountExamineController controller = new UpdAccountExamineController();
        	controller.ApprovalAction = 'Approve';
        	controller.Approval();
		}
    }

    static testMethod void testMethod10() {
        User user = getUser();
        System.runAs(user) {

            //新建省
            Address_Level__c al = new Address_Level__c();
            al.Name = '東京';
            al.Level1_Code__c = 'CN-99';
            al.Level1_Sys_No__c = '999999';
            insert al;

            //新建客户
            RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
            
            Account acc = new Account();
            acc.RecordTypeId = rectCo.Id;
            acc.Name = 'HP test1';
            acc.Is_Active__c = '有効';
            insert acc;

            //新建 客户变更申请
            Account_Delay_Apply__c ada = new Account_Delay_Apply__c();
            ada.Hospital__c = acc.Id;
            ada.ChangeReason__c = '地址错误';
            ada.State_Master__c = al.Id;
            ada.Is_Active__c = '草案中';
            ada.Is_upload_file__c = true;
            ada.OpenWindow__c = user.Id;
            insert ada;

            String testlink = '总经理审批';

	    	Approval.ProcessSubmitRequest r = new Approval.ProcessSubmitRequest();
		    r.setObjectId(ada.Id);
		    Approval.process(r);
		    String strid = r.getSubmitterId();
            //'/apex/UpdAccountExamine?AccId='+AccId+'&testlink='+testlink+'&'
		    PageReference page = new PageReference('/apex/UpdAccountExamine?AccId='+ada.Id+'&testlink='+testlink+'&');
        	System.Test.setCurrentPage(page);
        	UpdAccountExamineController controller = new UpdAccountExamineController();
        	controller.ApprovalAction = 'Approve';
        	controller.Approval();
		}
    }

}