@isTest
private class TenderLostControllerTest {

    @isTest 
	static void TestMethod1() {
        //创建客户
        Account hospital = new Account();
        hospital.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'HP'].id;
        hospital.Name = 'test hospital';
        hospital.Is_Active__c = '有効';
        hospital.Attribute_Type__c = '卫生部';
        hospital.Speciality_Type__c = '综合医院';
        hospital.Grade__c = '一级';
        hospital.OCM_Category__c = 'SLTV';
        hospital.Is_Medical__c = '医疗机构';
        hospital.Town__c = '东京';
        hospital.Department_Name__c = 'testKS';
        insert hospital;

        //创建招标项目
    	Tender_information__c Ten = new Tender_information__c();
		Ten.Name = '123456';
		Ten.ProjectId__c = '38_99df2844cf784982acdc61d00d7a7dbb';
        ten.Hospital__c = hospital.Id;
        ten.Hospital1__c = hospital.Id;
        ten.Hospital2__c = hospital.Id;
        ten.Hospital3__c = hospital.Id;
        ten.Hospital4__c = hospital.Id;
		insert Ten;

        //询价
        Opportunity opp = new Opportunity();
        opp.Name = 'Testname0922';
        opp.Fund_Basis__c = '政府資金';
        opp.Sales_Method__c = '政府招标';
        opp.StageName = '引合';
        opp.Opportunity_No__c = '0801';                                   
        opp.Purchase_Reason__c = '新期';                            
        opp.Trade__c = '内貿';                                     
        opp.Close_Forecasted_Date__c = Date.today().addDays(90);   
        opp.CloseDate = Date.today().addDays(120);                
        opp.Purchase_Type__c = '一般引合';                          
        opp.Sales_Root__c = '販売店';                            
        opp.ifOpenBid__c = '公开招标';                           
        opp.LeadSource = '招标网';                                 
        opp.LeakageNumber__c = 1;                                   
        opp.Tender_Number__c = 1;                                   
        opp.ConfirmationofAward_createTime__c = Date.today();       
        opp.ConfirmationofAward__c = '竞争对手中标'; 
        insert opp;

        Map<String, Object> oppMap = new Map<String, Object>();
        oppMap.put('AccountId',hospital.Id);

        String str1 = JSON.serialize(opp);
        String str2 = JSON.serialize(oppMap);

        TenderLostController.InitData(String.valueOf(Ten.Id));
        TenderLostController.LinkedHospitals(String.valueOf(Ten.Id));
        TenderLostController.SaveData(str1);
        TenderLostController.SaveData(str2);

    }

}