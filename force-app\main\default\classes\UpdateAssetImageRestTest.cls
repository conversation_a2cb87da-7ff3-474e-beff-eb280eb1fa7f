@isTest
public with sharing class UpdateAssetImageRestTest {
    /**
    *@description 造数据，一个清单，二个一览，每个一览下7条明细
    */
    Static String repId;
    Static String assId;

      // AWSServiceTool2没上线就把这部分注释 start 20220408
      @TestSetup
      static void setup(){
              TestDataUtility.CreatePIPolicyConfigurations(new string[]{'Agency_Contact__c','Contact'});
      } 
      
      @isTest
      static void Test1(){
              Test.setMock(HttpCalloutMock.class, new HttpMock());

              List<Agency_Contact__c> lra = new List<Agency_Contact__c>();
              lra.add(new Agency_Contact__c(
              ));
              insert lra;
              Test.startTest();
              //system.debug(PIHelper.getPIIntegrationInfo('Agency_Contact__c').newEncryptUrl);
              AWSServiceTool2.EncryptPushCore(Json.serialize(lra),'Agency_Contact__c');
              AWSServiceTool2.EncryptPushFuture(null,null);
              Test.stopTest();
      }

      @isTest
      static void Test2(){
              Test.setMock(HttpCalloutMock.class, new HttpMock());

              List<Agency_Contact__c> lra = new List<Agency_Contact__c>();
              lra.add(new Agency_Contact__c(
                      Aws_Data_Id__c = '123456'
              ));
              insert lra;
              Test.startTest();
              //system.debug(PIHelper.getPIIntegrationInfo('Agency_Contact__c').newEncryptUrl);
              AWSServiceTool2.EncryptPushCore(Json.serialize(lra),'Agency_Contact__c');
              Test.stopTest();
      }

      @isTest
      static void Test3(){
              Test.setMock(HttpCalloutMock.class, new HttpMock());


              Test.startTest();
              AWSServiceTool2.EncryptPushData(new string[]{'0031000000O4Cff'});

              Test.stopTest();
      }

      //@isTest
      //  static void Test2(){
      //      Test.setMock(HttpCalloutMock.class, new HttpMock());

      //      List<Agency_Contact__c> lra = new List<Agency_Contact__c>();
      //      lra.add(new Agency_Contact__c(
      //          Id = 'a2R1m0000007BPD',
      //          Aws_Data_Id__c = '123456'
      //      ));
      //      Test.startTest();
      //      //system.debug(PIHelper.getPIIntegrationInfo('Agency_Contact__c').newEncryptUrl);
      //      AWSServiceTool2.EncryptPushCore(Json.serialize(lra),'Agency_Contact__c');
      //      Test.stopTest();
      //  }
      
      class HttpMock implements HttpCalloutMock{   
              public HTTPResponse respond(HTTPRequest request) {
                      // 创建一个假的回应
                      System.debug('------------------------------------------------------');
                      HttpResponse response = new HttpResponse();
                      string body = '';
                      system.debug(request.getEndpoint());
                      if(request.getEndpoint().contains('token')){
                      system.debug('url=token');
                      response.setHeader('Content-Type', 'application/json');
                      body='{ "message": "", "object": "freqfewqfewewfewfew", "status": "", "success": true, "timestamp": 0, "txId": "" }';          
                      } else if(request.getEndpoint().contains('insert')){
                      system.debug('url=Insert');
                      response.setHeader('Content-Type', 'application/json');
                      body='{ "message": "", "object": [ { "dataId": "123456", "directShippmentAddress": "", "directShippmentAddressEncrypt": "", "isDelete": 0, "phoneNumber": "", "phoneNumberEncrypt": "", "sfRecordId": "a2R1m0000007BPD" } ], "status": "", "success": true, "timestamp": 0, "txId": "" }';          
                      } else if(request.getEndpoint().contains('update')){
                      system.debug('url=update');
                      response.setHeader('Content-Type', 'application/json');
                      body='{ "message": "", "object": [ { "dataId": "123456", "directShippmentAddress": "", "directShippmentAddressEncrypt": "", "isDelete": 0, "phoneNumber": "", "phoneNumberEncrypt": "", "sfRecordId": "a2R1m0000007BPD" } ], "status": "", "success": true, "timestamp": 0, "txId": "" }';          
                      } else{
                      
                      }
                      
                      response.setBody(body);
                      response.setStatus('OK');
                      response.setStatusCode(200);
                      return response;
                      // }
              }
      }
      // AWSServiceTool2没上线就把这部分注释 end
      // ********* ljh end


    static void setupTestData() {
        String rectHp = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('HP').getRecordTypeId();
        String rectSct = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_GI').getRecordTypeId();
        String rectDpt = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_GI').getRecordTypeId();

        Profile p = [select Id from Profile where id =:System.Label.ProfileId_SystemAdmin];

        User hpOwner = new User(Test_staff__c = true, LastName = 'hp', FirstName = 'owner', Alias = 'hp', Work_Location__c = '北京', CommunityNickname = 'hpOwner', Email = '<EMAIL>', Username = '<EMAIL>', IsActive = true, EmailEncodingKey = 'ISO-2022-JP', TimeZoneSidKey = 'Asia/Tokyo', LocaleSidKey = 'ja_JP', LanguageLocaleKey = 'ja', ProfileId = p.id);
        insert hpOwner;
        User hpOwner2 = new User(Test_staff__c = true, LastName = 'hp2', FirstName = 'owner', Alias = 'hp2', Work_Location__c = '重庆', CommunityNickname = 'hpOwner2', Email = '<EMAIL>', Username = '<EMAIL>', IsActive = true, EmailEncodingKey = 'ISO-2022-JP', TimeZoneSidKey = 'Asia/Tokyo', LocaleSidKey = 'ja_JP', LanguageLocaleKey = 'ja', ProfileId = p.id);
        insert hpOwner2;
        StaticParameter.MaintenanceContractHpDeptUpdTrigger = true;
        FixtureUtil.SkipTrigger = true;
        // 病院作成
        Account hp = new Account(RecordTypeId = rectHp, Name = 'hp', OwnerId = hpOwner.Id);
        hp.FSE_GI_Main_Leader__c = hpOwner.Id;
        hp.FSE_SP_Main_Leader__c = hpOwner2.Id;
        insert hp;

        Account dc = [select Id, Name, RecordType_DeveloperName__c, Account2__c from Account where ParentId = :hp.Id and RecordType_DeveloperName__c = 'Department_Class_GI'];
        
        // 診療科を作る
        Account dpt = new Account(RecordTypeId = rectDpt);
        dpt.Name         = '*';
        dpt.Department_Name__c  = 'TestDepart';
        dpt.ParentId            = dc.Id;
        dpt.Department_Class__c = dc.Id;
        dpt.Hospital__c         = hp.Id;
        insert dpt;
        // 納入機器を作成する
        Asset asset = new Asset();
        asset.Name = 'asset1';
        asset.AccountId = dpt.Id;
        asset.Department_Class__c = dc.Id;
        asset.Hospital__c = hp.Id;
        asset.SerialNumber = '7450035';
        // asset.ProductCode = 'N1063441';
        asset.Product_Serial_No__c = 'N1063441:7450035';
        insert asset;
        assId = asset.Id;

        Repair__c repair = new Repair__c();
        repair.Account__c = dpt.Id;
        repair.Department_Class__c = dc.Id;
        repair.Hospital__c = hp.Id;
        repair.Delivered_Product__c = asset.Id;
        repair.SERVICE_CONTRACT_JUDEGE_DAY__C = Date.today().addDays(0);  // 维修合同判断日がサービス契約の中間辺りの日付
        repair.FSE_Work_Location__c = '上海';
        repair.Repair_Returned_To_HP_Date__c = Date.today();
        insert repair;
        repId = repair.Id;
        
    }

    static testMethod void testDoPost1(){
        setupTestData();
        RestRequest req = new RestRequest(); 
        RestResponse res = new RestResponse();

        req.requestURI = '/services/apexrest/v1/cans/';

        req.httpMethod = 'POST';
        RestContext.request = req;
        RestContext.response = res;

        String linkdId = System.Label.AssetImageFolder_new;
        String imageAsset =String.valueOf(EncodingUtil.base64Encode(Blob.valueof('TestA'))) ;
        String imageSerial = String.valueOf(EncodingUtil.base64Encode(Blob.valueof('TestB')));
        Asset asset = [SELECT Id,Product_Serial_No__c from Asset WHERE Id =:assId];
        Repair__c rep = [SELECT Id FROM Repair__c WHERE id =: repId];
        String repairDate = String.valueOf(Date.today().format());
        String remarkText = 'text';
        Test.startTest();
        ContentVersion versionA = new ContentVersion();
        versionA.VersionData = EncodingUtil.base64Decode(imageAsset);
        versionA.Title = asset.Product_Serial_No__c + '_Asset';
        versionA.PathOnClient = asset.Product_Serial_No__c + '_Asset'+'.jpg';
        insert versionA;
        versionA = [SELECT Id,VersionData,Title,ContentDocumentId From ContentVersion WHERE Id =: versionA.Id];
        ContentDocumentLink link = new ContentDocumentLink();
        link.ContentDocumentId = versionA.ContentDocumentId;
        link.LinkedEntityId = linkdId;
        link.ShareType = 'I';
        link.Visibility = 'AllUsers';
        StaticParameter.ContentDocumentLink = false;
        StaticParameter.ContentDocumentTrigger = false;
        insert link;


        ContentVersion versionS = new ContentVersion();
        versionS.VersionData = EncodingUtil.base64Decode(imageSerial);
        versionS.Title = asset.Product_Serial_No__c + '_Serial';
        versionS.PathOnClient = asset.Product_Serial_No__c + '_Serial'+'.jpg';
        insert versionS;

        versionS = [SELECT Id,VersionData,Title,ContentDocumentId From ContentVersion WHERE Id =: versionS.Id];
        ContentDocumentLink linkS = new ContentDocumentLink();
        linkS.ContentDocumentId = versionS.ContentDocumentId;
        linkS.LinkedEntityId = linkdId;
        linkS.ShareType = 'I';
        linkS.Visibility = 'AllUsers';
        StaticParameter.ContentDocumentLink = false;
        StaticParameter.ContentDocumentTrigger = false;
        insert linkS;
        
        // UpdateAssetImageRest.doPost(imageAsset, imageSerial,asset.Product_Serial_No__c,null,null,remarkText);
        UpdateAssetImageRest.doPost(imageAsset, imageSerial ,asset.Product_Serial_No__c ,rep.id ,repairDate ,remarkText);
        Test.stopTest();
    }

    static testMethod void testDoPost2(){
        setupTestData();
        RestRequest req = new RestRequest(); 
        RestResponse res = new RestResponse();

        req.requestURI = '/services/apexrest/v1/cans/';

        req.httpMethod = 'POST';
        RestContext.request = req;
        RestContext.response = res;

        String imageAsset ='' ;
        String imageSerial = '';
        Asset asset = [SELECT Id,Product_Serial_No__c from Asset WHERE Id =:assId];
        Repair__c rep = [SELECT Id FROM Repair__c WHERE id =: repId];
        String repairDate = String.valueOf(Date.today());
        String remarkText = 'text';
        String id = System.Label.ProductImageFolder_New;
        Test.startTest();

        UpdateAssetImageRest.doPost('', '',asset.Product_Serial_No__c,asset.id,repairDate,remarkText);

        UpdateAssetImageRest.doPost('', '',asset.Product_Serial_No__c,rep.id,repairDate,remarkText);

        repairDate = String.valueOf(Date.today().format());
        UpdateAssetImageRest.doPost('', '',asset.Product_Serial_No__c+'1',rep.id,repairDate,remarkText);


        Test.stopTest();
    }

    

    // static testMethod void testDoPost1_1(){
    //     setupTestData();
    //     RestRequest req = new RestRequest(); 
    //     RestResponse res = new RestResponse();

    //     req.requestURI = '/services/apexrest/v1/cans/';

    //     req.httpMethod = 'POST';
    //     RestContext.request = req;
    //     RestContext.response = res;

    //     String imageAsset =String.valueOf(EncodingUtil.base64Encode(Blob.valueof('TestA'))) ;
    //     String imageSerial = String.valueOf(EncodingUtil.base64Encode(Blob.valueof('TestB')));
    //     Asset asset = [SELECT Id,Product_Serial_No__c from Asset WHERE Id =:assId];
    //     Repair__c rep = [SELECT Id FROM Repair__c WHERE id =: repId];
    //     String repairDate = String.valueOf(Date.today().format());
    //     String remarkText = 'text';
    //     Test.startTest();

    //     ContentVersion version = new ContentVersion();
    //     version.VersionData =EncodingUtil.base64Decode('test3');
    //     version.Title = asset.Product_Serial_No__c+ '_Asset';
    //     version.PathOnClient = asset.Product_Serial_No__c+ '_Asset'+'.jpg';
    //     insert version;

        
    //     // UpdateAssetImageRest.doPost(imageAsset, imageSerial,asset.Product_Serial_No__c,null,null,remarkText);
    //     UpdateAssetImageRest.doPost(imageAsset, imageSerial ,asset.Product_Serial_No__c ,rep.id ,repairDate ,remarkText);
    //     Test.stopTest();
    // }

    // static testMethod void testDoPost1_2(){
    //     setupTestData();
    //     RestRequest req = new RestRequest(); 
    //     RestResponse res = new RestResponse();

    //     req.requestURI = '/services/apexrest/v1/cans/';

    //     req.httpMethod = 'POST';
    //     RestContext.request = req;
    //     RestContext.response = res;

    //     String imageAsset =String.valueOf(EncodingUtil.base64Encode(Blob.valueof('TestA'))) ;
    //     String imageSerial = String.valueOf(EncodingUtil.base64Encode(Blob.valueof('TestB')));
    //     Asset asset = [SELECT Id,Product_Serial_No__c from Asset WHERE Id =:assId];
    //     Repair__c rep = [SELECT Id FROM Repair__c WHERE id =: repId];
    //     String repairDate = String.valueOf(Date.today().format());
    //     String remarkText = 'text';
    //     Test.startTest();
        
    //     // UpdateAssetImageRest.doPost(imageAsset, imageSerial,asset.Product_Serial_No__c,null,null,remarkText);
    //     UpdateAssetImageRest.doPost('', imageSerial ,asset.Product_Serial_No__c ,asset.id ,repairDate ,remarkText);
    //     Test.stopTest();
    // }

    // static testMethod void testDoPost1_3(){
    //     setupTestData();
    //     RestRequest req = new RestRequest(); 
    //     RestResponse res = new RestResponse();

    //     req.requestURI = '/services/apexrest/v1/cans/';

    //     req.httpMethod = 'POST';
    //     RestContext.request = req;
    //     RestContext.response = res;

    //     String imageAsset =String.valueOf(EncodingUtil.base64Encode(Blob.valueof('TestA'))) ;
    //     String imageSerial = String.valueOf(EncodingUtil.base64Encode(Blob.valueof('TestB')));
    //     Asset asset = [SELECT Id,Product_Serial_No__c from Asset WHERE Id =:assId];
    //     Repair__c rep = [SELECT Id FROM Repair__c WHERE id =: repId];
    //     String repairDate = String.valueOf(Date.today().format());
    //     String remarkText = 'text';
    //     Test.startTest();


    //     ContentVersion version1 = new ContentVersion();
    //     version1.VersionData =EncodingUtil.base64Decode('test3');
    //     version1.Title = asset.Product_Serial_No__c + '_Serial';
    //     version1.PathOnClient = asset.Product_Serial_No__c+ '_Serial'+'.jpg';
    //     insert version1;
        
    //     // UpdateAssetImageRest.doPost(imageAsset, imageSerial,asset.Product_Serial_No__c,null,null,remarkText);
    //     UpdateAssetImageRest.doPost('', imageSerial ,asset.Product_Serial_No__c ,rep.id ,repairDate ,remarkText);
    //     Test.stopTest();
    // }


    // static testMethod void testDoPost2(){
    //     setupTestData();
    //     RestRequest req = new RestRequest(); 
    //     RestResponse res = new RestResponse();

    //     req.requestURI = '/services/apexrest/v1/cans/';

    //     req.httpMethod = 'POST';
    //     RestContext.request = req;
    //     RestContext.response = res;

    //     String imageAsset ='' ;
    //     String imageSerial = String.valueOf(EncodingUtil.base64Encode(Blob.valueof('TestB')));
    //     Asset asset = [SELECT Id,Product_Serial_No__c from Asset WHERE Id =:assId];
    //     Repair__c rep = [SELECT Id FROM Repair__c WHERE id =: repId];
    //     String repairDate = String.valueOf(Date.today().format());
    //     String remarkText = 'text';
    //     String id = System.Label.ProductImageFolder_New;
    //     Test.startTest();


        
    //     // UpdateAssetImageRest.doPost(imageAsset, imageSerial,asset.Product_Serial_No__c,null,null,remarkText);
    //     UpdateAssetImageRest.doPost('', '',asset.Product_Serial_No__c,rep.id,repairDate,remarkText);
    //     Test.stopTest();
    // }

    // static testMethod void testDoPost2_2(){
    //     setupTestData();
    //     RestRequest req = new RestRequest(); 
    //     RestResponse res = new RestResponse();

    //     req.requestURI = '/services/apexrest/v1/cans/';

    //     req.httpMethod = 'POST';
    //     RestContext.request = req;
    //     RestContext.response = res;

    //     String imageAsset ='' ;
    //     String imageSerial = String.valueOf(EncodingUtil.base64Encode(Blob.valueof('TestB')));
    //     Asset asset = [SELECT Id,Product_Serial_No__c from Asset WHERE Id =:assId];
    //     Repair__c rep = [SELECT Id FROM Repair__c WHERE id =: repId];
    //     String repairDate = String.valueOf(Date.today());
    //     String remarkText = 'text';
    //     String id = System.Label.ProductImageFolder_New;
    //     Test.startTest();


        
    //     // UpdateAssetImageRest.doPost(imageAsset, imageSerial,asset.Product_Serial_No__c,null,null,remarkText);
    //     UpdateAssetImageRest.doPost('', '',asset.Product_Serial_No__c,rep.id,repairDate,remarkText);
    //     Test.stopTest();
    // }

    // static testMethod void testDoPost3(){
    //     setupTestData();
    //     RestRequest req = new RestRequest(); 
    //     RestResponse res = new RestResponse();

    //     req.requestURI = '/services/apexrest/v1/cans/';

    //     req.httpMethod = 'POST';
    //     RestContext.request = req;
    //     RestContext.response = res;

    //     String imageAsset ='' ;
    //     String imageSerial = String.valueOf(EncodingUtil.base64Encode(Blob.valueof('TestB')));
    //     Asset asset = [SELECT Id,Product_Serial_No__c from Asset WHERE Id =:assId];
    //     String remarkText = 'text';
    //     Test.startTest();


        
    //     // UpdateAssetImageRest.doPost(imageAsset, imageSerial,asset.Product_Serial_No__c,null,null,remarkText);
    //     UpdateAssetImageRest.doPost('', imageSerial,asset.Product_Serial_No__c,'','',remarkText);
    //     Test.stopTest();
    // }

    

}