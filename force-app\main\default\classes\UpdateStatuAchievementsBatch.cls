global class UpdateStatuAchievementsBatch implements Database.Batchable<sObject> {
    
    /**
     * コンスタント
     */
    global UpdateStatuAchievementsBatch() {
    }
    
    /**
     * startには、queryを実行、备品Set明细を検索
     */
    global Database.QueryLocator start(Database.BatchableContext BC) {
        return Database.getQueryLocator(
            [select Id, Current_status__c, Last_week__c, X2weeks_ago__c, X3Weeks_ago__c, X4weeks_ago__c, X5weeks_ago__c, Last_Batch_Day__c
               from Statu_Achievements__c
              order by Id]
        );
    }
    
    global void execute(Database.BatchableContext BC, List<SObject> saList) {
        List<Statu_Achievements__c> updateList = new List<Statu_Achievements__c>();
        
        for (SObject obj: saList) {
            Statu_Achievements__c sa = (Statu_Achievements__c) obj;
            Date lastweek = sa.Last_Batch_Day__c == null ? Date.today().addDays(-7).toStartOfWeek() : sa.Last_Batch_Day__c.toStartOfWeek();
            Date todayweek = Date.today().toStartOfWeek();
            Integer cntweek = lastweek.daysBetween(todayweek) / 7;

            if (cntweek == 0) {
                continue;
            } else if (cntweek == 1) {
                sa.X5weeks_ago__c = sa.X4weeks_ago__c;
                sa.X4weeks_ago__c = sa.X3Weeks_ago__c;
                sa.X3Weeks_ago__c = sa.X2weeks_ago__c;
                sa.X2weeks_ago__c = sa.Last_week__c;
                sa.Last_week__c = sa.Current_status__c;
                sa.Last_Batch_Day__c = Date.today();
                updateList.add(sa);
            } else if (cntweek == 2) {
                sa.X5weeks_ago__c = sa.X3weeks_ago__c;
                sa.X4weeks_ago__c = sa.X2Weeks_ago__c;
                sa.X3Weeks_ago__c = sa.Last_week__c;
                sa.X2weeks_ago__c = null;
                sa.Last_week__c = sa.Current_status__c;
                sa.Last_Batch_Day__c = Date.today();
                updateList.add(sa);
            } else if (cntweek == 3) {
                sa.X5weeks_ago__c = sa.X2Weeks_ago__c;
                sa.X4weeks_ago__c = sa.Last_week__c;
                sa.X3Weeks_ago__c = null;
                sa.X2weeks_ago__c = null;
                sa.Last_week__c = sa.Current_status__c;
                sa.Last_Batch_Day__c = Date.today();
                updateList.add(sa);
            } else if (cntweek == 4) {
                sa.X5weeks_ago__c = sa.Last_week__c;
                sa.X4weeks_ago__c = null;
                sa.X3Weeks_ago__c = null;
                sa.X2weeks_ago__c = null;
                sa.Last_week__c = sa.Current_status__c;
                sa.Last_Batch_Day__c = Date.today();
                updateList.add(sa);
            } else if (cntweek >= 5) {
                sa.X5weeks_ago__c = null;
                sa.X4weeks_ago__c = null;
                sa.X3Weeks_ago__c = null;
                sa.X2weeks_ago__c = null;
                sa.Last_week__c = sa.Current_status__c;
                sa.Last_Batch_Day__c = Date.today();
                updateList.add(sa);
            }
        }
        
        if (updateList.size() > 0) update updateList;
    }
    
    global void finish(Database.BatchableContext BC) {
        // 今回はやることないです
    }
    
    
}