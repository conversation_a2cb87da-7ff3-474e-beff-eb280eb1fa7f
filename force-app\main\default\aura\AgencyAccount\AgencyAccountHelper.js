({
	doinit : function(component, event, helper) {
		component.set('v.login',true);
        component.set("v.awsdata",{});
        
        var action = component.get("c.getfiledsmap");
        action.setCallback(this, function(response) {
            var state = response.getState();
            if(state === "SUCCESS") {
                var res = response.getReturnValue();
                
                component.set("v.filedsmap", res);
            } else if (state === "ERROR") {
                var errors = response.getError();
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "错误",
                    "type":"error",
                    "message": errors[0].message
                });
                toastEvent.fire();
            }
        });
        $A.enqueueAction(action);
		var action2 = component.get("c.getAccounts");
        action2.setCallback(this, function(response) {
        	var state = response.getState();
            console.log('state = ' + JSON.stringify(state))
            if(state === "SUCCESS") {
                var acList = response.getReturnValue();
                component.set("v.accounts", acList);
                //deloitte-zhj ******** PIPL还原 start
                // //deloitte-zhj 2023/07/17 PIPL解密 start  
                // helper.Decrypt(component,helper,function(){
                //     setTimeout(function() {
                //         component.set('v.login',false);
                //         helper.hiddenEncrypt();
                //     }, 1000);
                // })
                // //end
                //deloitte-zhj ******** PIPL还原 end
                component.set('v.login',false);
                
            } else if (state === "ERROR") {
                var errors = response.getError();
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "错误",
                    "type":"error",
                    "message": errors[0].message
                });
                toastEvent.fire();
                component.set('v.login',false);
            }
        });


        var action3 = component.get("c.GetConfig");
        action3.setCallback(this, function(response) {
            var state = response.getState();
            if(state === "SUCCESS") {
                var res = response.getReturnValue();
                //AWSService.sfSessionId = res.sessionId;
                //component.set("v.staticResource",JSON.parse(res.staticResource));
                $A.enqueueAction(action2);
                
            } else if (state === "ERROR") {
                var errors = response.getError();
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "错误",
                    "type":"error",
                    "message": errors[0].message
                });
                toastEvent.fire();
            }
        });
        $A.enqueueAction(action3);
        
        
	},

	clearContact: function(component, event, helper) {
		component.set('v.hosStr','');
		component.set('v.conStr','');
		this.searchContact(component, event, helper);
	},

	searchContact: function(component, event, helper) {
		component.set('v.login',true);
        var hosStr = component.get("v.hosStr");
        var conStr = component.get("v.conStr");
        //deloitte-zhj ******** PIPL还原
        var action = component.get("c.searchAccounts");
        // var action = component.get("c.searchAccounts2");
        // let awsdata = component.get("v.awsdata");
        // let staticResource = component.get("v.staticResource");
        action.setCallback(this, function(response) {
        	var state = response.getState();
            if(state === "SUCCESS") {
                var acList = response.getReturnValue();
                component.set("v.accounts", acList);
                //deloitte-zhj ******** PIPL还原
                // //deloitte-zhj 2023/07/17 PIPL解密 start  
                // helper.Decrypt(component,helper,function(){
                //     setTimeout(function() {
                //         component.set('v.login',false);
                //         helper.hiddenEncrypt();
                //     }, 1000);
                // })
                // //end
                component.set('v.login',false);
            } else if (state === "ERROR") {
                var errors = response.getError();
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "错误",
                    "type":"error",
                    "message": errors[0].message
                });
                toastEvent.fire();
                component.set('v.login',false);
            }
        });

        if (conStr) {
            //deloitte-zhhj ******** PIPL还原
            //     AWSService.search(staticResource.searchUrl,JSON.stringify({
            //         "name":conStr
            //     }),function(data){
            //         $A.getCallback(function(){
            //             let dataIds = [];
            //             if(data.object && data.object.length > 0){
            //                 for(let d of data.object){
            //                     if(d.dataId){
            //                         dataIds.push(d.dataId);
            //                         awsdata[d.dataId] = d;
            //                     }else{
            //                         console.log('d.dataId == null : ' + JSON.stringify(d))
            //                     }
            //                 }
            //             }
                        
            //             action.setParams({"hosStr": hosStr, awsids:dataIds});
            //             $A.enqueueAction(action);
            //         })()
                    
            //    },staticResource.token);
            action.setParams({"hosStr": hosStr, "conStr": conStr});
            $A.enqueueAction(action);
        }else{
            action.setParams({"hosStr": hosStr});
            $A.enqueueAction(action);
        }

        

        

        

        
    },
    //deloitte-zhj ******** PIPL还原 start
    // Decrypt :function(component, helper, callback){
    //     let awsdata = component.get("v.awsdata");
    //     let need_query = [];
    //     let acList = component.get("v.accounts");
    //     let staticResource = component.get("v.staticResource");
    //     for (const acc of acList) {
    //         if(acc.AWS_Data_Id__c &&
    //             (!awsdata.hasOwnProperty(acc.AWS_Data_Id__c) || !awsdata[acc.AWS_Data_Id__c])
    //             ){
    //             need_query.push(acc.AWS_Data_Id__c);
    //         }
    //     }
    //     let Foo = function(){
    //         for (const acc of acList) {
    //             if(acc.AWS_Data_Id__c && awsdata.hasOwnProperty(acc.AWS_Data_Id__c)){
    //                 acc.awsdata = awsdata[acc.AWS_Data_Id__c];
    //             }
    //         }
    //         component.set("v.accounts", acList);
    //         if(callback)callback();
    //     }

    //     if(need_query.length>0){
    //         AWSService.search(staticResource.searchUrl,JSON.stringify({
    //              "dataIds":need_query
    //             }),function(data){
    //                 $A.getCallback(function(){
    //                     if(data.object && data.object.length > 0){
    //                         for(let d of data.object){
    //                             if(d.dataId){
    //                                 awsdata[d.dataId] = d;
    //                             }
    //                         }
    //                     }
                        
    //                     Foo();
    //                 })()
                    
    //         },staticResource.token);
    //     }else{
    //         Foo();
    //     }
    // },
    // saveLog:function(component,module,url,request,response,status){
    //     var action = component.get("c.SaveLog");
    //     action.setParams({
    //         "module": hosStr,
    //         "content": content,
    //         "status": status,
    //         "respMsg": respMsg
    //         });
            
    //     $A.enqueueAction(action);
    // },
    

    // //deloitte-zhj 2023/07/17 PIPL解密 start  
    // hiddenEncrypt:function(){
    //     console.log('enter hiddenEncrypt');
    //     let encryptElements = document.querySelectorAll(".encrypt");
    //     console.log('encryptElements length = ' + encryptElements.length);
    //     // 遍历所有的.encrypt元素并隐藏它们
    //     encryptElements.forEach(function(element) {
    //         element.style.display = "none";
    //     });
    //     let decryptElements = document.querySelectorAll(".decrypt");
    //     // 遍历所有的.decrypt元素并显示它们
    //     decryptElements.forEach(function(element) {
    //         element.style.display = "unset";
    //     });
    //     console.log('end hiddenEncrypt');
    // },
    // //end
    //deloitte-zhj ******** PIPL还原 end
})