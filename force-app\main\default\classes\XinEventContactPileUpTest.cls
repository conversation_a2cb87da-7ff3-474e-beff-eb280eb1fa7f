/**
 * This class contains unit tests for validating the behavior of Apex classes
 * and triggers.
 *
 * Unit tests are class methods that verify whether a particular piece
 * of code is working properly. Unit test methods take no arguments,
 * commit no data to the database, and are flagged with the testMethod
 * keyword in the method definition.
 *
 * All test methods in an organization are executed whenever Apex code is deployed
 * to a production organization to confirm correctness, ensure code
 * coverage, and prevent regressions. All Apex classes are
 * required to have at least 75% code coverage in order to be deployed
 * to a production organization. In addition, all triggers must have some code coverage.
 * 
 * The @isTest class annotation indicates this class only contains test
 * methods. Classes defined with the @isTest annotation do not count against
 * the organization size limit for all Apex scripts.
 *
 * See the Apex Language Reference for more information about Testing and Code Coverage.
 */
@isTest
private class XinEventContactPileUpTest {

    static testMethod void myUnitTest() {
        System.assertEquals(false, StaticParameter.EscapeActivityHistoryTrigger);
        Daily_Report__c dr1 = new Daily_Report__c(Reported_Date__c = Date.newInstance(2015, 1, 1), Status__c = '作成中',Reporter__c = Userinfo.getUserId());
        Daily_Report__c dr2 = new Daily_Report__c(Reported_Date__c = Date.newInstance(2015, 1, 2), Status__c = '申請中',Reporter__c = Userinfo.getUserId());
        insert new Daily_Report__c[] {dr1, dr2};
        
        RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        RecordType rtDoc = [select id from RecordType where IsActive = true and DeveloperName =:'Doctor'];
        
        Account acc = new Account();
        acc.RecordTypeId = rectCo.Id;
        acc.Name = 'HPテスト1';
        insert acc;
        
        Contact con1 = new Contact(
            Doctor_Division1__c = 'Director',
            LastName = 'yuanzhang',
            RecordTypeId = rtDoc.id,
            AccountId = acc.Id
        );
        Contact con2 = new Contact(
            Doctor_Division1__c = 'Vice-Chief of Department',
            LastName = 'zhuren',
            RecordTypeId = rtDoc.id,
            AccountId = acc.Id
        );
        insert new Contact[] {con1, con2};
        
        Event__c ec1 = new Event__c(
            Daily_Report__c = dr1.Id,
            StartDateTime__c = System.now(),
            EndDateTime__c = System.now(),
            Subject__c = 'Subject1',
            Location__c = 'Location',
            ActivityDate__c = dr1.reported_date__c,
            Visitor1_ID__c = con1.Id,
            Visitor2_ID__c = con2.Id
        );
        Event__c ec2 = new Event__c(
            Daily_Report__c = dr2.Id,
            StartDateTime__c = System.now(),
            EndDateTime__c = System.now(),
            Subject__c = 'Subject2',
            Location__c = 'Location',
            ActivityDate__c = dr2.reported_date__c,
            Visitor1_ID__c = con1.Id,
            Visitor2_ID__c = con2.Id
        );
        insert new Event__c[] {ec1, ec2};
        
        Activity_History_Daily_Report__c ah1 = new Activity_History_Daily_Report__c(
            EventC_ID__c = ec1.Id,
            Daily_Report__c = dr1.Id,
            Date__c = dr1.reported_date__c,
            Contact__c = con1.Id
        );
        Activity_History_Daily_Report__c ah2 = new Activity_History_Daily_Report__c(
            EventC_ID__c = ec1.Id,
            Daily_Report__c = dr1.Id,
            Date__c = dr1.reported_date__c,
            Contact__c = con2.Id
        );
        Activity_History_Daily_Report__c ah3 = new Activity_History_Daily_Report__c(
            EventC_ID__c = ec2.Id,
            Daily_Report__c = dr2.Id,
            Date__c = dr2.reported_date__c,
            Contact__c = con1.Id
        );
        Activity_History_Daily_Report__c ah4 = new Activity_History_Daily_Report__c(
            EventC_ID__c = ec2.Id,
            Daily_Report__c = dr2.Id,
            Date__c = dr2.reported_date__c,
            Contact__c = con2.Id
        );
        insert new Activity_History_Daily_Report__c[] {ah1, ah2, ah3, ah4};

        con1 = [select Visit_Count1__c, Visit_Count2__c, Visit_Count3__c, Visit_Count4__c, Visit_Count5__c, Visit_Count6__c, 
                       Visit_Count7__c, Visit_Count8__c, Visit_Count9__c, Visit_Count10__c, Visit_Count11__c, Visit_Count12__c 
                from Contact where Id = :con1.Id];
        System.assertEquals(1, con1.Visit_Count1__c);
        
        dr1.Status__c = '申請中';
        update dr1;
        
        update new Activity_History_Daily_Report__c[] {ah1, ah2, ah3, ah4};
        
        con2 = [select Visit_Count1__c, Visit_Count2__c, Visit_Count3__c, Visit_Count4__c, Visit_Count5__c, Visit_Count6__c, 
                       Visit_Count7__c, Visit_Count8__c, Visit_Count9__c, Visit_Count10__c, Visit_Count11__c, Visit_Count12__c 
                from Contact where Id = :con2.Id];
        System.assertEquals(2, con2.Visit_Count1__c);
        
        delete new Activity_History_Daily_Report__c[] {ah1, ah2, ah3, ah4};
        con2 = [select Visit_Count1__c, Visit_Count2__c, Visit_Count3__c, Visit_Count4__c, Visit_Count5__c, Visit_Count6__c, 
                       Visit_Count7__c, Visit_Count8__c, Visit_Count9__c, Visit_Count10__c, Visit_Count11__c, Visit_Count12__c 
                from Contact where Id = :con2.Id];
        System.assertEquals(null, con2.Visit_Count1__c);
    }
}