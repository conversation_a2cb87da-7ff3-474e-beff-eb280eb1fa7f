<aura:component controller="CreateTargetCmp" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,force:lightningQuickAction,forceCommunity:availableForAllPageTypes" access="global" >
	<aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
	<aura:attribute name="recordId" type="Id" />
	<aura:attribute name="hospital" type="string" default=""/>
	<aura:attribute name="test" type="string" default="1"/>
	<aura:handler event="force:refreshView" action="{!c.isRefreshed}" />
	<aura:attribute name="hospitalId" type="string" default=""/>
	<aura:attribute name="GIAmount" type="string" default="0.00"/>
	<aura:attribute name="BFAmount" type="string" default="0.00"/>
	<aura:attribute name="ETAmount" type="string" default="0.00"/>
	<aura:attribute name="GSAmount" type="string" default="0.00"/>
	<aura:attribute name="UROAmount" type="string" default="0.00"/>
	<aura:attribute name="GYNAmount" type="string" default="0.00"/>
	<aura:attribute name="ENTAmount" type="string" default="0.00"/>
	<aura:attribute name="OTHAmount" type="string" default="0.00"/>
	<aura:attribute name="OCMTerm" type="string" default=""/>
	<aura:attribute name="OCMTerm2" type="string" default=""/><!--WYL 20240830 add-->
	<aura:attribute name="button_text" type="string" default="新建"/>
	<aura:handler name="change" value="{!v.record.Agency_Hospital__c}" action="{!c.hosChange}"/>
	<aura:attribute name="hospitalList" type="sobject[]" />
	<aura:attribute name="record" type="Agency_Opportunity__c" default="{'sobjectType':'Agency_Opportunity__c'}"/>
    <aura:handler name="change" value="{!v.recordET.Product_Category1__c}" action="{!c.productcategoryETChange1}"/>
    <aura:handler name="change" value="{!v.recordET.Product_Category2__c}" action="{!c.productcategoryETChange2}"/>
    <aura:handler name="change" value="{!v.recordET.Product_Category3__c}" action="{!c.productcategoryETChange3}"/>
	

    <aura:handler name="change" value="{!v.recordBF.Product_Category1__c}" action="{!c.productcategoryBFChange1}"/>
    <aura:handler name="change" value="{!v.recordBF.Product_Category2__c}" action="{!c.productcategoryBFChange2}"/>
    <aura:handler name="change" value="{!v.recordBF.Product_Category3__c}" action="{!c.productcategoryBFChange3}"/>
    <aura:handler name="change" value="{!v.recordGS.Product_Category1__c}" action="{!c.productcategoryGSChange1}"/>
    <aura:handler name="change" value="{!v.recordGS.Product_Category2__c}" action="{!c.productcategoryGSChange2}"/>
    <aura:handler name="change" value="{!v.recordGS.Product_Category3__c}" action="{!c.productcategoryGSChange3}"/>
    <aura:handler name="change" value="{!v.recordURO.Product_Category1__c}" action="{!c.productcategoryUROChange1}"/>
    <aura:handler name="change" value="{!v.recordURO.Product_Category2__c}" action="{!c.productcategoryUROChange2}"/>
    <aura:handler name="change" value="{!v.recordURO.Product_Category3__c}" action="{!c.productcategoryUROChange3}"/>
    <aura:handler name="change" value="{!v.recordGYN.Product_Category1__c}" action="{!c.productcategoryGYNChange1}"/>
    <aura:handler name="change" value="{!v.recordGYN.Product_Category2__c}" action="{!c.productcategoryGYNChange2}"/>
    <aura:handler name="change" value="{!v.recordGYN.Product_Category3__c}" action="{!c.productcategoryGYNChange3}"/>
    <aura:handler name="change" value="{!v.recordENT.Product_Category1__c}" action="{!c.productcategoryENTChange1}"/>
    <aura:handler name="change" value="{!v.recordENT.Product_Category2__c}" action="{!c.productcategoryENTChange2}"/>
    <aura:handler name="change" value="{!v.recordENT.Product_Category3__c}" action="{!c.productcategoryENTChange3}"/>
    <aura:handler name="change" value="{!v.recordOTH.Product_Category1__c}" action="{!c.productcategoryOTHChange1}"/>
    <aura:handler name="change" value="{!v.recordOTH.Product_Category2__c}" action="{!c.productcategoryOTHChange2}"/>
    <aura:handler name="change" value="{!v.recordOTH.Product_Category3__c}" action="{!c.productcategoryOTHChange3}"/>
	<div class="button_area">
		<ui:button label="{!v.button_text}" press="{!c.toggle_report}"/>
    </div>
    <div aura:id="modal_window" role="dialog" tabindex="-1" class="disp_none slds-modal slds-fade-in-open slds-modal--large" aria-labelledby="headerTarget">
    	<div class="slds-modal__container">
	    	<div class="slds-modal__header">
	            <button class="slds-button slds-modal__close slds-button--icon-inverse" title="Close" onclick="{!c.toggle_report}">
	                <lightning:icon iconName="utility:close" size="medium" alternative-text="close" variant="inverse"/>
	                <span class="slds-assistive-text">Close</span>
	            </button>
	            <h2 id="headerTarget" class="slds-text-heading--medium">目标({!v.OCMTerm})</h2>
	        </div>
	        <div class="slds-modal__content slds-grow slds-p-around--medium">
	            <div class="slds-box slds-theme--shade ">
	            	<fieldset class="slds-form--compound">
						<div class="slds-form-element__group">
							<div class="slds-form-element__row">
							    <div class="slds-form-element slds-size--1-of-2">
									<label class="slds-form-element__label" for="input-hos">医院</label>
									<!-- <force:inputField value="{!v.record.Agency_Hospital__c}" aura:id="input-hos"/> -->
									<lightning:recordEditForm objectApiName="Agency_Opportunity__c">
										<lightning:inputField variant="label-hidden" fieldName="Agency_Hospital__c" value="{!v.record.Agency_Hospital__c}" aura:id="input-hos"/>
									</lightning:recordEditForm>
							    </div>
						    </div>
						    <!-- <h4 class="slds-section__title">访问目标</h4>
						    <div class="slds-form-element__row">
							    <div class="slds-form-element slds-size-1-of-2">
							    	<label class="slds-form-element__label" for="input-target">目标医院</label>
							        <force:inputField value="{!v.record2.TargetHospital__c}" aura:id="input-target"/>
								</div>
							</div> -->
							<h4 class="slds-section__title">产品目标</h4>
						    <div class="slds-form-element__row">
								<div class="slds-size--1-of-7 slds-grid--align-center">
									<div style="margin-top: 32px;">消化科</div>
								</div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分1</label>
							    	<ui:inputSelect aura:id="select_GI1" class="slds-select" change="{!c.productcategoryGIChange1}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_GI4_1" class="slds-select"/>
							    </div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分2</label>
							    	<ui:inputSelect aura:id="select_GI2" class="slds-select" change="{!c.productcategoryGIChange2}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_GI4_2" class="slds-select"/>
							    </div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分3</label>
							    	<ui:inputSelect aura:id="select_GI3" class="slds-select" change="{!c.productcategoryGIChange3}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_GI4_3" class="slds-select"/>
							    </div>
						    </div>
						    <div class="slds-form-element__row">
						    	<div class="slds-size--1-of-7 slds-grid--align-center">
									<div style="margin-top: 32px;">ET耗材</div>
								</div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分1</label>
							    	<ui:inputSelect aura:id="select_ET1" class="slds-select" change="{!c.productcategoryETChange1}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_ET4_1" class="slds-select"/>
							    </div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分2</label>
							    	<ui:inputSelect aura:id="select_ET2" class="slds-select" change="{!c.productcategoryETChange2}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_ET4_2" class="slds-select"/>
							    </div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分3</label>
							    	<ui:inputSelect aura:id="select_ET3" class="slds-select" change="{!c.productcategoryETChange3}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_ET4_3" class="slds-select" />
							    </div>
						    </div>
						    <div class="slds-form-element__row">
						    	<div class="slds-size--1-of-7 slds-grid--align-center">
									<div style="margin-top: 32px;">呼吸科</div>
								</div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分1</label>
							    	<ui:inputSelect aura:id="select_BF1" class="slds-select" change="{!c.productcategoryBFChange1}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_BF4_1" class="slds-select" />
							    </div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分2</label>
							    	<ui:inputSelect aura:id="select_BF2" class="slds-select" change="{!c.productcategoryBFChange2}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_BF4_2" class="slds-select" />
							    </div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分3</label>
							    	<ui:inputSelect aura:id="select_BF3" class="slds-select" change="{!c.productcategoryBFChange3}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_BF4_3" class="slds-select" />
							    </div>
						    </div>
						    <div class="slds-form-element__row">
						    	<div class="slds-size--1-of-7 slds-grid--align-center">
									<div style="margin-top: 32px;">普外科</div>
								</div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分1</label>
							    	<ui:inputSelect aura:id="select_GS1" class="slds-select" change="{!c.productcategoryGSChange1}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_GS4_1" class="slds-select" />
							    </div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分2</label>
							    	<ui:inputSelect aura:id="select_GS2" class="slds-select" change="{!c.productcategoryGSChange2}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_GS4_2" class="slds-select" />
							    </div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分3</label>
							    	<ui:inputSelect aura:id="select_GS3" class="slds-select" change="{!c.productcategoryGSChange3}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_GS4_3" class="slds-select" />
							    </div>
						    </div>
						    <div class="slds-form-element__row">
						    	<div class="slds-size--1-of-7 slds-grid--align-center">
									<div style="margin-top: 32px;">泌尿科</div>
								</div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分1</label>
							    	<ui:inputSelect aura:id="select_URO1" class="slds-select" change="{!c.productcategoryUROChange1}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_URO4_1" class="slds-select" />
							    </div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分2</label>
							    	<ui:inputSelect aura:id="select_URO2" class="slds-select" change="{!c.productcategoryUROChange2}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_URO4_2" class="slds-select" />
							    </div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分3</label>
							    	<ui:inputSelect aura:id="select_URO3" class="slds-select" change="{!c.productcategoryUROChange3}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_URO4_3" class="slds-select" />
							    </div>
						    </div>
						    <div class="slds-form-element__row">
						    	<div class="slds-size--1-of-7 slds-grid--align-center">
									<div style="margin-top: 32px;">妇科</div>
								</div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分1</label>
							    	<ui:inputSelect aura:id="select_GYN1" class="slds-select" change="{!c.productcategoryGYNChange1}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_GYN4_1" class="slds-select" />
							    </div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分2</label>
							    	<ui:inputSelect aura:id="select_GYN2" class="slds-select" change="{!c.productcategoryGYNChange2}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_GYN4_2" class="slds-select" />
							    </div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分3</label>
							    	<ui:inputSelect aura:id="select_GYN3" class="slds-select" change="{!c.productcategoryGYNChange3}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_GYN4_3" class="slds-select" />
							    </div>
						    </div>
						    <div class="slds-form-element__row">
						    	<div class="slds-size--1-of-7 slds-grid--align-center">
									<div style="margin-top: 32px;">耳鼻喉科</div>
								</div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分1</label>
							    	<ui:inputSelect aura:id="select_ENT1" class="slds-select" change="{!c.productcategoryENTChange1}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_ENT4_1" class="slds-select" />
							    </div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分2</label>
							    	<ui:inputSelect aura:id="select_ENT2" class="slds-select" change="{!c.productcategoryENTChange2}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_ENT4_2" class="slds-select" />
							    </div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分3</label>
							    	<ui:inputSelect aura:id="select_ENT3" class="slds-select" change="{!c.productcategoryENTChange3}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_ENT4_3" class="slds-select" />
							    </div>
						    </div>
						    <div class="slds-form-element__row">
						    	<div class="slds-size--1-of-7 slds-grid--align-center">
									<div style="margin-top: 32px;">其他</div>
								</div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分1</label>
							    	<ui:inputSelect aura:id="select_OTH1" class="slds-select" change="{!c.productcategoryOTHChange1}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_OTH4_1" class="slds-select" />
							    </div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分2</label>
							    	<ui:inputSelect aura:id="select_OTH2" class="slds-select" change="{!c.productcategoryOTHChange2}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_OTH4_2" class="slds-select" />
							    </div>
							    <div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">产品区分3</label>
							    	<ui:inputSelect aura:id="select_OTH3" class="slds-select" change="{!c.productcategoryOTHChange3}"/>
							    </div>
								<div class="slds-form-element slds-size--1-of-7">
							    	<label class="slds-form-element__label" for="input-hos">金额目标(不含税)</label>
							    	<ui:inputText aura:id="select_OTH4_3" class="slds-select" />
							    </div>
						    </div>
						</div>
					</fieldset>
	            </div>
	        </div>
	        <div class="slds-modal__footer slds-grid slds-grid--align-spread">
	        	<div class="slds-order--1" />
	        	<ui:button aura:id="save_button" label="保存" class="slds-button slds-button--brand slds-order--2" press="{!c.createTarget}"/>
	        	<div class="slds-order--3" />
	        </div>
    	</div>
    </div>
    <div aura:id="modal_bg" class="disp_none slds-backdrop slds-backdrop--open"></div>
</aura:component>