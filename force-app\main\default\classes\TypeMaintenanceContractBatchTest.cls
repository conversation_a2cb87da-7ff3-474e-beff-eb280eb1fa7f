@isTest
private class TypeMaintenanceContractBatchTest {
	static testMethod void TypeMaintenanceContractBatch() {
		ControllerUtil.EscapeNFM001Trigger = true;
		// 病院を作る
		Account hospital = new Account();
		hospital.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'HP'].id;
		hospital.Name = 'test hospital';
		hospital.FSE_SP_Main_Leader__c = UserInfo.getUserId();
		hospital.FSE_GI_Main_Leader__c = UserInfo.getUserId();
		insert hospital;

		// 戦略科室を得る
		List<Account> strategicDep = [SELECT ID, Name FROM Account WHERE parentId = : hospital.Id AND recordType.DeveloperName = 'Department_Class_GI'];

		// 診療科を作る
		Account dep = new Account();
		dep.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'Department_GI'].id;
		dep.Name = 'test dep';
		dep.ParentId = strategicDep[0].Id;
		dep.Department_Class__c = strategicDep[0].Id;
		dep.Hospital__c = hospital.Id;
		insert dep;

		// 维修合同を作成する
		Maintenance_Contract__c contract = new Maintenance_Contract__c();
		contract.Name = 'tect contract';
		contract.status__c ='契約';
		contract.Maintenance_Contract_No__c = 'Kami_Contract_No';
		contract.Contract_Conclusion_Date__c = Date.today();
		contract.Hospital__c = hospital.Id;
		contract.Department_Class__c = strategicDep[0].Id;
		contract.Department__c = dep.Id;
		contract.Contract_Start_Date__c = Date.today()-3;
		contract.Contract_End_Date__c = Date.today();
		contract.SalesOfficeCode_selection__c = '北京RC';

		Maintenance_Contract__c contract2 = contract.clone();
		contract2.Maintenance_Contract_No__c = 'Kami_Contract_No_2';
		contract2.Contract_Start_Date__c = Date.today()-3;
		contract2.Contract_End_Date__c = Date.today();

		Maintenance_Contract__c contract3 = contract.clone();
		contract3.Maintenance_Contract_No__c = 'Kami_Contract_No_3';
		contract3.Contract_Start_Date__c = Date.today()-3000;
		contract3.Contract_End_Date__c = Date.today()-600;

		insert new Maintenance_Contract__c[] {contract,contract2};

		Product2 prd1 = new Product2();
		prd1.ProductCode_Ext__c     = 'Prd1';
		prd1.ProductCode            = 'Prd1';
		prd1.Repair_Product_Code__c = 'Prd1_RP';
		prd1.Name                   = 'Prd1';
		prd1.Manual_Entry__c        = false;
		insert prd1;

		Asset ast1 = new Asset();
		ast1.Name= '保有設備1';
		ast1.Hospital__c= hospital.Id;
		ast1.Department_Class__c= strategicDep[0].Id;
		ast1.AccountId= dep.Id;
		ast1.Product2Id= prd1.Id;
		ast1.SerialNumber= 'SerialNumber1';
		ast1.Guarantee_period_for_products__c = Date.today();
		ast1.InstallDate= null;

		Asset ast2 = ast1.clone();
		ast2.Name= '保有設備2';
		ast2.SerialNumber= 'SerialNumber2';
		ast2.Guarantee_period_for_products__c = Date.today();
		ast2.InstallDate= Date.today()-400;

		Asset ast3 = ast1.clone();
		ast3.Name= '保有設備3';
		ast3.SerialNumber= 'SerialNumber3';
		ast3.InstallDate= Date.today()-39;

		insert new Asset[] {ast1,ast2,ast3};

		Maintenance_Contract_Asset__c mca1 = new Maintenance_Contract_Asset__c();
		mca1.Asset__c = ast1.Id;
		mca1.Maintenance_Contract__c = contract.id;

		Maintenance_Contract_Asset__c mca2 = new Maintenance_Contract_Asset__c();
		mca2.Asset__c = ast2.Id;
		mca2.Maintenance_Contract__c = contract.id;
		Maintenance_Contract_Asset__c mca3 = new Maintenance_Contract_Asset__c();
		mca3.Asset__c = ast3.Id;
		mca3.Maintenance_Contract__c = contract.id;

		Maintenance_Contract_Asset__c mca4 = new Maintenance_Contract_Asset__c();
		mca4.Asset__c = ast1.Id;
		mca4.Maintenance_Contract__c = contract2.id;
		Maintenance_Contract_Asset__c mca5 = new Maintenance_Contract_Asset__c();
		mca5.Asset__c = ast2.Id;
		mca5.Maintenance_Contract__c = contract2.id;
		Maintenance_Contract_Asset__c mca6 = new Maintenance_Contract_Asset__c();
		mca6.Asset__c = ast3.Id;
		mca6.Maintenance_Contract__c = contract2.id;

		insert new Maintenance_Contract_Asset__c[] {mca1,mca2,mca3,mca4,mca5,mca6};

		List<String> IdList=new List<String> {hospital.Id};
		Database.executeBatch(new TypeMaintenanceContractBatch(IdList));
		Database.executeBatch(new TypeMaintenanceContractBatch());
	}
    static testMethod void Type2MaintenanceContractBatch() {
		ControllerUtil.EscapeNFM001Trigger = true;
		// 病院を作る
		Account hospital = new Account();
		hospital.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'HP'].id;
		hospital.Name = 'test hospital';
		hospital.FSE_SP_Main_Leader__c = UserInfo.getUserId();
		hospital.FSE_GI_Main_Leader__c = UserInfo.getUserId();
		insert hospital;

		// 戦略科室を得る
		List<Account> strategicDep = [SELECT ID, Name FROM Account WHERE parentId = : hospital.Id AND recordType.DeveloperName = 'Department_Class_GI'];

		// 診療科を作る
		Account dep = new Account();
		dep.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'Department_GI'].id;
		dep.Name = 'test dep';
		dep.ParentId = strategicDep[0].Id;
		dep.Department_Class__c = strategicDep[0].Id;
		dep.Hospital__c = hospital.Id;
		insert dep;

		// 维修合同を作成する
		Maintenance_Contract__c contract = new Maintenance_Contract__c();
		contract.Name = 'tect contract';
		contract.status__c ='契約';
		contract.Maintenance_Contract_No__c = 'Kami_Contract_No';
		contract.Contract_Conclusion_Date__c = Date.today();
		contract.Hospital__c = hospital.Id;
		contract.Department_Class__c = strategicDep[0].Id;
		contract.Department__c = dep.Id;
		contract.Contract_Start_Date__c = Date.today()-3;
		contract.Contract_End_Date__c = Date.today();
		contract.SalesOfficeCode_selection__c = '北京RC';

		Maintenance_Contract__c contract2 = contract.clone();
		contract2.Maintenance_Contract_No__c = 'Kami_Contract_No_2';
		contract2.Contract_Start_Date__c = Date.today()-3;
		contract2.Contract_End_Date__c = Date.today();

		Maintenance_Contract__c contract3 = contract.clone();
		contract3.Maintenance_Contract_No__c = 'Kami_Contract_No_3';
		contract3.Contract_Start_Date__c = Date.today()-3000;
		contract3.Contract_End_Date__c = Date.today()-600;

		insert new Maintenance_Contract__c[] {contract,contract2};

		Product2 prd1 = new Product2();
		prd1.ProductCode_Ext__c     = 'Prd1';
		prd1.ProductCode            = 'Prd1';
		prd1.Repair_Product_Code__c = 'Prd1_RP';
		prd1.Name                   = 'Prd1';
		prd1.Manual_Entry__c        = false;
		insert prd1;

		Asset ast1 = new Asset();
		ast1.Name= '保有設備1';
		ast1.Hospital__c= hospital.Id;
		ast1.Department_Class__c= strategicDep[0].Id;
		ast1.AccountId= dep.Id;
		ast1.Product2Id= prd1.Id;
		ast1.SerialNumber= 'SerialNumber1';
		ast1.Guarantee_period_for_products__c = Date.today();
		ast1.InstallDate= null;

		Asset ast2 = ast1.clone();
		ast2.Name= '保有設備2';
		ast2.SerialNumber= 'SerialNumber2';
		ast2.Guarantee_period_for_products__c = Date.today();
		ast2.InstallDate= Date.today()-400;

		Asset ast3 = ast1.clone();
		ast3.Name= '保有設備3';
		ast3.SerialNumber= 'SerialNumber3';
		ast3.InstallDate= Date.today()-39;

		insert new Asset[] {ast1,ast2,ast3};

		Maintenance_Contract_Asset__c mca1 = new Maintenance_Contract_Asset__c();
		mca1.Asset__c = ast1.Id;
		mca1.Maintenance_Contract__c = contract.id;

		Maintenance_Contract_Asset__c mca2 = new Maintenance_Contract_Asset__c();
		mca2.Asset__c = ast2.Id;
		mca2.Maintenance_Contract__c = contract.id;
		Maintenance_Contract_Asset__c mca3 = new Maintenance_Contract_Asset__c();
		mca3.Asset__c = ast3.Id;
		mca3.Maintenance_Contract__c = contract.id;

		Maintenance_Contract_Asset__c mca4 = new Maintenance_Contract_Asset__c();
		mca4.Asset__c = ast1.Id;
		mca4.Maintenance_Contract__c = contract2.id;
		Maintenance_Contract_Asset__c mca5 = new Maintenance_Contract_Asset__c();
		mca5.Asset__c = ast2.Id;
		mca5.Maintenance_Contract__c = contract2.id;
		Maintenance_Contract_Asset__c mca6 = new Maintenance_Contract_Asset__c();
		mca6.Asset__c = ast3.Id;
		mca6.Maintenance_Contract__c = contract2.id;

		insert new Maintenance_Contract_Asset__c[] {mca1,mca2,mca3,mca4,mca5,mca6};

		List<String> IdList=new List<String> {hospital.Id};
		Database.executeBatch(new Type2MaintenanceContractBatch(IdList));
		Database.executeBatch(new Type2MaintenanceContractBatch());
	}

    static testMethod void Type3MaintenanceContractBatch() {
		ControllerUtil.EscapeNFM001Trigger = true;
		// 病院を作る
		Account hospital = new Account();
		hospital.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'HP'].id;
		hospital.Name = 'test hospital';
		hospital.FSE_SP_Main_Leader__c = UserInfo.getUserId();
		hospital.FSE_GI_Main_Leader__c = UserInfo.getUserId();
		insert hospital;

		// 戦略科室を得る
		List<Account> strategicDep = [SELECT ID, Name FROM Account WHERE parentId = : hospital.Id AND recordType.DeveloperName = 'Department_Class_GI'];

		// 診療科を作る
		Account dep = new Account();
		dep.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'Department_GI'].id;
		dep.Name = 'test dep';
		dep.ParentId = strategicDep[0].Id;
		dep.Department_Class__c = strategicDep[0].Id;
		dep.Hospital__c = hospital.Id;
		insert dep;

		// 维修合同を作成する
		Maintenance_Contract__c contract = new Maintenance_Contract__c();
		contract.Name = 'tect contract';
		contract.status__c ='契約';
		contract.Maintenance_Contract_No__c = 'Kami_Contract_No';
		contract.Contract_Conclusion_Date__c = Date.today();
		contract.Hospital__c = hospital.Id;
		contract.Department_Class__c = strategicDep[0].Id;
		contract.Department__c = dep.Id;
		contract.Contract_Start_Date__c = Date.today()-3;
		contract.Contract_End_Date__c = Date.today();
		contract.SalesOfficeCode_selection__c = '北京RC';

		Maintenance_Contract__c contract2 = contract.clone();
		contract2.Maintenance_Contract_No__c = 'Kami_Contract_No_2';
		contract2.Contract_Start_Date__c = Date.today()-3;
		contract2.Contract_End_Date__c = Date.today();

		Maintenance_Contract__c contract3 = contract.clone();
		contract3.Maintenance_Contract_No__c = 'Kami_Contract_No_3';
		contract3.Contract_Start_Date__c = Date.today()-3000;
		contract3.Contract_End_Date__c = Date.today()-600;

		insert new Maintenance_Contract__c[] {contract,contract2};

		Product2 prd1 = new Product2();
		prd1.ProductCode_Ext__c     = 'Prd1';
		prd1.ProductCode            = 'Prd1';
		prd1.Repair_Product_Code__c = 'Prd1_RP';
		prd1.Name                   = 'Prd1';
		prd1.Manual_Entry__c        = false;
		insert prd1;

		Asset ast1 = new Asset();
		ast1.Name= '保有設備1';
		ast1.Hospital__c= hospital.Id;
		ast1.Department_Class__c= strategicDep[0].Id;
		ast1.AccountId= dep.Id;
		ast1.Product2Id= prd1.Id;
		ast1.SerialNumber= 'SerialNumber1';
		ast1.Guarantee_period_for_products__c = Date.today();
		ast1.InstallDate= null;

		Asset ast2 = ast1.clone();
		ast2.Name= '保有設備2';
		ast2.SerialNumber= 'SerialNumber2';
		ast2.Guarantee_period_for_products__c = Date.today();
		ast2.InstallDate= Date.today()-400;

		Asset ast3 = ast1.clone();
		ast3.Name= '保有設備3';
		ast3.SerialNumber= 'SerialNumber3';
		ast3.InstallDate= Date.today()-39;

		insert new Asset[] {ast1,ast2,ast3};

		Maintenance_Contract_Asset__c mca1 = new Maintenance_Contract_Asset__c();
		mca1.Asset__c = ast1.Id;
		mca1.Maintenance_Contract__c = contract.id;

		Maintenance_Contract_Asset__c mca2 = new Maintenance_Contract_Asset__c();
		mca2.Asset__c = ast2.Id;
		mca2.Maintenance_Contract__c = contract.id;
		Maintenance_Contract_Asset__c mca3 = new Maintenance_Contract_Asset__c();
		mca3.Asset__c = ast3.Id;
		mca3.Maintenance_Contract__c = contract.id;

		Maintenance_Contract_Asset__c mca4 = new Maintenance_Contract_Asset__c();
		mca4.Asset__c = ast1.Id;
		mca4.Maintenance_Contract__c = contract2.id;
		Maintenance_Contract_Asset__c mca5 = new Maintenance_Contract_Asset__c();
		mca5.Asset__c = ast2.Id;
		mca5.Maintenance_Contract__c = contract2.id;
		Maintenance_Contract_Asset__c mca6 = new Maintenance_Contract_Asset__c();
		mca6.Asset__c = ast3.Id;
		mca6.Maintenance_Contract__c = contract2.id;

		insert new Maintenance_Contract_Asset__c[] {mca1,mca2,mca3,mca4,mca5,mca6};

		List<String> IdList=new List<String> {hospital.Id};
		Database.executeBatch(new Type3MaintenanceContractBatch(IdList));
		Database.executeBatch(new Type3MaintenanceContractBatch());
	}
}