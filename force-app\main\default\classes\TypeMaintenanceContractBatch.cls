// create tcm 20211203 更改维修合同——用户类型和合同种类
public class TypeMaintenanceContractBatch implements Database.Batchable<sObject> {

	public List<String> IdList;

	public TypeMaintenanceContractBatch() {
	}

	public TypeMaintenanceContractBatch(List<String> IdList) {
		this.IdList=IdList;
	}

	public Database.QueryLocator start(Database.BatchableContext BC) {
		//通过医院查保有设备
		string sql = 'select id from Asset where Hospital__r.RecordTypeId=\''+System.label.Hospital_RecordType+'\' ';
		if (IdList != null && IdList.size() > 0) {
			sql += 'and Hospital__c in :IdList ';
		}
		return Database.getQueryLocator(sql);
	}

	public void execute(Database.BatchableContext BC, List<Asset> asstList) {
		List<String> astIdList=new List<String>();
		for (Asset asst : asstList) {
			astIdList.add(asst.Id);
		}

		// 通过保有设备查所有维修合同/保有设备
		List<Maintenance_Contract_Asset__c> mcaList=[select id,Asset__c,Asset__r.name,asset__r.InstallDate,Asset__r.CurrentContract_F__c,Maintenance_Contract__r.name,Maintenance_Contract__r.Contract_Start_Date__c, Maintenance_Contract__r.Contract_end_Date__c from Maintenance_Contract_Asset__c where Asset__c in : astIdList and Maintenance_Contract__r.RecordTypeId!=:System.label.maintenance_contract and Maintenance_Contract__r.Status__c in ('契約','契約満了') and Maintenance_Contract__c!=:System.label.maintenance_contract_1 order by asset__c, Maintenance_Contract__r.Contract_Start_Date__c];
		List<String> astList=new List<String>();  //作为判断保有设备是否存在
		List<String> firstList=new List<String>();
		for (Maintenance_Contract_Asset__c mcai : mcaList) {
			if (astList.contains(mcai.asset__c)) {
				continue;
			}
			Integer i=0;
			Date lastEndDate=mcai.Maintenance_Contract__r.Contract_Start_Date__c;  //初始化上期结束日
			for (Maintenance_Contract_Asset__c mcaj : mcaList) {
				if (mcai.Asset__c==mcaj.Asset__c) {
					Date installDate=mcaj.Asset__r.InstallDate;  //安装日期
					Date startDate=mcaj.Maintenance_Contract__r.Contract_Start_Date__c;  //开始日
					Date endDate=mcaj.Maintenance_Contract__r.Contract_end_Date__c;  //结束日

					if (installDate==null) {
						mcaj.New_Contract_TypeF__c='首签合同';
						lastEndDate=mcaj.Maintenance_Contract__r.Contract_end_Date__c;
						i++;
						continue;
					}

					// 没有同期中的上期合同
					if (i==0) {
						// 且当期合同的安装日小于合同开始日且在半年以内，判断为新品合同
						if (installDate.monthsBetween(startDate)<=6&&installDate.monthsBetween(startDate)>=0) {
							mcaj.New_Contract_TypeF__c='新品合同';
						}else {
							// 否则判断为首签合同。
							mcaj.New_Contract_TypeF__c='首签合同';
						}
						i++;
					}else if(lastEndDate.monthsBetween(startDate)<=12) {
						mcaj.New_Contract_TypeF__c='续签合同';
					}else {
						mcaj.New_Contract_TypeF__c='非续签合同';
					}
					lastEndDate=mcaj.Maintenance_Contract__r.Contract_end_Date__c;
				}
			}
			astList.add(mcai.Asset__c);
		}
		update mcaList;

		System.debug('mcMap等于');
		System.debug('mcaList等于'+mcaList);
	}

	public void finish(Database.BatchableContext BC) {

	}
}

// List<String> IdList=new List<String>{'a0H1000000QCQfj'};
// Database.executeBatch(new TypeMaintenanceContractBatch(IdList));

// select id, Asset__c, New_Contract_TypeF__c, Asset__r.CurrentContract_F__c, Maintenance_Contract__r.name, Maintenance_Contract__r.New_Contract_TypeF_Text__c, Maintenance_Contract__r.Contract_Start_Date__c, Maintenance_Contract__r.Contract_end_Date__c, asset__r.InstallDate from Maintenance_Contract_Asset__c where Maintenance_Contract__r.Hospital__c= '0011000000V9P5Z' and Maintenance_Contract__r.RecordTypeId!='01210000000gTYv' and Maintenance_Contract__r.Status__c in ('契約', '契約満了') order by Maintenance_Contract__c, Maintenance_Contract__r.Contract_Start_Date__c