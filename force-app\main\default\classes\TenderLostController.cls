public without sharing class TenderLostController {

    //初始化
    @AuraEnabled
    public static  String InitData(String ParamIdStr) {
        //根据招标项目Id  查  招标项目名称  作为 询价名称
        List<Tender_information__c> tenList = Database.query('Select Id, Name From Tender_information__c Where Id = : ParamIdStr ');
        Opportunity opp = new Opportunity();
        if(tenList.size() > 0){
            opp.Name = tenList[0].Name;
            opp.Bidding_Project_Name_Bid__c = ParamIdStr;
        }
        return JSON.serialize(opp);
    }

    // 已关联的医院
    @AuraEnabled
    public static List<String> LinkedHospitals(String ParamIdStr) {
        List<Tender_information__c> tenList = Database.query('Select Id, Hospital__c, Hospital1__c, Hospital2__c, Hospital3__c, Hospital4__c From Tender_information__c Where Id = : ParamIdStr ');
        List<String> hospitals = new List<String>();
        if(tenList.size() > 0){
            if (String.isNotBlank(tenList[0].Hospital__c)) {
                hospitals.add(tenList[0].Hospital__c);
            }
            if (String.isNotBlank(tenList[0].Hospital1__c)) {
                hospitals.add(tenList[0].Hospital1__c);
            }
            if (String.isNotBlank(tenList[0].Hospital2__c)) {
                hospitals.add(tenList[0].Hospital2__c);
            }
            if (String.isNotBlank(tenList[0].Hospital3__c)) {
                hospitals.add(tenList[0].Hospital3__c);
            }
            if (String.isNotBlank(tenList[0].Hospital4__c)) {
                hospitals.add(tenList[0].Hospital4__c);
            }
        }
        return hospitals;
    }

    //把页面上的数据赋值到询价对象上
    private static Opportunity mergeInfo(Map<String, Object> oppMap){
        Opportunity opp = new Opportunity();

        // 名称
        opp.Name = String.valueOf(oppMap.get('Name'));
        //询价 关联 招标项目
        opp.Bidding_Project_Name_Bid__c = String.valueOf(oppMap.get('Bidding_Project_Name_Bid__c'));
        //页面上获取 资金来源
        opp.Fund_Basis__c = String.valueOf(oppMap.get('Fund_Basis__c'));
        //页面上获取 招标方式
        opp.Sales_Method__c = String.valueOf(oppMap.get('Sales_Method__c'));
        // 科室
        opp.AccountId = String.valueOf(oppMap.get('AccountId'));

        System.debug('lt123test01默认询价内容'+opp);

        // 查找科室相关信息
        Account acc = [select Id, Name, Parent.Parent.State_Master__r.Name, Parent.Department_Class_Label__c 
                        ,Parent.Parent.OCM_man_province_HP__c  //******** lt DB202303246427 --青岛拆分 -- 根据询“省（客户）”赋值SAP上传省 add
                       from Account where Id = :opp.AccountId];

        System.debug('lt123test02默认询价内容'+opp);
        // ******** ljh DB202212030068 start
        /* 以后变化不大就这样吧代码中写死，就这样几个省有问题
        内蒙古自治区--->内蒙古
        宁夏回族自治区--->宁夏自治区
        新疆维吾尔自治区--->新疆自治区
        黑龙江--->黑龙江省*/
        // opp.SAP_Province__c = acc.Parent.Parent.State_Master__r.Name; // SAP上传省
        //******** lt DB202303246427 --青岛拆分 -- 根据询“省（客户）”赋值SAP上传省 start
        Map<String,String> SAP_ProvinceMap = new Map<String,String>();
        // SAP_ProvinceMap.put('内蒙古自治区','内蒙古');
        // SAP_ProvinceMap.put('宁夏回族自治区','宁夏自治区');
        // SAP_ProvinceMap.put('新疆维吾尔自治区','新疆自治区');
        // SAP_ProvinceMap.put('黑龙江','黑龙江省');
        SAP_ProvinceMap.put('宁夏','宁夏自治区');
        SAP_ProvinceMap.put('新疆','新疆自治区');
        SAP_ProvinceMap.put('黑龙江','黑龙江省');
        SAP_ProvinceMap.put('广西','广西自治区');
        SAP_ProvinceMap.put('大连','大连市');
        SAP_ProvinceMap.put('沈阳','辽宁省');    //暂定辽宁
        SAP_ProvinceMap.put('广东','广东省');
        SAP_ProvinceMap.put('深圳','深圳市');
        SAP_ProvinceMap.put('青岛','青岛市');
        SAP_ProvinceMap.put('山东','山东省');
        SAP_ProvinceMap.put('四川/西藏','四川省');
        SAP_ProvinceMap.put('安徽','安徽省');
        SAP_ProvinceMap.put('北京','北京市');
        SAP_ProvinceMap.put('福建','福建省');
        SAP_ProvinceMap.put('甘肃','甘肃省');
        SAP_ProvinceMap.put('贵州','贵州省');
        SAP_ProvinceMap.put('河北','河北省');
        SAP_ProvinceMap.put('河南','河南省');
        SAP_ProvinceMap.put('湖北','湖北省');
        SAP_ProvinceMap.put('湖南','湖南省');
        SAP_ProvinceMap.put('吉林','吉林省');
        SAP_ProvinceMap.put('江苏','江苏省');
        SAP_ProvinceMap.put('江西','江西省');
        SAP_ProvinceMap.put('青海','青海省');
        SAP_ProvinceMap.put('山西','山西省');
        SAP_ProvinceMap.put('陕西','陕西省');
        SAP_ProvinceMap.put('上海','上海市');
        SAP_ProvinceMap.put('天津','天津市');
        SAP_ProvinceMap.put('云南','云南省');
        SAP_ProvinceMap.put('浙江','浙江省');
        SAP_ProvinceMap.put('重庆','重庆市');
        SAP_ProvinceMap.put('海南','海南省');
        //内蒙古一致

        // String SAP_Province = acc.Parent.Parent.State_Master__r.Name;
        String SAP_Province = acc.Parent.Parent.OCM_man_province_HP__c;
        //******** lt DB202303246427 --青岛拆分 -- 根据询“省（客户）”赋值SAP上传省 end 
        if(SAP_ProvinceMap.containsKey(SAP_Province)){
            SAP_Province = SAP_ProvinceMap.get(SAP_Province);
        }
        opp.SAP_Province__c = SAP_Province;
        // ******** ljh DB202212030068 end
        switch on acc.Parent.Department_Class_Label__c {              // 询价科室分类  询价编码自动生成要用到
            when '消化科' {
                opp.Opportunity_Category__c = 'GI';
            }
            when '呼吸科' {
                opp.Opportunity_Category__c = 'BF';
            }
            when '普外科' {
                opp.Opportunity_Category__c = 'GS';
            }
            when '泌尿科' {
                opp.Opportunity_Category__c = 'URO';
            }
            when '妇科' {
                opp.Opportunity_Category__c = 'GYN';
            }
            when '耳鼻喉科' {
                opp.Opportunity_Category__c = 'ENT';
            }
            when 'ET' {
                opp.Opportunity_Category__c = 'ET';
            }
            when '其他' {
                opp.Opportunity_Category__c = 'OTH';
            }
            when else {
                opp.Opportunity_Category__c = 'OTH';
            }
        }

        opp.StageName = '引合';                                     // 状态
        opp.Purchase_Reason__c = '新期';                            // 购买原因
        opp.Trade__c = '内貿';                                      // 内贸外贸
        opp.Close_Forecasted_Date__c = Date.today().addDays(90);    // 预测OCSM签约日
        opp.CloseDate = Date.today().addDays(120);                  // 预测发货日
        opp.Purchase_Type__c = '一般引合';                          // 订货方式
        opp.Sales_Root__c = '販売店';                               // 渠道为"经销商"
        opp.ifOpenBid__c = '公开招标';                              // 是否公开招标
        opp.LeadSource = '招标网';                                  // 潜在客户来源
        // opp.LeakageNumber__c = 1;                                   //漏单数 ////DB202307367354 lt 【招标项目】漏单计算逻辑修改 20231115 注释
        opp.Tender_Number__c = 1;                                   //招标数
        opp.DirectLossFLG__c = true;                                //后台用-招标项目直接失单标记
        //应标数为0   Authorized_DB_No__c为空时为0
        //中标数为0 
        opp.ConfirmationofAward_createTime__c = Date.today();       //中标结果确认日
        opp.ConfirmationofAward__c = '竞争对手中标';                 //中标确认结果

        System.debug('lt123opp默认询价内容'+opp);

        return opp;
    }

    //保存数据  JSONData 是 json格式的值 
    @AuraEnabled
    public static string SaveData(String JsonData){
        Opportunity opp = new Opportunity();
        try{
            Map<String, Object> oppMap = (Map<String, Object>)JSON.deserializeUntyped(JsonData);
            Set<Id> oppIdSet = new Set<Id>(); //DB202307367354 lt 【招标项目】漏单计算逻辑修改 20231115 add
            System.debug('lt123JsonData'+JsonData);
            opp = mergeInfo(oppMap);
            insert opp;
            //DB202307367354 lt 【招标项目】漏单计算逻辑修改 20231115 start
            oppIdSet.add(opp.Id);

            if(oppIdSet.Size() > 0){
                TenderUtil.UpdLeakageNum(oppIdSet);
            }
            //DB202307367354 lt 【招标项目】漏单计算逻辑修改 20231115 end
            System.debug('lt123opp.Id'+opp.Id);
            return opp.Id;
        }catch(Exception ex)
        {
            return  '错误：' + ex.getLineNumber()+'  行错误 :   '+ex.getMessage();
        }
    }

    //查询普通科室
    @AuraEnabled
    public  static String SearchPTKS(String content, List<String> hospitals){
        String profile_2S1 = System.Label.ProfileId2S1HP;
        Boolean is_2S1 = profile_2S1.contains(UserInfo.getProfileId()) ? true : false;
        String jsonData = CommonUtils.GetPTKS(content, hospitals, is_2S1);
        System.debug('hospitals: ' + hospitals);
        System.debug('offices: ' + jsonData);
        return jsonData;
    }

    //查询父类
    // @AuraEnabled
    // public  static String SearchParent(String Id){
    //     String jsonData = CommonUtils.GetParent(Id);
    //     return jsonData;
    // }

}