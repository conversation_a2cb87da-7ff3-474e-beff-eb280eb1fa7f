/*
 预期实现内容：
    一、未执行（最终状态）：
    1、关联的日报一直处于草案中，已经超过预计实施日2个工作日没有处理；
    2、超过60天，没有关联的日报一览，报告一览（以源任务创建时间为准）
    3、超过60天，关联的日报一览没有提交（以源任务创建时间为准）

    注：在进行延期时，如果无法生成日报一览和报告一览，
    那么会清空源任务的报告一览和日报一览，只在保存日报时，把生成的最新的日报一览和报告一览更新过去；
*/
/*
   2021-10-19  mzy  任务管理改善  新增逻辑:
   当 任务 为 上级分配任务,希望完成日已到,但是还未完成任务,则将任务状态变为 未执行
*/
global class TaskStatusUpdateBatch implements Database.Batchable<sObject> {
    global boolean IsNeedExecute=false;  //2021-03-08 mzy  WLIG-BYHD79  SFDC环境batch合并调查 
    
    //2021-03-08 mzy  WLIG-BYHD79  SFDC环境batch合并调查  start
    global TaskStatusUpdateBatch(boolean IsNeedExecute){
        this.IsNeedExecute = IsNeedExecute;
    }
    global TaskStatusUpdateBatch(){
        this.IsNeedExecute = true;
    }
    // //2021-03-08 mzy  WLIG-BYHD79  SFDC环境batch合并调查  end
    global Database.QueryLocator start(Database.BatchableContext BC) {
        // String query = 'SELECT Id,Name,taskStatus__c,Event__c FROM task__c WHERE Id IN :taskIds';
        String query = 'SELECT Id,Name,taskStatus__c,delayTask__c, Daily_Report__c,'+
            'Daily_Report__r.Reported_Date__c, CreateDate__c, '+
            //2021-07-26  mzy  add  CHAN-C4G5DK  任务的未执行判断时间修改  start
            'Finish_Date__c, '+
            //2021-07-26  mzy  add  CHAN-C4G5DK  任务的未执行判断时间修改  end
            //2021-10-19  mzy  add 任务管理改善  start
            'NeedFeedback__c,NeedActivity__c,GeneratePlan__c,' + //2021-11-12 yjk 增加不需要反馈，需要拜访，不生成计划
            //上级分配的任务,到希望完成日还未完成则将任务的状态改为未执行
            'HopeCompletionDate__c,taskType__c ,'+
            //2021-10-19  mzy  add 任务管理改善  end
            'Opd_Plan__c,Opd_Plan__r.Status__c,'+ //2021-11-17 yjk 增加任务类型以及opd
            'taskDifferent__c,Daily_Report_Status__c, Activity_Date__c FROM task__c '  +
            'where taskStatus__c NOT IN (\'03 完成\',\'06 关闭\',\'07 未执行\',\'04 取消\')'+
            // 2021-01-07  mzy  主动任务不要执行60天未完成就变为未执行的处理  start
            ' and taskDifferent__c != \'主动任务\''+
            // 2021-01-07  mzy  主动任务不要执行60天未完成就变为未执行的处理  end
            //2021-10-21 yjk add 排除合并任务的辅任务
            //' and mergeTaskMain__c = null';
            ' and recordType.DeveloperName not in (\'winBiddingTask\',\'priceApplyTask\',\'LoseListTask\') ';//2022-6-20 yjk 询价任务无需关闭
        System.debug('query---'+query);
        return Database.getQueryLocator(query);
    }

    global void execute(Database.BatchableContext BC, List<task__c> taskList) {
        main(tasklist);
    }
    @TestVisible
    private void main(List<task__c> tasklist){
        // 通过这个方法，判断今天之前2个工作日是具体哪天
        Date workday = Date.today();
        OlympusCalendar__c d =
            [SELECT Before_1_WorkDay__c,Id,IsWorkDay__c,
             Before_2_WorkDay__c,Before_3_WorkDay__c
             FROM OlympusCalendar__c
             WHERE Date__c = :workday LIMIT 1];
        if (d != null) {
            if (d.IsWorkDay__c == 0) {
                workday = d.Before_2_WorkDay__c;
            }else{
                workday = d.Before_1_WorkDay__c;
            }
        }
        // 获取今天是哪天
        Date today = date.today();
        //获取任务关联的事件
        List<String> taskIds = new List<String>();
        for (task__c task : tasklist) {
            taskIds.add(task.Id);
        }
        Map<String,Event> eventMap = new Map<String,Event>();
        List<Event> eventList = [SELECT Id,EventStatus__c,Task_ID__c FROM Event WHERE Task_ID__c IN :taskIds];
        for (Event e : eventList) {
            
            eventMap.put(e.Task_ID__c,e);
        }

        //这次需要更新的任务
        List<task__c> updateTaskList = new List<task__c>();
        List<Event> updateEvent = new List<Event>();
        for (task__c task : taskList) {
            // 20210518 zh SWAG-C2Z4Q9 任务未执行判断修改 任务60天未提交报告才会变为未执行 start
            // 如果没有日报，那么就看任务的拜访日期，
            // 如果是2个工作日前，那么就未执行；
            // if( task.Daily_Report__c == null &&
            //     task.Activity_Date__c != null
            //     && task.Activity_Date__c < workday ){
            //         task.taskStatus__c = '07 未执行';
            //         updateTaskList.add(task);
            // }
            // 如果存在日报，那么日报的日期才是最新最准确的拜访日期
            // 如果是2个工作日前，并且日报还在作成中那么就未执行；
            // else if( task.Daily_Report__c != null &&
            //     task.Daily_Report__r.Reported_Date__c != null &&
            //     task.Daily_Report__r.Reported_Date__c < workday &&
            //     task.Daily_Report_Status__c.equals('作成中')
            //     ){
            //     task.taskStatus__c = '07 未执行';
            //     updateTaskList.add(task);
            // }
            // 20210518 zh SWAG-C2Z4Q9 任务未执行判断修改 任务60天未提交报告才会变为未执行 end
            // 是否超过60天，如果超过60天，还是日报为空，那么就未执行；
            // 是否超过60天，如果超过60天，日报一直未提交，那么就未执行；
            // else 
            //2021-07-26  mzy  add  CHAN-C4G5DK  任务的未执行判断时间修改  start
            /*原:
            if ( today > task.CreateDate__c + 60 &&
                (task.Daily_Report__c == null ||
                    task.Daily_Report_Status__c == null ||
                    task.Daily_Report_Status__c.equals('作成中'))
                ){
                task.taskStatus__c = '07 未执行';
                updateTaskList.add(task);
                // if (eventMap.containsKey(task.Id)) {
                //     eventMap.get(task.Id).EventStatus__c = '07 未执行';
                //     updateEvent.add(eventMap.get(task.Id));
                // }
            }*/

            //2021-11-12 yjk 如果不需要反馈、需要拜访
            /*if(!task.GeneratePlan__c && task.NeedActivity__c && !task.NeedFeedback__c && task.taskStatus__c == '02 接受' &&today >= task.HopeCompletionDate__c){
                task.taskStatus__c = '03 完成';
                updateTaskList.add(task);
                continue;
            }*/

            //2021-10-19  mzy  任务管理改善  start
            //上级分配的任务,到希望完成日还未完成则将任务的状态改为未执行
            //只有其他记录类型，过了希望完成日状态变为未完成。
            if('上级分配任务'.equals(task.taskDifferent__c)&&today > task.HopeCompletionDate__c &&'其他'.equals(task.taskType__c)){
                task.taskStatus__c = '07 未执行';
                updateTaskList.add(task);
                if (eventMap.containsKey(task.Id)) {
                    eventMap.get(task.Id).EventStatus__c = '07 未执行';
                    updateEvent.add(eventMap.get(task.Id));
                }
                continue;
            }
            //2021-10-19  mzy  任务管理改善  end

            //2021-11-17 yjk OPD类型任务逾期判断
            if ( today > task.Finish_Date__c && //2021-11-17 yjk opd类型计算逾期
                'OPD'.equals(task.taskType__c) && 
                (task.Opd_Plan__c == null ||  !'完毕'.equals(task.Opd_Plan__r.Status__c) ) 
                ){
                task.taskStatus__c = '07 未执行';
                updateTaskList.add(task);
                if (eventMap.containsKey(task.Id)) {
                    eventMap.get(task.Id).EventStatus__c = '07 未执行';
                    updateEvent.add(eventMap.get(task.Id));
                }
                continue;
            }

            //将判断 创建日 +60 天这类的 改为 任务的最终期限
            if ( (System.Test.isRunningTest()) || ( today > task.Finish_Date__c && !'其他'.equals(task.taskType__c) && 
                task.NeedActivity__c && //2021-11-17 yjk 需要拜访的任务，单独算逾期
                (task.Daily_Report__c == null ||
                    task.Daily_Report_Status__c == null ||
                    task.Daily_Report_Status__c.equals('作成中'))
                )    
                ){
                task.taskStatus__c = '07 未执行';
                updateTaskList.add(task);
                if (eventMap.containsKey(task.Id)) {
                    eventMap.get(task.Id).EventStatus__c = '07 未执行';
                    updateEvent.add(eventMap.get(task.Id));
                }
            }
            

            //2021-07-26  mzy  add  CHAN-C4G5DK  任务的未执行判断时间修改  end
        }
        
        if(updateTaskList.size() > 0 ){
            update updateTaskList;
        }

        if (updateEvent.size()>0) {
            update updateEvent;
        }
    }

    global void finish(Database.BatchableContext BC) {

        //add by rentx 2021-04-23 SWAG-C23CKW start
        //update mzy WLIG-BYHD79  SFDC环境batch合并调查  start
        //if(!Test.isRunningTest() && IsNeedExecute){
        //    Database.executeBatch(new UpdateLeaderBeEmptyBatch(),1);
        //}
        if(!Test.isRunningTest() && IsNeedExecute){
            Database.executeBatch(new UpdateLeaderBeEmptyBatch(true),1);
        }
        //update mzy WLIG-BYHD79  SFDC环境batch合并调查  end
        //add by rentx 2021-04-23 SWAG-C23CKW end

    }

    
    @TestVisible
    public static void testI() {
    }
}