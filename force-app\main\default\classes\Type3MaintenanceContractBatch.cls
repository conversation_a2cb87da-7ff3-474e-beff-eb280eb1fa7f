// create tcm ******** 更改维修合同——用户类型和合同种类
public class Type3MaintenanceContractBatch implements Database.Batchable<sObject> {

	public List<String> IdList;

	public Type3MaintenanceContractBatch() {
	}

	public Type3MaintenanceContractBatch(List<String> IdList) {
		this.IdList=IdList;
	}

	public Database.QueryLocator start(Database.BatchableContext BC) {
		//查维修合同
		string sql = 'select id from Account where RecordTypeId=\''+System.label.Hospital_RecordType+'\' ';
		if (IdList != null && IdList.size() > 0) {
			sql += ' and Id in :IdList';
		}
		return Database.getQueryLocator(sql);
	}

	public void execute(Database.BatchableContext BC, List<Account> acList) {
		List<String> acIdList=new List<String>();
		for (Account ac : acList) {
			acIdList.add(ac.Id);
		}

		List<Maintenance_Contract__c> mcList=[select Id,Hospital__c,Contract_Start_Date__c from Maintenance_Contract__c where Hospital__c in : acIdList and RecordTypeId!=:System.label.maintenance_contract and Status__c in ('契約','契約満了') and Id!=:System.label.maintenance_contract_1 order by Contract_Start_Date__c ];

		List<String> strList=new List<String>();
		for (Maintenance_Contract__c mc : mcList) {
			if (!strList.contains(mc.Hospital__c)) {
				mc.UserType__c='新用户';
				strList.add(mc.Hospital__c);
                system.debug('进入');
			}else {
				mc.UserType__c='既有用户';
			}
		}
        system.debug('mcList等于'+mcList);
		update mcList;
	}
	public void finish(Database.BatchableContext BC) {

	}
}