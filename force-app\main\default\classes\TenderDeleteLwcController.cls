public without sharing class TenderDeleteLwcController {

    @AuraEnabled
    public static  String GetTenderinformationcData(String ParamIdStr) {

         List<Tender_information__c> arrays = Database.query('Select Id,Name,InfoId__c,Logical_delete__c,ProjectId__c,Retain_Tender__c From Tender_information__c Where id = : ParamIdStr');
         
         return JSON.serialize(arrays);
    }

	// 查询招标项目
    @AuraEnabled
    public  static String searchTender(String content){
		String soql = 'Select Id, Name, TenderManageCode__c, Logical_delete__c, Retain_Tender__c From Tender_information__c where Logical_delete__c = false ';
		if (String.isNotBlank(content) && String.isNotBlank(content.replaceAll(' ', ''))) {
			content = '%'+content+'%';
            soql += ' and TenderManageCode__c like :content ';
		}
		system.debug('System.Label.BiddingProjectRecordTypeID+++'+System.Label.BiddingProjectRecordTypeID);
		String RecordTypeIdvalue=System.Label.BiddingProjectRecordTypeID;
		//DB202308327700 【重要】新需求-招标项目中导入众成数据 fy 20231106 start
		// soql += 'and RecordTypeId =\''+RecordTypeIdvalue+'\' Order by CreatedDate desc limit 5';
		soql += ' Order by CreatedDate desc limit 5';
		//DB202308327700 【重要】新需求-招标项目中导入众成数据 fy 20231106 end
        String jsonData = JSON.serialize(Database.query(soql));
        return jsonData;
    }
	
    @AuraEnabled
    public static  String saveData(String Tenderinformationc) {
		Boolean QLMflg = false; //DB202308327700 20230920 lt add
		String QLMrecordId = Schema.SObjectType.Tender_information__c.getRecordTypeInfosByDeveloperName().get('QLM').getRecordTypeId();//DB202308327700 20230920 lt add

		system.debug('Tenderinformationc++'+Tenderinformationc);
        Tender_information__c TenInfo=(Tender_information__c)JSON.deserialize(Tenderinformationc,Tender_information__c.class);
        List<Tender_information__c> updateTenInfoList = new List<Tender_information__c>();
		// 更新保留招投标
		// List<Tender_information__c> updateBTenList = new List<Tender_information__c>();
		// 如果点击保存后,未选则保留招投标则报错
		// SaveErrorflag = false;
		System.debug('--------' + TenInfo.Retain_Tender__c);
		if (String.isNotBlank(TenInfo.Retain_Tender__c)) {
			System.debug('---------2--------' + TenInfo.Retain_Tender__c);

			//要保留的招投标
			//DB202308327700 20230920 lt add  ,RecordTypeId
			//lwt add 关联战略科室
			Tender_information__c delTender = [select Id, RecordTypeId,
                                           department_selection__c,department_selection1__c,department_selection2__c,department_selection3__c,department_selection4__c,
                                           Hospital__c,Hospital1__c,Hospital2__c,Hospital3__c,Hospital4__c from Tender_information__c where Id = :TenInfo.Id];
			Tender_information__c BTen =  [select Id, InfoId__c,RecordTypeId,
                                           department_selection__c,department_selection1__c,department_selection2__c,department_selection3__c,department_selection4__c,
                                           Hospital__c,Hospital1__c,Hospital2__c,Hospital3__c,Hospital4__c
                                           From Tender_information__c
			                               Where Id = : TenInfo.Retain_Tender__c];

			//DB202308327700 20230920 lt start
			if(delTender.RecordTypeId == QLMrecordId && BTen.RecordTypeId == QLMrecordId){
				QLMflg = true;
			}

			// update 20240304 By DTT-亚楠 去除fileaddress start
			//FILEADDRESS检索
			// List<FileAddress__c> faList =  [select Id, name,ParentRecordId__c From FileAddress__c
			//                                 Where ParentRecordId__c = : TenInfo.Id];
			// if(faList.Size() > 0 ){
			// 	for(FileAddress__c fa : faList){
			// 		fa.ParentRecordId__c = BTen.Id;
			// 	}
			// }
			List<ContentDocumentLink> cdList = [SELECT Id, ContentDocumentId, LinkedEntityId FROM ContentDocumentLink 
												WHERE LinkedEntityId = :TenInfo.Id];
			List<ContentDocumentLink> insertConDocuList = new List<ContentDocumentLink>();
			if (cdList.size() > 0) {
				for (ContentDocumentLink conDocu : cdList) {
					ContentDocumentLink link = new ContentDocumentLink();
					link.ContentDocumentId = conDocu.ContentDocumentId;
					link.LinkedEntityId = BTen.Id;
					// link.ShareType = 'I';
					link.Visibility = 'AllUsers';
					insertConDocuList.add(link);
				}
			}
			// update 20240304 By DTT-亚楠 去除fileaddress end
			//DB202308327700 20230920 lt end

			// 保留招投标关联的询价
			System.debug('---------BTen--------' + BTen);
			List<Tender_Opportunity_Link__c> BlinksList = [select Opportunity__c
			        from Tender_Opportunity_Link__c
			        where Tender_information__c = :BTen.Id];
			Set<Id> BlinkOppId = new Set<Id>();
			// if (BlinksList.size() > 0) {
			for (Tender_Opportunity_Link__c Blink : BlinksList) {
				BlinkOppId.add(Blink.Opportunity__c);
			}
			// 删除项目关联但不与保留项目关联的询价关联信息
			List<String> LinksIdList = new List<String>(); //******** lt add
			List<Tender_Opportunity_Link__c> linksList = [select id, Opportunity__c, Tender_information__c
			        from Tender_Opportunity_Link__c
			        where Tender_information__c = :TenInfo.Id ];
			                                    //  and Opportunity__c not in : BlinkOppId];
			// 把删除招投标 关联的询价 赋给 保留招投标上
			List<Tender_Opportunity_Link__c> addlinksList = new List<Tender_Opportunity_Link__c>();
			// 删除招投标关联的询价
			// 增加一个判断 看看是否还会写空进去
			System.debug('20240613---linksList:'+linksList);
			if (linksList != null && linksList.size() > 0) {
				for (Tender_Opportunity_Link__c link : linksList) {
					//******** lt start
					if(!LinksIdList.contains(link.Tender_information__c)){
						LinksIdList.add(link.Tender_information__c);
					}
					System.debug('20240613---LinksIdList:'+LinksIdList);
					//******** lt end
					if(!BlinkOppId.contains(link.Opportunity__c)){
						Tender_Opportunity_Link__c linkinfo =  new Tender_Opportunity_Link__c();
						linkinfo.Tender_information__c = BTen.Id;
						linkinfo.Opportunity__c = link.Opportunity__c;
						linkinfo.Tender_Opportunity_Uniq__c = BTen.Id + '' + link.Opportunity__c;
						linkinfo.IsRelated__c = true;
						// if (BlinksList.contains(linkinfo)) {
						addlinksList.add(linkinfo);
						// }
					}
				}


				//******** lt start
				// 保留项目新增关联询价
				//20240617  暂时放开，batch调好之后在batch的finish里新增
				// if (addlinksList.size() > 0) {
				// 	insert addlinksList;
				// }
				// 删除项目删掉关联询价
				
				//******** lt start
				// StaticParameter.EscapeTOLinkTrigger = true;//DB202307199492 lt 注释 20230810 add //20240428注释
				System.debug('20240613---1.1--LinksIdList:'+LinksIdList);
				if(LinksIdList != null && LinksIdList.Size() > 0 && (!Test.isRunningTest())){
					//在batch的finsih里insert保留项目的link，将待新增的link传到batch（有可能不需要新增。传入有可能为空。）
					Database.executeBatch (new TenderDeleteLinkBtach(LinksIdList,addlinksList),100);
				}
				// delete linksList;
				// StaticParameter.EscapeTOLinkTrigger = false;//DB202307199492 lt 注释 20230810 add //20240428注释
				//******** lt end
				
			}
			// 【委托】招标项目-逻辑删除的项目需要自动取消与询价的关系 fy 2022.3.7 start
			//DB202307199492 lt 注释 20230810 start
			// List<Tender_Opportunity_Link__c> linksListdelect = [select id, Opportunity__c, Tender_information__c
			//         from Tender_Opportunity_Link__c
			//         where Tender_information__c = :TenInfo.Id];
			// if(linksListdelect.size()>0){
			// 	delete linksListdelect;
			// }
			//DB202307199492 lt 注释 20230810 end
			// 【委托】招标项目-逻辑删除的项目需要自动取消与询价的关系 fy 2022.3.7 end
			// }
			// 互换保留招投标与删除招投标的信息Id
			TenInfo.Retain_Tender__c = BTen.Id;
			// 【委托】招标项目-逻辑删除的项目需要自动取消与询价的关系 fy 2022.3.7 start
			TenInfo.Name = '逻辑删除:'+TenInfo.Name;
            TenInfo.TenderDeleteDate__c=Date.Today();//lwt 项目合并时间
            TenInfo.TenderDeleteUser__c=UserInfo.getUserId();//lwt 项目合并时间
			// 20221018 ljh SWAG-CKB9NR add start
			if(TenInfo.Name.length() > 80){
				TenInfo.Name = TenInfo.Name.substring(0,80);
			}
			// 20221018 ljh SWAG-CKB9NR add end
			// 【委托】招标项目-逻辑删除的项目需要自动取消与询价的关系 fy 2022.3.7 end
			String BTenInfo = BTen.InfoId__c;
			//DB202308327700 20230920 lt add if条件
			if(QLMflg){
                // chenjingwu 20250518 start
				// BTen.InfoId__c = TenInfo.InfoId__c;//保留招投标的信息Id赋给删除招投标的信息Id
				// TenInfo.InfoId__c = BTenInfo;//删除招投标的信息Id赋给保留招投标的信息Id
                List<String> infoIdList = new List<String>();
                infoIdList.add(BTen.InfoId__c);
                infoIdList.add(TenInfo.InfoId__c);
                List<Tender_information_Intermediate__c> tiiList = [select Id,InfoId__c from Tender_information_Intermediate__c where InfoId__c in:infoIdList order by InfoPublishTime__c desc];
                if(tiiList.size() > 0){
                   BTen.InfoId__c = tiiList[0].InfoId__c;
                }
                 // chenjingwu 20250518 end
			}
			// 点击保存后 删除招投标上的逻辑删除字段变为true
			TenInfo.Logical_delete__c = true;
			//20231115 lt 询价数量清空 add 1行
			TenInfo.OpportunityNum__c = null;
			
			//DB202307199492 lt 注释 20230810 start
			TenInfo.Strategic_department_MainId__c = null;
			TenInfo.Strategic_department_MainId1__c = null;
			TenInfo.Strategic_department_MainId2__c = null;
			TenInfo.Strategic_department_MainId3__c = null;
			TenInfo.Strategic_department_MainId4__c = null;
			//DB202307199492 lt 注释 20230810 end
			// update TenInfo;
			
			//DB202405650513 lwt add ******** start
            // 招标项目的医院
            Map<String,String> fiveHospitalMap = new Map<String,String>();
            fiveHospitalMap.put('Hospital__c', BTen.Hospital__c);
            fiveHospitalMap.put('Hospital1__c', BTen.Hospital1__c);
            fiveHospitalMap.put('Hospital2__c', BTen.Hospital2__c);
            fiveHospitalMap.put('Hospital3__c', BTen.Hospital3__c);
            fiveHospitalMap.put('Hospital4__c', BTen.Hospital4__c);
            // 招标项目的医院
            Map<String,String> fiveDepMap = new Map<String,String>();
            fiveDepMap.put('department_selection__c', BTen.department_selection__c);
            fiveDepMap.put('department_selection1__c', BTen.department_selection1__c);
            fiveDepMap.put('department_selection2__c', BTen.department_selection2__c);
            fiveDepMap.put('department_selection3__c', BTen.department_selection3__c);
            fiveDepMap.put('department_selection4__c', BTen.department_selection4__c);
            //关联战略科室Map:DepartmentSelectionMap start  - 对应字段department_selection__c
            Map<String, String> DepartmentSelectionMap = new Map<String, String>();
            DepartmentSelectionMap.put('妇科',System.Label.tender_8);
            DepartmentSelectionMap.put('耳鼻喉科',System.Label.tender_9);
            DepartmentSelectionMap.put('消化科',System.Label.tender_4);
            DepartmentSelectionMap.put('泌尿科',System.Label.tender_7);
            DepartmentSelectionMap.put('呼吸科',System.Label.tender_5);
            DepartmentSelectionMap.put('普外科',System.Label.tender_6);
            DepartmentSelectionMap.put('其他',System.Label.tender_6);   //其他归普外
            //关联战略科室Map:DepartmentSelectionMap end
            //关联战略科室Map:DepartmentSelectionMap start  - 对应字段department_selection__c
            Map<String, String> DepartmentSelectionMapT = new Map<String, String>();
            DepartmentSelectionMapT.put(System.Label.tender_8,'妇科');
            DepartmentSelectionMapT.put(System.Label.tender_9,'耳鼻喉科');
            DepartmentSelectionMapT.put(System.Label.tender_4,'消化科');
            DepartmentSelectionMapT.put(System.Label.tender_7,'泌尿科');
            DepartmentSelectionMapT.put(System.Label.tender_5,'呼吸科');
            DepartmentSelectionMapT.put(System.Label.tender_6,'普外科');
            //关联战略科室Map:DepartmentSelectionMap end
            
            Map<String,String> fiveHospitalDepMap = new Map<String,String>();
            for(Integer i=0;i<5;i++){
                String hpApiName='Hospital'+i+'__c';
                String depApiNameT='department_selection'+i+'__c';
                if(i==0){
                    hpApiName='Hospital__c';
                    depApiNameT='department_selection__c';
                }
                if(delTender.get(hpApiName)==null){
                    continue;
                }
                String hp=(String)delTender.get(hpApiName);  
                String dep=(String)delTender.get(depApiNameT); 
                for(String ApiName : fiveHospitalMap.keySet()) {
                    String tempTenderHospId = fiveHospitalMap.get(ApiName)==null?'':fiveHospitalMap.get(ApiName);
                    String oppHospId = hp;
                    //如果招标项目已经有该医院就判断下一个询价的医院
                    system.debug('tempTenderHospId等于【'+tempTenderHospId+'】结束');
                    system.debug('oppHospId等于【'+oppHospId+'】结束');
                    if(tempTenderHospId.contains(oppHospId)) {
                        String newDepApiValue=dep;
                        String depApiName=ApiName.replace('Hospital','department_selection');
                        String oldDepApiValue=fiveDepMap.get(depApiName);
                        if(String.isBlank(newDepApiValue)){
                            break;
                        }
                        List<String> newDepList=newDepApiValue.split(';');
                        for(String newDep:newDepList){
                            if(String.IsBlank(newDep)){
                                continue;
                            }
                            if(depApiNameT=='department_selection__c'&&depApiName!='department_selection__c'){
                                newDep=DepartmentSelectionMapT.get(newDep);
                            }
                            if(depApiNameT!='department_selection__c'&&depApiName=='department_selection__c'){
                                newDep=DepartmentSelectionMap.get(newDep);
                            }
                            if(!String.isBlank(oldDepApiValue)&&!oldDepApiValue.contains(newDep)){
                                oldDepApiValue+=';'+newDep;
                            }else if(String.isBlank(oldDepApiValue)){
                                oldDepApiValue=newDep;
                            }
                        }
                        BTen.put(depApiName,oldDepApiValue);           
                        break;
                    }
                    
                    //医院为空,赋值医院(赋值之后进行赋值下一个医院)
                    if(fiveHospitalMap.get(ApiName) == null || String.isBlank( fiveHospitalMap.get(ApiName) )) {
                        fiveHospitalMap.put(ApiName,oppHospId);
                        String newDepApiValue=dep;
                        String depApiName=ApiName.replace('Hospital','department_selection');
                        String oldDepApiValue=(String)fiveDepMap.get(depApiName);
                        if(String.isBlank(newDepApiValue)){
                            break;
                        }
                        List<String> newDepList=newDepApiValue.split(';');
                        for(String newDep:newDepList){
                            if(String.IsBlank(newDep)){
                                continue;
                            }
                            if(depApiNameT=='department_selection__c'&&depApiName!='department_selection__c'){
                                newDep=DepartmentSelectionMapT.get(newDep);
                            }
                            if(depApiNameT!='department_selection__c'&&depApiName=='department_selection__c'){
                                newDep=DepartmentSelectionMap.get(newDep);
                            }
                            if(!String.isBlank(oldDepApiValue)&&!oldDepApiValue.contains(newDep)){
                                oldDepApiValue+=';'+newDep;
                            }else if(String.isBlank(oldDepApiValue)){
                                oldDepApiValue=newDep;
                            }
                        }
                        BTen.put(depApiName,oldDepApiValue);
                        break;
                    }
                }
            }
            
            BTen.Hospital__c = fiveHospitalMap.get('Hospital__c');
            BTen.Hospital1__c = fiveHospitalMap.get('Hospital1__c');
            BTen.Hospital2__c = fiveHospitalMap.get('Hospital2__c');
            BTen.Hospital3__c = fiveHospitalMap.get('Hospital3__c');
            BTen.Hospital4__c = fiveHospitalMap.get('Hospital4__c');
            //DB202405650513 lwt add ******** end
			// 反正要更新 放在一起也是一样的
			
			//lwt ******** start
            // 仅新增关联询价时判断是否要写入医院
            List<Tender_Opportunity_Link__c> links = null;
            //lwt ******** 更换科室字段
            links = [select id, Tender_information__c,Opportunity__r.Department_Name__c,Opportunity__r.Account.Department_Class_Name__c, Opportunity__c, Opportunity__r.Hospital__c,Opportunity__r.Department_Class__r.Department_Class_Label__c, Opportunity__r.OwnerId from Tender_Opportunity_Link__c where Tender_information__c = :BTen.Id];
			if ( links != null && links.size() > 0) {
                system.debug('links等于【'+links+'】结束'+links.size());
                fiveHospitalMap = new Map<String,String>();
                fiveHospitalMap.put('Hospital__c', BTen.Hospital__c);
                fiveHospitalMap.put('Hospital1__c', BTen.Hospital1__c);
                fiveHospitalMap.put('Hospital2__c', BTen.Hospital2__c);
                fiveHospitalMap.put('Hospital3__c', BTen.Hospital3__c);
                fiveHospitalMap.put('Hospital4__c', BTen.Hospital4__c);
                // 整理所有的所有人
                List<String> owners = new List<String>();
                for (Tender_Opportunity_Link__c link : links) {
                    owners.add(link.Opportunity__r.OwnerId);
                }
                
                
                for(Integer i = 0; i < links.size(); i++) {
                    //给招投标项目的5个医院设值
                    for(String ApiName : fiveHospitalMap.keySet()) {
                        String tempTenderHospId = fiveHospitalMap.get(ApiName)==null?'':fiveHospitalMap.get(ApiName);
                        String oppHospId = links.get(i).Opportunity__r.Hospital__c;
                        //如果招标项目已经有该医院就判断下一个询价的医院
                        system.debug('tempTenderHospId等于【'+tempTenderHospId+'】结束');
                        system.debug('oppHospId等于【'+oppHospId+'】结束？');
                        if(tempTenderHospId.contains(oppHospId)) {
                            //DB202405650513 lwt add ******** start
                            System.debug('5');
                            String depApiName=ApiName.replace('Hospital','department_selection');
                            String depName=links.get(i).Opportunity__r.Account.Department_Class_Name__c;//lwt ******** 更换科室字段
                            //lwt ******** DB202503473587 start
                            if(depName=='其他'){
                                depName='普外科';
                            }
                            //lwt ******** DB202503473587 end
                            String newDepApiValue=DepartmentSelectionMap.get(depName);
                            if(depApiName!='department_selection__c'){
                                newDepApiValue=depName;
                                System.debug('6');
                            }
                            String oldDepApiValue=(String)BTen.get(depApiName);
                            System.debug('新增前'+oldDepApiValue);
                            if(!String.isBlank(oldDepApiValue)&&!oldDepApiValue.contains(newDepApiValue)){
                                oldDepApiValue+=';'+newDepApiValue;
                                System.debug('7');
                            }else if(String.isBlank(oldDepApiValue)){
                                oldDepApiValue=newDepApiValue;
                                System.debug('8');
                            }
                            System.debug('9');
                            System.debug(depApiName);
                            System.debug(oldDepApiValue);
                            BTen.put(depApiName,oldDepApiValue);
                            System.debug('==============oldDepApiValue1');
                            System.debug(oldDepApiValue);
                            //DB202405650513 lwt add ******** end
                            break;
                        }
                        
                        //医院为空,赋值医院(赋值之后进行赋值下一个医院)
                        if(fiveHospitalMap.get(ApiName) == null || String.isBlank( fiveHospitalMap.get(ApiName) )) {
                            fiveHospitalMap.put(ApiName,oppHospId);
                            
                            //DB202405650513 lwt add ******** start
                            String depApiName=ApiName.replace('Hospital','department_selection');
                            String depName=links.get(i).Opportunity__r.Account.Department_Class_Name__c;//lwt ******** 更换科室字段
                            //lwt ******** DB202503473587 start
                            if(depName=='其他'){
                                depName='普外科';
                            }
                            //lwt ******** DB202503473587 end
                            String newDepApiValue=DepartmentSelectionMap.get(depName);
                            if(depApiName!='department_selection__c'){
                                newDepApiValue=depName;
                            }
                            String oldDepApiValue=(String)BTen.get(depApiName);
                            if(!String.isBlank(oldDepApiValue)&&!oldDepApiValue.contains(newDepApiValue)){
                                oldDepApiValue+=';'+newDepApiValue;
                            }else if(String.isBlank(oldDepApiValue)){
                                oldDepApiValue=newDepApiValue;
                            }
                            BTen.put(depApiName,oldDepApiValue);
                            System.debug('==============oldDepApiValue2');
                            System.debug(oldDepApiValue);
                            //DB202405650513 lwt add ******** end
                            break;
                        }
                    }
                }
                BTen.Hospital__c = fiveHospitalMap.get('Hospital__c');
                BTen.Hospital1__c = fiveHospitalMap.get('Hospital1__c');
                BTen.Hospital2__c = fiveHospitalMap.get('Hospital2__c');
                BTen.Hospital3__c = fiveHospitalMap.get('Hospital3__c');
                BTen.Hospital4__c = fiveHospitalMap.get('Hospital4__c');
            }       
    		//lwt ******** end
            // chenjingwu ******** start
            if(System.Label.LogicDeleteComFlag == '是'){
                compareTwo(TenInfo,BTen);
            }
            // chenjingwu ******** end
			updateTenInfoList.add(TenInfo);
			updateTenInfoList.add(BTen);
            System.debug('chenjingwu=>' + updateTenInfoList);
			if (!TenInfo.Id.equals(BTen.Id)) {
				StaticParameter.EscapeTenderInformationUpdate =false;//lwt ******** add 
				update updateTenInfoList;
				StaticParameter.EscapeTenderInformationUpdate =true;//lwt ******** add

				// update 20240304 By DTT-亚楠 去除fileaddress start
				//DB202308327700 20230920 lt start 更新文件地址
				// if(faList.Size() > 0 ){
				// 	update faList;
				// }
				//DB202308327700 20230920 lt end
				if(insertConDocuList.size() > 0) {
					insert insertConDocuList;
				}
				//if(cdList.size() > 0) {
					//delete cdList;
				//}
			// update 20240304 By DTT-亚楠 去除fileaddress end
			}
			// updateBTenList.add(BTen);
			// update updateBTenList;
		}
		if (String.isNotBlank(TenInfo.Retain_Tender__c)&&String.isNotBlank(TenInfo.ProjectId__c)&&String.isNotBlank(TenInfo.InfoId__c)) {
			// 调用接口
			//DB202308327700 20230920 lt add if条件
			if(QLMflg){
				String userId = UserInfo.getUserId();
				String batchUser = System.Label.Batch_User_Id;
                if (userId.contains(batchUser)) {
                    // 暂不执行接口 ssm
                    // NFM504Controller.sendRequestNotFuture(TenInfo.Id);
                } else {
                    NFM504Controller.sendRequest(TenInfo.Id);
                }
			}
		}
		// System.debug('1111111122222' + SaveErrorflag);

		return '成功';
        //  return Tenderinformationc;
    }
	// public PageReference returnFresh() {
	// 	// System.debug('1234567891234567890');
	// 	String url = '/' + TenIds;
	// 	PageReference ref =  new Pagereference(url);
	// 	ref.setRedirect(true);
	// 	return ref;
	// }
    // chenjingwu ******** start
    public static void compareTwo(Tender_information__c del,Tender_information__c baoliu){
        Tender_information__c deleteInfo = [Select Id,BigAmoutTag__c,MaintenanceTag__c, OwnerId, IsDeleted, Name, CurrencyIsoCode, RecordTypeId, CreatedDate, CreatedById, LastModifiedDate, LastModifiedById, SystemModstamp, LastViewedDate, LastReferencedDate, AWS_Data_Id__c, AccountOwner__c, AgentRelationName_Encrypted__c, AgentRelationName__c, AgentRelationWay_Encrypted__c, AgentRelationWay__c, AgentUnit1__c, AgentUnit2__c, AgentUnit3__c, AgentUnit4__c, AgentUnit5__c, AreaCity__c, AreaCountry__c, AreaProvince__c, AutoNumber__c, BidWinningNumber__c, Bid_Winning_Date__c, BiddingType__c, BidingAcquireTime__c, BidingEndTime__c, Buchang_sales__c, BudgetAmount1__c, BudgetAmount2__c, BudgetAmount3__c, BudgetAmount4__c, BudgetAmount5__c, BudgetUnit__c, CentralizedProject__c, ConfirmationAssistant__c, ConfirmationofAward__c, CursorMark__c, Discountloan__c, Domestic_Or_Import__c, File_Surpass_12M__c, GI_assistant__c, Hospital1__c, Hospital2__c, Hospital3__c, Hospital4__c, Hospital__c, IFActiveUpdateOwner__c, InfoFile__c, InfoId__c, InfoOwner__c, InfoPublishTime__c, InfoQianlimaUrl__c, InfoTitle__c, InfoType__c, InvolveBudgetAmount__c, IsBid__c, IsElectronic__c, IsOther__c, IsReactionOpp__c, IsRelateProject__c, IsTerminate__c, Keywords__c, Logical_delete__c, Manager_sales__c, Migrate_Status__c, Migration_GUID__c, NotBidApprovalStatus__c, NumberOfBids__c, OBSAP_PR__c, OBSAP_WinnerAmount__c, OBSAP_relativeTime__c, OCSMProvinceS__c, OCSMProvinceText__c, OlyNumberHosts__c, OpenBidingTime__c, OpportunityNum__c, OpportunityStatus__c, zcdataFileName__c, OtherInfoTitle__c, Other_units__c, Overstep_12M_infofile__c, OwnerIsActive__c, ProjectId__c, ProjectRestartFLG__c, PushNewInformation__c, QLMGetDataTime__c, QuoteIrai__c, RelateOppTime__c, RepeatProMergeFLG__c, RepsExtraContent__c, ResultDate__c, Retain_Tender__c, RivalHostsNumber__c, SP_assistant__c, SalesDepartmentOwner__c, Salesdepartment_SAP_Text__c, Spot_Check_Result_Op__c, Spot_Check_Result_Text__c, Spot_Check__c, Strategic_department_MainId1__c, Strategic_department_MainId2__c, Strategic_department_MainId3__c, Strategic_department_MainId4__c, Strategic_department_MainId__c, Strategic_department_Opp__c, SumUnit__c, Sum__c, TenderAssistant1__c, TenderAssistant2__c, TenderBeginTime__c, TenderDate__c, TenderEndTime__c, TenderManageCode__c, Tender_Number__c, Tender_Olympus__c, TerminateApplyTime__c, TerminateApprovalStatus__c, TerminateApprovalTime__c, TerminateExtraContent__c, TerminateReason__c, TotalNumberHosts__c, Turn_off_homepage_reminder__c, Window2__c, WinnerAmount1__c, WinnerAmount2__c, WinnerAmount3__c, WinnerAmount4__c, WinnerAmount5__c, WinnerUnit__c, XmNumber__c, YingyeWindow__c, ZCDataUpdate__c, ZCInfoId__c, ZCProjectId__c, ZCUpdateDate__c, ZhaoBiaoUnit1__c, ZhaoBiaoUnit2__c, ZhaoBiaoUnit3__c, ZhaoBiaoUnit4__c, ZhaoBiaoUnit5__c, ZhaoRelationName_Encrypted__c, ZhaoRelationName__c, ZhaoRelationWay_Encrypted__c, ZhaoRelationWay__c, ZhongBiaoUnit1__c, ZhongBiaoUnit2__c, ZhongBiaoUnit3__c, ZhongBiaoUnit4__c, ZhongBiaoUnit5__c, ZhongRelationName_Encrypted__c, ZhongRelationName__c, ZhongRelationWay_Encrypted__c, ZhongRelationWay__c, Zongjian_Sales__c, department__c, department_selection__c, funding_source__c, host_if__c, irrelevantReasonOther__c, irrelevantReasons__c, irresponseApplyTime__c, irresponseApprovalTime__c, irresponsibleReasonOther__c, irresponsibleReason__c, noticeDate__c, original_url__c, otherInfo__c, proInvolvedManual__c, proInvolved__c, publicDate__c, relativeTime__c, remarks1__c, remarks2__c, responseTime__c, status_text__c, subDepartment1__c, subDepartment2__c, subDepartment3__c, subDepartment4__c, url__c, AccountOwnerFormula__c, BiddingOCSMAdministration__c, BudgetAmountNumber__c, BudgetAmount__c, Hospital_GI_Main__c, Hospital_SP_Main__c, If_Tender_Intime__c, IsBuchangActive__c, IsManagerActive__c, IsZongjianActive__c, Is_GIORSP__c, Is_InfoOwner__c, ManagerEqualBuchan__c, OCSMProvince__c, OwnerOcsm__c, ProductCategory__c, ProvinceComparison__c, RecordType_Formula__c, RelateOppDate__c, Salesdepartment_SAP__c, TAT_Opportunity_Num__c, TAT_Relative_Num__c, TenderNum__c, Tender_Order__c, ViewBidConfirm__c, ViewRelatedOppAlert__c, ViewRelatedOpp__c, ViewWaitConfirm__c, WinnerAmountKRMB__c, WinnerAmountNumber__c, WinnerAmount__c, buchangEqualZongjian__c, department_category__c, relativeDate__c, relativeTime_F__c, status__c, subInfoType__c, Custom_Last_ModifiedById__c, Custom_Last_Modified_Date__c, fm_Last_Modified_ById__c, fm_Last_Modified_Date__c, SpotCheckByText__c, SpotCheckDate__c, project_judge_id__c, TerminateApplyTimeFormula__c, TerminateApprovalTimeFormula__c, BudgetAmountKRMB__c, Custom_CreatedById__c, Custom_CreatedDate__c, fm_CreatedById__c, fm_CreatedDate__c, Calculation_Items__c, department_selection1__c, department_selection2__c, department_selection3__c, department_selection4__c, Watch_DepartmentOwner1__c, Watch_DepartmentOwner2__c, Watch_DepartmentOwner3__c, Watch_DepartmentOwner4__c, Watch_DepartmentOwner__c, ViewRelatedOppTenAlert1__c, ViewRelatedOppTenAlert2__c, ViewRelatedOppTenAlert3__c, ViewRelatedOppTenAlert4__c, ViewRelatedOppTenAlert__c FROM Tender_information__c where Id =: del.Id];
        Tender_information__c baoliuInfo = [Select Id,BigAmoutTag__c,MaintenanceTag__c, OwnerId, IsDeleted, Name, CurrencyIsoCode, RecordTypeId, CreatedDate, CreatedById, LastModifiedDate, LastModifiedById, SystemModstamp, LastViewedDate, LastReferencedDate, AWS_Data_Id__c, AccountOwner__c, AgentRelationName_Encrypted__c, AgentRelationName__c, AgentRelationWay_Encrypted__c, AgentRelationWay__c, AgentUnit1__c, AgentUnit2__c, AgentUnit3__c, AgentUnit4__c, AgentUnit5__c, AreaCity__c, AreaCountry__c, AreaProvince__c, AutoNumber__c, BidWinningNumber__c, Bid_Winning_Date__c, BiddingType__c, BidingAcquireTime__c, BidingEndTime__c, Buchang_sales__c, BudgetAmount1__c, BudgetAmount2__c, BudgetAmount3__c, BudgetAmount4__c, BudgetAmount5__c, BudgetUnit__c, CentralizedProject__c, ConfirmationAssistant__c, ConfirmationofAward__c, CursorMark__c, Discountloan__c, Domestic_Or_Import__c, File_Surpass_12M__c, GI_assistant__c, Hospital1__c, Hospital2__c, Hospital3__c, Hospital4__c, Hospital__c, IFActiveUpdateOwner__c, InfoFile__c, InfoId__c, InfoOwner__c, InfoPublishTime__c, InfoQianlimaUrl__c, InfoTitle__c, InfoType__c, InvolveBudgetAmount__c, IsBid__c, IsElectronic__c, IsOther__c, IsReactionOpp__c, IsRelateProject__c, IsTerminate__c, Keywords__c, Logical_delete__c, Manager_sales__c, Migrate_Status__c, Migration_GUID__c, NotBidApprovalStatus__c, NumberOfBids__c, OBSAP_PR__c, OBSAP_WinnerAmount__c, OBSAP_relativeTime__c, OCSMProvinceS__c, OCSMProvinceText__c, OlyNumberHosts__c, OpenBidingTime__c, OpportunityNum__c, OpportunityStatus__c, zcdataFileName__c, OtherInfoTitle__c, Other_units__c, Overstep_12M_infofile__c, OwnerIsActive__c, ProjectId__c, ProjectRestartFLG__c, PushNewInformation__c, QLMGetDataTime__c, QuoteIrai__c, RelateOppTime__c, RepeatProMergeFLG__c, RepsExtraContent__c, ResultDate__c, Retain_Tender__c, RivalHostsNumber__c, SP_assistant__c, SalesDepartmentOwner__c, Salesdepartment_SAP_Text__c, Spot_Check_Result_Op__c, Spot_Check_Result_Text__c, Spot_Check__c, Strategic_department_MainId1__c, Strategic_department_MainId2__c, Strategic_department_MainId3__c, Strategic_department_MainId4__c, Strategic_department_MainId__c, Strategic_department_Opp__c, SumUnit__c, Sum__c, TenderAssistant1__c, TenderAssistant2__c, TenderBeginTime__c, TenderDate__c, TenderEndTime__c, TenderManageCode__c, Tender_Number__c, Tender_Olympus__c, TerminateApplyTime__c, TerminateApprovalStatus__c, TerminateApprovalTime__c, TerminateExtraContent__c, TerminateReason__c, TotalNumberHosts__c, Turn_off_homepage_reminder__c, Window2__c, WinnerAmount1__c, WinnerAmount2__c, WinnerAmount3__c, WinnerAmount4__c, WinnerAmount5__c, WinnerUnit__c, XmNumber__c, YingyeWindow__c, ZCDataUpdate__c, ZCInfoId__c, ZCProjectId__c, ZCUpdateDate__c, ZhaoBiaoUnit1__c, ZhaoBiaoUnit2__c, ZhaoBiaoUnit3__c, ZhaoBiaoUnit4__c, ZhaoBiaoUnit5__c, ZhaoRelationName_Encrypted__c, ZhaoRelationName__c, ZhaoRelationWay_Encrypted__c, ZhaoRelationWay__c, ZhongBiaoUnit1__c, ZhongBiaoUnit2__c, ZhongBiaoUnit3__c, ZhongBiaoUnit4__c, ZhongBiaoUnit5__c, ZhongRelationName_Encrypted__c, ZhongRelationName__c, ZhongRelationWay_Encrypted__c, ZhongRelationWay__c, Zongjian_Sales__c, department__c, department_selection__c, funding_source__c, host_if__c, irrelevantReasonOther__c, irrelevantReasons__c, irresponseApplyTime__c, irresponseApprovalTime__c, irresponsibleReasonOther__c, irresponsibleReason__c, noticeDate__c, original_url__c, otherInfo__c, proInvolvedManual__c, proInvolved__c, publicDate__c, relativeTime__c, remarks1__c, remarks2__c, responseTime__c, status_text__c, subDepartment1__c, subDepartment2__c, subDepartment3__c, subDepartment4__c, url__c, AccountOwnerFormula__c, BiddingOCSMAdministration__c, BudgetAmountNumber__c, BudgetAmount__c, Hospital_GI_Main__c, Hospital_SP_Main__c, If_Tender_Intime__c, IsBuchangActive__c, IsManagerActive__c, IsZongjianActive__c, Is_GIORSP__c, Is_InfoOwner__c, ManagerEqualBuchan__c, OCSMProvince__c, OwnerOcsm__c, ProductCategory__c, ProvinceComparison__c, RecordType_Formula__c, RelateOppDate__c, Salesdepartment_SAP__c, TAT_Opportunity_Num__c, TAT_Relative_Num__c, TenderNum__c, Tender_Order__c, ViewBidConfirm__c, ViewRelatedOppAlert__c, ViewRelatedOpp__c, ViewWaitConfirm__c, WinnerAmountKRMB__c, WinnerAmountNumber__c, WinnerAmount__c, buchangEqualZongjian__c, department_category__c, relativeDate__c, relativeTime_F__c, status__c, subInfoType__c, Custom_Last_ModifiedById__c, Custom_Last_Modified_Date__c, fm_Last_Modified_ById__c, fm_Last_Modified_Date__c, SpotCheckByText__c, SpotCheckDate__c, project_judge_id__c, TerminateApplyTimeFormula__c, TerminateApprovalTimeFormula__c, BudgetAmountKRMB__c, Custom_CreatedById__c, Custom_CreatedDate__c, fm_CreatedById__c, fm_CreatedDate__c, Calculation_Items__c, department_selection1__c, department_selection2__c, department_selection3__c, department_selection4__c, Watch_DepartmentOwner1__c, Watch_DepartmentOwner2__c, Watch_DepartmentOwner3__c, Watch_DepartmentOwner4__c, Watch_DepartmentOwner__c, ViewRelatedOppTenAlert1__c, ViewRelatedOppTenAlert2__c, ViewRelatedOppTenAlert3__c, ViewRelatedOppTenAlert4__c, ViewRelatedOppTenAlert__c FROM Tender_information__c where Id =: baoliu.Id];
        Integer deleteInfoTypeNumber1 = Integer.valueOf(deleteInfo.InfoType__c.subString(0,1));
        Integer baoliuInfoTypeNumber1 = Integer.valueOf(baoliuInfo.InfoType__c.subString(0,1));
        String QLMrecordId = Schema.SObjectType.Tender_information__c.getRecordTypeInfosByDeveloperName().get('QLM').getRecordTypeId();
        String ZCrecordId = Schema.SObjectType.Tender_information__c.getRecordTypeInfosByDeveloperName().get('ZC').getRecordTypeId();
        System.debug('chenjingwu=>' + deleteInfoTypeNumber1);
        System.debug('chenjingwu=>' + baoliuInfoTypeNumber1);
        List<Date> baoliuDate = new List<Date>();
        baoliuDate.add(baoliuInfo.ZCUpdateDate__c);
        baoliuDate.add(baoliuInfo.QLMGetDataTime__c);
        baoliuDate.add(Date.valueOf(baoliuInfo.CreatedDate));
        baoliuDate.sort();
        List<Date> deleteDate = new List<Date>();
        deleteDate.add(deleteInfo.ZCUpdateDate__c);
        deleteDate.add(deleteInfo.QLMGetDataTime__c);
        deleteDate.add(Date.valueOf(deleteInfo.CreatedDate));
        deleteDate.sort();
        Date maxBaoliuDate = baoliuDate.get(baoliuDate.size() - 1);
        Date maxDeleteDate = deleteDate.get(deleteDate.size() - 1);
        // 同阶段
        if(deleteInfoTypeNumber1 == baoliuInfoTypeNumber1){
            
            if((deleteInfo.subInfoType__c == '3-5：中标通知' || deleteInfo.subInfoType__c == '3-6：合同公告') && baoliuInfo.subInfoType__c != '3-5：中标通知' && baoliuInfo.subInfoType__c != '3-6：合同公告'){
                // 增量 + 覆盖
                morethanChange(del,baoliu,deleteInfo,baoliuInfo);
            }else if(deleteInfo.subInfoType__c != '3-1：废标公告' && deleteInfo.subInfoType__c != '3-2：流标公告' && (baoliuInfo.subInfoType__c == '3-1：废标公告' || baoliuInfo.subInfoType__c == '3-2：流标公告')){
                // 增量 + 覆盖
                morethanChange(del,baoliu,deleteInfo,baoliuInfo);
            }else if(deleteInfo.subInfoType__c == '3-5：中标通知' && baoliuInfo.subInfoType__c == '3-6：合同公告'){
                //增量
                equalChange(del,baoliu,deleteInfo,baoliuInfo);
            }else{
                // 增量
                equalChange(del,baoliu,deleteInfo,baoliuInfo);
            }
        // 推进
        }else if(deleteInfoTypeNumber1 > baoliuInfoTypeNumber1){
            if((deleteInfo.subInfoType__c == '3-1：废标公告' || deleteInfo.subInfoType__c == '3-2：流标公告') && maxDeleteDate < maxBaoliuDate){
                // 增量
                equalChange(del,baoliu,deleteInfo,baoliuInfo);
            }else{
                // 增量 + 覆盖
                morethanChange(del,baoliu,deleteInfo,baoliuInfo);
            }
            
        // 回退
        }else{
            if((baoliuInfo.subInfoType__c == '3-1：废标公告' || baoliuInfo.subInfoType__c == '3-2：流标公告') && maxBaoliuDate < maxDeleteDate){
                // 增量 + 覆盖
                morethanChange(del,baoliu,deleteInfo,baoliuInfo);
            }else{
                // 增量
                equalChange(del,baoliu,deleteInfo,baoliuInfo);
            }
           
        }
        // 众成->千里马 	如果保留项目没有众成信息，要无脑赋值
        if(deleteInfo.RecordTypeId == QLMrecordId && baoliuInfo.RecordTypeId == ZCrecordId){
            specialChange(del,baoliu,deleteInfo,baoliuInfo);
        }
        
        List<Tender_information_details__c> deleteDetailList = [Select IsDeleted, Name, CurrencyIsoCode, Tender_information__c, Brand__c, Is_Final__c, Keywords__c, Migration_GUID__c, Model__c, Name__c, NumberUnit__c, Number__c, Original_ID__c, PriceUnit__c, Price__c, ProjectId__c, TotalPriceUnit__c, TotalPrice__c, brand_type__c, one_class__c, source__c, three_class__c, two_class__c, winning_detail_id__c, Number_F__c, Price_N__c, TotalPrice_N__c FROM Tender_information_details__c where Tender_information__c =: deleteInfo.Id];
        List<Tender_information_details__c> baoliuDetailList = [Select IsDeleted, Name, CurrencyIsoCode, Tender_information__c, Brand__c, Is_Final__c, Keywords__c, Migration_GUID__c, Model__c, Name__c, NumberUnit__c, Number__c, Original_ID__c, PriceUnit__c, Price__c, ProjectId__c, TotalPriceUnit__c, TotalPrice__c, brand_type__c, one_class__c, source__c, three_class__c, two_class__c, winning_detail_id__c, Number_F__c, Price_N__c, TotalPrice_N__c FROM Tender_information_details__c where Tender_information__c =: baoliuInfo.Id];
        List<Tender_information_details__c> insertDetailList = new List<Tender_information_details__c>();
        List<Tender_information_details__c> deleteList = new List<Tender_information_details__c>();
        // chenjingwu 20240730 start
        if((deleteDetailList.size() > 0 && baoliuDetailList.size() > 0)){
            String baoliuRecordType = baoliuDetailList[0].source__c;
            String deleteRecordType = deleteDetailList[0].source__c;
            if(deleteRecordType == '众成' && baoliuRecordType == '千里马'){
                if(!((deleteInfo.subInfoType__c == '3-1：废标公告' || deleteInfo.subInfoType__c == '3-2：流标公告') && baoliuInfo.subInfoType__c != '3-1：废标公告' && baoliuInfo.subInfoType__c != '3-2：流标公告')){
                    for(Tender_information_details__c detail : deleteDetailList){
                        Tender_information_details__c info = new Tender_information_details__c();
                        info.Name = detail.Name;
                        info.CurrencyIsoCode = detail.CurrencyIsoCode;
                        info.Tender_information__c = baoliuInfo.Id;
                        info.Brand__c = detail.Brand__c;
                        info.Is_Final__c = detail.Is_Final__c;
                        info.Keywords__c = detail.Keywords__c;
                        info.Model__c = detail.Model__c;
                        info.Name__c = detail.Name__c;
                        info.NumberUnit__c = detail.NumberUnit__c;
                        info.Number__c = detail.Number__c;
                        info.PriceUnit__c = detail.PriceUnit__c;
                        info.Price__c = detail.Price__c;
                        info.ProjectId__c = detail.ProjectId__c;
                        info.TotalPriceUnit__c = detail.TotalPriceUnit__c;
                        info.TotalPrice__c = detail.TotalPrice__c;
                        info.brand_type__c = detail.brand_type__c;
                        info.one_class__c = detail.one_class__c;
                        info.source__c = detail.source__c;
                        info.three_class__c = detail.three_class__c;
                        info.two_class__c = detail.two_class__c;
                        info.winning_detail_id__c = detail.winning_detail_id__c;
                        insertDetailList.add(info);
                    }
                    for(Tender_information_details__c detail : baoliuDetailList){
                        deleteList.add(detail);
                    }
                }
            }
    
            if((deleteRecordType == '千里马' && baoliuRecordType == '千里马') || (deleteRecordType == '众成' && baoliuRecordType == '众成')){
                if(deleteInfoTypeNumber1 == baoliuInfoTypeNumber1){
                    if((deleteInfo.subInfoType__c == '3-5：中标通知' || deleteInfo.subInfoType__c == '3-6：合同公告') && baoliuInfo.subInfoType__c != '3-5：中标通知' && baoliuInfo.subInfoType__c != '3-6：合同公告'){
                        // 覆盖
                        for(Tender_information_details__c detail : deleteDetailList){
                    Tender_information_details__c info = new Tender_information_details__c();
                    info.Name = detail.Name;
                    info.CurrencyIsoCode = detail.CurrencyIsoCode;
                    info.Tender_information__c = baoliuInfo.Id;
                    info.Brand__c = detail.Brand__c;
                    info.Is_Final__c = detail.Is_Final__c;
                    info.Keywords__c = detail.Keywords__c;
                    info.Model__c = detail.Model__c;
                    info.Name__c = detail.Name__c;
                    info.NumberUnit__c = detail.NumberUnit__c;
                    info.Number__c = detail.Number__c;
                    info.PriceUnit__c = detail.PriceUnit__c;
                    info.Price__c = detail.Price__c;
                    info.ProjectId__c = detail.ProjectId__c;
                    info.TotalPriceUnit__c = detail.TotalPriceUnit__c;
                    info.TotalPrice__c = detail.TotalPrice__c;
                    info.brand_type__c = detail.brand_type__c;
                    info.one_class__c = detail.one_class__c;
                    info.source__c = detail.source__c;
                    info.three_class__c = detail.three_class__c;
                    info.two_class__c = detail.two_class__c;
                    info.winning_detail_id__c = detail.winning_detail_id__c;
                    insertDetailList.add(info);
                }
                        for(Tender_information_details__c detail : baoliuDetailList){
                            deleteList.add(detail);
                        }
                    }else if(deleteInfo.subInfoType__c != '3-1：废标公告' && deleteInfo.subInfoType__c != '3-2：流标公告' && (baoliuInfo.subInfoType__c == '3-1：废标公告' || baoliuInfo.subInfoType__c == '3-2：流标公告')){
                        // 覆盖
                        for(Tender_information_details__c detail : deleteDetailList){
                    Tender_information_details__c info = new Tender_information_details__c();
                    info.Name = detail.Name;
                    info.CurrencyIsoCode = detail.CurrencyIsoCode;
                    info.Tender_information__c = baoliuInfo.Id;
                    info.Brand__c = detail.Brand__c;
                    info.Is_Final__c = detail.Is_Final__c;
                    info.Keywords__c = detail.Keywords__c;
                    info.Model__c = detail.Model__c;
                    info.Name__c = detail.Name__c;
                    info.NumberUnit__c = detail.NumberUnit__c;
                    info.Number__c = detail.Number__c;
                    info.PriceUnit__c = detail.PriceUnit__c;
                    info.Price__c = detail.Price__c;
                    info.ProjectId__c = detail.ProjectId__c;
                    info.TotalPriceUnit__c = detail.TotalPriceUnit__c;
                    info.TotalPrice__c = detail.TotalPrice__c;
                    info.brand_type__c = detail.brand_type__c;
                    info.one_class__c = detail.one_class__c;
                    info.source__c = detail.source__c;
                    info.three_class__c = detail.three_class__c;
                    info.two_class__c = detail.two_class__c;
                    info.winning_detail_id__c = detail.winning_detail_id__c;
                    insertDetailList.add(info);
                }
                        for(Tender_information_details__c detail : baoliuDetailList){
                            deleteList.add(detail);
                        }
                    }
                    else if(!((deleteInfo.subInfoType__c == '3-1：废标公告' || deleteInfo.subInfoType__c == '3-2：流标公告') && baoliuInfo.subInfoType__c != '3-1：废标公告' && baoliuInfo.subInfoType__c != '3-2：流标公告')){
                        if((baoliuInfo.subInfoType__c != '3-5：中标通知' && maxDeleteDate > maxBaoliuDate) ||(baoliuInfo.subInfoType__c != '3-5：中标通知' && deleteInfo.subInfoType__c == '3-5：中标通知')){
                            // 覆盖
                            for(Tender_information_details__c detail : deleteDetailList){
                                Tender_information_details__c info = new Tender_information_details__c();
                                info.Name = detail.Name;
                                info.CurrencyIsoCode = detail.CurrencyIsoCode;
                                info.Tender_information__c = baoliuInfo.Id;
                                info.Brand__c = detail.Brand__c;
                                info.Is_Final__c = detail.Is_Final__c;
                                info.Keywords__c = detail.Keywords__c;
                                info.Model__c = detail.Model__c;
                                info.Name__c = detail.Name__c;
                                info.NumberUnit__c = detail.NumberUnit__c;
                                info.Number__c = detail.Number__c;
                                info.PriceUnit__c = detail.PriceUnit__c;
                                info.Price__c = detail.Price__c;
                                info.ProjectId__c = detail.ProjectId__c;
                                info.TotalPriceUnit__c = detail.TotalPriceUnit__c;
                                info.TotalPrice__c = detail.TotalPrice__c;
                                info.brand_type__c = detail.brand_type__c;
                                info.one_class__c = detail.one_class__c;
                                info.source__c = detail.source__c;
                                info.three_class__c = detail.three_class__c;
                                info.two_class__c = detail.two_class__c;
                                info.winning_detail_id__c = detail.winning_detail_id__c;
                                insertDetailList.add(info);
                            }
                                    for(Tender_information_details__c detail : baoliuDetailList){
                                        deleteList.add(detail);
                                    }
                        }                 
                    }
                // 推进
                }else if(deleteInfoTypeNumber1 > baoliuInfoTypeNumber1){
                    if(!((deleteInfo.subInfoType__c == '3-1：废标公告' || deleteInfo.subInfoType__c == '3-2：流标公告') && maxDeleteDate < maxBaoliuDate)){
                        //覆盖
                        for(Tender_information_details__c detail : deleteDetailList){
                    Tender_information_details__c info = new Tender_information_details__c();
                    info.Name = detail.Name;
                    info.CurrencyIsoCode = detail.CurrencyIsoCode;
                    info.Tender_information__c = baoliuInfo.Id;
                    info.Brand__c = detail.Brand__c;
                    info.Is_Final__c = detail.Is_Final__c;
                    info.Keywords__c = detail.Keywords__c;
                    info.Model__c = detail.Model__c;
                    info.Name__c = detail.Name__c;
                    info.NumberUnit__c = detail.NumberUnit__c;
                    info.Number__c = detail.Number__c;
                    info.PriceUnit__c = detail.PriceUnit__c;
                    info.Price__c = detail.Price__c;
                    info.ProjectId__c = detail.ProjectId__c;
                    info.TotalPriceUnit__c = detail.TotalPriceUnit__c;
                    info.TotalPrice__c = detail.TotalPrice__c;
                    info.brand_type__c = detail.brand_type__c;
                    info.one_class__c = detail.one_class__c;
                    info.source__c = detail.source__c;
                    info.three_class__c = detail.three_class__c;
                    info.two_class__c = detail.two_class__c;
                    info.winning_detail_id__c = detail.winning_detail_id__c;
                    insertDetailList.add(info);
                }
                        for(Tender_information_details__c detail : baoliuDetailList){
                            deleteList.add(detail);
                        }
                    }
                // 回退
                }else{
                    if((baoliuInfo.subInfoType__c == '3-1：废标公告' || baoliuInfo.subInfoType__c == '3-2：流标公告') && maxBaoliuDate < maxDeleteDate){
                        //覆盖
                        for(Tender_information_details__c detail : deleteDetailList){
                    Tender_information_details__c info = new Tender_information_details__c();
                    info.Name = detail.Name;
                    info.CurrencyIsoCode = detail.CurrencyIsoCode;
                    info.Tender_information__c = baoliuInfo.Id;
                    info.Brand__c = detail.Brand__c;
                    info.Is_Final__c = detail.Is_Final__c;
                    info.Keywords__c = detail.Keywords__c;
                    info.Model__c = detail.Model__c;
                    info.Name__c = detail.Name__c;
                    info.NumberUnit__c = detail.NumberUnit__c;
                    info.Number__c = detail.Number__c;
                    info.PriceUnit__c = detail.PriceUnit__c;
                    info.Price__c = detail.Price__c;
                    info.ProjectId__c = detail.ProjectId__c;
                    info.TotalPriceUnit__c = detail.TotalPriceUnit__c;
                    info.TotalPrice__c = detail.TotalPrice__c;
                    info.brand_type__c = detail.brand_type__c;
                    info.one_class__c = detail.one_class__c;
                    info.source__c = detail.source__c;
                    info.three_class__c = detail.three_class__c;
                    info.two_class__c = detail.two_class__c;
                    info.winning_detail_id__c = detail.winning_detail_id__c;
                    insertDetailList.add(info);
                }
                        for(Tender_information_details__c detail : baoliuDetailList){
                            deleteList.add(detail);
                        }
                    }
                }
            }
            
        }else {
            if(deleteDetailList.size() > 0 && baoliuDetailList.size() == 0){
                for(Tender_information_details__c detail : deleteDetailList){
                    Tender_information_details__c info = new Tender_information_details__c();
                    info.Name = detail.Name;
                    info.CurrencyIsoCode = detail.CurrencyIsoCode;
                    info.Tender_information__c = baoliuInfo.Id;
                    info.Brand__c = detail.Brand__c;
                    info.Is_Final__c = detail.Is_Final__c;
                    info.Keywords__c = detail.Keywords__c;
                    info.Model__c = detail.Model__c;
                    info.Name__c = detail.Name__c;
                    info.NumberUnit__c = detail.NumberUnit__c;
                    info.Number__c = detail.Number__c;
                    info.PriceUnit__c = detail.PriceUnit__c;
                    info.Price__c = detail.Price__c;
                    info.ProjectId__c = detail.ProjectId__c;
                    info.TotalPriceUnit__c = detail.TotalPriceUnit__c;
                    info.TotalPrice__c = detail.TotalPrice__c;
                    info.brand_type__c = detail.brand_type__c;
                    info.one_class__c = detail.one_class__c;
                    info.source__c = detail.source__c;
                    info.three_class__c = detail.three_class__c;
                    info.two_class__c = detail.two_class__c;
                    info.winning_detail_id__c = detail.winning_detail_id__c;
                    insertDetailList.add(info);
                }
            }
        }
        if(insertDetailList.size() > 0){
            insert insertDetailList;
            if(deleteList.size() > 0){
                delete deleteList;
            }
        }
        
        // chenjingwu 20240730 end
        
    }
    public static void specialChange(Tender_information__c del,Tender_information__c baoliu,Tender_information__c deleteInfo,Tender_information__c baoliuInfo){
        // 是否包含主机
        baoliu.host_if__c = baoliuInfo.host_if__c == null && deleteInfo.host_if__c != null ? deleteInfo.host_if__c : baoliuInfo.host_if__c;
        // 资金来源
        baoliu.funding_source__c = baoliuInfo.funding_source__c == null && deleteInfo.funding_source__c != null ? deleteInfo.funding_source__c : baoliuInfo.funding_source__c;
        // 众成链接
        baoliu.url__c = baoliuInfo.url__c == null && deleteInfo.url__c != null ? deleteInfo.url__c : baoliuInfo.url__c;
        // 原文链接
        baoliu.original_url__c = baoliuInfo.original_url__c == null && deleteInfo.original_url__c != null ? deleteInfo.original_url__c : baoliuInfo.original_url__c;
        // 项目ID-大包标识
        baoliu.project_judge_id__c = baoliuInfo.project_judge_id__c == null && deleteInfo.project_judge_id__c != null ? deleteInfo.project_judge_id__c : baoliuInfo.project_judge_id__c;
        // 众成更新日期
        baoliu.ZCUpdateDate__c = baoliuInfo.ZCUpdateDate__c == null && deleteInfo.ZCUpdateDate__c != null ? deleteInfo.ZCUpdateDate__c : baoliuInfo.ZCUpdateDate__c;
        // 众成项目ID
        baoliu.ZCProjectId__c = baoliuInfo.ZCProjectId__c == null && deleteInfo.ZCProjectId__c != null ? deleteInfo.ZCProjectId__c : baoliuInfo.ZCProjectId__c;
        // 众成信息ID
        baoliu.ZCInfoId__c = baoliuInfo.ZCInfoId__c == null && deleteInfo.ZCInfoId__c != null ? deleteInfo.ZCInfoId__c : baoliuInfo.ZCInfoId__c;
        // 众成数据更新
        baoliu.ZCDataUpdate__c = deleteInfo.ZCDataUpdate__c == true ? true : false;

        
    }

    public static void equalChange(Tender_information__c del,Tender_information__c baoliu,Tender_information__c deleteInfo,Tender_information__c baoliuInfo){
        // 招标编号
        baoliu.XmNumber__c = baoliuInfo.XmNumber__c == null && deleteInfo.XmNumber__c != null ? deleteInfo.XmNumber__c : baoliuInfo.XmNumber__c;
        // 其他补充说明
        baoliu.otherInfo__c = baoliuInfo.otherInfo__c == null && deleteInfo.otherInfo__c != null ? deleteInfo.otherInfo__c : baoliuInfo.otherInfo__c;
        // 招标单位
        baoliu.ZhaoBiaoUnit1__c = baoliuInfo.ZhaoBiaoUnit1__c == null && deleteInfo.ZhaoBiaoUnit1__c != null ? deleteInfo.ZhaoBiaoUnit1__c : baoliuInfo.ZhaoBiaoUnit1__c;
        baoliu.ZhaoBiaoUnit2__c = baoliuInfo.ZhaoBiaoUnit2__c == null && deleteInfo.ZhaoBiaoUnit2__c != null ? deleteInfo.ZhaoBiaoUnit2__c : baoliuInfo.ZhaoBiaoUnit2__c;
        baoliu.ZhaoBiaoUnit3__c = baoliuInfo.ZhaoBiaoUnit3__c == null && deleteInfo.ZhaoBiaoUnit3__c != null ? deleteInfo.ZhaoBiaoUnit3__c : baoliuInfo.ZhaoBiaoUnit3__c;
        baoliu.ZhaoBiaoUnit4__c = baoliuInfo.ZhaoBiaoUnit4__c == null && deleteInfo.ZhaoBiaoUnit4__c != null ? deleteInfo.ZhaoBiaoUnit4__c : baoliuInfo.ZhaoBiaoUnit4__c;
        baoliu.ZhaoBiaoUnit5__c = baoliuInfo.ZhaoBiaoUnit5__c == null && deleteInfo.ZhaoBiaoUnit5__c != null ? deleteInfo.ZhaoBiaoUnit5__c : baoliuInfo.ZhaoBiaoUnit5__c;
        // 中标单位
        baoliu.ZhongBiaoUnit1__c = baoliuInfo.ZhongBiaoUnit1__c == null && deleteInfo.ZhongBiaoUnit1__c != null ? deleteInfo.ZhongBiaoUnit1__c : baoliuInfo.ZhongBiaoUnit1__c;
        baoliu.ZhongBiaoUnit2__c = baoliuInfo.ZhongBiaoUnit2__c == null && deleteInfo.ZhongBiaoUnit2__c != null ? deleteInfo.ZhongBiaoUnit2__c : baoliuInfo.ZhongBiaoUnit2__c;
        baoliu.ZhongBiaoUnit3__c = baoliuInfo.ZhongBiaoUnit3__c == null && deleteInfo.ZhongBiaoUnit3__c != null ? deleteInfo.ZhongBiaoUnit3__c : baoliuInfo.ZhongBiaoUnit3__c;
        baoliu.ZhongBiaoUnit4__c = baoliuInfo.ZhongBiaoUnit4__c == null && deleteInfo.ZhongBiaoUnit4__c != null ? deleteInfo.ZhongBiaoUnit4__c : baoliuInfo.ZhongBiaoUnit4__c;
        baoliu.ZhongBiaoUnit5__c = baoliuInfo.ZhongBiaoUnit5__c == null && deleteInfo.ZhongBiaoUnit5__c != null ? deleteInfo.ZhongBiaoUnit5__c : baoliuInfo.ZhongBiaoUnit5__c;
        // 大金额
        baoliu.BigAmoutTag__c = baoliuInfo.BigAmoutTag__c == null && deleteInfo.BigAmoutTag__c != null ? deleteInfo.BigAmoutTag__c : baoliuInfo.BigAmoutTag__c;
        // 维修/维保
        baoliu.MaintenanceTag__c = baoliuInfo.MaintenanceTag__c == null && deleteInfo.MaintenanceTag__c != null ? deleteInfo.MaintenanceTag__c : baoliuInfo.MaintenanceTag__c;
        // 信息省
        baoliu.AreaProvince__c = baoliuInfo.AreaProvince__c == null && deleteInfo.AreaProvince__c != null ? deleteInfo.AreaProvince__c : baoliuInfo.AreaProvince__c;
        // 信息市
        baoliu.AreaCity__c = baoliuInfo.AreaCity__c == null && deleteInfo.AreaCity__c != null ? deleteInfo.AreaCity__c : baoliuInfo.AreaCity__c;
        // 信息区县
        baoliu.AreaCountry__c = baoliuInfo.AreaCountry__c == null && deleteInfo.AreaCountry__c != null ? deleteInfo.AreaCountry__c : baoliuInfo.AreaCountry__c;
        // 结果记录日
        baoliu.ResultDate__c = baoliuInfo.ResultDate__c == null && deleteInfo.ResultDate__c != null ? deleteInfo.ResultDate__c : baoliuInfo.ResultDate__c;
        if((baoliuInfo.InfoType__c == '4：变更' || baoliuInfo.InfoType__c == '5：其他') && deleteInfo.InfoType__c != '4：变更' && deleteInfo.InfoType__c != '5：其他'){
            // 项目阶段
            baoliu.InfoType__c = deleteInfo.InfoType__c != null ? deleteInfo.InfoType__c : baoliuInfo.InfoType__c;
            // 阶段补充说明
            baoliu.subInfoType__c = deleteInfo.subInfoType__c != null ? deleteInfo.subInfoType__c : baoliuInfo.subInfoType__c;
        }else{
            // 项目阶段
            baoliu.InfoType__c = baoliuInfo.InfoType__c == null && deleteInfo.InfoType__c != null ? deleteInfo.InfoType__c : baoliuInfo.InfoType__c;
            // 阶段补充说明
            baoliu.subInfoType__c = baoliuInfo.subInfoType__c == null && deleteInfo.subInfoType__c != null ? deleteInfo.subInfoType__c : baoliuInfo.subInfoType__c;
        }
        // 项目ID
        if(baoliuInfo.ProjectId__c == null && deleteInfo.ProjectId__c != null){
            baoliu.ProjectId__c = deleteInfo.ProjectId__c;
            del.ProjectId__c = null;
        }
        // 游标
        baoliu.CursorMark__c = baoliuInfo.CursorMark__c == null && deleteInfo.CursorMark__c != null ? deleteInfo.CursorMark__c : baoliuInfo.CursorMark__c;
        // 预告记录日
        baoliu.noticeDate__c = baoliuInfo.noticeDate__c == null && deleteInfo.noticeDate__c != null ? deleteInfo.noticeDate__c : baoliuInfo.noticeDate__c;
        // 公告记录日
        baoliu.publicDate__c = baoliuInfo.publicDate__c == null && deleteInfo.publicDate__c != null ? deleteInfo.publicDate__c : baoliuInfo.publicDate__c;
        // 代理机构
        baoliu.AgentUnit1__c = baoliuInfo.AgentUnit1__c == null && deleteInfo.AgentUnit1__c != null ? deleteInfo.AgentUnit1__c : baoliuInfo.AgentUnit1__c;
        baoliu.AgentUnit2__c = baoliuInfo.AgentUnit2__c == null && deleteInfo.AgentUnit2__c != null ? deleteInfo.AgentUnit2__c : baoliuInfo.AgentUnit2__c;
        baoliu.AgentUnit3__c = baoliuInfo.AgentUnit3__c == null && deleteInfo.AgentUnit3__c != null ? deleteInfo.AgentUnit3__c : baoliuInfo.AgentUnit3__c;
        baoliu.AgentUnit4__c = baoliuInfo.AgentUnit4__c == null && deleteInfo.AgentUnit4__c != null ? deleteInfo.AgentUnit4__c : baoliuInfo.AgentUnit4__c;
        baoliu.AgentUnit5__c = baoliuInfo.AgentUnit5__c == null && deleteInfo.AgentUnit5__c != null ? deleteInfo.AgentUnit5__c : baoliuInfo.AgentUnit5__c;
        // 超过五个(后台用)
        baoliu.Other_units__c = baoliuInfo.Other_units__c == null && deleteInfo.Other_units__c != null ? deleteInfo.Other_units__c : baoliuInfo.Other_units__c;
        // 关键词
        baoliu.Keywords__c = baoliuInfo.Keywords__c == null && deleteInfo.Keywords__c != null ? deleteInfo.Keywords__c : baoliuInfo.Keywords__c;
        // 千里马链接
        baoliu.InfoQianlimaUrl__c = baoliuInfo.InfoQianlimaUrl__c == null && deleteInfo.InfoQianlimaUrl__c != null ? deleteInfo.InfoQianlimaUrl__c : baoliuInfo.InfoQianlimaUrl__c;
        // 国产/进口
        baoliu.Domestic_Or_Import__c = baoliuInfo.Domestic_Or_Import__c == null && deleteInfo.Domestic_Or_Import__c != null ? deleteInfo.Domestic_Or_Import__c : baoliuInfo.Domestic_Or_Import__c;
        // 招标方式
        baoliu.BiddingType__c = baoliuInfo.BiddingType__c == null && deleteInfo.BiddingType__c != null ? deleteInfo.BiddingType__c : baoliuInfo.BiddingType__c;
        // 招标单位联系方式
        baoliu.ZhaoRelationWay__c = baoliuInfo.ZhaoRelationWay__c == null && deleteInfo.ZhaoRelationWay__c != null ? deleteInfo.ZhaoRelationWay__c : baoliuInfo.ZhaoRelationWay__c;
        // 是否电子招标
        baoliu.IsElectronic__c = baoliuInfo.IsElectronic__c == null && deleteInfo.IsElectronic__c != null ? deleteInfo.IsElectronic__c : baoliuInfo.IsElectronic__c;
        // 投标截止时间
        baoliu.TenderEndTime__c = baoliuInfo.TenderEndTime__c == null && deleteInfo.TenderEndTime__c != null ? deleteInfo.TenderEndTime__c : baoliuInfo.TenderEndTime__c;
        // 是否有其他信息
        baoliu.IsOther__c = deleteInfo.IsOther__c == true ? true : false;
        // 其他信息标题
        baoliu.OtherInfoTitle__c = baoliuInfo.OtherInfoTitle__c == null && deleteInfo.OtherInfoTitle__c != null ? deleteInfo.OtherInfoTitle__c : baoliuInfo.OtherInfoTitle__c;
        // 开标时间
        baoliu.OpenBidingTime__c = baoliuInfo.OpenBidingTime__c == null && deleteInfo.OpenBidingTime__c != null ? deleteInfo.OpenBidingTime__c : baoliuInfo.OpenBidingTime__c;
        // 代理机构联系方式
        baoliu.AgentRelationWay__c = baoliuInfo.AgentRelationWay__c == null && deleteInfo.AgentRelationWay__c != null ? deleteInfo.AgentRelationWay__c : baoliuInfo.AgentRelationWay__c;
        // 附件列表
        baoliu.InfoFile__c = baoliuInfo.InfoFile__c == null && deleteInfo.InfoFile__c != null ? deleteInfo.InfoFile__c : baoliuInfo.InfoFile__c;
        // 预计金额的计量单位
        baoliu.BudgetUnit__c = baoliuInfo.BudgetUnit__c == null && deleteInfo.BudgetUnit__c != null ? deleteInfo.BudgetUnit__c : baoliuInfo.BudgetUnit__c;
        // 中标金额的计量单位
        baoliu.WinnerUnit__c = baoliuInfo.WinnerUnit__c == null && deleteInfo.WinnerUnit__c != null ? deleteInfo.WinnerUnit__c : baoliuInfo.WinnerUnit__c;
        // 发布时间
        baoliu.InfoPublishTime__c = baoliuInfo.InfoPublishTime__c == null && deleteInfo.InfoPublishTime__c != null ? deleteInfo.InfoPublishTime__c : baoliuInfo.InfoPublishTime__c;
        // 代理机构联系人
        baoliu.AgentRelationName__c = baoliuInfo.AgentRelationName__c == null && deleteInfo.AgentRelationName__c != null ? deleteInfo.AgentRelationName__c : baoliuInfo.AgentRelationName__c;
        //标书截止时间
        baoliu.BidingEndTime__c = baoliuInfo.BidingEndTime__c == null && deleteInfo.BidingEndTime__c != null ? deleteInfo.BidingEndTime__c : baoliuInfo.BidingEndTime__c;
        // 中标单位联系方式
        baoliu.ZhongRelationWay__c = baoliuInfo.ZhongRelationWay__c == null && deleteInfo.ZhongRelationWay__c != null ? deleteInfo.ZhongRelationWay__c : baoliuInfo.ZhongRelationWay__c;
        // 标书获取开始时间
        baoliu.BidingAcquireTime__c = baoliuInfo.BidingAcquireTime__c == null && deleteInfo.BidingAcquireTime__c != null ? deleteInfo.BidingAcquireTime__c : baoliuInfo.BidingAcquireTime__c;
        // 投标开始时间
        baoliu.TenderBeginTime__c = baoliuInfo.TenderBeginTime__c == null && deleteInfo.TenderBeginTime__c != null ? deleteInfo.TenderBeginTime__c : baoliuInfo.TenderBeginTime__c;
        // 标的物总价之和
        baoliu.Sum__c = baoliuInfo.Sum__c == null && deleteInfo.Sum__c != null ? deleteInfo.Sum__c : baoliuInfo.Sum__c;
        // 标的物总价（计量单位）
        baoliu.SumUnit__c = baoliuInfo.SumUnit__c == null && deleteInfo.SumUnit__c != null ? deleteInfo.SumUnit__c : baoliuInfo.SumUnit__c;
        // 招标单位联系人
        baoliu.ZhaoRelationName__c = baoliuInfo.ZhaoRelationName__c == null && deleteInfo.ZhaoRelationName__c != null ? deleteInfo.ZhaoRelationName__c : baoliuInfo.ZhaoRelationName__c;
        // 中标单位联系人
        baoliu.ZhongRelationName__c = baoliuInfo.ZhongRelationName__c == null && deleteInfo.ZhongRelationName__c != null ? deleteInfo.ZhongRelationName__c : baoliuInfo.ZhongRelationName__c;
        // 千里马获取时间
        baoliu.QLMGetDataTime__c = baoliuInfo.QLMGetDataTime__c != null && baoliuInfo.QLMGetDataTime__c > deleteInfo.QLMGetDataTime__c? baoliuInfo.QLMGetDataTime__c : deleteInfo.QLMGetDataTime__c;
        // 众成项目ID
        baoliu.ZCProjectId__c = baoliuInfo.ZCProjectId__c == null && deleteInfo.ZCProjectId__c != null ? deleteInfo.ZCProjectId__c : baoliuInfo.ZCProjectId__c;
        // 众成信息ID
        baoliu.ZCInfoId__c = baoliuInfo.ZCInfoId__c == null && deleteInfo.ZCInfoId__c != null ? deleteInfo.ZCInfoId__c : baoliuInfo.ZCInfoId__c;
        // 原文链接
        baoliu.original_url__c = baoliuInfo.original_url__c == null && deleteInfo.original_url__c != null ? deleteInfo.original_url__c : baoliuInfo.original_url__c;
        // 中标金额
        baoliu.WinnerAmount1__c = baoliuInfo.WinnerAmount1__c == null && deleteInfo.WinnerAmount1__c != null ? deleteInfo.WinnerAmount1__c : baoliuInfo.WinnerAmount1__c;
        baoliu.WinnerAmount2__c = baoliuInfo.WinnerAmount2__c == null && deleteInfo.WinnerAmount2__c != null ? deleteInfo.WinnerAmount2__c : baoliuInfo.WinnerAmount2__c;
        baoliu.WinnerAmount3__c = baoliuInfo.WinnerAmount3__c == null && deleteInfo.WinnerAmount3__c != null ? deleteInfo.WinnerAmount3__c : baoliuInfo.WinnerAmount3__c;
        baoliu.WinnerAmount4__c = baoliuInfo.WinnerAmount4__c == null && deleteInfo.WinnerAmount4__c != null ? deleteInfo.WinnerAmount4__c : baoliuInfo.WinnerAmount4__c;
        baoliu.WinnerAmount5__c = baoliuInfo.WinnerAmount5__c == null && deleteInfo.WinnerAmount5__c != null ? deleteInfo.WinnerAmount5__c : baoliuInfo.WinnerAmount5__c;
        // 预测金额
        baoliu.BudgetAmount1__c = baoliuInfo.BudgetAmount1__c == null && deleteInfo.BudgetAmount1__c != null ? deleteInfo.BudgetAmount1__c : baoliuInfo.BudgetAmount1__c;
        baoliu.BudgetAmount2__c = baoliuInfo.BudgetAmount2__c == null && deleteInfo.BudgetAmount2__c != null ? deleteInfo.BudgetAmount2__c : baoliuInfo.BudgetAmount2__c;
        baoliu.BudgetAmount3__c = baoliuInfo.BudgetAmount3__c == null && deleteInfo.BudgetAmount3__c != null ? deleteInfo.BudgetAmount3__c : baoliuInfo.BudgetAmount3__c;
        baoliu.BudgetAmount4__c = baoliuInfo.BudgetAmount4__c == null && deleteInfo.BudgetAmount4__c != null ? deleteInfo.BudgetAmount4__c : baoliuInfo.BudgetAmount4__c;
        baoliu.BudgetAmount5__c = baoliuInfo.BudgetAmount5__c == null && deleteInfo.BudgetAmount5__c != null ? deleteInfo.BudgetAmount5__c : baoliuInfo.BudgetAmount5__c;
        // 预测金额
        // 中标日（OBSAP）
        baoliu.Bid_Winning_Date__c = baoliuInfo.Bid_Winning_Date__c == null && deleteInfo.Bid_Winning_Date__c != null ? deleteInfo.Bid_Winning_Date__c : baoliuInfo.Bid_Winning_Date__c;
        // 是否包含主机
        baoliu.host_if__c = baoliuInfo.host_if__c == null && deleteInfo.host_if__c != null ? deleteInfo.host_if__c : baoliuInfo.host_if__c;
        // 资金来源
        baoliu.funding_source__c = baoliuInfo.funding_source__c == null && deleteInfo.funding_source__c != null ? deleteInfo.funding_source__c : baoliuInfo.funding_source__c;
        // 众成链接
        baoliu.url__c = baoliuInfo.url__c == null && deleteInfo.url__c != null ? deleteInfo.url__c : baoliuInfo.url__c;
        // 项目ID-大包标识
        baoliu.project_judge_id__c = baoliuInfo.project_judge_id__c == null && deleteInfo.project_judge_id__c != null ? deleteInfo.project_judge_id__c : baoliuInfo.project_judge_id__c;
        // 众成更新日期
        baoliu.ZCUpdateDate__c = baoliuInfo.ZCUpdateDate__c == null && deleteInfo.ZCUpdateDate__c != null ? deleteInfo.ZCUpdateDate__c : baoliuInfo.ZCUpdateDate__c;
        // 众成数据更新
        baoliu.ZCDataUpdate__c = deleteInfo.ZCDataUpdate__c == true ? true : false;

    }
    public static void morethanChange(Tender_information__c del,Tender_information__c baoliu,Tender_information__c deleteInfo,Tender_information__c baoliuInfo){
        // chenjingwu 20240729 start
        // 项目名称
        baoliu.InfoTitle__c = deleteInfo.InfoTitle__c != null ? deleteInfo.InfoTitle__c : baoliuInfo.InfoTitle__c;
        baoliu.Name = deleteInfo.InfoTitle__c != null ? deleteInfo.InfoTitle__c : baoliuInfo.InfoTitle__c;
        // 招标编号
        baoliu.XmNumber__c = deleteInfo.XmNumber__c != null ? deleteInfo.XmNumber__c : baoliuInfo.XmNumber__c;
        // chenjingwu 20240729 end
        // 其他补充说明
        baoliu.otherInfo__c = deleteInfo.otherInfo__c != null ? deleteInfo.otherInfo__c : baoliuInfo.otherInfo__c;
        // 招标单位
        baoliu.ZhaoBiaoUnit1__c = deleteInfo.ZhaoBiaoUnit1__c != null ? deleteInfo.ZhaoBiaoUnit1__c : baoliuInfo.ZhaoBiaoUnit1__c;
        baoliu.ZhaoBiaoUnit2__c = deleteInfo.ZhaoBiaoUnit2__c != null ? deleteInfo.ZhaoBiaoUnit2__c : baoliuInfo.ZhaoBiaoUnit2__c;
        baoliu.ZhaoBiaoUnit3__c = deleteInfo.ZhaoBiaoUnit3__c != null ? deleteInfo.ZhaoBiaoUnit3__c : baoliuInfo.ZhaoBiaoUnit3__c;
        baoliu.ZhaoBiaoUnit4__c = deleteInfo.ZhaoBiaoUnit4__c != null ? deleteInfo.ZhaoBiaoUnit4__c : baoliuInfo.ZhaoBiaoUnit4__c;
        baoliu.ZhaoBiaoUnit5__c = deleteInfo.ZhaoBiaoUnit5__c != null ? deleteInfo.ZhaoBiaoUnit5__c : baoliuInfo.ZhaoBiaoUnit5__c;
        // 中标单位
        baoliu.ZhongBiaoUnit1__c = deleteInfo.ZhongBiaoUnit1__c != null ? deleteInfo.ZhongBiaoUnit1__c : baoliuInfo.ZhongBiaoUnit1__c;
        baoliu.ZhongBiaoUnit2__c = deleteInfo.ZhongBiaoUnit2__c != null ? deleteInfo.ZhongBiaoUnit2__c : baoliuInfo.ZhongBiaoUnit2__c;
        baoliu.ZhongBiaoUnit3__c = deleteInfo.ZhongBiaoUnit3__c != null ? deleteInfo.ZhongBiaoUnit3__c : baoliuInfo.ZhongBiaoUnit3__c;
        baoliu.ZhongBiaoUnit4__c = deleteInfo.ZhongBiaoUnit4__c != null ? deleteInfo.ZhongBiaoUnit4__c : baoliuInfo.ZhongBiaoUnit4__c;
        baoliu.ZhongBiaoUnit5__c = deleteInfo.ZhongBiaoUnit5__c != null ? deleteInfo.ZhongBiaoUnit5__c : baoliuInfo.ZhongBiaoUnit5__c;
        // 大金额
        baoliu.BigAmoutTag__c = baoliuInfo.BigAmoutTag__c == null && deleteInfo.BigAmoutTag__c != null ? deleteInfo.BigAmoutTag__c : baoliuInfo.BigAmoutTag__c;
        // 维修/维保
        baoliu.MaintenanceTag__c = baoliuInfo.MaintenanceTag__c == null && deleteInfo.MaintenanceTag__c != null ? deleteInfo.MaintenanceTag__c : baoliuInfo.MaintenanceTag__c;
        // 信息省
        baoliu.AreaProvince__c = deleteInfo.AreaProvince__c != null ? deleteInfo.AreaProvince__c : baoliuInfo.AreaProvince__c;
        // 信息市
        baoliu.AreaCity__c = deleteInfo.AreaCity__c != null ? deleteInfo.AreaCity__c : baoliuInfo.AreaCity__c;
        // 信息区县
        baoliu.AreaCountry__c = deleteInfo.AreaCountry__c != null ? deleteInfo.AreaCountry__c : baoliuInfo.AreaCountry__c;
        // 结果记录日
        baoliu.ResultDate__c = deleteInfo.ResultDate__c != null ? deleteInfo.ResultDate__c : baoliuInfo.ResultDate__c;
        if(deleteInfo.InfoType__c != '4：变更' && deleteInfo.InfoType__c != '5：其他'){
            // 项目阶段
            baoliu.InfoType__c = deleteInfo.InfoType__c != null ? deleteInfo.InfoType__c : baoliuInfo.InfoType__c;
            // 阶段补充说明
            baoliu.subInfoType__c = deleteInfo.subInfoType__c != null ? deleteInfo.subInfoType__c : baoliuInfo.subInfoType__c;
        }
        // 项目ID
        if(baoliuInfo.ProjectId__c == null && deleteInfo.ProjectId__c != null){
            baoliu.ProjectId__c = deleteInfo.ProjectId__c;
            del.ProjectId__c = null;
        }
        // 游标
        baoliu.CursorMark__c = deleteInfo.CursorMark__c != null ? deleteInfo.CursorMark__c : baoliuInfo.CursorMark__c;
        // 预告记录日
        baoliu.noticeDate__c = deleteInfo.noticeDate__c != null ? deleteInfo.noticeDate__c : baoliuInfo.noticeDate__c;
        // 公告记录日
        baoliu.publicDate__c = deleteInfo.publicDate__c != null ? deleteInfo.publicDate__c : baoliuInfo.publicDate__c;
        // 代理机构
        baoliu.AgentUnit1__c = deleteInfo.AgentUnit1__c != null ? deleteInfo.AgentUnit1__c : baoliuInfo.AgentUnit1__c;
        baoliu.AgentUnit2__c = deleteInfo.AgentUnit2__c != null ? deleteInfo.AgentUnit2__c : baoliuInfo.AgentUnit2__c;
        baoliu.AgentUnit3__c = deleteInfo.AgentUnit3__c != null ? deleteInfo.AgentUnit3__c : baoliuInfo.AgentUnit3__c;
        baoliu.AgentUnit4__c = deleteInfo.AgentUnit4__c != null ? deleteInfo.AgentUnit4__c : baoliuInfo.AgentUnit4__c;
        baoliu.AgentUnit5__c = deleteInfo.AgentUnit5__c != null ? deleteInfo.AgentUnit5__c : baoliuInfo.AgentUnit5__c;
        // 超过五个(后台用)
        baoliu.Other_units__c = deleteInfo.Other_units__c != null ? deleteInfo.Other_units__c : baoliuInfo.Other_units__c;
        // 关键词
        baoliu.Keywords__c = deleteInfo.Keywords__c != null ? deleteInfo.Keywords__c : baoliuInfo.Keywords__c;
        // 千里马链接
        baoliu.InfoQianlimaUrl__c = deleteInfo.InfoQianlimaUrl__c != null ? deleteInfo.InfoQianlimaUrl__c : baoliuInfo.InfoQianlimaUrl__c;
        // 国产/进口
        baoliu.Domestic_Or_Import__c = deleteInfo.Domestic_Or_Import__c != null ? deleteInfo.Domestic_Or_Import__c : baoliuInfo.Domestic_Or_Import__c;
        // 招标方式
        baoliu.BiddingType__c = deleteInfo.BiddingType__c != null ? deleteInfo.BiddingType__c : baoliuInfo.BiddingType__c;
        // 招标单位联系方式
        baoliu.ZhaoRelationWay__c = deleteInfo.ZhaoRelationWay__c != null ? deleteInfo.ZhaoRelationWay__c : baoliuInfo.ZhaoRelationWay__c;
        // 是否电子招标
        baoliu.IsElectronic__c = deleteInfo.IsElectronic__c != null ? deleteInfo.IsElectronic__c : baoliuInfo.IsElectronic__c;
        // 投标截止时间
        baoliu.TenderEndTime__c = deleteInfo.TenderEndTime__c != null ? deleteInfo.TenderEndTime__c : baoliuInfo.TenderEndTime__c;
        // 是否有其他信息
        baoliu.IsOther__c = deleteInfo.IsOther__c == true ? true : false;
        // 其他信息标题
        baoliu.OtherInfoTitle__c = deleteInfo.OtherInfoTitle__c != null ? deleteInfo.OtherInfoTitle__c : baoliuInfo.OtherInfoTitle__c;
        // 开标时间
        baoliu.OpenBidingTime__c = deleteInfo.OpenBidingTime__c != null ? deleteInfo.OpenBidingTime__c : baoliuInfo.OpenBidingTime__c;
        // 代理机构联系方式
        baoliu.AgentRelationWay__c = deleteInfo.AgentRelationWay__c != null ? deleteInfo.AgentRelationWay__c : baoliuInfo.AgentRelationWay__c;
        // 附件列表
        baoliu.InfoFile__c = deleteInfo.InfoFile__c != null ? deleteInfo.InfoFile__c : baoliuInfo.InfoFile__c;
        // 预计金额的计量单位
        baoliu.BudgetUnit__c = deleteInfo.BudgetUnit__c != null ? deleteInfo.BudgetUnit__c : baoliuInfo.BudgetUnit__c;
        // 中标金额的计量单位
        baoliu.WinnerUnit__c = deleteInfo.WinnerUnit__c != null ? deleteInfo.WinnerUnit__c : baoliuInfo.WinnerUnit__c;
        // 发布时间
        baoliu.InfoPublishTime__c = deleteInfo.InfoPublishTime__c != null ? deleteInfo.InfoPublishTime__c : baoliuInfo.InfoPublishTime__c;
        // 代理机构联系人
        baoliu.AgentRelationName__c = deleteInfo.AgentRelationName__c != null ? deleteInfo.AgentRelationName__c : baoliuInfo.AgentRelationName__c;
        //标书截止时间
        baoliu.BidingEndTime__c = deleteInfo.BidingEndTime__c != null ? deleteInfo.BidingEndTime__c : baoliuInfo.BidingEndTime__c;
        // 中标单位联系方式
        baoliu.ZhongRelationWay__c = deleteInfo.ZhongRelationWay__c != null ? deleteInfo.ZhongRelationWay__c : baoliuInfo.ZhongRelationWay__c;
        // 标书获取开始时间
        baoliu.BidingAcquireTime__c =deleteInfo.BidingAcquireTime__c != null ? deleteInfo.BidingAcquireTime__c : baoliuInfo.BidingAcquireTime__c;
        // 投标开始时间
        baoliu.TenderBeginTime__c = deleteInfo.TenderBeginTime__c != null ? deleteInfo.TenderBeginTime__c : baoliuInfo.TenderBeginTime__c;
        // 标的物总价之和
        baoliu.Sum__c = deleteInfo.Sum__c != null ? deleteInfo.Sum__c : baoliuInfo.Sum__c;
        // 标的物总价（计量单位）
        baoliu.SumUnit__c = deleteInfo.SumUnit__c != null ? deleteInfo.SumUnit__c : baoliuInfo.SumUnit__c;
        // 招标单位联系人
        baoliu.ZhaoRelationName__c = deleteInfo.ZhaoRelationName__c != null ? deleteInfo.ZhaoRelationName__c : baoliuInfo.ZhaoRelationName__c;
        // 中标单位联系人
        baoliu.ZhongRelationName__c = deleteInfo.ZhongRelationName__c != null ? deleteInfo.ZhongRelationName__c : baoliuInfo.ZhongRelationName__c;
        // 千里马获取时间
        baoliu.QLMGetDataTime__c = baoliuInfo.QLMGetDataTime__c != null && baoliuInfo.QLMGetDataTime__c > deleteInfo.QLMGetDataTime__c? baoliuInfo.QLMGetDataTime__c : deleteInfo.QLMGetDataTime__c;
        // 众成项目ID
        baoliu.ZCProjectId__c = deleteInfo.ZCProjectId__c != null ? deleteInfo.ZCProjectId__c : baoliuInfo.ZCProjectId__c;
        // 众成信息ID
        baoliu.ZCInfoId__c = deleteInfo.ZCInfoId__c != null ? deleteInfo.ZCInfoId__c : baoliuInfo.ZCInfoId__c;
        // 原文链接
        baoliu.original_url__c = deleteInfo.original_url__c != null ? deleteInfo.original_url__c : baoliuInfo.original_url__c;
        // 中标金额
        baoliu.WinnerAmount1__c = deleteInfo.WinnerAmount1__c != null ? deleteInfo.WinnerAmount1__c : baoliuInfo.WinnerAmount1__c;
        baoliu.WinnerAmount2__c = deleteInfo.WinnerAmount2__c != null ? deleteInfo.WinnerAmount2__c : baoliuInfo.WinnerAmount2__c;
        baoliu.WinnerAmount3__c = deleteInfo.WinnerAmount3__c != null ? deleteInfo.WinnerAmount3__c : baoliuInfo.WinnerAmount3__c;
        baoliu.WinnerAmount4__c = deleteInfo.WinnerAmount4__c != null ? deleteInfo.WinnerAmount4__c : baoliuInfo.WinnerAmount4__c;
        baoliu.WinnerAmount5__c = deleteInfo.WinnerAmount5__c != null ? deleteInfo.WinnerAmount5__c : baoliuInfo.WinnerAmount5__c;
        // 预测金额
        baoliu.BudgetAmount1__c = deleteInfo.BudgetAmount1__c != null ? deleteInfo.BudgetAmount1__c : baoliuInfo.BudgetAmount1__c;
        baoliu.BudgetAmount2__c = deleteInfo.BudgetAmount2__c != null ? deleteInfo.BudgetAmount2__c : baoliuInfo.BudgetAmount2__c;
        baoliu.BudgetAmount3__c = deleteInfo.BudgetAmount3__c != null ? deleteInfo.BudgetAmount3__c : baoliuInfo.BudgetAmount3__c;
        baoliu.BudgetAmount4__c = deleteInfo.BudgetAmount4__c != null ? deleteInfo.BudgetAmount4__c : baoliuInfo.BudgetAmount4__c;
        baoliu.BudgetAmount5__c = deleteInfo.BudgetAmount5__c != null ? deleteInfo.BudgetAmount5__c : baoliuInfo.BudgetAmount5__c;
        // 中标日（OBSAP）
        baoliu.Bid_Winning_Date__c = deleteInfo.Bid_Winning_Date__c != null ? deleteInfo.Bid_Winning_Date__c : baoliuInfo.Bid_Winning_Date__c;
        // 是否包含主机
        baoliu.host_if__c = deleteInfo.host_if__c != null ? deleteInfo.host_if__c : baoliuInfo.host_if__c;
        // 资金来源
        baoliu.funding_source__c = deleteInfo.funding_source__c != null ? deleteInfo.funding_source__c : baoliuInfo.funding_source__c;
        // 众成链接
        baoliu.url__c = deleteInfo.url__c != null ? deleteInfo.url__c : baoliuInfo.url__c;
        // 项目ID-大包标识
        baoliu.project_judge_id__c = deleteInfo.project_judge_id__c != null ? deleteInfo.project_judge_id__c : baoliuInfo.project_judge_id__c;
        // 众成更新日期
        baoliu.ZCUpdateDate__c = deleteInfo.ZCUpdateDate__c != null ? deleteInfo.ZCUpdateDate__c : baoliuInfo.ZCUpdateDate__c;
        // 众成数据更新
        baoliu.ZCDataUpdate__c = deleteInfo.ZCDataUpdate__c == true ? true : false;

    }
    public static void test(){
        Integer i = 0;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
    }
//    chenjingwu
}