/*
 * @Author: Thhto <EMAIL>
 * @Date: 2022-11-04 14:53:06
 * @LastEditors: Thhto <EMAIL>
 * @LastEditTime: 2023-03-02 17:48:08
 * @FilePath: \ceshihuanj\force-app\main\default\aura\AttachmentDisplay\AttachmentDisplayHelper.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
({
    // 获取数据
    getFrameNumManage : function(cmp) {
        debugger
        // this.showSpinner(cmp);
        var action = cmp.get("c.getFrameNumManage");
        // var DNId = cmp.get('v.DNId').toString();
        var DNId = this.getQueryVariable('DNId');
        action.setParams({
            'DNId' : DNId,
        });
        action.setCallback(this,function(response) {
            var state = response.getState();
            console.log(state);
            if (state === "SUCCESS") {
                var resultData = response.getReturnValue();
                cmp.set("v.currentData", resultData);
                this.hideSpinner(cmp);
            }else{
                cmp.set("v.errorMessage", '加载失败，请重新打开此页面！');
            }
        });
        $A.enqueueAction(action);
    },
    showSpinner: function(cmp) {
        debugger
        var spinner = cmp.find("mySpinner");
        $A.util.removeClass(spinner, "slds-hide");
    },
    hideSpinner: function(cmp) {
        var spinner = cmp.find("mySpinner");
        $A.util.addClass(spinner, "slds-hide");
    },
    getQueryVariable: function(variable) { //id字符串
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split("=");
            if (pair[0] == variable) {
                return pair[1];
            }
        }
        return (false);
    },
    //20230302 进口单证改造 fy start
    rebackDNListPage: function(variable) { //id字符串
        debugger
        var AgencyIDM = this.getQueryVariable('AgencyIDM');
        window.open('/eSignSystem/s/EsignDataEntry?AgencyID='+AgencyIDM+'&pinit=true', '_self');
    },
    //20230302 进口单证改造 fy end
})