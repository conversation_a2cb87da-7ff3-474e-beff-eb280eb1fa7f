<aura:component implements="flexipage:availableForAllPageTypes,forceCommunity:availableForAllPageTypes,lightning:isUrlAddressable" access="global" controller="AttachmentDisplayController">
	<aura:attribute name="DNId" type="String"
					description="用来从父对象接收传递过来的签收单id"
					default=""/>
	<aura:handler name="init" action="{!c.doInit}" value="{!this}"
					description="初始化方法"/>
	<aura:attribute name="currentData" type="Object[]" description="明细页当前显示的明细的信息" />
	<div>
		<lightning:spinner aura:id="mySpinner" alternativeText='请等待' size="large"  class="slds-spinner_container"/>
	</div>
	<div id="allDivs" >
		<div id="ins" style=" width:1000px;margin-right: 4px; ">
			<!-- 显示在明细页的当前明细的表格 -->
			<div id="inTables">
				<!-- <lightning:card> -->
				<aura:set attribute="title">
					进口单证明细
				</aura:set>
                <label style="float: left;font-size:20px">进口单证明细</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<lightning:button variant="neutral" label="返回" title="Cancel" onclick="{! c.rebackDNListPage }" />
				<table class="slds-table slds-table_cell-buffer slds-table_bordered slds-hint-parent slds-no-row-hover " style="border-collapse: collapse;border-color:#D7D7D7;" border="1px " >
					<thead>
						<tr class="slds-line-height_reset slds-text-title_caps">
							<!-- 第一列 产品明细 -->
							<!-- <th class="slds-is-resizable" tabindex="-1" data-label="产品明细" scope="row">
								<span class="slds-grid slds-grid_align-spread" style="width: 120px;text-align: left;display:block;">
									<div class="slds-truncate">
										<span title="产品明细" class="slds-truncate" style="padding-bottom: 3px;">产品明细</span>
									</div>
								</span>
							</th> -->
							<th class="slds-is-resizable" tabindex="-1" data-label="产品明细" scope="row" style="width: 146px;position:static">
								<span class="slds-grid slds-grid_align-spread" style="text-align: left;display:block;">
									<div class="slds-truncate">
										<span title="产品明细" class="slds-truncate" style="padding-bottom: 3px;">产品明细</span>
									</div>
								</span>
							</th>
							<!-- 第二列 机身编码 -->
							<th class="slds-is-resizable" tabindex="-1" data-label="机身编码" scope="row" style="width: 240px;height: 35px;">
								<span class="slds-grid slds-grid_align-spread" style="text-align: left;display:block;">
									<div class="slds-truncate">
										<span title="机身编码" class="slds-truncate" style="padding-bottom: 3px;">机身编码</span>
									</div>
								</span>
							</th>
							 <!-- 第三列 管理编码 -->
							 <th class="slds-is-resizable" tabindex="-1" data-label="管理编码" scope="row" style="width: 240px;height: 35px;">
								<span class="slds-grid slds-grid_align-spread" style="text-align: left;display:block;">
									<div class="slds-truncate">
										<span title="管理编码" class="slds-truncate" style="padding-bottom: 3px;">管理编码</span>
									</div>
								</span>
							</th>
							<!-- 第四列 预览链接 -->
							<!-- <th class="slds-is-resizable" tabindex="-1" data-label="预览链接" scope="row" style="width: 249px;height: 35px;">
								<span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
									<div class="slds-truncate">
										<span title="预览链接" class="slds-truncate" style="padding-bottom: 3px;">预览链接</span>
									</div>
								</span>
							</th> -->
							<!-- 第五列 报关单下载链接 -->
							<th class="slds-is-resizable" tabindex="-1" data-label="报关单" scope="row" style="width: 249px;">
								<span class="slds-grid slds-grid_align-spread" style="width: 240px;text-align: center;display:block;">
									<div class="slds-truncate">
										<span title="报关单下载链接" class="slds-truncate" style="padding-bottom: 3px;">报关单下载链接</span>
									</div>
								</span>
							</th>
							<!-- 第六列 商检证下载链接 -->
							<th class="slds-is-resizable" tabindex="-1" data-label="商检证" scope="row" style="width: 249px;">
								<span class="slds-grid slds-grid_align-spread" style="width: 240px;text-align: center;display:block;">
									<div class="slds-truncate">
										<span title="商检证下载链接" class="slds-truncate" style="padding-bottom: 3px;">商检证下载链接</span>
									</div>
								</span>
							</th>
						</tr>
					</thead>
					<aura:if isTrue="{!v.currentData.length > 0}">
						<tbody>
							<aura:iteration items="{!v.currentData}" var="curr" >
								<tr class="slds-hint-parent">
									<!-- 行项目 第一列  产品明细-->
									<!-- <th class="slds-is-resizable" tabindex="-1" data-label="DN单号" >
										<span class="slds-grid slds-grid_align-spread" style="width: 140px;height:18px;text-align: left;display:block;border: bottom 1px;">
											<lightning:formattedText value="{!curr.Name}" style="padding-bottom: 3px;"/>
										</span>
									</th> -->
									<th class="slds-is-resizable" tabindex="-1" data-label="产品明细" style="width: 146px;height:36px;position:static">
										<span class="slds-grid slds-grid_align-spread" style="text-align: left;display:block;">
											<div class="slds-truncate">
												<lightning:formattedText value="{!curr.ProductName}" style="padding-bottom: 3px;"/>
											</div>
										</span>
									</th>
									<!-- 行项目 第二列  机身编码-->
									<th class="slds-is-resizable" tabindex="-1" data-label="机身编码" style="width: 240px;height: 35px;">
										<span class="slds-grid slds-grid_align-spread" style="text-align: left;display:block;">
											<div class="slds-truncate">
												<lightning:formattedText value="{!curr.FrameNo}" style="padding-bottom: 3px;"/>
											</div>
										</span>
									</th>
									<!-- 行项目 第三列  管理编码-->
									<th class="slds-is-resizable" tabindex="-1" data-label="管理编码" style="width: 240px;height: 35px;">
										<span class="slds-grid slds-grid_align-spread" style="text-align: left;display:block;">
											<div class="slds-truncate">
												<lightning:formattedText value="{!curr.Name}" style="padding-bottom: 3px;"/>
											</div>
										</span>
									</th>
									<!-- 行项目 第四列  预览链接-->
									<!-- <aura:if isTrue="{!IF(curr.count ==null,false,true)}">
										<th class="slds-is-resizable" tabindex="-1" data-label="报关单" style="width: 249px;" rowspan="{!curr.count}">  
										</th>
									</aura:if> -->
									<aura:if isTrue="{!IF(curr.count ==null,false,true)}">		
										<aura:if isTrue="{!IF(AND(curr.code !='',curr.codeyu !='',curr.codeyu.length > 25,curr.code.length > 25),false,true)}">
											<!-- 只有预览 -->
											<aura:if isTrue="{!IF(AND(curr.codeyu !='',curr.codeyu.length > 25,curr.code ==''),true,false)}">
												<th class="slds-is-resizable" tabindex="-1" data-label="报关单" style="width: 249px;" rowspan="{!curr.count}">
													<span class="slds-grid slds-grid_align-spread" style="width:240px;text-align: center;display:block;">
														<div class="slds-truncate">
															<!-- update 20240202 by DTT-亚楠 start -->
															<!-- <a href="{!curr.iseSignSystem + '/apex/FilePreviewDownVF?type=preview&amp;key=' + curr.awsKey + '&amp;name=' + curr.fileName}" target="_blank">预览</a>   -->
															<a href="{!curr.iseSignSystem + curr.codeyu}" target="_blank">预览</a>
															<!-- update 20240202 by DTT-亚楠 end -->
														</div>
													</span>
												</th>
											</aura:if>
											<!-- 只有下载 -->
											<aura:if isTrue="{!IF(AND(curr.code !='',curr.code.length > 25,curr.codeyu ==''),true,false)}">
												<th class="slds-is-resizable" tabindex="-1" data-label="报关单" style="width: 249px;" rowspan="{!curr.count}">
													<span class="slds-grid slds-grid_align-spread" style="width:240px;text-align: center;display:block;">
														<div class="slds-truncate">
															<!-- update 20240202 by DTT-亚楠 start -->
															 <!-- <a href="{!curr.iseSignSystem + '/apex/FilePreviewDownVF?type=download&amp;key=' + curr.awsKey + '&amp;name=' + curr.fileName}" target="_blank">下载</a>  -->
															 <a href="{!curr.iseSignSystem + curr.code}" target="_blank">下载</a>
															 <!-- update 20240202 by DTT-亚楠 start -->
														</div>
													</span>
												</th>
											</aura:if>
											<!-- 其他情况显示自己  -->
											<aura:if isTrue="{!IF(AND(AND(curr.codeyu !='',curr.codeyu.length > 25,curr.code =='') == false , AND(curr.code !='',curr.code.length > 25,curr.codeyu =='')== false) ,true,false)}">
												<th class="slds-is-resizable" tabindex="-1" data-label="报关单" style="width: 249px;" rowspan="{!curr.count}">
													<span class="slds-grid slds-grid_align-spread" style="width:240px;text-align: center;display:block;">
														<div class="slds-truncate">
															<lightning:formattedText value="{!curr.code}" style="padding-bottom: 3px;"/>
														</div>
													</span>
												</th>
											</aura:if>
										</aura:if>
										<!-- 预览和下载都有 -->
										<aura:if isTrue="{!IF(AND(curr.code !='',curr.codeyu !='',curr.codeyu.length > 25,curr.code.length > 25),true,false)}">
											<th class="slds-is-resizable" tabindex="-1" data-label="报关单" style="width: 249px;" rowspan="{!curr.count}">
												<span class="slds-grid slds-grid_align-spread" style="width:240px;text-align: center;display:block;">
													<div class="slds-truncate">
														<!-- <a href="/eSignSystem/apex/FilePreviewDownVF?type=preview&amp;key={!curr.awsKey}&amp;name={!curr.fileName}" target="_blank">预览</a>  |  <a href="/eSignSystem/apex/FilePreviewDownVF?type=download&amp;key={!curr.awsKey}&amp;name={!curr.fileName}" target="_blank">下载</a> -->
														<!-- update 20240202 by DTT-亚楠 start -->
														<!-- <a href="{!curr.iseSignSystem + '/apex/FilePreviewDownVF?type=preview&amp;key=' + curr.awsKey + '&amp;name=' + curr.fileName}" target="_blank">预览</a>  |  <a href="{!curr.iseSignSystem + '/apex/FilePreviewDownVF?type=download&amp;key=' + curr.awsKey + '&amp;name=' + curr.fileName}" target="_blank">下载</a>  -->
														<a href="{!curr.iseSignSystem + curr.codeyu}" target="_blank">预览</a>  |  <a href="{!curr.iseSignSystem + curr.code}" target="_blank">下载</a>
														<!-- update 20240202 by DTT-亚楠 end -->
													</div>
												</span>
											</th>
										</aura:if>
									</aura:if>
									<!-- 行项目 第六列  商检证下载链接-->
									<aura:if isTrue="{!IF(curr.count_sj ==null,false,true)}">
										<aura:if isTrue="{!IF(AND(curr.sj_code !='',curr.sj_codeyu !='',curr.sj_code.length > 25,curr.sj_codeyu.length > 25),false,true)}">
											<!-- 只有预览 -->
											<aura:if isTrue="{!IF(AND(curr.sj_codeyu !='',curr.sj_codeyu.length > 25,curr.sj_code ==''),true,false)}">
												<th class="slds-is-resizable" tabindex="-1" data-label="商检证" style="width: 249px;" rowspan="{!curr.count_sj}">
													<span class="slds-grid slds-grid_align-spread" style="width:240px;text-align: center;display:block;">
														<div class="slds-truncate">
															<!-- <a href="/eSignSystem/apex/FilePreviewDownVF?type=preview&amp;key={!curr.sj_awsKey}&amp;name={!curr.sj_fileName}" target="_blank">预览</a> -->
															<!-- update 20240202 by DTT-亚楠 start -->
															<!-- <a href="{!curr.iseSignSystem + '/apex/FilePreviewDownVF?type=preview&amp;key=' + curr.sj_awsKey + '&amp;name=' + curr.sj_fileName}" target="_blank">预览</a> -->
															<a href="{!curr.iseSignSystem + curr.sj_codeyu}" target="_blank">预览</a>
															<!-- update 20240202 by DTT-亚楠 end -->

														</div>
													</span>
												</th>
											</aura:if>
											<!-- 只有下载 -->
											<aura:if isTrue="{!IF(AND(curr.sj_code !='',curr.sj_code.length > 25,curr.sj_codeyu ==''),true,false)}">
												<th class="slds-is-resizable" tabindex="-1" data-label="商检证" style="width: 249px;" rowspan="{!curr.count_sj}">
													<span class="slds-grid slds-grid_align-spread" style="width:240px;text-align: center;display:block;">
														<div class="slds-truncate">
															<!-- <a href="/eSignSystem/apex/FilePreviewDownVF?type=download&amp;key={!curr.sj_awsKey}&amp;name={!curr.sj_fileName}" target="_blank">下载</a> -->
															<!-- update 20240202 by DTT-亚楠 start -->
															<!-- <a href="{!curr.iseSignSystem + '/apex/FilePreviewDownVF?type=download&amp;key=' + curr.sj_awsKey + '&amp;name=' + curr.sj_fileName}" target="_blank">下载</a> -->
															<a href="{!curr.iseSignSystem + curr.sj_code}" target="_blank">下载</a>
															<!-- update 20240202 by DTT-亚楠 end -->
														</div>
													</span>
												</th>
											</aura:if>
											<!-- 其他情况显示自己 -->
											<aura:if isTrue="{!IF(AND(AND(curr.sj_codeyu !='',curr.sj_codeyu.length > 25,curr.sj_code =='')== false,AND(curr.sj_code !='',curr.sj_code.length > 25,curr.sj_codeyu =='') == false),true,false)}">
												<th class="slds-is-resizable" tabindex="-1" data-label="商检证" style="width: 249px;" rowspan="{!curr.count_sj}">
													<span class="slds-grid slds-grid_align-spread" style="width:240px;text-align: center;display:block;">
														<div class="slds-truncate">
															<lightning:formattedText value="{!curr.sj_code}" style="padding-bottom: 3px;"/>
														</div>
													</span>
												</th>
											</aura:if>
										</aura:if>
										<!-- 预览和下载都有 -->
										<aura:if isTrue="{!IF(AND(curr.sj_code !='',curr.sj_codeyu !='',curr.sj_code.length > 25,curr.sj_codeyu.length > 25),true,false)}">
											<th class="slds-is-resizable" tabindex="-1" data-label="商检证" style="width: 249px;" rowspan="{!curr.count_sj}">
												<span class="slds-grid slds-grid_align-spread" style="width:240px;text-align: center;display:block;">
													<div class="slds-truncate">
														<!-- <a href="/eSignSystem/apex/FilePreviewDownVF?type=preview&amp;key={!curr.sj_awsKey}&amp;name={!curr.sj_fileName}" target="_blank">预览</a>  |  <a href="/eSignSystem/apex/FilePreviewDownVF?type=download&amp;key={!curr.sj_awsKey}&amp;name={!curr.sj_fileName}" target="_blank">下载</a> -->
															<!-- update 20240202 by DTT-亚楠 start -->
														<!-- <a href="{!curr.iseSignSystem + '/apex/FilePreviewDownVF?type=preview&amp;key=' + curr.sj_awsKey + '&amp;name=' + curr.sj_fileName}" target="_blank">预览</a>  |  <a href="{!curr.iseSignSystem + '/apex/FilePreviewDownVF?type=download&amp;key=' + curr.sj_awsKey + '&amp;name=' + curr.sj_fileName}" target="_blank">下载</a> -->
														<a href="{!curr.iseSignSystem + curr.sj_codeyu}" target="_blank">预览</a>  |  <a href="{!curr.iseSignSystem + curr.sj_code}" target="_blank">下载</a>
														<!-- update 20240202 by DTT-亚楠 end -->
													</div>
												</span>
											</th>
										</aura:if>
									</aura:if>	
									<!-- </aura:if> -->
									<!-- 2023 0208 fy add   报关单商检证新需求，重新判断 start -->
									<!-- </aura:if>
									<aura:if isTrue="{!IF(curr.sj_code =='国内采购',true,false)}">
										<th class="slds-is-resizable" tabindex="-1" data-label="商检证" style="width: 249px;" >
											<span class="slds-grid slds-grid_align-spread" style="width: 240px;text-align: center;display:block;">
												<div class="slds-truncate">
													国内采购品
												</div>
											</span>
										</th>
									</aura:if> -->
									<!-- 2023 0208 fy add   报关单商检证新需求，重新判断 end -->
								</tr>
							</aura:iteration>
						</tbody>
					</aura:if>
				</table>
				<aura:if isTrue="{!empty(v.currentData)}">
				<div class="slds-align_absolute-center">
					No records found
				</div>
			</aura:if>
			</div>
		</div>
	</div>
</aura:component>