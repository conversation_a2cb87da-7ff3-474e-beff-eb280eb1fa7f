@isTest
private class TenderDeleteLinkBtachTest {

    @IsTest
	static void TestMethod01() {
        StaticParameter.EscapeTenderInformationUpdate =false;
        StaticParameter.EscapeOtherUpdateTenOwner = false;
        Oly_TriggerHandler.bypass('TenderInformationHandler');

        StaticParameter.EscapeAccountTrigger = true;

        //医院
        RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
        RecordType rectCo1 = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Department_Class_BF'];

        Account hp = new Account();
        hp.RecordTypeId = rectCo.Id;
        hp.Name = 'HPTest0618';
        insert hp;

        Account depart = new Account();
        depart.RecordTypeId = rectCo1.Id;
        depart.Name = 'DepartTest0618';
        depart.Department_Class_Label__c = '呼吸科';
        depart.ParentId = hp.Id;
        insert depart;

        //战略科室

        //询价
        Opportunity opp = new Opportunity();
        opp.Name = 'OppTest0618';
        opp.StageName = '引合';
        opp.ConfirmationofAward__c ='OLY中标';
        opp.CloseDate = date.newinstance(2025, 11, 30);
        opp.Hospital__c = hp.Id;
        opp.Department_Class__c = depart.Id;
        insert opp;

        //招标项目
        Tender_information__c ten = new Tender_information__c();
        ten.Name = 'TenTest0618';
        ten.AreaProvince__c = '北京';
        ten.InfoType__c= '3：结果';
        ten.subInfoType__c = '3-5：中标通知';
        ten.relativeTime__c =System.now();
        ten.IsReactionOpp__c = false;
        insert ten;

        //关联关系
        Tender_Opportunity_Link__c link = new Tender_Opportunity_Link__c();
        link.Opportunity__c = opp.Id;
        link.CurrencyIsoCode = 'CNY';
        link.IsRelated__c = false;
        link.Tender_information__c = ten.Id;
        insert link;

        List<String> linkstenList = new List<String>();
        linkstenList.add(ten.Id);

        List<Tender_Opportunity_Link__c> addlinksList = new List<Tender_Opportunity_Link__c>();
        addlinksList.add(link);

		Test.StartTest();
        // Database.executeBatch( new TenderDeleteLinkBtach(),100);
        Database.executeBatch( new TenderDeleteLinkBtach(linkstenList),100);
		// Database.executeBatch( new TenderDeleteLinkBtach(linkstenList,addLinksList),100);
		Test.stopTest();
        StaticParameter.EscapeOtherUpdateTenOwner = true;
        StaticParameter.EscapeTenderInformationUpdate =true;

        StaticParameter.EscapeAccountTrigger = false;
	}
}