public with sharing class Xin_SearchOpportunity {
    public List<Opportunity> results {get; private set;}
    public Boolean getIsOverLimit() {
        if (results != null && results.size() > 30) {
            return true;
        }
        return false;
    }

    public Xin_SearchOpportunity() {
        this.results = new List<Opportunity>();
    }

    public void search() {
        // 検索条件を用意
        String query = System.currentPageReference().getParameters().get('q');
        String visitorPlaceId = System.currentPageReference().getParameters().get('r');
        // 全角：：をreplace
        query = query.replaceAll('：：', '::');
        if(query == null || query == '' || query.length() < 2) {
            return;
        }
        this.search(query, visitorPlaceId);
    }
    public void search(String query, String visitorPlaceId) {
        List<String> qwords = new List<String>();
        List<String> qwordstmp = query.split(' ');
        String nameCondition = '';
        for (String qword : qwordstmp) {
            if (String.isBlank(qword) == false) {
                qwords.add('%' + String.escapeSingleQuotes(qword.replaceAll('%', '')) + '%');
                nameCondition += ' Name like \'' + qwords[qwords.size() - 1] + '\' and';
            }
        }
        
        //ControllerUtilで検索を行うよう変更
        /*Account a = null;
        try {

            a = [select Id, Hospital__c, Agent_Ref__c, Parent.Parent.RecordType.DeveloperName, Parent.RecordType.DeveloperName, RecordType.DeveloperName from Account where Id=:visitorPlaceId];
        }
        catch ( System.QueryException qe) {
            this.results = new List<Opportunity>();
            return;
        }
        
//        String nameCondition = '%' + String.escapeSingleQuotes(query.replaceAll('%', '')) + '%';
        system.debug('cond=' + nameCondition);
        
        // 検索
        String queryString = '';
        // 病院
        if (a.Parent.Parent.RecordType.DeveloperName == 'HP') {
            queryString = 'Select Id, Opportunity_No__c, Name, toLabel(StageName), Department_Name__c, Close_Forecasted_Date__c, Competitor__c, Wholesale_Price__c from Opportunity where' + nameCondition + ' Account.Hospital__c=\'' + a.Hospital__c + '\' and StageName IN (\'引合\',\'注残\',\'出荷\') and RecordTypeId = \'01210000000QekK\' order by Name, Opportunity_No__c, Department_Name__c limit 31';
        }
        // 販売店
        else if (a.RecordType.DeveloperName == 'Agency') {
            queryString = 'Select Id, Opportunity_No__c, Name, toLabel(StageName), Department_Name__c, Close_Forecasted_Date__c, Competitor__c, Wholesale_Price__c from Opportunity where' + nameCondition + ' (Agency1__c=\'' + a.Id + '\' or Agency2__c=\'' + a.Id + '\') and StageName IN (\'引合\',\'注残\',\'出荷\') and RecordTypeId = \'01210000000QekK\' order by Name, Opportunity_No__c, Department_Name__c limit 31';
        }
        else {
            return;
        }*/

        String queryString = '';
        try{
            queryString = ControllerUtil.getOppSearchSQOL(visitorPlaceId, nameCondition);
        }
        catch ( System.QueryException qe) {
            this.results = new List<Opportunity>();
            return;
        }

        system.debug('queryString=' + queryString);
        if(queryString == null || queryString == ''){
            return;
        }
        this.results = (List<Opportunity>) Database.query(queryString);
    }
}