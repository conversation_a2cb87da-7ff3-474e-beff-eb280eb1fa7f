<aura:component controller="WeeklyReportCmp" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,force:lightningQuickAction,forceCommunity:availableForAllPageTypes" access="global" >
    <ltng:require styles="{!$Resource.multilineToastCSS}" /> 
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
    <!-- DTT-亚楠 20241025 DB202409327066 DAMS系统界面优化需求 Start -->
    <!-- <aura:handler event="c:strike_evt" action="{!c.createAopp}" name="strike_evt_addNewRecord"/> -->
    <aura:attribute name="recordTypeId" type="String" default = ""/>
    <aura:handler event="c:strike_evt" action="{!c.showNewAgency}" name="strike_evt_addNewRecord"/>
    <aura:handler name="NewAgencyOpportunityEvent" event="c:NewAgencyOpportunityEvent" action="{!c.closeAgencyOpportunity}"/>
    <aura:attribute name="showNewAgencyOpportunity" type="Boolean" default="false"/>
    <aura:attribute name="agencyHospitalLinkId" type="String" default = ""/>
    <aura:attribute name="departmentName" type="String" default = ""/>
    <!-- DTT-亚楠 20241025 DB202409327066 DAMS系统界面优化需求 End -->
    <aura:attribute name="data" type="Agency_Report__c" default="{sobjectType:'Agency_Report__c'}"/>
    <aura:attribute name="oppdata" type="Agency_Opportunity__c" default="{sobjectType:'Agency_Opportunity__c'}"/>
    <aura:attribute name="acondata" type="Agency_Contact__c" default="{sobjectType:'Agency_Contact__c'}"/>
    <!-- <aura:attribute name="oppdata_old" type="Agency_Opportunity__c" default="{sobjectType:'Agency_Opportunity__c'}"/> -->
    <aura:attribute name="Close_Forecasted_Date__c_o" type="Date"/>
    <aura:attribute name="Amount__c_o" type="String"/>
    <aura:attribute name="OCMSale_Price__c_o" type="String"/>
    <aura:attribute name="StageName__c_o" type="String"/>
    <aura:attribute name="fieldsmap" type="Map"/>
    <aura:attribute name="alldata" type="List"/>
    <aura:attribute name="allselectlist" type="Map"/>
    <aura:attribute name="doclist" type="List"/>
    <aura:attribute name="docmap" type="Map"/>
    <aura:attribute name="login" type="Boolean" default="false"/>
    <aura:attribute name="loginEdit" type="Boolean" default="false" />
    <aura:attribute name="loadOpp" type="Boolean" default="false"/>
    <aura:attribute name="reportPageData" type="Map"/>
    <aura:attribute name="selectbody" type="String"/>
    <aura:attribute name="report_date_list" type="Map"/>
    <aura:attribute name="selected_date" type="String"/>
    <aura:attribute name="selected_agency_person" type="String"/>
    <aura:attribute name="agency_report_header" type="String"/>
    <aura:attribute name="agency_report_header_id" type="String"/>
    <aura:attribute name="result" type="String" default=""/>
    <aura:attribute name="hospital" type="string" default=""/>
    <aura:attribute name="hospitalId" type="string" default=""/>
    <aura:attribute name="hospitalLinkId" type="string" default=""/>
    <aura:handler name="change" value="{!v.hospital}" action="{!c.hosChange}"/>
    <aura:attribute name="hospitalList" type="Map" />
    <aura:attribute name="doctor_list" type="Map"/>
    <aura:attribute name="doctor_title" type="String"/>
    <aura:attribute name="opportunity_list" type="Map"/>
    <aura:handler name="change" value="{!v.data.Opportunity__c}" action="{!c.opportunityChange}"/>
    <aura:attribute name="edit_copy_select_report_id" type="String"/>
    <aura:attribute name="create_agency_report_id" type="String"/>
    <aura:attribute name="reports" type="List"/>
    <aura:attribute name="reports_now" type="List"/>
    <aura:attribute name="reports_now_count" type="Integer"/>
    <aura:attribute name="mode" type="String"/>
    <aura:attribute name="select_report_data" type="Map"/>
    <aura:attribute name="select_report_data_radio" type="Map"/>
    <aura:attribute name="default_select_doctor_id" type="String"/>
    <aura:attribute name="default_select_opportunity_id" type="String"/>
    <aura:attribute name="report_count" type="Integer"/>
    <aura:attribute name="dialog_type" type="String"/>
    <aura:attribute name="truthy" type="Boolean" default="false"/>
    <aura:attribute name="list_message" type="String" default="搜索" />
    <aura:attribute name="confirm_status" type="Integer" default="0" />
    <aura:attribute name="modal_confirm_title" type="String" />
    <aura:attribute name="modal_confirm_text" type="String" />
    <aura:attribute name="opportunity_cfilter" type="String" />
    <!-- add by Link : 2024-1-2 PIPL 客户人员选取 start-->
    <aura:attribute name="contact_cfilter" type="String" />
    <aura:attribute name="contact_disable" type="Boolean" />
    <aura:attribute name="contactValue" type="String" />
    <!-- DTT-亚楠 20240410 优化客户人员查询速度 Start -->
    <aura:attribute name="isNew" type="Boolean" />
    <!-- DTT-亚楠 20240410 优化客户人员查询速度 End -->
    <aura:attribute name="isFirstOpen" type="Boolean" />
    <aura:attribute name="newContactFlag" type="Integer" />
    <aura:handler name="change" value="{!v.contactValue}" action="{!c.contactValueChange}"/>
    <aura:attribute name="doctor_Name" type="String"/>
    <!-- add by Link : 2024-1-2 PIPL 客户人员选取 end-->
    <!-- <aura:handler name="change" value="{!v.data.Product_Category1__c}" action="{!c.productcategoryChange1}"/>
    <aura:handler name="change" value="{!v.data.Product_Category2__c}" action="{!c.productcategoryChange2}"/>
    <aura:handler name="change" value="{!v.data.Product_Category3__c}" action="{!c.productcategoryChange3}"/> -->
    <!-- <aura:handler name="change" value="{!v.oppdata.StageName__c}" action="{!c.stageNameChange}"/> -->
    <aura:attribute name="update_select_report_data_id" type="String" default=""/>

    <!-- 批量添加周报 start-->
    <aura:attribute name="reports_date" type="List" />
    <!-- <aura:attribute name="reports_date1" type="Date" />
    <aura:attribute name="reports_date2" type="Date" /> -->
    <aura:attribute name="TableContent" type="String"  description=" Show the Result class"/>
    <aura:attribute name="TableContent2" type="String"  description=" Show the Result class"/>
    <aura:attribute name="TargetFileName" type="String"  description="Name of the file"/>
    <aura:attribute name="tableheaders" type="Object[]" />
    <aura:attribute name="fileContentData" type="String"/>
    <aura:attribute name="filename" type="String"/>
    <!-- It will display 100 records . Change for your requirement-->
    <aura:attribute name="NumOfRecords" type="Integer" default="1000"/> 
    <aura:attribute name="showMain" type="Boolean" default="true"/>
    <!-- PIPL update Yin Mingjie 21/02/2022 start -->
    <!-- deloitte-zhj 20231116 PIPL还原 -->
    <!-- <aura:attribute name="AWStoken" type="String"/>  
    <aura:attribute name="AWSsearch" type="String"/>
    <aura:attribute name="AWSinsert" type="String"/>
    <aura:attribute name="AWStransactionURL" type="String"/>
    <aura:attribute name="AWSDoctor2Map" type="String"/>
    <aura:attribute name="awsurl" type="Map"/>
    <aura:attribute name="contactawsurl" type="Map"/>
    <aura:attribute name="allselectlistAgencyPerson" type="Map"/> --> 
    <!-- PIPL update Yin Mingjie 21/02/2022 end -->

    <!-- start DTT-zhj 增加错误信息table 2023-05-16-->
    <aura:attribute name="errorData" type="Object" />
    <aura:attribute name="errorColumns" type="List" />
    <!-- show Error information -->
    <aura:attribute name="showErrorInfo" type="Boolean" default="false" />
    <!-- end DTT-zhj 增加错误信息table 2023-05-16-->

    <!-- 批量添加周报 end-->
    <ltng:require scripts="{! $Resource.AWSService+'/AWSService.js' }" />
    <ltng:require scripts="{! $Resource.jquery183minjs }" />
    
    <!--ロード中...-->
    <aura:renderIf isTrue="{!v.login}">
        <aura:renderIf isTrue="{!!v.loginEdit}">
            <!-- add by Deloitte-Link 2023-6-19 -->
            <div class="weeklyReportSpinner">
                <div class="slds-spinner_container">
                    <div class="slds-spinner--brand slds-spinner slds-spinner--medium" role="alert">
                        <span id="aa" class="slds-assistive-text">Loading</span>
                        <div class="slds-spinner__dot-a"></div>
                        <div class="slds-spinner__dot-b"></div>
                    </div>
                </div>
            </div>
        </aura:renderIf>
    </aura:renderIf>
    
    <div aura:id="report" id="report" class="contents_wrapper">
        <div class="slds-grid slds-wrap slds-grid--pull-padded">
            <div class="tr mt5 slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--1-of-12 slds-large-size--1-of-12">
                周
            </div>
            <div class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--3-of-12 slds-large-size--3-of-12">
                <ui:inputSelect aura:id="select_date" class="slds-select" change="{!c.select_date_change}"/>
            </div>
            
            <!-- 代理店担当者 -->
            <div class="tr mt5 slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--1-of-12 slds-large-size--1-of-12">
                {!v.fieldsmap.Person_In_Charge2__c}
            </div>
            <div class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--3-of-12 slds-large-size--3-of-12">
                <ui:inputSelect aura:id="select_agency_person" class="slds-select agency_person_select" change="{!c.select_agency_change}"/>
            </div>
            <div class="tr slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--1-of-12 slds-large-size--1-of-12">
                <ui:button aura:id="new_button" label="新建" press="{!c.new_report}" disabled="true"/>
            </div>
            <div class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--3-of-12 slds-large-size--3-of-12">
                <ui:button aura:id="copy_button" label="复制" press="{!c.copy_button}" disabled="true"/>
                <!-- <ui:button aura:id="import" label="导入" press="{!c.import}" /> -->
                <ui:button aura:id="import" label="导入" press="{!c.import}" />
                
                <!-- <input type="file" class="file" aura:id="file" onchange="{!c.CreateRecord}"  style="position:absolute;filter:alpha(opacity=0);" size="1" hidden="true" />  -->
                <!-- <ui:button aura:id="import" label="导出" press="{!c.export}"/> -->
                <ui:button aura:id="export" label="导出" press="{!c.export_condition}"/>
            </div>
            
        </div>
    </div>
    
    <div aura:id="report_list" class="report_list_area" >
        <div class="slds-table--edit_container slds-is-relative">
            <table class="slds-table slds-table--edit slds-table--bordered slds-table--fixed-layout slds-no-cell-focus" role="grid" style="width:66.75rem;">
                <thead>
                    <tr class="slds-line-height--reset">
                        <th class="table_header slds-text-title--caps" style="width: 250px;font-size: 13px;">
                            <span class="slds-truncate" title="Name">周报编号</span>
                        </th>
                        <th class="table_header slds-text-title--caps" style="width: 100px;font-size: 13px;">
                            <span class="slds-truncate" title="Name">{!v.fieldsmap.Person_In_Charge2__c}</span>
                        </th>
                        <th class="table_header slds-text-title--caps" style="width: 250px;font-size: 13px;">
                            <span class="slds-truncate" title="Name">医院</span>
                        </th>
                        <th class="table_header slds-text-title--caps" style="width: 100px;font-size: 13px;">
                            <span class="slds-truncate" title="Name">{!v.fieldsmap.Department_Cateogy__c}</span>
                        </th>
                        <th class="table_header slds-text-title--caps" style="width: 100px;font-size: 13px;">
                            <!-- PIPL update Yin Mingjie 21/02/2022 start
                            <span class="slds-truncate" title="Name">{!v.fieldsmap.doctor2__r}</span>
                            PIPL update Yin Mingjie 21/02/2022 end-->
                            <span class="slds-truncate" title="Name" style="width: 100px;font-size: 13px;">{!v.fieldsmap.Agency_Contact__c}</span>
                            
                        </th>
                        <th class="table_header slds-text-title--caps" style="width: 100px;font-size: 13px;">
                            <span class="slds-truncate" title="Name">{!v.fieldsmap.visitor_title__c}</span>
                        </th>
                        <th class="table_header slds-text-title--caps" style="width: 100px;font-size: 13px;">
                            <span class="slds-truncate" title="Name">{!v.fieldsmap.Purpose_Type__c}</span>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <aura:iteration items="{!v.reports}" var="item" indexVar="index">
                        <tr class="slds-hint-parent">
                            <td role="gridcell" class="slds-cell-edit">
                                <ui:inputRadio class="report_radio" name="report_radio" label="{!item.Name}" change="{!c.change_report_radio}"/>
                                <lightning:buttonIcon class="edit_button" iconName="utility:edit" size="small" alternativeText="edit" value="{!index}" onclick="{!c.edit_button}"/>
                            </td>
                            <td role="gridcell" class="slds-cell-edit">
                                <span class="slds-grid slds-grid--align-spread">
                                    <span class="slds-truncate" title="{!item.Person_In_Charge2__r.Name}">
                                        {!item.Person_In_Charge2__r.Name}
                                        <!-- <span class="decrypt">{!item.Person_In_Charge2__r.awsdata.lastName}</span> -->
                                    </span>
                                </span>
                            </td>
                            <th scope="row" tabindex="0" class="slds-cell-edit">
                                <span class="slds-grid slds-grid--align-spread">
                                    {!item.Agency_Hospital__r.Name}
                                </span>
                            </th>
                            <td role="gridcell" class="slds-cell-edit">
                                <span class="slds-grid slds-grid--align-spread">
                                    <span class="slds-truncate" title="{!item.Department_Cateogy__c}">{!item.Department_Cateogy__c}</span>
                                </span>
                            </td>
                            <td role="gridcell" class="slds-cell-edit">
                                <span class="slds-grid slds-grid--align-spread">
                                    <span class="slds-truncate" title="{!item.doctor2__r.Name}">
                                        <span class="encrypt">{!item.doctor2__r.Name}</span>
                                        <!-- <span class="decrypt">{!item.doctor2__r.awsdata.name}</span> -->
                                        <span class="decrypt">{!item.doctor2__r.Name}</span>
                                    </span>
                                </span>
                            </td>
                            <td role="gridcell" class="slds-cell-edit">
                                <span class="slds-grid slds-grid--align-spread">
                                    <span class="slds-truncate" title="{!item.visitor_title__c}">
                                        <span class="encrypt">{!item.visitor_title__c}</span>
                                        <span class="decrypt">{!item.visitor_title__c}</span>
                                        <!-- <span class="decrypt">{!item.doctor2__r.awsdata.doctorDivision1}</span> -->
                                    </span>
                                </span>
                            </td>
                            <td role="gridcell" class="slds-cell-edit">
                                <span class="slds-grid slds-grid--align-spread">
                                    <span class="slds-truncate" title="{!item.Purpose_Type__c}">{!item.Purpose_Type__c}</span>
                                </span>
                            </td>
                        </tr>
                    </aura:iteration>
                </tbody>
            </table>
        </div>
        <aura:renderIf isTrue="{!v.report_count > 0}">
            <aura:set attribute="else">
                <div class="no_data_area">
                    {!v.list_message}
                </div>
            </aura:set>
        </aura:renderIf>
    </div>

    <!--    <ui:button aura:id="test_button" label="test" class="fade animation_on" press="{!c.test}"/>-->
    <!-- 批量添加周报 start-->
    <div aura:id="modal_import" role="dialog" tabindex="-1" class="disp_none slds-modal slds-fade-in-open slds-modal--large" aria-labelledby="headerTarget">
        <div class="slds-modal__container ">
            <aura:renderIf isTrue="{!v.loginEdit}">
                <lightning:spinner alternativeText="Loading" size="medium" style="width: 100%; height: 80%;top:50px;" />
            </aura:renderIf>
            <div class="slds-modal__header ">
                <h4 id="headerTarget" class="slds-float--left"></h4>
                <h2 id="headerTarget" class="slds-text-heading--medium">周报批量导入</h2>
                <ui:button aura:id="close_button" label="关闭" class="close_button slds-button slds-button--neutral slds-order--1" press="{!c.close_import}"/>
            </div>
            <div class="slds-modal__content slds-scrollable slds-grow slds-p-around--medium " >
                <aura:if isTrue="{!v.showMain}">
                    <div>
                        <!-- <input type="file" class="file" aura:id="file" onchange="{!c.CreateRecord}" value="导入" /> -->
                        <lightning:input type="file" class="file" uara:id="file" onchange="{!c.CreateRecord}" />
                        <!-- <ui:button label="Create Accounts" press="{!c.CreateRecord}"/> -->
                    </div>
                    <aura:set attribute="else">
                        <!-- <ui:outputRichText class="uiOutputRichText slds-m-around-large" value="{!v.TargetFileName}"/>  -->
                        <ui:outputRichText class="uiOutputRichText slds-m--around-large" value="{!v.TableContent}"/>
                        <div class="slds-p-around--large slds-align--absolute-center">
                            <lightning:button label="保存" variant="brand" onclick="{!c.processFileContent}"
                                              />
                            <lightning:button label="取消" variant="brand" 
                                              onclick="{!c.cancel}" /> 
                        </div>
                        <!-- start DTT-zhj 增加错误信息table 2023-05-16-->
                        <aura:if isTrue="{!v.showErrorInfo}">
                            <div class="slds-p-around--large slds-align--absolute-center">
                                <lightning:button label="导出错误信息" variant="brand" onclick="{!c.exportErrorInfo}" />
                            </div>
                            <div class="slds-p-around--large slds-align--absolute-center">
                                <div style="height: 300px;overflow-x:scroll">
                                    <lightning:datatable keyField="id" data="{! v.errorData }"
                                        columns="{! v.errorColumns }" hideCheckboxColumn="true" />
                                </div>
                            </div>
                        </aura:if>
                        <!-- start DTT-zhj 增加错误信息table 2023-05-16-->
                    </aura:set>
                </aura:if>
            </div>
        </div>
    </div>
    <div aura:id="modal_importbg" class="disp_none slds-backdrop slds-backdrop--open"></div>
    <!-- 批量添加周报 end-->

    <!-- 批量导出周报 start-->
    <div aura:id="modal_export" role="dialog" tabindex="-1" class="disp_none slds-modal slds-fade-in-open slds-modal--large" >
        <div class="slds-modal__container">
            <div class="slds-modal__header">
                <h4 id="headerTarget" class="slds-float--left"></h4>
                <h2 id="headerTarget" class="slds-text-heading--medium">周报批量导出</h2>
                <ui:button aura:id="close_button" label="关闭" class="close_button slds-button slds-button--neutral slds-order--1" press="{!c.close_export}"/>
            </div>
            <div class="slds-modal__content slds-scrollable slds-grow slds-p-around--medium">
                <aura:if isTrue="{!v.showMain}">
                    <div class="slds-grid slds-wrap slds-grid--pull-padded" >
                        <div class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--1-of-5 slds-large-size--1-of-4">
                        </div>
                        <div class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--1-of-5 slds-large-size--1-of-4">
                            活动日开始日期
                            
                            <lightning:input type="Date" class="slds-input slds-input_bare " aura:id="input-report-date1" />
                        </div>
                        <div class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--1-of-5 slds-large-size--1-of-4">
                            活动日结束日期
                            <lightning:input type="Date" class="slds-input slds-input_bare " aura:id="input-report-date2" />
                        </div>
                        <div class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--1-of-5 slds-large-size--1-of-4">
                        </div>

                        <div class="slds-p-around--large slds-align--absolute-center">
                            <lightning:button label="确认" variant="brand" onclick="{!c.select_repores_date}"
                                                  />
                            <lightning:button label="取消" variant="brand" 
                                                  onclick="{!c.close_export}" /> 
                        </div>
                    </div>
                    <!-- <div>
                        请输入要导出日报的报活动日的开始和结束日期
                        <force:inputField  aura:id="input-report-date1"/>到
                        <force:inputField  aura:id="input-report-date2"/>
                        <lightning:button label="确认" variant="brand" onclick="{!c.select_repores_date}"
                                              />
                        <lightning:button label="取消" variant="brand" 
                                              onclick="{!c.close_export}" /> 
                    </div> -->
                    <aura:set attribute="else">
                        <ui:outputRichText class="uiOutputRichText slds-m--around-large" value="{!v.TableContent2}"/>
                        <div class="slds-p-around--large slds-align--absolute-center">
                            <lightning:button label="确认" variant="brand" onclick="{!c.exportDate}"
                                              />
                            <lightning:button label="取消" variant="brand" 
                                              onclick="{!c.close_export}" /> 
                        </div>
                        
                    </aura:set>
                </aura:if>
            </div>
        </div>
    </div>
    <div aura:id="modal_exportbg" class="disp_none slds-backdrop slds-backdrop--open"></div>
    <!-- 批量导出周报 end-->

    <div aura:id="modal_window" role="dialog" tabindex="-1" class="disp_none slds-modal slds-fade-in-open slds-modal--large" aria-labelledby="headerTarget">
        <div class="slds-modal__container" >
            <aura:renderIf isTrue="{!v.loginEdit}">
                <lightning:spinner alternativeText="Loading" size="medium" style="width: 100%; height: 80%;top:50px;" />
            </aura:renderIf>
            <div class="slds-modal__header">
                <h4 id="headerTarget" class="slds-float--left"></h4>
                <h2 id="headerTarget" class="slds-text-heading--medium">周报({!v.selected_date}&nbsp;{!v.selected_agency_person})</h2>
                <ui:button aura:id="close_button" label="关闭" class="close_button slds-button slds-button--neutral slds-order--1" press="{!c.toggle_report}"/>
            </div>
            <div class="slds-modal__content slds-grow slds-p-around--medium">
                <div class="slds-box slds-theme--shade">
                    <div class="slds-grid slds-wrap slds-grid--pull-padded" >
                        <div class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--1-of-5 slds-large-size--1-of-6 marginTop" >
                            活动日
                            <!-- <ui:inputSelect aura:id="select_opportunity" class="slds-select"/> -->
                            <!-- <force:inputField value="{!v.data.Report_Date__c}" aura:id="input-report-date"/> -->
                            <lightning:input type="date" value="{!v.data.Report_Date__c}" aura:id="input-report-date"
                                variant="label-hidden" />
                        </div>
                        <!-- 医院 -->
                        <div aura:id="hospital_list" class="slds-form-element slds-lookup slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--1-of-5 slds-large-size--1-of-5 marginTop" data-select="single">
                            <!-- <ui:inputText aura:id="hospital_input_text" label="医院" class="field" value="{!v.hospital}" updateOn="keyup"/> -->
                            医院
                            <lightning:input aura:id="hospital_input_text" class="field" value="{!v.hospital}" variant="label-hidden" updateOn="keyup"/>
                            <div class="slds-lookup__menu" id="lookup-66">
                                <ul class="slds-lookup__list" role="listbox">
                                    <aura:iteration var="hospital" items="{!v.hospitalList}">
                                        <li role="presentation" onclick="{!c.selectHos}" data-accName="{!hospital.Hospital_Name_readonly__c}" data-accId="{!hospital.Hospital__c}">
                                            <span class="slds-lookup__item-action slds-media" id="lookup-option-498" role="option">
                                                <div class="slds-media__body">
                                                    <div class="slds-lookup__result-text">{!hospital.Hospital_Name_readonly__c}</div>
                                                </div>
                                            </span>
                                        </li>
                                    </aura:iteration>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- 科室分类 -->
                        <div class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--1-of-5 slds-large-size--1-of-8 marginTop">
                            {!v.fieldsmap.Department_Cateogy__c}
                            <ui:inputSelect aura:id="select_department" class="slds-select" change="{!c.select_department}"/>
                        </div>
                        
                        <!-- 先生 -->
                        <div class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--1-of-5 slds-large-size--1-of-8 marginTop">
                            {!v.fieldsmap.doctor2__c}
                            <!--  add by Link : 2024-1-2 PIPL 客户人员选取 -->
                            <ui:inputSelect aura:id="select_doctor" class="slds-select hideInput" change="{!c.doctor_change}"/>
                            <!-- <ui:inputSelect aura:id="select_doctor" change="{!c.doctor_change}"/> -->
                            <c:strike_lookup label=""
                                    object="Agency_Contact__c"
                                    searchField="Name"
                                    subtitleField="Doctor_Division1__c,Type__c,Agency_Hospital__c"
                                    order="Name"
                                    loadingMessage="Loading..."
                                    errorMessage="Invalid input"
                                    allowNewRecords = "false"
                                    overrideNewEvent = "false"
                                    showRecentRecords ="false"
                                    value="{!v.contactValue}"
                                    valueLabel="{!v.doctor_Name}"
                                    valueSublabel="{!v.doctor_title}"
                                    filter="{!v.contact_cfilter}"
                                    subTitleFormat="{0}"
                                    disabled="{!v.contact_disable}"
                                    needDefaultValue = "true"
                                    isFirst = "{!v.isFirstOpen}"
                                    showSearchIcon = "false"
                                    isNew = "{!v.isNew}"
                                    newContactFlag = "{!v.newContactFlag}"/>
                        </div>
                        
                        <div class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--1-of-5 slds-large-size--1-of-12 marginTop">
                            <br/>
                            <ui:button label="新建" class="slds-button" press="{!c.createCon}" disabled="true" aura:id="new_con"/>
                        </div>
                        
                        <!-- 职位 -->
                        <div class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--1-of-5 slds-large-size--1-of-8 marginTop">
                            {!v.fieldsmap.visitor_title__c}
                            <div>{!v.doctor_title}</div>
                        </div>       
                        <!-- 活动区分 -->
                        <div class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--1-of-5 slds-large-size--1-of-6 marginTop">
                            {!v.fieldsmap.Purpose_Type__c}
                            <ui:inputSelect aura:id="select_purpose_type" class="slds-select" change="{!c.select_purpose_type}"/>
                        </div>
                        <!-- 询价 -->
                        <div aura:id="input-opportunity-opp" class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--4-of-12 slds-large-size--4-of-12 marginTop">
                            {!v.fieldsmap.Opportunity__c}
                            <!-- <ui:inputSelect aura:id="select_opportunity" class="slds-select"/> -->
                            <!-- <force:inputField value="{!v.data.Opportunity__c}" aura:id="input-opportunity"/> -->
                            <c:strike_lookup label=""
                                    object="Agency_Opportunity__c"
                                    searchField="Name"
                                    placeholder="搜索经销商询价..."
                                    iconName="standard:opportunity"
                                    subtitleField="Department_Cateogy__c,Agency_Opportunity_No__c,Change_To_Opportunity__r.Name"
                                    order="StageName__c"
                                    limit="100"
                                    loadingMessage="Loading..."
                                    errorMessage="Invalid input"
                                    allowNewRecords = "true"
                                    overrideNewEvent = "true"
                                    showRecentRecords ="true"
                                    value="{!v.data.Opportunity__c}"
                                    filter="{!v.opportunity_cfilter}"
                                    subTitleFormat="{0}+{1}+{2}"/>
                        </div>
                        <div aura:id="input-opportunity-blank" class="lds-p-horizontal--small slds-size--1-of-1 slds-medium-size--8-of-12 slds-large-size--8-of-12 marginTop">
                        </div>
                        <div aura:id="input-opportunity-stage" class="disp_none slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--2-of-12 slds-large-size--2-of-12 marginTop">
                            {!v.fieldsmap.StageName__c}
                            <!-- <ui:inputSelect aura:id="select_opportunity" class="slds-select"/> -->
                            <ui:inputSelect aura:id="select_stageName" class="slds-select"/>
                            <!-- <force:inputField value="{!v.oppdata.StageName__c}" aura:id="input-oppstage" /> -->
                        </div>
                        <div aura:id="input-opportunity-amount1" class="disp_none slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--2-of-12 slds-large-size--2-of-12 marginTop">
                            {!v.fieldsmap.Amount__c}
                            <!-- <ui:inputSelect aura:id="select_opportunity" class="slds-select"/> -->
                            <!-- DTT-亚楠 20241025 DB202409327066 DAMS系统界面优化需求 Start -->
                            <!-- <force:inputField value="{!v.oppdata.Amount__c}" class=""/> -->
                            <lightning:input variant="label-hidden" value="{!v.oppdata.Amount__c}" class="" />
                            <!-- DTT-亚楠 20241025 DB202409327066 DAMS系统界面优化需求 End -->
                        </div>
                        <div aura:id="input-opportunity-amount2" class="disp_none slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--2-of-12 slds-large-size--2-of-12 marginTop">
                            {!v.fieldsmap.OCMSale_Price__c}
                            <!-- <ui:inputSelect aura:id="select_opportunity" class="slds-select"/> -->
                            <!-- DTT-亚楠 20241016 DAMS周报页面问题修复 Start  -->
                            <!-- <force:inputField value="{!v.oppdata.OCMSale_Price__c}" class=""/> -->
                            <lightning:input variant="label-hidden" value="{!v.oppdata.OCMSale_Price__c}" class="" />
                            <!-- DTT-亚楠 20241016 DAMS周报页面问题修复 End  -->
                        </div>
                        <div aura:id="input-opportunity-date" class="disp_none slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--2-of-12 slds-large-size--2-of-12 marginTop">
                            {!v.fieldsmap.Close_Forecasted_Date__c}
                            <!-- <ui:inputSelect aura:id="select_opportunity" class="slds-select"/> -->
                            <!-- DTT-亚楠 20241025 DB202409327066 DAMS系统界面优化需求 Start -->
                            <!-- <force:inputField value="{!v.oppdata.Close_Forecasted_Date__c}" class=""/> -->
                            <lightning:input variant="label-hidden" type="date" value="{!v.oppdata.Close_Forecasted_Date__c}" class="" />
                            <!-- DTT-亚楠 20241025 DB202409327066 DAMS系统界面优化需求 End -->
                        </div>
                        <!-- 製品分類 -->
                        <div aura:id="product_category_select1" class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--3-of-12 slds-large-size--3-of-12 marginTop">
                            {!v.fieldsmap.Product_Category1__c}
                            <!-- <force:inputField value="{!v.data.Product_Category1__c}" aura:id="input-product-category1"/> -->
                            <ui:inputSelect aura:id="select_Product1" class="slds-select" change="{!c.productcategoryChange1}"/>
                        </div>
                        <div aura:id="product_category_select2" class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--3-of-12 slds-large-size--3-of-12 marginTop">
                            {!v.fieldsmap.Product_Category2__c}
                            <!-- <force:inputField value="{!v.data.Product_Category2__c}" aura:id="input-product-category2"/> -->
                            <ui:inputSelect aura:id="select_Product2" class="slds-select" change="{!c.productcategoryChange2}"/>
                        </div>
                        <div aura:id="product_category_select3" class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--3-of-12 slds-large-size--3-of-12 marginTop">
                            {!v.fieldsmap.Product_Category3__c}
                            <!-- <force:inputField value="{!v.data.Product_Category3__c}" aura:id="input-product-category3"/> -->
                            <ui:inputSelect aura:id="select_Product3" class="slds-select" change="{!c.productcategoryChange3}"/>
                        </div>
                        <!--SWAG-CBX68C fy 【委托】DAMS系统周报模块内容需求增加 start-->
                        <!-- 支援需求 change="{!c.SupportNeeds__c}"-->
                        <div class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--1-of-5 slds-large-size--1-of-6 marginTop">
                            {!v.fieldsmap.SupportNeeds__c}
                            <ui:inputSelect aura:id="SupportNeeds__c" class="slds-select" />
                        </div>
                        <!--SWAG-CBX68C fy 【委托】DAMS系统周报模块内容需求增加 end-->
                        <div aura:id="result" class="disp_none slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--3-of-12 slds-large-size--3-of-12 marginTop">
                            {!v.fieldsmap.Result__c}
                            <ui:inputSelect aura:id="select_result" class="slds-select"/>
                            <!-- <ui:inputText label="结果" class="bcolor" value="{!v.result}"/> -->
                        </div>
                    </div>
                </div>
                <div class="save_button_area">
                	<ui:button aura:id="save_button" label="保存并新建" class="save_button slds-button slds-button--brand slds-order--3" press="{!c.save_report}"/>
                </div>
                
                <aura:renderIf isTrue="{!v.reports_now_count > 0}">
                    <div class="mt40 slds-box slds-theme--shade">
                        <div class="slds-table--edit_container slds-is-relative">
                            <table class="slds-table slds-table--edit slds-table--bordered slds-table--fixed-layout slds-no-cell-focus" role="grid">
                                <thead>
                                    <tr class="slds-line-height--reset">
                                        <th class="table_header slds-text-title--caps">
                                            <span class="slds-truncate" title="Name"></span>
                                        </th>
                                        <th class="table_header slds-text-title--caps">
                                            <span class="slds-truncate" title="Name">医院</span>
                                        </th>
                                        <th class="table_header slds-text-title--caps">
                                            <span class="slds-truncate" title="Name">{!v.fieldsmap.Department_Cateogy__c}</span>
                                        </th>
                                        <th class="table_header slds-text-title--caps">
                                            <span class="slds-truncate" title="Name">{!v.fieldsmap.doctor2__c}</span>
                                        </th>
                                        <th class="table_header slds-text-title--caps">
                                            <span class="slds-truncate" title="Name">{!v.fieldsmap.visitor_title__c}</span>
                                        </th>
                                        <th class="table_header slds-text-title--caps">
                                            <span class="slds-truncate" title="Name">{!v.fieldsmap.Purpose_Type__c}</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <aura:iteration items="{!v.reports_now}" var="item" indexVar="index">
                                        <tr aura:id="modal_data_list_tr" class="slds-hint-parent">
                                            <td role="gridcell" class="slds-cell-edit">
                                                <span class="slds-grid slds-grid--align-spread">
                                                    <span class="slds-truncate" title="{!item.Department_Cateogy__c}">{!item.Name}</span>
                                                </span>
                                            </td>
                                            <td role="gridcell" class="slds-cell-edit">
                                                <span class="slds-grid slds-grid--align-spread">
                                                    <span class="slds-truncate" title="{!item.Agency_Hospital__r.Name}">{!item.Agency_Hospital__r.Name}</span>
                                                </span>
                                            </td>
                                            <td role="gridcell" class="slds-cell-edit">
                                                <span class="slds-grid slds-grid--align-spread">
                                                    <span class="slds-truncate" title="{!item.Department_Cateogy__c}">{!item.Department_Cateogy__c}</span>
                                                </span>
                                            </td>
                                            <td role="gridcell" class="slds-cell-edit">
                                                <span class="slds-grid slds-grid--align-spread">
                                                    <span class="slds-truncate" title="{!item.doctor2__r.Name}">
                                                        <span class="encrypt">{!item.doctor2__r.Name}</span>
                                                        <span class="decrypt">{!item.doctor2__r.Name}</span>
                                                        <!-- deloitte-zhj 20231225 PIPL还原 -->
                                                        <!-- <span class="decrypt">{!item.doctor2NameOrigin}</span> -->
                                                    </span>
                                                </span>
                                            </td>
                                            <td role="gridcell" class="slds-cell-edit">
                                                <span class="slds-grid slds-grid--align-spread">
                                                    <span class="slds-truncate" title="{!item.visitor_title__c}">
                                                        <span class="encrypt">{!item.visitor_title__c}</span>
                                                        <span class="decrypt">{!item.visitor_title__c}</span>
                                                        <!-- deloitte-zhj 20231225 PIPL还原 -->
                                                        <!-- <span class="decrypt">{!item.visitorTitleOrigin}</span> -->
                                                    </span>
                                                </span>
                                            </td>
                                            <td role="gridcell" class="slds-cell-edit">
                                                <span class="slds-grid slds-grid--align-spread">
                                                    <span class="slds-truncate" title="{!item.Purpose_Type__c}">{!item.Purpose_Type__c}</span>
                                                </span>
                                            </td>
                                        </tr>
                                    </aura:iteration>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </aura:renderIf>
            </div>
        </div>
    </div>
    <div aura:id="modal_bg" class="disp_none slds-backdrop slds-backdrop--open"></div>
    
    <div aura:id="modal_confirm" role="dialog" tabindex="-2" aria-labelledby="header43" class="disp_none slds-modal slds-fade-in-open">
        <div class="slds-modal__container">
            <div class="slds-modal__header">
                <h2 id="header43" class="slds-text-heading--medium">{!v.modal_confirm_title}</h2>
            </div>
            <div class="slds-modal__content slds-p-around--medium">
                <p>{!v.modal_confirm_text}</p>
            </div>
            <div class="slds-modal__footer">
                <ui:button label="是" class="slds-button slds-button--brand" press="{!c.yes_button}"/>
                <ui:button label="否" class="slds-button slds-button--neutral" press="{!c.no_button}" />
            </div>
        </div>
    </div>
    <div aura:id="modal_confirm_bg" class="disp_none slds-backdrop slds-backdrop--open"></div>

    <div aura:id="modal_newAC" role="dialog" tabindex="-3" aria-labelledby="headertest" class="disp_none slds-modal slds-fade-in-open">
        <div class="slds-modal__container">
            <aura:renderIf isTrue="{!v.loginEdit}">
                <lightning:spinner alternativeText="Loading" size="medium" style="width: 100%; height: 80%;top:50px;" />
            </aura:renderIf>
            <div class="slds-modal__header">
                <h2 id="headertest" class="slds-text-heading--medium">新建.客户人员</h2>
            </div>
            <div class="slds-modal__content slds-p-around--medium">
                <lightning:recordEditForm aura:id="recordEditForm"  objectApiName="Agency_Contact__c" onsuccess="{!c.handleSuccess}" onload="{!c.showRequiredFields}" onsubmit="{!c.handleSubmit}">
        			<lightning:messages aura:id="OppMessage" />
                    <!-- onload="{!c.showRequiredFields}" -->
                    <aura:renderIf isTrue="{!v.truthy}">
                        <lightning:inputField fieldName="Name" aura:id="newOpportunityField" />
                        <lightning:inputField fieldName="Type__c" aura:id="newOpportunityField" />
                        <!-- // DTT-亚楠 20240410 优化客户人员查询速度 Start  -->
                        <!-- <lightning:inputField fieldName="Doctor_Division1__c" aura:id="doctorDivision1" onchange="{!c.avoidPI}"/>--><!-- //deloitte-zhj 2023/07/18 防止选择***** -->
                        <lightning:inputField fieldName="Doctor_Division1__c" aura:id="newOpportunityField" />
                        <!-- // DTT-亚楠 20240410 优化客户人员查询速度 End  -->
                        <!-- <lightning:inputField class="customRequired none" aura:id="Input_type__c" fieldName="Type__c"/>
                        <lightning:inputField class="customRequired none" aura:id="Input_Doctor_Division1__c" fieldName="Doctor_Division1__c"/> -->
                    </aura:renderIf>
                    <br/>
                    <br/>
                    <br/>
                    <br/>
                    <br/>
                    <div class="save_button_area">
                        <lightning:button class="slds-m-top_small" type="button" label="取消" onclick="{!c.createCancel}"/>
                        <lightning:button class="slds-m-top_small" type="submit" label="保存" />
                    </div>

                    
    			</lightning:recordEditForm>
            </div>
        </div>
    </div>
   
    
    <!-- DTT-亚楠 20241025 DB202409327066 DAMS系统界面优化需求 Start -->
    <aura:if isTrue="{!v.showNewAgencyOpportunity}">
        <section role="dialog" tabindex="-1" aria-modal="true" aria-labelledby="modal-heading-01"
            class="slds-modal slds-fade-in-open" aura:id="NewAgencyOpportunity"
            id="NewAgencyOpportunity" aria-describedby="modal-content-id-2" style="z-index: 9999;">
            <div class="slds-modal__container">
                <c:NewAgencyOpportunity2 recordTypeId="{!v.recordTypeId}" agencyHospitalLinkId="{!v.agencyHospitalLinkId}" departmentName="{!v.departmentName}"/>
            </div>
        </section>
        <div class="slds-backdrop2 slds-backdrop_open2"></div>
    </aura:if>
    <!-- DTT-亚楠 20241025 DB202409327066 DAMS系统界面优化需求 End -->
</aura:component>