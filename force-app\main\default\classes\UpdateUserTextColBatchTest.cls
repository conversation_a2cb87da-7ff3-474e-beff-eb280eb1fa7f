/**
 * This class contains unit tests for validating the behavior of Apex classes
 * and triggers.
 *
 * Unit tests are class methods that verify whether a particular piece
 * of code is working properly. Unit test methods take no arguments,
 * commit no data to the database, and are flagged with the testMethod
 * keyword in the method definition.
 *
 * All test methods in an organization are executed whenever Apex code is deployed
 * to a production organization to confirm correctness, ensure code
 * coverage, and prevent regressions. All Apex classes are
 * required to have at least 75% code coverage in order to be deployed
 * to a production organization. In addition, all triggers must have some code coverage.
 *
 * The @isTest class annotation indicates this class only contains test
 * methods. Classes defined with the @isTest annotation do not count against
 * the organization size limit for all Apex scripts.
 *
 * See the Apex Language Reference for more information about Testing and Code Coverage.
 */
@isTest
private class UpdateUserTextColBatchTest {

    @testSetup static void methodName() {
        String loginId = UserInfo.getUserId();
        list<PermissionSetAssignment> PermissionSetAssignmentList = new list<PermissionSetAssignment>();
        PermissionSetAssignmentList.add(new PermissionSetAssignment(AssigneeId = loginId,
                                        PermissionSetId = system.Label.PermissionSet_Createdashboard_ID));
        PermissionSetAssignmentList.add(new PermissionSetAssignment(AssigneeId = loginId,
                                        PermissionSetId = system.Label.PermissionSet_ENDOPARTNER_ID));
        PermissionSetAssignmentList.add(new PermissionSetAssignment(AssigneeId = loginId,
                                        PermissionSetId = system.Label.PermissionSet_Group_purchse_dept_ID));
        PermissionSetAssignmentList.add(new PermissionSetAssignment(AssigneeId = loginId,
                                        PermissionSetId = system.Label.PermissionSet_P002_Agent_ID));
        PermissionSetAssignmentList.add(new PermissionSetAssignment(AssigneeId = loginId,
                                        PermissionSetId = system.Label.PermissionSet_P002_OCM_ID));
        PermissionSetAssignmentList.add(new PermissionSetAssignment(AssigneeId = loginId,
                                        PermissionSetId = system.Label.PermissionSet_Plan_report_permission_ID));
        PermissionSetAssignmentList.add(new PermissionSetAssignment(AssigneeId = loginId,
                                        PermissionSetId = system.Label.PermissionSet_ProductCost_ID));
        PermissionSetAssignmentList.add(new PermissionSetAssignment(AssigneeId = loginId,
                                        PermissionSetId = system.Label.PermissionSet_Report_ID));
        PermissionSetAssignmentList.add(new PermissionSetAssignment(AssigneeId = loginId,
                                        PermissionSetId = system.Label.PermissionSet_SI_ID));
        Database.upsert(PermissionSetAssignmentList, false);

    }

    static testMethod void testUpdateUserText() {
        String loginId = UserInfo.getUserId();
        Date toDate = Date.today();
        Date mon1stDate = Date.newInstance(toDate.year(), toDate.month(), 1);
        Integer SSBacth_Execute_Day = Integer.valueOf(System.Label.SSBacth_Execute_Day);
        List<OlympusCalendar__c> ocList = new List<OlympusCalendar__c>();
        for (Integer i = 0; i <= SSBacth_Execute_Day; i++) {
            OlympusCalendar__c oc = new OlympusCalendar__c(Date__c = mon1stDate.addDays(i), ChangeToWorkday__c = true);
            ocList.add(oc);
        }
        insert ocList;


        System.Test.StartTest();
        Id execBTId = Database.executeBatch(new UpdateUserTextColBatch(loginId), 20);
        System.Test.StopTest();

        // assert できないですね。OlympusCalendarにデータを作ったら連番できなくなります。
        //List<User> uList = [select Id, Fiscal_Worddays_System__c, Fiscal_Worddays__c from User where Id = :loginId];
        //System.assertEquals(uList[0].Fiscal_Worddays_System__c, uList[0].Fiscal_Worddays__c);
    }

    /* 2018-12-10 CHAN-B6W86D 根据权限集进行权限设置
     * 插入用户，对用户插入权限集
     */



}