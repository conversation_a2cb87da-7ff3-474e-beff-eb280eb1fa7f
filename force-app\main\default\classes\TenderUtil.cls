//lt add 20231110  DB202307367354  【招标项目】漏单计算逻辑修改 add
//lt  20231213 update 漏单逻辑：创建日-（项目：招标日-60天)>0  ==>  漏单数为1   DB202311665664  【重要紧急课题】询价中的“项目：招标日”和漏单数修改
global without sharing class TenderUtil{

    public TenderUtil() {
    }

    //计算漏单逻辑： 询价关联的多条招标项目中(询价关联招标项目)
    //询价的创建日 > 最早公告记录日-60  ==>  漏单数为1
    //,List<Tender_Opportunity_Link__c> linkList
    public static void UpdLeakageNum(Set<Id> oppSet){
        //漏单数需要更新为1的询价Map
        Map<String,Opportunity> UpdOppMap = new Map<String,Opportunity>();

        //更新 询价 招标日
        Map<String,Opportunity> UpdOpp1Map = new Map<String,Opportunity>();

        //公告记录日变化导致 询价关联的的link里没有漏单  1-0 的list
        List<Opportunity> UpdOppnullList = new List<Opportunity>();

        //需要更新的询价下所有的link
        List<Tender_Opportunity_Link__c> sLinkList = [
            Select id,name,IsLeakage_New__c,Opportunity__c,Tender_information__c, 
                   Tender_information__r.publicDate__c,Opportunity__r.Created_Day__c,
                   Opportunity__r.LeakageNumber__c,
                   Opportunity__r.DirectLossFLG__c  
                   ,Opportunity__r.TenderBeginDate_New__c,Tender_information__r.TenderDate__c  //lt DB202311665664
                   ,Opportunity__r.old_Oppo_No__c  //zzm 20240416 DB202403627483
                   ,Tender_information__r.Logical_delete__c //lt 20240716 add 招标日计算问题
            From Tender_Opportunity_Link__c
            Where Opportunity__c in :oppSet
            AND Tender_information__r.Logical_delete__c = false //lt 20240716 add 招标日计算问题
        ];

        for(Tender_Opportunity_Link__c slink: sLinkList){
            Opportunity opp = new Opportunity();

            //lt DB202311665664 start
            System.debug('20231109-漏单-共通招标日'+slink.Tender_information__r.TenderDate__c);
            // if(slink.Tender_information__r.TenderDate__c != null){ //lt 20240522 注释 DB202403627483
                opp.Id = slink.Opportunity__c;
                if(!UpdOpp1Map.containsKey(slink.Opportunity__c)){
                    opp.TenderBeginDate_New__c = slink.Tender_information__r.TenderDate__c;
                    //lt 20240522  DB202403627483 add opp.TenderBeginDate_New__c != null && 
                    if(opp.TenderBeginDate_New__c != null && slink.Opportunity__r.Created_Day__c > opp.TenderBeginDate_New__c.addDays(-60) && slink.Opportunity__r.old_Oppo_No__c ==null){//zzm 20240416 DB202403627483
                        opp.LeakageNumber__c = 1;
                    }else{
                        opp.LeakageNumber__c = null;
                    }
                    UpdOpp1Map.put(slink.Opportunity__c,opp);
                    // oppSet.remove(slink.Opportunity__c);
                }else{
                    opp = UpdOpp1Map.get(slink.Opportunity__c);
                    if(String.isNotBlank(String.valueOf(opp.TenderBeginDate_New__c))){
                        if(opp.TenderBeginDate_New__c > slink.Tender_information__r.TenderDate__c){
                            opp.TenderBeginDate_New__c = slink.Tender_information__r.TenderDate__c;
                            //lt 20240522  DB202403627483 add opp.TenderBeginDate_New__c != null && 
                            if(opp.TenderBeginDate_New__c != null && slink.Opportunity__r.Created_Day__c > opp.TenderBeginDate_New__c.addDays(-60) && slink.Opportunity__r.old_Oppo_No__c ==null){//zzm 20240416 DB202403627483
                                opp.LeakageNumber__c = 1;
                            }else{
                                opp.LeakageNumber__c = null;
                            }
                        }
                    }else{
                        opp.TenderBeginDate_New__c = slink.Tender_information__r.TenderDate__c;
                        //lt 20240522  DB202403627483 add opp.TenderBeginDate_New__c != null &&
                        if(opp.TenderBeginDate_New__c != null && slink.Opportunity__r.Created_Day__c > opp.TenderBeginDate_New__c.addDays(-60) && slink.Opportunity__r.old_Oppo_No__c ==null){//zzm 20240416 DB202403627483
                            opp.LeakageNumber__c = 1;
                        }else{
                            opp.LeakageNumber__c = null;
                        }
                    }
                    UpdOpp1Map.put(slink.Opportunity__c,opp);
                }
                // System.debug('20231109-漏单-共通opp招标日'+slink.Opportunity__r.TenderBeginDate_New__c);
                // if(String.isNotBlank(String.valueOf(slink.Opportunity__r.TenderBeginDate_New__c))){
                //     if(slink.Opportunity__r.TenderBeginDate_New__c > slink.Tender_information__r.TenderDate__c){
                //         opp.TenderBeginDate_New__c = slink.Tender_information__r.TenderDate__c;
                //     }
                // }else{
                //     opp.TenderBeginDate_New__c = slink.Tender_information__r.TenderDate__c;
                // }
                // System.debug('20231109-漏单-共通询价'+opp);
                // if(!UpdOpp1Map.containsKey(slink.Opportunity__c)){
                //     UpdOpp1Map.put(slink.Opportunity__c,opp);
                // }
            // }  //lt 20240522 注释 DB202403627483
            //lt DB202311665664 end

            //后台用-招标项目直接失单标记
            //招标项目 直接点 失单 标记漏单为1 时，不对漏单进行操作
            // if(!slink.Opportunity__r.DirectLossFLG__c){  //2023-11-22 需求，取消看直接失单
                //link里只要有符合 漏单 逻辑 （opp创建日 > tender公告记录日-60）  --  询价漏单数为1
                // if(slink.Tender_information__r.publicDate__c != null && slink.Opportunity__r.Created_Day__c > slink.Tender_information__r.publicDate__c.addDays(-60)){
                //DB202311665664 20231213  update 取 创建日-（项目：招标日-60天)>0
                // if(slink.Opportunity__r.TenderBeginDate_New__c != null && slink.Opportunity__r.Created_Day__c > slink.Opportunity__r.TenderBeginDate_New__c.addDays(-60)){
                //     opp.Id = slink.Opportunity__c;
                //     opp.LeakageNumber__c = 1;

                //     //可能存在 一个询价下多个link漏单 ，更一次询价
                //     if(!UpdOppMap.containsKey(slink.Opportunity__c)){
                //         UpdOppMap.put(slink.Opportunity__c,opp);
                //         oppSet.remove(slink.Opportunity__c);
                //     }
                // }
            // }

            
            
        }

        // if(oppSet.Size() > 0){
        //     List<Opportunity> oppnullList = [
        //     Select id,name 
        //     From Opportunity
        //     Where id in :oppSet 
        //     // And DirectLossFLG__c = false
        //     ];

        //     if(oppnullList.Size() > 0){
        //         for(Opportunity oppnull: oppnullList){
        //             Opportunity opp1 = new Opportunity();
        //             opp1.Id = oppnull.Id;
        //             opp1.LeakageNumber__c = null;
        //             UpdOppnullList.add(opp1);
        //         }
        //     }
        // }
        
        System.debug('20231109-漏单-upd共通询价1'+UpdOppMap.values());
        System.debug('20231109-漏单-upd共通询价2'+UpdOpp1Map.values());
        if(UpdOpp1Map.values().Size() > 0){
            update UpdOpp1Map.values();
        }

        // if(UpdOppMap.values().Size() > 0){
        //     update UpdOppMap.values();
        // }
        
        // if(UpdOppnullList.size() > 0 ){
        //     update UpdOppnullList;
        // }
        
    }

    
    
}