/**
 * This class contains unit tests for validating the behavior of Apex classes
 * and triggers.
 *
 * Unit tests are class methods that verify whether a particular piece
 * of code is working properly. Unit test methods take no arguments,
 * commit no data to the database, and are flagged with the testMethod
 * keyword in the method definition.
 *
 * All test methods in an organization are executed whenever Apex code is deployed
 * to a production organization to confirm correctness, ensure code
 * coverage, and prevent regressions. All Apex classes are
 * required to have at least 75% code coverage in order to be deployed
 * to a production organization. In addition, all triggers must have some code coverage.
 *
 * The @isTest class annotation indicates this class only contains test
 * methods. Classes defined with the @isTest annotation do not count against
 * the organization size limit for all Apex scripts.
 *
 * See the Apex Language Reference for more information about Testing and Code Coverage.
 */
@isTest
private class UpdateAssetToCurrentMCBatchTest {

  public static Account company ;
  public final Integer N_ASSET = 10; //納入機器数
  public final Integer N_CONTRACT = 5; //契約数

  static Asset createAsset() {

    ID rectCoid =
      Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('HP').getRecordTypeId();
    ID rectSctid =
      Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_GI').getRecordTypeId();


    ID rectDptid =
      Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_GI').getRecordTypeId();


    // テストデータ
    company = new Account();
    company.RecordTypeId = rectCoId;
    company.Name         = 'HistoryTestCompany';
    upsert company;
    Account section =
      [select id from account
       where ParentId = : company.id
                        and RecordTypeId = : rectSctid];
    Account depart = new Account();
    depart.RecordTypeId = rectDptId;
    depart.Name         = '*';
    depart.Department_Name__c  = 'HistoryTestDepart';
    depart.ParentId            = section.Id;
    depart.Department_Class__c = section.Id;
    depart.Hospital__c         = company.Id;
    upsert depart;
    // 再取得
    List<Id> accountIds = new Id[] {company.Id, section.Id, depart.Id};
    List<Account> accList = [select Management_Code__c, Management_Code_Auto__c, Name, Id from Account where Id In :accountIds order by Management_Code_Auto__c];

    List<Product2> prdList = new List<Product2>();
    Product2 prd1 = new Product2();
    prd1.ProductCode_Ext__c     = 'HistoryPrd1';
    prd1.ProductCode            = 'HistoryPrd1';
    prd1.Repair_Product_Code__c = 'HistoryPrd1_RP';
    prd1.Name                   = 'HistoryPrd1';
    prd1.Manual_Entry__c        = false;
    prdList.add(prd1);
    Product2 prd2 = new Product2();
    prd2.ProductCode_Ext__c     = 'HistoryPrd2';
    prd2.ProductCode            = 'HistoryPrd2';
    prd2.Repair_Product_Code__c = 'HistoryPrd2_RP';
    prd2.Name                   = 'HistoryPrd2';
    prd2.Manual_Entry__c        = false;
    prdList.add(prd2);
    insert prdList;

    Asset ast = new Asset();
    ast.Name                   = 'HistoryAst1';
    ast.AccountId              = depart.Id;
    ast.Department_Class__c    = section.Id;
    ast.Hospital__c            = company.Id;
    ast.Product2Id             = prd1.Id;
    ast.SerialNumber           = 'HistorySerialNumber';
    ast.Guarantee_period_for_products__c = Date.today();
    ast.InstallDate                      = Date.today();
    insert ast;
    ast = [select Id, CurrencyIsoCode, Name, Product_Serial_No__c, AccountId, Department_Class__c, Department_Class__r.Management_Code_Auto__c, Hospital__c, Product2Id, Product2.ProductCode, Product2.Repair_Product_Code__c, SerialNumber
           from Asset
           where Id = :ast.Id];

    return ast;
  }


  static Maintenance_Contract__c creatContract(Asset a, Date endDate, Date startDate, String mcNo ) {
    Maintenance_Contract__c crt = new Maintenance_Contract__c();
    crt.Past_update_contract__c = true;
    crt.Name = startDate.format() + endDate.format();
    crt.Department__c = a.AccountId;
    crt.Department_Class__c = a.Department_Class__c;
    crt.Hospital__c = a.Hospital__c;
    crt.Sales_Estimate_Money__c = 3000;
    crt.Closing_Prediction_Day__c = Date.today();
    crt.CurrencyIsoCode = a.CurrencyIsoCode;
    crt.Status__c = '契約';
    crt.Maintenance_Contract_No__c = mcNo;
    crt.Contract_Conclusion_Date__c = Date.today();
    crt.Contract_End_Date__c = endDate;
    crt.Contract_Start_Date__c = startDate;
    crt.SalesOfficeCode_selection__c = '北京RC';
    crt.RecordTypeId = 
              Schema.SObjectType.Maintenance_Contract__c.getRecordTypeInfosByDeveloperName().get('VM_Contract').getRecordTypeId();
    insert crt;
    crt = [select Id, Contract_End_Date__c
           from Maintenance_Contract__c
           where Id = :crt.Id];
    return crt;

  }

  static Maintenance_Contract_Asset__c creatMca(Asset a, Maintenance_Contract__c crt) {
    Maintenance_Contract_Asset__c newMca = new Maintenance_Contract_Asset__c();

    newMca.Asset__c = a.Id;
    newMca.Maintenance_Contract__c = crt.Id;
    insert newMca;
    newMca = [select Id, Asset__c, Maintenance_Contract__c
              from Maintenance_Contract_Asset__c
              where Id = : newMca.Id];
    return newMca;
  }

  //将Asset的当前维修合同更新为指定合同
  static void updAsset(Asset a, Maintenance_Contract__c crt) {
    a.CurrentContract__c = crt.Id;
    a.Service_Agreement__c = '有';
    a.CurrentContract_End_Date__c = crt.Contract_End_Date__c;
    update a;
  }

  //结束日期为昨天的合同相应修改测试
  static testMethod void endDateTest() {

    Asset testAst = createAsset();
    Date TDay = Date.today();
    Date YesDay = Date.today().addDays(-1);
    Maintenance_Contract__c testCrt = creatContract(testAst, YesDay, TDay.addDays(-20), 'crt');
    updAsset(testAst, testCrt);
    creatMca(testAst, testCrt);
    List<Maintenance_Contract_Asset__c> mcaList = [select id, Asset__c, Maintenance_Contract__c, Maintenance_Contract__r.Contract_End_Date__c,
                                        Maintenance_Contract__r.Contract_Start_Date__c,
                                        Asset__r.Service_Agreement__c, Asset__r.CurrentContract__c,
                                        Asset__r.CurrentContract_End_Date__c
                                        from Maintenance_Contract_Asset__c
                                        where Maintenance_Contract__r.Contract_End_Date__c = : YesDay
                                            or Maintenance_Contract__r.Contract_Start_Date__c = : TDay];

    //system.assertEquals(1, mcaList.size());
    //system.assertEquals('有', mcaList[0].Asset__r.Service_Agreement__c);
    //system.assertEquals(testCrt.Id, mcaList[0].Asset__r.CurrentContract__c);
    System.runAs(new User(Id = Userinfo.getUserId())) {
      system.Test.StartTest();
      StaticParameter.EventOpportunityPileUpExeFlg = true;
      // EventC⇒Event処理後、逆更新をスルー用
      StaticParameter.NotUpdEventCFlg = true;
      // 積み上げ処理後、トリガをスルー用
      StaticParameter.EscapeNFM001Trigger = true;
      StaticParameter.EscapeNFM001AgencyContractTrigger = true;
      StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
      StaticParameter.EscapeNFM007Trigger = true;
      StaticParameter.EscapeOpportunityBefUpdTrigger = true;
      StaticParameter.EscapeOpportunityHpDeptUpdTrigger = true;
      StaticParameter.EscapeSyncOpportunityTrigger = true;
      StaticParameter.EscapeMaintenanceContractAfterUpdateTrigger = true;
      StaticParameter.EscapeSyncProduct2Trigger = true;
      // trueのとき、リードのトリガーをスルーする
      StaticParameter.EscapeLeadTrigger = true;
      StaticParameter.EscapeAccountTrigger = true;

      StaticParameter.EscapeConsumableOrderDetail2Trigger = true;

      StaticParameter.EscapeNFM010UpsertStatuAchievementsTrigger = true;
      // 直销耗材不直销 ConsumableAssetHander
      StaticParameter.ConsumableAssetHanderTrigger = true;

      // 跳过不涉及备品借出的逻辑
      StaticParameter.rentalApplyIsRunning = true;

      // accountdailyUpdate 跳过 更新询价
      StaticParameter.EscapeOpportunityownerUpdate  = true;
      UpdateAssetToCurrentMCBatch batchTest = new UpdateAssetToCurrentMCBatch();
      Database.executeBatch(batchTest);
      System.Test.StopTest();
    }
    //batchTest.execute( null, mcaList);
    mcaList = [select id, Asset__c, Maintenance_Contract__c, Maintenance_Contract__r.Contract_End_Date__c,
               Maintenance_Contract__r.Contract_Start_Date__c,
               Asset__r.Service_Agreement__c, Asset__r.CurrentContract__c,
               Asset__r.CurrentContract_End_Date__c
               from Maintenance_Contract_Asset__c
               where Maintenance_Contract__r.Contract_End_Date__c = : YesDay
                   or Maintenance_Contract__r.Contract_Start_Date__c = : TDay];
    //system.assertEquals(1, mcaList.size());
    //system.assertEquals('無', mcaList[0].Asset__r.Service_Agreement__c);
    //system.assertEquals(testCrt.Id , mcaList[0].Asset__r.CurrentContract__c);

  }

  //开始日期为今天的合同修改
  static testMethod void startDateTest() {
    // TO DO: implement unit test

    Asset testAst = createAsset();
    Date TDay = Date.today();
    Date YesDay = Date.today().addDays(-1);
    Maintenance_Contract__c testCrt = creatContract(testAst, TDay.addDays(30), TDay, 'crt');
    creatMca(testAst, testCrt);
    List<Maintenance_Contract_Asset__c> mcaList = [select id, Asset__c, Maintenance_Contract__c, Maintenance_Contract__r.Contract_End_Date__c,
                                        Maintenance_Contract__r.Contract_Start_Date__c,
                                        Asset__r.Service_Agreement__c, Asset__r.CurrentContract__c,
                                        Asset__r.CurrentContract_End_Date__c
                                        from Maintenance_Contract_Asset__c
                                        where Maintenance_Contract__r.Contract_End_Date__c = : YesDay
                                            or Maintenance_Contract__r.Contract_Start_Date__c = : TDay];
    //system.assertEquals(1, mcaList.size());
    //system.assertEquals('無', mcaList[0].Asset__r.Service_Agreement__c);
    //system.assertEquals(null, mcaList[0].Asset__r.CurrentContract__c);
    System.runAs(new User(Id = Userinfo.getUserId())) {

      system.Test.StartTest();
      StaticParameter.EventOpportunityPileUpExeFlg = true;
      // EventC⇒Event処理後、逆更新をスルー用
      StaticParameter.NotUpdEventCFlg = true;
      // 積み上げ処理後、トリガをスルー用
      StaticParameter.EscapeNFM001Trigger = true;
      StaticParameter.EscapeNFM001AgencyContractTrigger = true;
      StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
      StaticParameter.EscapeNFM007Trigger = true;
      StaticParameter.EscapeOpportunityBefUpdTrigger = true;
      StaticParameter.EscapeOpportunityHpDeptUpdTrigger = true;
      StaticParameter.EscapeSyncOpportunityTrigger = true;
      StaticParameter.EscapeMaintenanceContractAfterUpdateTrigger = true;
      StaticParameter.EscapeSyncProduct2Trigger = true;
      // trueのとき、リードのトリガーをスルーする
      StaticParameter.EscapeLeadTrigger = true;
      StaticParameter.EscapeAccountTrigger = true;

      StaticParameter.EscapeConsumableOrderDetail2Trigger = true;

      StaticParameter.EscapeNFM010UpsertStatuAchievementsTrigger = true;
      // 直销耗材不直销 ConsumableAssetHander
      StaticParameter.ConsumableAssetHanderTrigger = true;

      // 跳过不涉及备品借出的逻辑
      StaticParameter.rentalApplyIsRunning = true;

      // accountdailyUpdate 跳过 更新询价
      StaticParameter.EscapeOpportunityownerUpdate  = true;
      UpdateAssetToCurrentMCBatch batchTest = new UpdateAssetToCurrentMCBatch();
      Database.executeBatch(batchTest);
      System.Test.StopTest();
    }
    //batchTest.execute( null, mcaList);
    mcaList = [select id, Asset__c, Maintenance_Contract__c, Maintenance_Contract__r.Contract_End_Date__c,
               Maintenance_Contract__r.Contract_Start_Date__c,
               Asset__r.Service_Agreement__c, Asset__r.CurrentContract__c,
               Asset__r.CurrentContract_End_Date__c
               from Maintenance_Contract_Asset__c
               where Maintenance_Contract__r.Contract_End_Date__c = : YesDay
                   or Maintenance_Contract__r.Contract_Start_Date__c = : TDay];
    //system.assertEquals(1, mcaList.size());
    //system.assertEquals('有', mcaList[0].Asset__r.Service_Agreement__c);
    //system.assertEquals(testCrt.Id , mcaList[0].Asset__r.CurrentContract__c);
  }

  //开始日期为今天的合同B，其保有设备Asset已有有效合同A，且A比B更晚结束
  static testMethod void ALateThenBTest() {

    Asset testAst = createAsset();
    Date TDay = Date.today();
    Date YesDay = Date.today().addDays(-1);
    Maintenance_Contract__c testCrtA = creatContract(testAst, TDay.addDays(60), TDay.addDays(-20), 'crtA');
    Maintenance_Contract__c testCrtB = creatContract(testAst, TDay.addDays(30), TDay, 'crtB');
    updAsset(testAst, testCrtA);

    creatMca(testAst, testCrtA);
    creatMca(testAst, testCrtB);
    List<Maintenance_Contract_Asset__c> mcaList = [select id, Asset__c, Maintenance_Contract__c, Maintenance_Contract__r.Contract_End_Date__c,
                                        Maintenance_Contract__r.Contract_Start_Date__c,
                                        Asset__r.Service_Agreement__c, Asset__r.CurrentContract__c,
                                        Asset__r.CurrentContract_End_Date__c
                                        from Maintenance_Contract_Asset__c
                                        where Maintenance_Contract__r.Contract_End_Date__c = : YesDay
                                            or Maintenance_Contract__r.Contract_Start_Date__c = : TDay];

    //system.assertEquals(1, mcaList.size());
    //system.assertEquals('有', mcaList[0].Asset__r.Service_Agreement__c);
    //system.assertEquals(testCrtA.Id, mcaList[0].Asset__r.CurrentContract__c);
    System.runAs(new User(Id = Userinfo.getUserId())) {
      system.Test.StartTest();
      StaticParameter.EventOpportunityPileUpExeFlg = true;
      // EventC⇒Event処理後、逆更新をスルー用
      StaticParameter.NotUpdEventCFlg = true;
      // 積み上げ処理後、トリガをスルー用
      StaticParameter.EscapeNFM001Trigger = true;
      StaticParameter.EscapeNFM001AgencyContractTrigger = true;
      StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
      StaticParameter.EscapeNFM007Trigger = true;
      StaticParameter.EscapeOpportunityBefUpdTrigger = true;
      StaticParameter.EscapeOpportunityHpDeptUpdTrigger = true;
      StaticParameter.EscapeSyncOpportunityTrigger = true;
      StaticParameter.EscapeMaintenanceContractAfterUpdateTrigger = true;
      StaticParameter.EscapeSyncProduct2Trigger = true;
      // trueのとき、リードのトリガーをスルーする
      StaticParameter.EscapeLeadTrigger = true;
      StaticParameter.EscapeAccountTrigger = true;

      StaticParameter.EscapeConsumableOrderDetail2Trigger = true;

      StaticParameter.EscapeNFM010UpsertStatuAchievementsTrigger = true;
      // 直销耗材不直销 ConsumableAssetHander
      StaticParameter.ConsumableAssetHanderTrigger = true;

      // 跳过不涉及备品借出的逻辑
      StaticParameter.rentalApplyIsRunning = true;

      // accountdailyUpdate 跳过 更新询价
      StaticParameter.EscapeOpportunityownerUpdate  = true;
      UpdateAssetToCurrentMCBatch batchTest = new UpdateAssetToCurrentMCBatch();
      Database.executeBatch(batchTest);
      System.Test.StopTest();
    }
    //batchTest.execute( null, mcaList);
    mcaList = [select id, Asset__c, Maintenance_Contract__c, Maintenance_Contract__r.Contract_End_Date__c,
               Maintenance_Contract__r.Contract_Start_Date__c,
               Asset__r.Service_Agreement__c, Asset__r.CurrentContract__c,
               Asset__r.CurrentContract_End_Date__c
               from Maintenance_Contract_Asset__c
               where Maintenance_Contract__r.Contract_End_Date__c = : YesDay
                   or Maintenance_Contract__r.Contract_Start_Date__c = : TDay];
    //system.assertEquals(1, mcaList.size());
    //system.assertEquals('有', mcaList[0].Asset__r.Service_Agreement__c);
    //system.assertEquals(testCrtB.Id , mcaList[0].Asset__r.CurrentContract__c, 'Expert:合同B、実際はそうではないです');

  }

  //开始日期为今天的合同B，其保有设备Asset已有有效合同A，但A比B早结束
  static testMethod void AEarlyThanBTest() {

    Asset testAst = createAsset();
    Date TDay = Date.today();
    Date YesDay = Date.today().addDays(-1);

    Maintenance_Contract__c testCrtA = creatContract(testAst, TDay.addDays(30), TDay.addDays(-20), 'crtA');
    Maintenance_Contract__c testCrtB = creatContract(testAst, TDay.addDays(60), TDay, 'crtB');
    updAsset(testAst, testCrtA);
    creatMca(testAst, testCrtA);
    creatMca(testAst, testCrtB);
    List<Maintenance_Contract_Asset__c> mcaList = [select id, Asset__c, Maintenance_Contract__c, Maintenance_Contract__r.Contract_End_Date__c,
                                        Maintenance_Contract__r.Contract_Start_Date__c,
                                        Asset__r.Service_Agreement__c, Asset__r.CurrentContract__c,
                                        Asset__r.CurrentContract_End_Date__c
                                        from Maintenance_Contract_Asset__c
                                        where Maintenance_Contract__r.Contract_End_Date__c = : YesDay
                                            or Maintenance_Contract__r.Contract_Start_Date__c = : TDay];

    //system.assertEquals(1, mcaList.size());
    //system.assertEquals('有', mcaList[0].Asset__r.Service_Agreement__c);
    //system.assertEquals(testCrtA.Id, mcaList[0].Asset__r.CurrentContract__c);
    System.runAs(new User(Id = Userinfo.getUserId())) {
      system.Test.StartTest();
      StaticParameter.EventOpportunityPileUpExeFlg = true;
      // EventC⇒Event処理後、逆更新をスルー用
      StaticParameter.NotUpdEventCFlg = true;
      // 積み上げ処理後、トリガをスルー用
      StaticParameter.EscapeNFM001Trigger = true;
      StaticParameter.EscapeNFM001AgencyContractTrigger = true;
      StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
      StaticParameter.EscapeNFM007Trigger = true;
      StaticParameter.EscapeOpportunityBefUpdTrigger = true;
      StaticParameter.EscapeOpportunityHpDeptUpdTrigger = true;
      StaticParameter.EscapeSyncOpportunityTrigger = true;
      StaticParameter.EscapeMaintenanceContractAfterUpdateTrigger = true;
      StaticParameter.EscapeSyncProduct2Trigger = true;
      // trueのとき、リードのトリガーをスルーする
      StaticParameter.EscapeLeadTrigger = true;
      StaticParameter.EscapeAccountTrigger = true;

      StaticParameter.EscapeConsumableOrderDetail2Trigger = true;

      StaticParameter.EscapeNFM010UpsertStatuAchievementsTrigger = true;
      // 直销耗材不直销 ConsumableAssetHander
      StaticParameter.ConsumableAssetHanderTrigger = true;

      // 跳过不涉及备品借出的逻辑
      StaticParameter.rentalApplyIsRunning = true;

      // accountdailyUpdate 跳过 更新询价
      StaticParameter.EscapeOpportunityownerUpdate  = true;
      UpdateAssetToCurrentMCBatch batchTest = new UpdateAssetToCurrentMCBatch();
      Database.executeBatch(batchTest);
      System.Test.StopTest();
    }
    //batchTest.execute( null, mcaList);
    mcaList = [select id, Asset__c, Maintenance_Contract__c, Maintenance_Contract__r.Contract_End_Date__c,
               Maintenance_Contract__r.Contract_Start_Date__c,
               Asset__r.Service_Agreement__c, Asset__r.CurrentContract__c,
               Asset__r.CurrentContract_End_Date__c
               from Maintenance_Contract_Asset__c
               where Maintenance_Contract__r.Contract_End_Date__c = : YesDay
                   or Maintenance_Contract__r.Contract_Start_Date__c = : TDay];
    //system.assertEquals(1, mcaList.size());
    //system.assertEquals('有', mcaList[0].Asset__r.Service_Agreement__c);
    ////system.assertEquals(testCrtB.Id , mcaList[0].Asset__r.CurrentContract__c, '合同未改换为B');
  }
  //对当前目标Assert进行维修合同的更新
  static testMethod void startAssertIDTest() {
    // TO DO: implement unit test

    Asset testAst = createAsset();
    Date TDay = Date.today();
    Date YesDay = Date.today().addDays(-1);
    Maintenance_Contract__c testCrt = creatContract(testAst, TDay.addDays(30), TDay, 'crt');
    creatMca(testAst, testCrt);
    Maintenance_Contract_Asset__c mca = [select id, Asset__c, Maintenance_Contract__c, Maintenance_Contract__r.Contract_End_Date__c,
                                         Maintenance_Contract__r.Contract_Start_Date__c,
                                         Asset__r.Service_Agreement__c, Asset__r.CurrentContract__c,
                                         Asset__r.CurrentContract_End_Date__c
                                         from Maintenance_Contract_Asset__c
                                         where Asset__c = : testAst.id];

    //system.assertEquals('無', mca.Asset__r.Service_Agreement__c);
    //system.assertEquals(null, mca.Asset__r.CurrentContract__c);
    System.runAs(new User(Id = Userinfo.getUserId())) {
      system.Test.StartTest();
      StaticParameter.EventOpportunityPileUpExeFlg = true;
      // EventC⇒Event処理後、逆更新をスルー用
      StaticParameter.NotUpdEventCFlg = true;
      // 積み上げ処理後、トリガをスルー用
      StaticParameter.EscapeNFM001Trigger = true;
      StaticParameter.EscapeNFM001AgencyContractTrigger = true;
      StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
      StaticParameter.EscapeNFM007Trigger = true;
      StaticParameter.EscapeOpportunityBefUpdTrigger = true;
      StaticParameter.EscapeOpportunityHpDeptUpdTrigger = true;
      StaticParameter.EscapeSyncOpportunityTrigger = true;
      StaticParameter.EscapeMaintenanceContractAfterUpdateTrigger = true;
      StaticParameter.EscapeSyncProduct2Trigger = true;
      // trueのとき、リードのトリガーをスルーする
      StaticParameter.EscapeLeadTrigger = true;
      StaticParameter.EscapeAccountTrigger = true;

      StaticParameter.EscapeConsumableOrderDetail2Trigger = true;

      StaticParameter.EscapeNFM010UpsertStatuAchievementsTrigger = true;
      // 直销耗材不直销 ConsumableAssetHander
      StaticParameter.ConsumableAssetHanderTrigger = true;

      // 跳过不涉及备品借出的逻辑
      StaticParameter.rentalApplyIsRunning = true;

      // accountdailyUpdate 跳过 更新询价
      StaticParameter.EscapeOpportunityownerUpdate  = true;
      UpdateAssetToCurrentMCBatch batchTest = new UpdateAssetToCurrentMCBatch(testAst.id);
      Database.executeBatch(batchTest);
      System.Test.StopTest();
    }
    //batchTest.execute( null, mcaList);
    mca = [select id, Asset__c, Maintenance_Contract__c, Maintenance_Contract__r.Contract_End_Date__c,
           Maintenance_Contract__r.Contract_Start_Date__c,
           Asset__r.Service_Agreement__c, Asset__r.CurrentContract__c,
           Asset__r.CurrentContract_End_Date__c
           from Maintenance_Contract_Asset__c
           where Asset__c = : testAst.id];

    //system.assertEquals('有', mca.Asset__r.Service_Agreement__c);
    //system.assertEquals(testCrt.Id , mca.Asset__r.CurrentContract__c);
  }
  static testMethod void test1() {
    UpdateAssetToCurrentMCBatch.test1();
  }
  
}