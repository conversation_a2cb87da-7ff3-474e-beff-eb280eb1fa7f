global class UpAccountProLimit {
    webservice static String upAccount(String accountId,String productLimit,String userPro_Type){
        //--------AddStart-----XHL--------------********-------------
        Boolean EngFlag = false;
        Boolean ETFlag = false; 
        String userPro_Typestr = null; 
        if(String.isBlank(userPro_Type)){
            userPro_Type = 'ET';
        }
        if(userPro_Type == 'ENG'){
            EngFlag = true;
        }else{
            ETFlag = true;
        }
        //ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,'userPro_Type:'+ userPro_Type));
        //ApexPages.message(ApexPages.severity.ERROR,'userPro_Type:'+ userPro_Type));
        userPro_Typestr = '%' + userPro_Type + '%';
        //--------AddEnd-----XHL--------------********------------- 
        system.debug('userPro_Type' + userPro_Type);
        String checkResoultstr =  checkResoult(productLimit);
        if(String.isNotBlank(checkResoultstr)){
            return checkResoultstr;
        }
        //String[] proidList =new String[]{};
        String[] proList =new String[]{};
        String[] pro_List =new String[]{};
        List<Account> acc = [
            SELECT
                    Id,
                    Name,
                    Product_Limit_Date__c
            FROM
                    Account
            WHERE
                    Id = : accountId
            FOR UPDATE
        ];
        if(null == acc || acc.size() == 0) {
            return '没有经销商：' + accountId + '的数据。';
        }
        //proidList = acc[0].view_product__c.split(',');
        //--------UpdateStart-----XHL--------------********-------------
        String sql = '';
        // List<Product2__c> roduct2List = [SELECT Asset_Model_No__c
        //                                   FROM Product2__c  
        //                                   WHERE Product_Type__c like :userPro_Typestr];
        List<Product2__c> roduct2List = [SELECT Asset_Model_No__c,Estimation_Entry_Possibility__c
        FROM Product2__c  
        WHERE Product_Type__c like :userPro_Typestr and Estimation_Entry_Possibility__c = '○'];//Add  by WangXueqin ********
        //ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,'roduct2List:'+ roduct2List));
        //sql = 'SELECT Asset_Model_No__c '
        //    + ' FROM Product2__c '
        //    + ' WHERE Pro2_Dealer_Object__c = true';
        //if(EngFlag){
        //   sql += ' AND Pro2_Dealer_ENG__c =' + EngFlag ;
        //}else if(ETFlag){
        //   sql += ' AND Pro2_Dealer_Object__c = ' + ETFlag; 
        //}
        //system.debug('sqlZZZZZZ' + sql);
        //List<Product2__c> roduct2List = Database.query(sql); 
        //--------UpdateEnd-----XHL--------------********-------------
        Map<String, String> productMap = new Map<String, String>();
        proList = productLimit.split(',');
        system.debug('productLimitDDDDD' + productLimit);
        system.debug('proListFFFFF' + proList);
        if(proList.size() > 0 &&  String.isNotEmpty(proList[0])){
            for(Integer i = 0; i < proList.size(); i++){
                pro_List.add(proList[i].subString(0,proList[i].indexOf( '|')));
            }
        }
        if(roduct2List.size() > 0){
            for(Product2__c pro2 :roduct2List){
                productMap.put(pro2.Asset_Model_No__c, pro2.Asset_Model_No__c);
            }
        }
        for(Integer i = 0; i < pro_List.size(); i++){
            if(!productMap.containsKey(pro_List[i])){
                return '经销商没有 ' + pro_List[i] + ' 的产品上限设定权限，或产品不存在！';
            }
        }
        String  productLimitDa = '';
        for(String str : productLimit.split(',')){
            
            if(str != null && str != ''){
                productLimitDa += ',' + str;
            }
        } 
        Savepoint sp = Database.setSavepoint();
        try{
            Account accinfo = new Account();
            accinfo.Id = accountId;
            if(EngFlag){
               accinfo.Product_Limit_DateENG__c = String.isBlank(productLimitDa) ? productLimitDa : productLimitDa.subString(1); 
            }else if(ETFlag){
               accinfo.Product_Limit_Date__c = String.isBlank(productLimitDa) ? productLimitDa : productLimitDa.subString(1);
            }
            
            UPDATE accinfo;
            return '';
        }catch(DmlException de){ 
            Database.rollback(sp);
            throw de;
        } catch (Exception e) {
            Database.rollback(sp);
            throw e;
        }
    }

    webservice static String checkResoult (String productLimit){
        String[] proLimit = productLimit.split(',');
        String nowName = null, nowRightAsstModelNo = null;
        List<String> asstModelNo = new List<String>();
        Map<String,String> asstModelNoMap = new Map<String,String>();
        List<String> asstModelNoCount = new List<String>();
        for(Integer i = 0; i < proLimit.size(); i++){
            nowName = proLimit[i];
            if( nowName.indexOf( '|') >= 0) {
                nowRightAsstModelNo = nowName.subString( 0, nowName.indexOf( '|'));
            }
            asstModelNo.add(nowRightAsstModelNo);
        }

        for(Integer i = 0; i < asstModelNo.size(); i++){
            if(asstModelNoMap.containsKey(asstModelNo[i])){
                asstModelNoCount.add(asstModelNo[i]);
            }else{
                asstModelNoMap.put(asstModelNo[i], asstModelNo[i]);
            }
        }
        if(asstModelNoCount.size() > 0){
            return '产品：' + asstModelNoCount + '的数据重复。';
        }else{
            return '';
        }
    }
}