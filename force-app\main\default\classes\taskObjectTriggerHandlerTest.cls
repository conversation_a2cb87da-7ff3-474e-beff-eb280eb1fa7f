@isTest
public with sharing class taskObjectTriggerHandlerTest {

    @testSetup
    static void makeTestRepair() {
        
    }
	static testMethod void testMethod8() {
		RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
		List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName IN ('Department_GI') order by DeveloperName desc];

		Account acc = new Account();
		acc.RecordTypeId = rectCo.Id;
		acc.Name = 'HP test1';

		insert acc;

		List<Account> dept = [select Id, Name from Account where ParentId = :acc.Id and Department_Class_Label__c IN ('消化科') order by Department_Class_Label__c];

		Account depart1 = new Account();
		depart1.RecordTypeId = rectDpt[0].Id;
		depart1.Name         = '*';
		depart1.Department_Name__c  = 'Gastoro Intestin Test';
		depart1.ParentId            = dept[0].Id;
		depart1.Department_Class__c = dept[0].Id;
		depart1.Hospital__c         = acc.Id;

		Account depart2 = new Account();
		depart2.RecordTypeId = rectDpt[0].Id;
		depart2.Name         = '*';
		depart2.Department_Name__c  = '診療科2';
		depart2.ParentId            = dept[0].Id;
		depart2.Department_Class__c = dept[0].Id;
		depart2.Hospital__c         = acc.Id;
		insert new Account[] {depart1, depart2};


        String timenow = Datetime.now().format('yyyyMMddHHmmss');
        //FirstName = 'TestMaoF',
		User thisUser= [select Id from User where Id=:UserInfo.getUserId()];
        System.runAs (thisUser){
            User hpOwner2 = new User(Test_staff__c = true, LastName = 'TestMao',
                    Alias = 'hp', CommunityNickname = 'TestMao', Email = '<EMAIL>',
                    Username = 'Test' + timenow + '@sunbridge.com', IsActive = true, EmailEncodingKey = 'ISO-2022-JP', 
                    TimeZoneSidKey = 'Asia/Tokyo', LocaleSidKey = 'ja_JP', LanguageLocaleKey = 'ja', ProfileId = System.Label.ProfileId_SystemAdmin,
                    Dept__c = '医疗华北营业本部', Province__c = '北京',Batch_User__c = true,SalesManager__c=UserInfo.getUserId(),BuchangApprovalManagerSales__c=UserInfo.getUserId(),ZongjianApprovalManager__c=UserInfo.getUserId(),TongkuoZongjian__c=UserInfo.getUserId(),JingliEquipmentManager__c=UserInfo.getUserId(),Buzhang_Equipment_Manager__c=UserInfo.getUserId(),JingliApprovalManager__c=UserInfo.getUserId(),BuchangApprovalManager__c=UserInfo.getUserId());
            insert hpOwner2;
        
            User hpOwner = new User(Test_staff__c = true, LastName = 'TestMao1',
                    Alias = 'hp1', CommunityNickname = 'TestMao1', Email = '<EMAIL>',
                    Username = 'Test1' + timenow + '@sunbridge.com', IsActive = true, EmailEncodingKey = 'ISO-2022-JP', 
                    TimeZoneSidKey = 'Asia/Tokyo', LocaleSidKey = 'ja_JP', LanguageLocaleKey = 'ja', ProfileId = System.Label.ProfileId_SystemAdmin,
                    Dept__c = '医疗华北营业本部', Province__c = '北京',Batch_User__c = true,SalesManager__c=hpOwner2.id,BuchangApprovalManagerSales__c=hpOwner2.id,ZongjianApprovalManager__c=hpOwner2.id,TongkuoZongjian__c=hpOwner2.id,JingliEquipmentManager__c=hpOwner2.id,Buzhang_Equipment_Manager__c=hpOwner2.id,JingliApprovalManager__c=hpOwner2.id,BuchangApprovalManager__c=hpOwner2.id);
            insert hpOwner;
            Task__c task = new Task__c();
            task.Account__c = depart2.id;
            task.Name = '11111';
            task.taskStatus__c = '01 分配';
            task.assignee__c = UserInfo.getUserId();
        
        //  2021-10-22  mzy  任务管理改善   start
            Task__c task2 = new Task__c();
            task2.Account__c = depart2.id;
            task2.Name = '22222';
            task2.taskStatus__c = '01 分配';
            task2.TaskDifferent__c = '上级分配任务';
            task2.assignee__c = UserInfo.getUserId();
    
            Task__c task3 = new Task__c();
            task3.Account__c = depart2.id;
            task3.Name = '3333';
            task3.taskStatus__c = '04 取消';
            task3.assignee__c = UserInfo.getUserId();
            
            
            Task__c task4 = new Task__c();
            //task4.Account__c = depart2.id;
            task4.Name = '3333';
            task4.taskStatus__c = '02 接受';
            task4.TaskDifferent__c = '被动任务';
            task4.assignee__c = hpOwner.id;
            task4.taskTypeFlow__c = '中标结果确认';
            task4.mergeTaskMain__c=null;
            task4.recordtypeId='012C80000000NJmIAM';
            
            Infrastructure_Project__c project = new Infrastructure_Project__c();
            project.Name = 'Test';
            project.Province__c = '北京市';
            project.City__c = '北京市';
            project.OCSM_Hospital__c = acc.Id;
            project.OwnerId=hpOwner.id;
            insert project;
            Task__c task5 = new Task__c();
            //task5.Account__c = depart2.id;
            task5.Name = '3333';
            task5.taskStatus__c = '01 分配';
            task5.taskTypeFlow__c = '基建项目跟进';
            task5.TaskDifferent__c = '上级分配任务';
            task5.assignee__c = hpOwner.id;
            task5.mergeTaskMain__c=null;
			
            // task5.recordtypeId='012C50000001O57IAE';
            task5.recordtypeId='012C80000006hWvIAI';
            task5.Infrastructure_Project__c=project.id;
            Task__c task6 = new Task__c();
            //task4.Account__c = depart2.id;
            task6.Name = '666';
            task6.taskStatus__c = '01 分配';
            task6.TaskDifferent__c = '被动任务';
            task6.assignee__c = hpOwner.id;
            task6.taskTypeFlow__c = '盘点检查计划';
            task6.mergeTaskMain__c=null;
            task6.recordtypeId='012C80000000NJZIA2';
            Task__c task7 = new Task__c();
            //task5.Account__c = depart2.id;
            task7.Name = '3333';
            task7.taskStatus__c = '01 分配';
            task7.taskTypeFlow__c = '多年保修任务';
            task7.TaskDifferent__c = '被动任务';
            task7.assignee__c = hpOwner.id;
            task7.mergeTaskMain__c=null;
            task7.recordtypeId='012C80000000NJkIAM';   

            // Task__c task8 = new Task__c();
            // //task5.Account__c = depart2.id;
            // task8.Name = '777';
            // task8.taskStatus__c = '01 分配';
            // task8.taskTypeFlow__c = 'SLA报告书任务';
            // task8.TaskDifferent__c = '被动任务';
            // task8.assignee__c = hpOwner.id;
            // task8.mergeTaskMain__c=null;
            // task8.recordtypeId='012C80000000NJgIAM';
            

            insert new Task__c[]{task,task2,task3,task4,task5,task6,task7};
        }
     //  2021-10-22   mzy  任务管理改善  end
	}
    
    // 消化科
    static testMethod void testMethod1() {
        test.startTest();

        // List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        // if (rectCo.size() == 0) {
        //     throw new ControllerUtil.myException('not found 病院 recodetype');
        // }
        // List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 消化科'];
        // if (rectSct.size() == 0) {
        //     throw new ControllerUtil.myException('not found 戦略科室分類 消化科 recodetype');
        // }
        // List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 消化科'];
        // if (rectDpt.size() == 0) {
        //     throw new ControllerUtil.myException('not found 診療科 消化科 recodetype');
        // }
        String rectCo = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('HP').getRecordTypeId();
        String rectSct = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_GI').getRecordTypeId();
        String rectDpt = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_GI').getRecordTypeId();
        User u = [SELECT id,Alias FROM User WHERE Id = :UserInfo.getUserId()];
        // テストデータ
        Account company = new Account();
        company.RecordTypeId = rectCo;
        company.Name         = 'NFM105TestCompany';
        company.OwnerId      = UserInfo.getUserId();
        company.GI_Main__c = UserInfo.getUserId();
        company.GI_Product_Leader__c = u.Alias +','+u.Alias;
        insert company;
        // Account section = [Select Management_Code__c, Management_Code_Auto__c, Name, Id,Department_Class_Label__c from Account where ParentId = :company.Id and RecordTypeId = :rectSct[0].Id];
        Account section = [Select Management_Code__c, Name, Id,Department_Class_Label__c from Account where ParentId = :company.Id and RecordTypeId = :rectSct limit 1];


        Account depart = new Account();
        depart.RecordTypeId        = rectDpt;
        depart.Name                = '*';
        depart.Department_Name__c  = 'NFM105TestDepart';
        depart.ParentId            = section.Id;
        depart.Department_Class__c = section.Id;
        depart.Hospital__c         = company.Id;
        depart.OwnerId      = UserInfo.getUserId();
        upsert depart;

        Opportunity opp1 = new Opportunity(Name='aiueo1', StageName='引合', CurrencyIsoCode='CNY', CloseDate=Date.today());
        insert opp1;
        // ------------ 
        
        task__c tsk = new task__c();
        tsk.name = '*';
        tsk.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('OPD').getRecordTypeId();
        tsk.distributionCount__c = 1;
        tsk.taskDifferent__c = '被动任务';
        tsk.taskStatus__c = '01 分配';
        tsk.account__c = depart.id;
        tsk.assignee__c =  UserInfo.getUserId();
        tsk.OpportunityId__c = opp1.Id;
        insert tsk;

        tsk.taskStatus__c = '02 接受';
        update tsk;

        task__c tsk2 = new task__c();
        tsk2.name = '*';
        tsk2.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('OPD').getRecordTypeId();
        tsk2.distributionCount__c = 1;
        tsk2.taskDifferent__c = '上级分配任务';
        tsk2.taskStatus__c = '01 分配';
        tsk2.account__c = depart.id;
        tsk2.assignee__c =  UserInfo.getUserId();
        insert tsk2;

        task__c tsk3 = new task__c();
        tsk3.name = '123';
        tsk3.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('Other').getRecordTypeId();
        tsk3.distributionCount__c = 1;
        tsk3.taskDifferent__c = '上级分配任务';
        tsk3.taskStatus__c = '01 分配';
        tsk3.assignee__c =  UserInfo.getUserId();
        tsk3.OpportunityId__c = opp1.Id;
        insert tsk3;
        test.stopTest();
    }

    //呼吸科
    static testMethod void testMethod2() {
        test.startTest();

        // List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        // if (rectCo.size() == 0) {
        //     throw new ControllerUtil.myException('not found 病院 recodetype');
        // }
        // List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 呼吸科'];
        // if (rectSct.size() == 0) {
        //     throw new ControllerUtil.myException('not found 戦略科室分類 呼吸科 recodetype');
        // }
        // List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 呼吸科'];
        // if (rectDpt.size() == 0) {
        //     throw new ControllerUtil.myException('not found 診療科 呼吸科 recodetype');
        // }
        String rectCo = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('HP').getRecordTypeId();
        String rectSct = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_BF').getRecordTypeId();
        String rectDpt = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_BF').getRecordTypeId();
        User u = [SELECT id,Alias FROM User WHERE Id = :UserInfo.getUserId()];
        // テストデータ
        Account company = new Account();
        company.RecordTypeId = rectCo;
        company.Name         = 'NFM105TestCompany';
        company.OwnerId      = UserInfo.getUserId();
        company.BF_owner__c = UserInfo.getUserId();
        company.BF_Product_Leader__c = u.Alias +','+u.Alias;
        upsert company;
        // Account section = [Select Management_Code__c, Management_Code_Auto__c, Name, Id,Department_Class_Label__c from Account where ParentId = :company.Id and RecordTypeId = :rectSct[0].Id];
        Account section = [Select Management_Code__c, Name, Id,Department_Class_Label__c from Account where ParentId = :company.Id and RecordTypeId = :rectSct limit 1];


        Account depart = new Account();
        depart.RecordTypeId        = rectDpt;
        depart.Name                = '*';
        depart.Department_Name__c  = 'NFM105TestDepart';
        depart.ParentId            = section.Id;
        depart.Department_Class__c = section.Id;
        depart.Hospital__c         = company.Id;
        depart.OwnerId      = UserInfo.getUserId();
        upsert depart;

        // ------------ 
        
        task__c tsk = new task__c();
        tsk.name = '*';
        tsk.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('OPD').getRecordTypeId();
        tsk.distributionCount__c = 1;
        tsk.taskDifferent__c = '被动任务';
        tsk.taskStatus__c = '01 分配';
        tsk.account__c = depart.id;
        tsk.assignee__c =  UserInfo.getUserId();
        insert tsk;

        tsk.taskStatus__c = '02 接受';
        update tsk;

        task__c tsk2 = new task__c();
        tsk2.name = '*';
        tsk2.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('OPD').getRecordTypeId();
        tsk2.distributionCount__c = 1;
        tsk2.taskDifferent__c = '上级分配任务';
        tsk2.taskStatus__c = '01 分配';
        tsk2.account__c = depart.id;
        tsk2.assignee__c =  UserInfo.getUserId();
        insert tsk2;
        test.stopTest();
    }

    //普外科
    static testMethod void testMethod3() {
        test.startTest();

        // List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        // if (rectCo.size() == 0) {
        //     throw new ControllerUtil.myException('not found 病院 recodetype');
        // }
        // List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 普外科'];
        // if (rectSct.size() == 0) {
        //     throw new ControllerUtil.myException('not found 戦略科室分類 普外科 recodetype');
        // }
        // List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 普外科'];
        // if (rectDpt.size() == 0) {
        //     throw new ControllerUtil.myException('not found 診療科 普外科 recodetype');
        // }
        String rectCo = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('HP').getRecordTypeId();
        String rectSct = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_GS').getRecordTypeId();
        String rectDpt = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_GS').getRecordTypeId();
        User u = [SELECT id,Alias FROM User WHERE Id = :UserInfo.getUserId()];
        // テストデータ
        Account company = new Account();
        company.RecordTypeId = rectCo;
        company.Name         = 'NFM105TestCompany';
        company.OwnerId      = UserInfo.getUserId();
        company.SP_Main__c = UserInfo.getUserId();
        company.GS_Product_Leader__c = u.Alias +','+u.Alias;
        upsert company;
        // Account section = [Select Management_Code__c, Management_Code_Auto__c, Name, Id,Department_Class_Label__c from Account where ParentId = :company.Id and RecordTypeId = :rectSct[0].Id];
        Account section = [Select Management_Code__c, Name, Id,Department_Class_Label__c from Account where ParentId = :company.Id and RecordTypeId = :rectSct limit 1];


        Account depart = new Account();
        depart.RecordTypeId        = rectDpt;
        depart.Name                = '*';
        depart.Department_Name__c  = 'NFM105TestDepart';
        depart.ParentId            = section.Id;
        depart.Department_Class__c = section.Id;
        depart.Hospital__c         = company.Id;
        depart.OwnerId      = UserInfo.getUserId();
        upsert depart;

        // ------------ 
        
        task__c tsk = new task__c();
        tsk.name = '*';
        tsk.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('OPD').getRecordTypeId();
        tsk.distributionCount__c = 1;
        tsk.taskDifferent__c = '被动任务';
        tsk.taskStatus__c = '01 分配';
        tsk.account__c = depart.id;
        tsk.assignee__c =  UserInfo.getUserId();
        insert tsk;

        tsk.taskStatus__c = '02 接受';
        update tsk;

        task__c tsk2 = new task__c();
        tsk2.name = '*';
        tsk2.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('OPD').getRecordTypeId();
        tsk2.distributionCount__c = 1;
        tsk2.taskDifferent__c = '上级分配任务';
        tsk2.taskStatus__c = '01 分配';
        tsk2.account__c = depart.id;
        tsk2.assignee__c =  UserInfo.getUserId();
        insert tsk2;
        test.stopTest();
    }

     //泌尿科
     static testMethod void testMethod4() {
        test.startTest();

        // List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        // if (rectCo.size() == 0) {
        //     throw new ControllerUtil.myException('not found 病院 recodetype');
        // }
        // List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 泌尿科'];
        // if (rectSct.size() == 0) {
        //     throw new ControllerUtil.myException('not found 戦略科室分類 泌尿科 recodetype');
        // }
        // List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 泌尿科'];
        // if (rectDpt.size() == 0) {
        //     throw new ControllerUtil.myException('not found 診療科 泌尿科 recodetype');
        // }
        String rectCo = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('HP').getRecordTypeId();
        String rectSct = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_URO').getRecordTypeId();
        String rectDpt = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_URO').getRecordTypeId();
        User u = [SELECT id,Alias FROM User WHERE Id = :UserInfo.getUserId()];
        // テストデータ
        Account company = new Account();
        company.RecordTypeId = rectCo;
        company.Name         = 'NFM105TestCompany';
        company.OwnerId      = UserInfo.getUserId();
        company.URO_owner_ID__c = UserInfo.getUserId();
        company.URO_Produc_Leader__c = u.Alias +','+u.Alias;
        upsert company;
        // Account section = [Select Management_Code__c, Management_Code_Auto__c, Name, Id,Department_Class_Label__c from Account where ParentId = :company.Id and RecordTypeId = :rectSct[0].Id];
        Account section = [Select Management_Code__c, Name, Id,Department_Class_Label__c from Account where ParentId = :company.Id and RecordTypeId = :rectSct limit 1];


        Account depart = new Account();
        depart.RecordTypeId        = rectDpt;
        depart.Name                = '*';
        depart.Department_Name__c  = 'NFM105TestDepart';
        depart.ParentId            = section.Id;
        depart.Department_Class__c = section.Id;
        depart.Hospital__c         = company.Id;
        depart.OwnerId      = UserInfo.getUserId();
        upsert depart;

        // ------------ 
        
        task__c tsk = new task__c();
        tsk.name = '*';
        tsk.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('OPD').getRecordTypeId();
        tsk.distributionCount__c = 1;
        tsk.taskDifferent__c = '被动任务';
        tsk.taskStatus__c = '01 分配';
        tsk.account__c = depart.id;
        tsk.assignee__c =  UserInfo.getUserId();
        insert tsk;

        tsk.taskStatus__c = '02 接受';
        update tsk;

        task__c tsk2 = new task__c();
        tsk2.name = '*';
        tsk2.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('OPD').getRecordTypeId();
        tsk2.distributionCount__c = 1;
        tsk2.taskDifferent__c = '上级分配任务';
        tsk2.taskStatus__c = '01 分配';
        tsk2.account__c = depart.id;
        tsk2.assignee__c =  UserInfo.getUserId();
        insert tsk2;
        test.stopTest();
    }

    //婦人科
    static testMethod void testMethod5() {
        test.startTest();

        // List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        // if (rectCo.size() == 0) {
        //     throw new ControllerUtil.myException('not found 病院 recodetype');
        // }
        // List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 婦人科'];
        // if (rectSct.size() == 0) {
        //     throw new ControllerUtil.myException('not found 戦略科室分類 婦人科 recodetype');
        // }
        // List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 婦人科'];
        // if (rectDpt.size() == 0) {
        //     throw new ControllerUtil.myException('not found 診療科 婦人科 recodetype');
        // }
        String rectCo = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('HP').getRecordTypeId();
        String rectSct = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_GYN').getRecordTypeId();
        String rectDpt = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_GYN').getRecordTypeId();
        User u = [SELECT id,Alias FROM User WHERE Id = :UserInfo.getUserId()];
        // テストデータ
        Account company = new Account();
        company.RecordTypeId = rectCo;
        company.Name         = 'NFM105TestCompany';
        company.OwnerId      = UserInfo.getUserId();
        company.GYN_owner__c = UserInfo.getUserId();
        company.GYN_Product_Leader__c = u.Alias +','+u.Alias;
        upsert company;
        // Account section = [Select Management_Code__c, Management_Code_Auto__c, Name, Id,Department_Class_Label__c from Account where ParentId = :company.Id and RecordTypeId = :rectSct[0].Id];
        Account section = [Select Management_Code__c, Name, Id,Department_Class_Label__c from Account where ParentId = :company.Id and RecordTypeId = :rectSct limit 1];


        Account depart = new Account();
        depart.RecordTypeId        = rectDpt;
        depart.Name                = '*';
        depart.Department_Name__c  = 'NFM105TestDepart';
        depart.ParentId            = section.Id;
        depart.Department_Class__c = section.Id;
        depart.Hospital__c         = company.Id;
        depart.OwnerId      = UserInfo.getUserId();
        upsert depart;

        // ------------ 
        
        task__c tsk = new task__c();
        tsk.name = '*';
        tsk.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('OPD').getRecordTypeId();
        tsk.distributionCount__c = 1;
        tsk.taskDifferent__c = '被动任务';
        tsk.taskStatus__c = '01 分配';
        tsk.account__c = depart.id;
        tsk.assignee__c =  UserInfo.getUserId();
        insert tsk;

        tsk.taskStatus__c = '02 接受';
        update tsk;

        task__c tsk2 = new task__c();
        tsk2.name = '*';
        tsk2.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('OPD').getRecordTypeId();
        tsk2.distributionCount__c = 1;
        tsk2.taskDifferent__c = '上级分配任务';
        tsk2.taskStatus__c = '01 分配';
        tsk2.account__c = depart.id;
        tsk2.assignee__c =  UserInfo.getUserId();
        insert tsk2;
        test.stopTest();
    }

     //耳鼻喉科
     static testMethod void testMethod6() {
        test.startTest();

        // List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        // if (rectCo.size() == 0) {
        //     throw new ControllerUtil.myException('not found 病院 recodetype');
        // }
        // List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 耳鼻喉科'];
        // if (rectSct.size() == 0) {
        //     throw new ControllerUtil.myException('not found 戦略科室分類 耳鼻喉科 recodetype');
        // }
        // List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 耳鼻喉科'];
        // if (rectDpt.size() == 0) {
        //     throw new ControllerUtil.myException('not found 診療科 耳鼻喉科 recodetype');
        // }
        String rectCo = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('HP').getRecordTypeId();
        String rectSct = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_ENT').getRecordTypeId();
        String rectDpt = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_ENT').getRecordTypeId();
        User u = [SELECT id,Alias FROM User WHERE Id = :UserInfo.getUserId()];
        // テストデータ
        Account company = new Account();
        company.RecordTypeId = rectCo;
        company.Name         = 'NFM105TestCompany';
        company.OwnerId      = UserInfo.getUserId();
        company.ENT_owner_ID__c = UserInfo.getUserId();
        company.ENT_Product_Leader__c = u.Alias +','+u.Alias;
        upsert company;
        // Account section = [Select Management_Code__c, Management_Code_Auto__c, Name, Id,Department_Class_Label__c from Account where ParentId = :company.Id and RecordTypeId = :rectSct[0].Id];
        Account section = [Select Management_Code__c, Name, Id,Department_Class_Label__c from Account where ParentId = :company.Id and RecordTypeId = :rectSct limit 1];


        Account depart = new Account();
        depart.RecordTypeId        = rectDpt;
        depart.Name                = '*';
        depart.Department_Name__c  = 'NFM105TestDepart';
        depart.ParentId            = section.Id;
        depart.Department_Class__c = section.Id;
        depart.Hospital__c         = company.Id;
        depart.OwnerId      = UserInfo.getUserId();
        upsert depart;

        // ------------ 
        
        task__c tsk = new task__c();
        tsk.name = '*';
        tsk.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('OPD').getRecordTypeId();
        tsk.distributionCount__c = 1;
        tsk.taskDifferent__c = '被动任务';
        tsk.taskStatus__c = '01 分配';
        tsk.account__c = depart.id;
        tsk.assignee__c =  UserInfo.getUserId();
        insert tsk;

        tsk.taskStatus__c = '02 接受';
        update tsk;

        task__c tsk2 = new task__c();
        tsk2.name = '*';
        tsk2.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('OPD').getRecordTypeId();
        tsk2.distributionCount__c = 1;
        tsk2.taskDifferent__c = '上级分配任务';
        tsk2.taskStatus__c = '01 分配';
        tsk2.account__c = depart.id;
        tsk2.assignee__c =  UserInfo.getUserId();
        insert tsk2;
        test.stopTest();
    }  
    //基建项目
    static testMethod void testMethod7() {
        test.startTest();
        String rectCo = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('HP').getRecordTypeId();
        String rectSct = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_Class_ENT').getRecordTypeId();
        String rectDpt = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Department_ENT').getRecordTypeId();
        User u = [SELECT id,Alias FROM User WHERE Id = :UserInfo.getUserId()];
        // テストデータ
        Account company = new Account();
        company.RecordTypeId = rectCo;
        company.Name         = 'NFM105TestCompany';
        company.OwnerId      = UserInfo.getUserId();
        company.ENT_owner_ID__c = UserInfo.getUserId();
        company.ENT_Product_Leader__c = u.Alias +','+u.Alias;
        upsert company;
        // Account section = [Select Management_Code__c, Management_Code_Auto__c, Name, Id,Department_Class_Label__c from Account where ParentId = :company.Id and RecordTypeId = :rectSct[0].Id];
        Account section = [Select Management_Code__c, Name, Id,Department_Class_Label__c from Account where ParentId = :company.Id and RecordTypeId = :rectSct limit 1];


        Account depart = new Account();
        depart.RecordTypeId        = rectDpt;
        depart.Name                = '*';
        depart.Department_Name__c  = 'NFM105TestDepart';
        depart.ParentId            = section.Id;
        depart.Department_Class__c = section.Id;
        depart.Hospital__c         = company.Id;
        depart.OwnerId      = UserInfo.getUserId();
        upsert depart;

        Infrastructure_Project__c jijian = new Infrastructure_Project__c();
        jijian.Name = 'ceshi jijian ';
        jijian.OCSM_Hospital__c = depart.Id;
        jijian.IfFollow__c = 1;
        jijian.RelatedInquiryDate__c = Date.today();
        jijian.Types__c = '新';
        jijian.Province__c = '浙江省';
        jijian.Node_Options__c = '验收运营';
        insert jijian;

        task__c tsk2 = new task__c();
        tsk2.name = '*';
        tsk2.recordtypeId = Schema.SObjectType.task__c.getRecordTypeInfosByDeveloperName().get('InfrastructureProjectFollow').getRecordTypeId();
        tsk2.distributionCount__c = 1;
        tsk2.taskDifferent__c = '上级分配任务';
        tsk2.taskStatus__c = '01 分配';
        tsk2.Infrastructure_Project__c = jijian.id;
        tsk2.Reason_for_allocation_option__c = '1、需新建询价';
        tsk2.account__c = depart.id;
        tsk2.assignee__c =  UserInfo.getUserId();
        //insert tsk2;
        test.stopTest();
    }   
}