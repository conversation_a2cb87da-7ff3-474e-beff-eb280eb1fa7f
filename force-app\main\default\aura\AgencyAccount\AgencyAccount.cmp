<aura:component controller="AgencyAccountCmp" implements="forceCommunity:availableForAllPageTypes" access="global" >
	<aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
	<aura:attribute name="login" type="Boolean" default="false"/>
	<aura:attribute name="accounts" type="List"/>
	<aura:attribute name="hosStr" type="String"/>
	<aura:attribute name="conStr" type="String"/>
	<aura:attribute name="filedsmap" type="Map"/>
	<aura:attribute name="awsdata" type="Map"/>
	<!-- deloitte-zhj ******** PIPL还原 -->
	<!-- <aura:attribute name="temp_aws_id" type="String"/>
	<aura:attribute name="staticResource" type="Map"/> -->
	
	<ltng:require scripts="{! $Resource.jquery183minjs }" />
	<!-- deloitte-zhj ******** PIPL还原 -->
	<!-- <ltng:require scripts="{! $Resource.AWSService+'/AWSService.js' }" afterScriptsLoaded="{!c.scriptsLoaded}" /> -->
	<aura:renderIf isTrue="{!v.login}">
		<!-- add by Deloitte-Link 2023-6-19 -->
        <div class="weeklyReportSpinner">
			<div class="slds-spinner_container">
				<div class="slds-spinner--brand slds-spinner slds-spinner--medium" role="alert">
					<span id="aa" class="slds-assistive-text">Loading</span>
					<div class="slds-spinner__dot-a"></div>
					<div class="slds-spinner__dot-b"></div>
				</div>
			</div>
		</div>
    </aura:renderIf>
    <form class="slds-form--inline">
	  <div class="slds-form-element">
	    <label class="slds-form-element__label">{!v.filedsmap.Hospital_DC_Name__c}</label>
	    <div class="slds-form-element__control">
	    	<ui:inputText class="slds-input" value="{!v.hosStr}"/>
	    </div>
	  </div>
	  <div class="slds-form-element">
	    <label class="slds-form-element__label">{!v.filedsmap.Name}</label>
	    <div class="slds-form-element__control">
	    	<ui:inputText class="slds-input" value="{!v.conStr}"/>
	    </div>
	  </div>
	  <div class="slds-form-element">
	    <button type="button" class="slds-button slds-button--brand" onclick="{!c.searchContact}">检索</button>
	  </div>
	  <div class="slds-form-element">
	    <button type="button" class="slds-button slds-button--brand" onclick="{!c.clearContact}">清除</button>
	  </div>
	</form>
	<div class="contents_wrapper">
    <table class="slds-table slds-table--bordered slds-table--cell-buffer">
	  <thead>
	    <tr class="slds-text-title--caps">
	      <th scope="col">
	        <div class="slds-truncate" title="{!v.filedsmap.Name}">{!v.filedsmap.Name}</div>
	      </th>
	      <!-- 精琢科技  zxk  SWAG-C8F8TU start -->
	      <th scope="col" style = "width:5px;">
	        <div class="slds-truncate" title="{!v.filedsmap.Hospital_Name__c}">{!v.filedsmap.Hospital_Name__c}</div>
	      </th>
	      <th scope="col" style="width: 150px;">
	        <div class="slds-truncate" title="{!v.filedsmap.Department_Cateogy_F__c}">{!v.filedsmap.Department_Cateogy_F__c}</div>
	      </th>
	      <!-- 精琢科技  zxk  SWAG-C8F8TU end -->
	      <!-- <th scope="col">
	        <div class="slds-truncate" title="{!v.filedsmap.Hospital_DC_Name__c}">{!v.filedsmap.Hospital_DC_Name__c}</div>
	      </th> -->
	      <th scope="col" style="width: 150px;">
	        <div class="slds-truncate" title="{!v.filedsmap.Type__c}">{!v.filedsmap.Type__c}</div>
	      </th>
	      <th scope="col" style="width: 150px;">
	        <div class="slds-truncate" title="{!v.filedsmap.Doctor_Division1__c}">{!v.filedsmap.Doctor_Division1__c}</div>
	      </th>
	      <th scope="col">
	        <div class="slds-truncate" title="{!v.filedsmap.IsOlympusContact__c}">{!v.filedsmap.IsOlympusContact__c}</div>
	      </th>
	    </tr>
	  </thead>
	  <tbody>
	  	<aura:iteration items="{!v.accounts}" var="item" indexVar="index">
	    <tr>
	      <th scope="row" data-label="Opportunity Name">
			<!-- deloitte-zhj ******** PIPL还原 -->
			<!-- <aura:set attribute="temp_aws_id" value="{! item.AWS_Data_Id__c }" /> -->
			
	        <div class="slds-truncate">
				<a href="{!'/partner/s/agency-contact/' + item.Id}" style="position: relative;">
					<!-- deloitte-zhj ******** PIPL还原 -->
					<span class="encrypt">{!item.Name}</span>
					<span class="decrypt">{!item.awsdata.name}</span>
				</a>
			</div>
	      </th>
            <!-- 精琢科技  zxk  SWAG-C8F8TU start -->
             <td data-label="Account Name">
	      	<aura:renderIf isTrue="{!item.IsOlympusContact__c == false}">
	        	<div class="slds-truncate" title="{!item.Hospital_Name__c}"><a href="{!'/partner/s/agency-hospital-link/' + item.Agency_Hospital__c}">{!item.Hospital_Name__c}</a></div>
	    	</aura:renderIf>
	    	<aura:renderIf isTrue="{!item.IsOlympusContact__c}">
	    		<div class="slds-truncate" title="{!item.Hospital_Name__c}">{!item.Hospital_Name__c}</div>
	    	</aura:renderIf>
	      </td>
	      <td data-label="Account Name01">
	      	<div class="slds-truncate" title="{!item.Department_Cateogy_F__c}">{!item.Department_Cateogy_F__c}</div>
	      </td>
           <!-- 精琢科技  zxk  SWAG-C8F8TU end -->

	     <!--  <td data-label="Account Name">
	      	<aura:renderIf isTrue="{!item.IsOlympusContact__c == false}">
	        	<div class="slds-truncate" title="{!item.Hospital_DC_Name__c}"><a href="{!'/partner/s/agency-hospital-link/' + item.Agency_Hospital__c}">{!item.Hospital_DC_Name__c}</a></div>
	    	</aura:renderIf>
	    	<aura:renderIf isTrue="{!item.IsOlympusContact__c}">
	    		<div class="slds-truncate" title="{!item.Hospital_DC_Name__c}">{!item.Hospital_DC_Name__c}</div>
	    	</aura:renderIf>
	      </td> -->

	      <td data-label="Close Date">
	        <div class="slds-truncate" title="{!item.Type__c}">
				<span class="encrypt">{!item.Type__c}</span>
				<span class="decrypt">{!item.awsdata.type}</span>
			</div>
	      </td>
	      <td data-label="Stage">
	        <div class="slds-truncate" title="{!item.Doctor_Division1__c}">
				<span class="encrypt">{!item.Doctor_Division1__c}</span>
				<span class="decrypt">{!item.awsdata.doctorDivision1}</span>
			</div>
	      </td>
	      <td data-label="IsOlympusContact__c">
	        <ui:outputCheckbox value="{!item.IsOlympusContact__c}" />
	      </td>
	    </tr>
		</aura:iteration>
	  </tbody>
	</table>
	</div>
        <!-- <aura:renderIf isTrue="{!v.report_count > 0}">
            <aura:set attribute="else">
                <div class="no_data_area">
                    {!v.list_message}
                </div>
            </aura:set>
        </aura:renderIf> -->
</aura:component>