@isTest
private class TransferApplySummaryHandlerTest {
    static private User u;
    static private Id taId;
    static void setupTestData1() {
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        Oly_TriggerHandler.bypass(ContactTriggerHandler.class.getName());
        Oly_TriggerHandler.bypass(AgencyHospitalHandler.class.getName());
        // 省
        Address_Level__c al = new Address_Level__c();
        al.Name = '東京';
        al.Level1_Code__c = 'CN-99';
        al.Level1_Sys_No__c = '999999';
        insert al;
        // 市
        Address_Level2__c al2 = new Address_Level2__c();
        al2.Level1_Code__c = 'CN-99';
        al2.Level1_Sys_No__c = '999999';
        al2.Level1_Name__c = '東京';
        al2.Name = '渋谷区';
        al2.Level2_Code__c = 'CN-9999';
        al2.Level2_Sys_No__c = '9999999';
        al2.Address_Level__c = al.id;
        insert al2;

        // 病院を作る
        Account hospital = new Account();
        hospital.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'HP'].id;
        hospital.Name = 'test hospital';
        hospital.Is_Active__c = '有効';
        hospital.Attribute_Type__c = '卫生部';
        hospital.Speciality_Type__c = '综合医院';
        hospital.Grade__c = '一级';
        hospital.OCM_Category__c = 'SLTV';
        hospital.Is_Medical__c = '医疗机构';
        hospital.State_Master__c = al.id;
        hospital.City_Master__c = al2.id;
        hospital.Town__c = '东京';
        insert hospital;

        StaticParameter.EscapeAccountTrigger = true;
        // 戦略科室を得る
        Account[] strategicDep = [SELECT ID, Name FROM Account WHERE parentId = :hospital.Id AND recordType.DeveloperName = 'Department_Class_OTH'];
        // 診療科を作る
        Account dep = new Account();
        dep.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'Department_OTH'].id;
        dep.Name = 'test dep1';
        dep.AgentCode_Ext__c = System.Label.Account_Asset_FJZ;
        dep.ParentId = strategicDep[0].Id;
        dep.Department_Class__c = strategicDep[0].Id;
        dep.Hospital__c = hospital.Id;

        Account dep1 = new Account();
        dep1.recordtypeId = dep.recordtypeId;
        dep1.Name = 'test dep1';
        dep1.AgentCode_Ext__c = System.Label.Account_Asset;
        dep1.ParentId = strategicDep[0].Id;
        dep1.Department_Class__c = strategicDep[0].Id;
        dep1.Hospital__c = hospital.Id;

        insert new Account[]{dep, dep1};

        // 产品
        Product2 pro1 = new Product2(Name='CLH-250:内窥镜冷光源',IsActive=true,Family='GI',
                Fixture_Model_No__c='CLH-250',Serial_Lot_No__c='S/N tracing',
                Fixture_Model_No_T__c = 'CLH-250', Asset_Model_No__c = 'Pro1',
                ProductCode_Ext__c='4604362',Manual_Entry__c=false);
        Product2 pro2 = new Product2(Name='电源线',IsActive=true,Family='GI',
                Fixture_Model_No__c='电源线',Serial_Lot_No__c='Lot tracing',
                Fixture_Model_No_T__c = '电源线', Asset_Model_No__c = 'Pro2',
                ProductCode_Ext__c='BP900003',Manual_Entry__c=false);
        Product2 pro3 = new Product2(Name='MAJ-1933:数字调光电缆',IsActive=true,Family='GI',
                Fixture_Model_No__c='MAJ-1933',Serial_Lot_No__c='Lot tracing',
                Fixture_Model_No_T__c = 'MAJ-1933', Asset_Model_No__c = 'Pro3',
                ProductCode_Ext__c='N3647100',Manual_Entry__c=false);
        insert new Product2[] {pro1, pro2, pro3};


        // 保有设备
        Asset asset1 = new Asset(Asset_Owner__c = 'Olympus');
        asset1.RecordTypeId = System.Label.Asset_RecordType;
        asset1.SerialNumber = 'T1';
        asset1.Name = 'CLH-250:内窥镜冷光源';
        asset1.AccountId = dep.Id;
        asset1.Department_Class__c = strategicDep[0].Id;
        asset1.Hospital__c = hospital.Id;
        asset1.Product2Id = pro1.Id;
        asset1.Quantity = 1;
        asset1.Status = '使用中';
        asset1.Manage_type__c = '个体管理';
        asset1.Loaner_accsessary__c = false;
        asset1.Out_of_wh__c = 0;
        asset1.Salesdepartment__c = '0.备品中心';
        asset1.Internal_asset_location__c = '北京 备品中心';
        asset1.Product_category__c = 'GI';
        asset1.Equipment_Type__c = '产品试用';
        asset1.SalesProvince__c = '北京';
        asset1.CompanyOfEquipment__c = '北京';
        asset1.Internal_Asset_number__c = '0001';
        asset1.WH_location__c = '货架号1';
        asset1.AssetManageConfirm__c = true;
        asset1.Asset_loaner_category__c = '固定资产';

        Asset asset2 = new Asset(Asset_Owner__c = 'Olympus');
        asset2.RecordTypeId = System.Label.Asset_RecordType;
        asset2.SerialNumber = 'T2';
        asset2.Name = '电源线';
        asset2.AccountId = dep.Id;
        asset2.Department_Class__c = strategicDep[0].Id;
        asset2.Hospital__c = hospital.Id;
        asset2.Product2Id = pro2.Id;
        asset2.Quantity = 1;
        asset2.Status = '使用中';
        asset2.Manage_type__c = '数量管理';
        asset2.Loaner_accsessary__c = true;
        asset2.Out_of_wh__c = 0;
        asset2.Salesdepartment__c = '0.备品中心';
        asset2.Internal_asset_location__c = '北京 备品中心';
        asset2.Product_category__c = 'GI';
        asset2.Equipment_Type__c = '产品试用';
        asset2.SalesProvince__c = '北京';
        asset2.CompanyOfEquipment__c = '北京';
        asset2.Internal_Asset_number__c = '0002';
        asset2.WH_location__c = '货架号2';
        asset2.AssetManageConfirm__c = true;
        asset2.Asset_loaner_category__c = '附属品';

        Asset asset3 = new Asset(Asset_Owner__c = 'Olympus');
        asset3.RecordTypeId = System.Label.Asset_RecordType;
        asset3.SerialNumber = 'UK-19-2401685';
        asset3.Name = 'MAJ-1933:数字调光电缆';
        asset3.AccountId = dep.Id;
        asset3.Department_Class__c = strategicDep[0].Id;
        asset3.Hospital__c = hospital.Id;
        asset3.Product2Id = pro3.Id;
        asset3.Quantity = 5;
        asset3.Status = '使用中';
        asset3.Manage_type__c = '数量管理';
        asset3.Loaner_accsessary__c = true;
        asset3.Out_of_wh__c = 0;
        asset3.Salesdepartment__c = '0.备品中心';
        asset3.Internal_asset_location__c = '北京 备品中心';
        asset3.Product_category__c = 'GI';
        asset3.Equipment_Type__c = '产品试用';
        asset3.SalesProvince__c = '北京';
        asset3.CompanyOfEquipment__c = '北京';
        asset3.Internal_Asset_number__c = '0003';
        asset3.WH_location__c = '货架号3';
        asset3.AssetManageConfirm__c = true;
        asset3.Asset_loaner_category__c = '附属品';

        insert new Asset[] {asset1, asset2, asset3};

        // 备品一对一Link表
        Fixture_OneToOne_Link__c foLink1 = new Fixture_OneToOne_Link__c();
        foLink1.Main_Asset__c = asset1.Id;
        foLink1.Accessory_Asset__c = asset2.Id;
        foLink1.Quantity__c = 1;
        Fixture_OneToOne_Link__c foLink2 = new Fixture_OneToOne_Link__c();
        foLink2.Main_Asset__c = asset1.Id;
        foLink2.Accessory_Asset__c = asset3.Id;
        foLink2.Quantity__c = 1;
        insert new Fixture_OneToOne_Link__c[] {foLink1, foLink2};

        // 备品配套
        Fixture_Set__c fsObj1 = new Fixture_Set__c();
        fsObj1.Name = 'set1';
        fsObj1.Fixture_Set_Body_Model_No__c = 'modelNo1';
        fsObj1.Loaner_name__c = 'name1';
        insert fsObj1;

        // 备品配套明细
        Fixture_Set_Detail__c fsdObjA1 = new Fixture_Set_Detail__c();

        fsdObjA1.Name = '备品配套明细名1';
        fsdObjA1.Name_CHN_Created__c = '中文名称1';
        fsdObjA1.Product2__c = pro1.Id;
        fsdObjA1.Fixture_Set__c = fsObj1.Id;
        fsdObjA1.Is_Body__c = true;
        fsdObjA1.Is_Optional__c = false;
        fsdObjA1.UniqueKey__c = fsObj1.Id + ':' + pro1.Id;
        fsdObjA1.SortInt__c = 1;
        fsdObjA1.Quantity__c = 1;

        Fixture_Set_Detail__c fsdObjA2 = new Fixture_Set_Detail__c();
        fsdObjA2.Name = '备品配套明细名2';
        fsdObjA2.Name_CHN_Created__c = '中文名称2';
        fsdObjA2.Product2__c = pro2.Id;
        fsdObjA2.Fixture_Set__c = fsObj1.Id;
        fsdObjA2.Is_Body__c = false;
        fsdObjA2.Is_Optional__c = true;
        fsdObjA2.UniqueKey__c = fsObj1.Id + ':' + pro2.Id;
        fsdObjA2.SortInt__c = 2;
        fsdObjA2.Quantity__c = 1;
        fsdObjA2.Is_OneToOne__c = true;

        Fixture_Set_Detail__c fsdObjA3 = new Fixture_Set_Detail__c();
        fsdObjA3.Name = '备品配套明细名3';
        fsdObjA3.Name_CHN_Created__c = '中文名称3';
        fsdObjA3.Product2__c = pro3.Id;
        fsdObjA3.Fixture_Set__c = fsObj1.Id;
        fsdObjA3.Is_Body__c = false;
        fsdObjA3.Is_Optional__c = true;
        fsdObjA3.UniqueKey__c = fsObj1.Id + ':' + pro3.Id;
        fsdObjA3.SortInt__c = 3;
        fsdObjA3.Quantity__c = 1;
        fsdObjA3.Is_OneToOne__c = true;

        insert new Fixture_Set_Detail__c[] {fsdObjA1, fsdObjA2, fsdObjA3};

        //调拨申请
        TransferApply__c raObj = new TransferApply__c();
        raObj.Name = 'testra';
        raObj.From_Location__c = '北京';
        raObj.Destination_Location__c = '北京 备品中心';
        Map<String,Schema.RecordTypeInfo>  DEVELOPERNAMEMAP  = Schema.SObjectType.TransferApply__c.getRecordTypeInfosByDeveloperName();
        raObj.RecordTypeId = DEVELOPERNAMEMAP.get('AgencyToCenter').getRecordTypeId();
        insert raObj;
        taId = raObj.Id;

        // 调拨备品配套一览
        TransferApplySummary__c raesObj = new TransferApplySummary__c();
        raesObj.TransferApply__c = raObj.Id;
        raesObj.Fixture_Set__c = fsObj1.Id;
        raesObj.Cancel_Select__c = false;
        raesObj.IndexFromUniqueKey__c = 1;
        insert raesObj;

        //调拨申请一览明细
        TransferApplyDetail__c raesdObj1 = new TransferApplyDetail__c();
        raesdObj1.TransferApply__c = raObj.Id;
        raesdObj1.Fixture_Set_Detail__c = fsdObjA1.Id;
        raesdObj1.TransferApplySummary__c = raesObj.Id;
        raesdObj1.IndexFromUniqueKey__c = 1;
        raesdObj1.FSD_OneToOneAccessory_Cnt__c = 2;
        raesdObj1.ApplyPersonAppended__c = false;
        raesdObj1.TransferCount__c = 1;
        raesdObj1.FSD_SortInt__c = 01;
        raesdObj1.Asset__c = asset1.Id;
        raesdObj1.Main_OneToOne__c = true;
        raesdObj1.Loaner_accsessary__c = false;

        TransferApplyDetail__c raesdObj2 = new TransferApplyDetail__c();
        raesdObj2.TransferApply__c = raObj.Id;
        raesdObj2.Fixture_Set_Detail__c = fsdObjA2.Id;
        raesdObj2.TransferApplySummary__c = raesObj.Id;
        raesdObj2.IndexFromUniqueKey__c = 3;
        raesdObj2.FSD_OneToOneAccessory_Cnt__c = 2;
        raesdObj2.ApplyPersonAppended__c = false;
        raesdObj2.TransferCount__c = 1;
        raesdObj2.FSD_SortInt__c = 02;
        raesdObj2.Asset__c = asset2.Id;
        raesdObj2.Loaner_accsessary__c = true;
        raesdObj2.OneToOneAccessory__c = true;

        TransferApplyDetail__c raesdObj3 = new TransferApplyDetail__c();
        raesdObj3.TransferApply__c = raObj.Id;
        raesdObj3.Fixture_Set_Detail__c = fsdObjA3.Id;
        raesdObj3.TransferApplySummary__c = raesObj.Id;
        raesdObj3.IndexFromUniqueKey__c = 3;
        raesdObj3.FSD_OneToOneAccessory_Cnt__c = 2;
        raesdObj3.ApplyPersonAppended__c = false;
        raesdObj3.FSD_SortInt__c = 03;
        raesdObj3.Asset__c = asset3.Id;
        raesdObj3.OneToOneAccessory__c = true;
        raesdObj3.Loaner_accsessary__c = true;

        insert new TransferApplyDetail__c[] {raesdObj1,raesdObj2,raesdObj3};
    }
    @isTest
    public static void test_cancelSummary() {
        setupTestData1();
        Test.startTest();
        TransferApplySummary__c tas = [SELECT Id FROM TransferApplySummary__c];
        tas.Cancel_Select__c = true;
        tas.Cancel_Reason__c = '123';
        update tas;
        Test.stopTest();
        tas = [SELECT Id, Cancel_Select__c FROM TransferApplySummary__c];
        System.assertEquals(true, tas.Cancel_Select__c);
        for(TransferApplyDetail__c tad: [SELECT Cancel_Select__c FROM TransferApplyDetail__c]) {
            System.assertEquals(true, tad.Cancel_Select__c);
        }
    }
}