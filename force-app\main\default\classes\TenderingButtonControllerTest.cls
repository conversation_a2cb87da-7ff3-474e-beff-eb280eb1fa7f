@isTest
//author  kk  20231010  for TenderingButtonController
public with sharing class TenderingButtonControllerTest {
    @IsTest
    static void testMethod1(){
        ControllerUtil.EscapeNFM001Trigger = true;
        ControllerUtil.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
        StaticParameter.EscapeMaintenanceContractAfterUpdateTrigger = true;

        Oly_TriggerHandler.bypass('UpdateContractAimAmountHandler');

        
        RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName IN ('Department_GI', 'Department_BF') order by DeveloperName desc];
        
        Account acc = new Account();
        acc.RecordTypeId = rectCo.Id;
        acc.Name = 'HP test1';
        acc.Assume_Change__c = true;
        insert acc;
        Account acc1 = new Account();
        acc1.RecordTypeId = rectCo.Id;
        acc1.Name = 'HP test1';
        acc1.Assume_Change__c = true;
        insert acc1;
        Account acc2 = new Account();
        acc2.RecordTypeId = rectCo.Id;
        acc2.Name = 'HP test1';
        acc2.Assume_Change__c = true;
        insert acc2;
        Account acc3 = new Account();
        acc3.RecordTypeId = rectCo.Id;
        acc3.Name = 'HP test1';
        acc3.Assume_Change__c = true;
        insert acc3;
        Account acc4 = new Account();
        acc4.RecordTypeId = rectCo.Id;
        acc4.Name = 'HP test1';
        acc4.Assume_Change__c = true;
        insert acc4;

        Tender_information__c Ten = new Tender_information__c();
        Ten.Name = 'TenTest01';
        Ten.Hospital__c = acc.Id;
        Ten.Hospital1__c = acc1.Id;
        Ten.Hospital2__c = acc2.Id;
        Ten.Hospital3__c = acc3.Id;
        Ten.Hospital4__c = acc4.Id;

        insert Ten;

        TenderingButtonController.initTenderingController('123');
        TenderingButtonController.initTenderingController(Ten.Id);
        TenderingButtonController.sqlResult(Ten.Id);
        TenderingButtonController.projectRestart(Ten.Id);
        TenderingButtonController.projectRestart('123xx');
    }
}