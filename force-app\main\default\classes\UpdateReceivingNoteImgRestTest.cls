@isTest
public with sharing class UpdateReceivingNoteImgRestTest {

    static Id rnId;
    static Id rndId;

    /**
    *@description 造数据，一个清单，二个一览，每个一览下7条明细
    */
    static void setupTestData(Boolean haveAsset) {
        // 省
        Address_Level__c al = new Address_Level__c();
        al.Name = '東京';
        al.Level1_Code__c = 'CN-99';
        al.Level1_Sys_No__c = '999999';
        insert al;
        // 市
        Address_Level2__c al2 = new Address_Level2__c();
        al2.Level1_Code__c = 'CN-99';
        al2.Level1_Sys_No__c = '999999';
        al2.Level1_Name__c = '東京';
        al2.Name = '渋谷区';
        al2.Level2_Code__c = 'CN-9999';
        al2.Level2_Sys_No__c = '9999999';
        al2.Address_Level__c = al.id;
        insert al2;
        // 病院を作る  
        Account hospital = new Account();
        hospital.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'HP'].id;
        hospital.Name = 'test hospital';
        hospital.Is_Active__c = '有効';
        hospital.Attribute_Type__c = '卫生部';
        hospital.Speciality_Type__c = '综合医院';
        hospital.Grade__c = '一级';
        hospital.OCM_Category__c = 'SLTV';
        hospital.Is_Medical__c = '医疗机构';
        hospital.State_Master__c = al.id;
        hospital.City_Master__c = al2.id;
        hospital.Town__c = '东京';
        insert hospital;

        // 戦略科室を得る
        Account[] strategicDep = [SELECT ID, Name FROM Account WHERE parentId = :hospital.Id AND recordType.DeveloperName = 'Department_Class_OTH'];
        // 診療科を作る
        Account dep = new Account();
        dep.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'Department_OTH'].id;
        dep.Name = 'test dep';
        dep.AgentCode_Ext__c = '9999998';
        dep.ParentId = strategicDep[0].Id;
        dep.Department_Class__c = strategicDep[0].Id;
        dep.Hospital__c = hospital.Id;
        insert dep;

        // 产品
        Product2 pro1 = new Product2(Name='name01',IsActive=true,Family='GI',
                Fixture_Model_No__c='TD-TB400',Serial_Lot_No__c='S/N tracing',
                Fixture_Model_No_T__c = 'TD-TB400', Asset_Model_No__c = 'Pro1',
                ProductCode_Ext__c='pc01',Manual_Entry__c=false,ProductCode = 'PRODUCTCODE',
                Packing_list_manual__c = 1);
        insert pro1;

        // 一个FS
        Fixture_Set__c fs = new Fixture_Set__c();
        fs.Name = 'SB-FS-01';
        fs.Fixture_Set_Body_Model_No__c = 'SB-FS-MODEL-01';
        fs.Loaner_name__c = 'SB-FS-1';
        insert fs;

        // 一个FSD
        Fixture_Set_Detail__c fsd = new Fixture_Set_Detail__c();
        fsd.Name = 'SB-FSD-1';
        fsd.Name_CHN_Created__c = 'SB-中文FSD-1';
        fsd.Product2__c = pro1.Id;
        fsd.Fixture_Set__c = fs.Id;
        fsd.Is_Body__c = true;
        fsd.Quantity__c = 1;
        fsd.UniqueKey__c = fs.Id + ':' + pro1.Id;
        fsd.SortInt__c = 1;
        upsert fsd;

        // 保有设备
        Asset asset1 = new Asset(Asset_Owner__c = 'Olympus');
        asset1.RecordTypeId = System.Label.Asset_RecordType;
        asset1.SerialNumber = 'asset1';
        asset1.Name = 'asset1';
        asset1.AccountId = dep.Id;
        asset1.Department_Class__c = strategicDep[0].Id;
        asset1.Hospital__c = hospital.Id;
        asset1.Product2Id = pro1.Id;
        asset1.Quantity = 1;
        asset1.Status = '不明';
        asset1.Manage_type__c = '个体管理';
        asset1.Loaner_accsessary__c = false;
        asset1.Out_of_wh__c = 0;
        asset1.Salesdepartment__c = '1.华北营业本部';
        asset1.Internal_asset_location__c = '北京 备品中心';
        asset1.Product_category__c = 'GI';
        asset1.Equipment_Type__c = '产品试用';
        asset1.SalesProvince__c = '大连';
        asset1.CompanyOfEquipment__c = '北京';
        asset1.Internal_Asset_number__c = '0001';
        asset1.Rental_Count__c = 0;
        asset1.Asset_loaner_category__c = '固定资产';
        asset1.Consumable_Guaranteen_end__c = System.Today() + 1;
        // asset1.OT_CODE__c = F:Product2.ProductCode = 'PRODUCTCODE'
        // asset1.Fixture_Model_No_F__c = F:Product2.Fixture_Model_No_T__c = 'TD-TB400'
        // asset1.CompanyCodeOfEquipment__c = auto CompanyOfEquipment__c '北京'-> "5111"
        // asset1.Internal_Asset_number_key__c = F: CompanyCodeOfEquipment__c & Internal_Asset_number__c or "" = '51110001'
        insert asset1;

        ////收货清单
        ReceivingNote__c rn = new ReceivingNote__c();
        rn.Sales_order_number__c = 'SALES_ORDER_NUMBER';      //合同编码字段
        rn.OrderCode__c = 'ORDERCODE';                    //订单编码
        rn.ApprovalNumber__c = 'APPROVALNUMBER';          //采购申请决裁号
        rn.DateOfDelivery__c = System.Today();             //发货日
        rn.Internal_asset_location__c = 'INTERNAL_ASSET_LOCATION'; //备品存放地
        rn.ManagementCenter__c = 'MGCT';
        rn.Asset_loaner_category__c = '固定资产';
        // rn.OCM_period__c = auto
        // rn.passNo7__c = auto 
        // rn.ReceivingNoteNo__c = ManagementCenter__c & ":" & OCM_period__c & ":" & passNo7__c   //清单编号
        rn.QRId__c = 'QRID';                     //二维码
        insert rn;

        //收货清单一览A
        ReceivingNoteSummary__c rnsA = new ReceivingNoteSummary__c();
        rnsA.ReceivingNote__c = rn.Id;
        rnsA.Fixture_Model_No__c = 'FIXTURE_MODEL_NO_A';
        rnsA.Fixture_Set__c = fs.Id;
        rnsA.passTxtNo3__c = '001';
        // rnsA.ReceivingNoteSummaryNo__c = ReceivingNote__r.passNo7__c & ":" & Fixture_Model_No__c & ":" & passTxtNo3__c = 7位数字:TD-TB400:001
        insert rnsA;

        //一览A下的7条明细
        List<ReceivingNoteDetail__c> rndListA = new List<ReceivingNoteDetail__c> ();
        ReceivingNoteDetail__c rnd = new ReceivingNoteDetail__c();
        rnd.ReceivingNoteSummary__c = rnsA.Id;
        rnd.passNo2__c = '01';
        rnd.passNo3__c = ''+(0+1);
        rnd.Name = 'NAME_A'+ 0;
        rndListA.add(rnd);

        //对一览A下的第一条明细中的需要展示的字段赋值
        // rndListA[0].ReceivingNoteDetailNo__c = F:ReceivingNoteSummary__r.ReceivingNoteSummaryNo__c & ":" & passNo2__c & ":" & passNo3__c = '7位数字:FIXTURE_MODEL_NO_A:3位数字:01:1'
        if (haveAsset) {
            rndListA[0].RNDAssert__c = asset1.Id;
        }
        // rndListA[0].OT_CODE__c = F:RNDAssert__r.OT_CODE__c = 'PRODUCTCODE'
        // rndListA[0].Fixture_Model_No_F__c = F: RNDAssert__r.Fixture_Model_No_F__c or Fixture_Set_Detail__r.Fixture_Model_No_F__c or Fixture_Model_No_text__c ='TD-TB400'
        // rndListA[0].Internal_Asset_number_key__c = F: RNDAssert__r.Internal_Asset_number_key__c = '51110001'
        // rndListA[0].PeriodOfUse__c = F: RNDAssert__r.Consumable_Guaranteen_end__c = System.Today + 1;
        // rndListA[0].Packing_list_manual__c = F: TEXT( RNDAssert__r.Product2.Packing_list_manual__c ) = '1'
        rndListA[0].Fixture_Set_Detail__c = fsd.Id;
        rndListA[0].SerialNumber__c = 'SERIAL_NUMBER_A0';
        rndListA[0].EquipmentSet_Managment_Code__c = 'EQUIPMENTSET_MANAGMENT_CODE_A0';
        rndListA[0].Salesdepartment__c = '1.华北营业本部';
        rndListA[0].SalesProvince__c = '大连';
        rndListA[0].Product_category__c = 'GI';
        rndListA[0].Equipment_Type__c = '产品试用';
        rndListA[0].Manage_type__c = '个体管理';
        rndListA[0].unknow_serial_NO_product__c = 'FALSE';
        rndListA[0].Loaner_accsessary__c = 'TRUE';
        insert rndListA;
        rndId = rndListA[0].Id;

        //收货清单一览B
        ReceivingNoteSummary__c rnsB = new ReceivingNoteSummary__c();
        rnsB.ReceivingNote__c = rn.Id;
        insert rnsB;

        // 一览B下的7条明细
        List<ReceivingNoteDetail__c> rndListB = new List<ReceivingNoteDetail__c> ();
        for(Integer i = 0;i<7;i++){
            ReceivingNoteDetail__c rnd1 = new ReceivingNoteDetail__c();
            rnd1.passNo2__c = '02';
            rnd1.passNo3__c = ''+(i+1);
            rnd1.ReceivingNoteSummary__c = rnsB.Id;
            rnd1.Name = 'NAME_B'+i;
            rndListB.add(rnd1);
        }
        insert rndListB;

        rnId = rn.Id;
    }

    static testMethod void testDoPost(){
        setupTestData(true);
        RestRequest req = new RestRequest(); 
        RestResponse res = new RestResponse();

        req.requestURI = '/services/apexrest/v1/cans/';


        req.httpMethod = 'POST';
        RestContext.request = req;
        RestContext.response = res;
        ContentVersion content_version=new ContentVersion();
        content_version.Title='Proto Suggestion Test Title';
        content_version.PathOnClient='/' + content_version.Title;
        Blob bodyBlob=Blob.valueOf('Proto Suggestion Test Body');
        content_version.VersionData=bodyBlob;
        content_version.origin = 'H';
        insert content_version;

        ContentVersion content_version_afterInsert = [SELECT Id, ContentDocumentId FROM ContentVersion WHERE Id =: content_version.Id][0];
        Test.startTest();
        UpdateReceivingNoteImgRest.doPost(content_version_afterInsert.ContentDocumentId, rnId, rndId);
        Test.stopTest();
    }
}