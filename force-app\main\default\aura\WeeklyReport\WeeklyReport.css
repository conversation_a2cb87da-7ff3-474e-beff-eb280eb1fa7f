.THIS .bcolor {
    color: #333;
}
.THIS.height100vh {
    height: 100vh;
}
/* .THIS.contents_wrapper {
    padding-top: 50px;
    width: 105%;
} */
.THIS .mt5 {
    margin-top: 5px;
}
.THIS .mt40 {
    margin-top: 40px;
}
.THIS .edit_button {
    float: right;
    margin: -1.5em 0.5em 0 0;
    border: 0;
}
.THIS .no_data_area {
    text-align: center;
    margin: .5em 0;
}
.THIS.report_list_area {
    margin: 40px 0 20px 0px;
    height: 60vh;
    overflow-y: scroll;
}
.THIS .report_radio {
    float: left;
}
.THIS .table_header {
    width: 9.4rem;
    height: 2em;
}
.THIS.disp_none {
    display: none;
}
.THIS .disp_none {
    display: none;
}
.THIS .tr {
    text-align: right;
}
.THIS .close_button {
    float: right;
    margin-top: -2em;
}
.THIS .close_icn svg {
    fill: #fefefe;
}
.THIS .slds-progress__marker {
    cursor: default;
}
.THIS .progress_0 {
    width: 0%;
}
.THIS .progress_100 {
    width: 100%;
}
.THIS .new_btn_area {
    text-align: center;
    margin-top: 22px;
}
.THIS .new_btn {
    width: 100%;
}
.THIS .save_button_area {
    margin: 1em 0;
    text-align: right;
}
.THIS .save_button {
    background-color: #ffa500;
    border: 1px solid #ff8c00;
}
.THIS .report_data_header {
    border-bottom: 2px solid #ccc;
}
.THIS .report_data_area {
    border-bottom: 1px solid #ccc;
}
.THIS.fade {
    transition: 5s opacity linear;
}
.THIS.animation_off {
    opacity: 0;
}
.THIS.animation_on {
    opacity: 1;
}
.THIS .fade {
    transition: 5s opacity linear;
}
.THIS .animation_off {
    opacity: 0;
}
.THIS .animation_on {
    opacity: 1;
}
.THIS .divbotuser{
    width:fit-content;
}

.THIS .toastMessage.forceActionsText{ 
    white-space : pre-line !important;
}

.THIS .slds-file-selector__dropzone .slds-file-selector__text{
    display: none;
}

.THIS .slds-input:focus, .THIS .slds-input:active {
     border-color: rgb(216, 221, 230);
     box-shadow: none;
}

.THIS .customRequired{
     font-weight: 400; 
} 
.THIS .customRequired:before{    
     content: "*";
     margin: 0 0.125rem 0 0.125rem;
     color: rgb(194, 57, 52);
     float: left; 
} 
.THIS .none{
     display:none; 
}

.THIS tr .decrypt{
    /* display: none;  */
     display: unset; /*deloitte-zhj 20230825 PIPL解密 */
}

/*deloitte-zhj 20230825 PIPL解密 */
/* .THIS tr:hover .decrypt{
    display: none;  
} */

/*deloitte-zhj 20230825 PIPL解密 */
/* .THIS tr:hover .encrypt{ */
.THIS .encrypt{
    display: none;
}

/* add by Deloitte-Link 2023-6-19 start*/
.THIS .weeklyReportSpinner{
    top:100px;
}

.THIS .weeklyReportSpinnerEdit{
    top:50px;
}

.THIS .slds-spinner_container{
    position: absolute;
    top:100px;
}

.THIS .slds-spinner_containerEdit{
    position: absolute;
    top:50px;
}

.THIS .marginTop{
    margin-top: 10px;
}
.THIS .custom-input .uiInput {
    height: 2rem; 
}
/* add by Deloitte-Link 2023-6-19 end*/

/* add by Link : 2024-1-2 PIPL 客户人员选取 */
.THIS .hideInput {
    display: none;
}