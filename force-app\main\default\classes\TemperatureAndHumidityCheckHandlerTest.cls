@isTest
private class TemperatureAndHumidityCheckHandlerTest {
    static testMethod void testMethod1() {
    	List<Temperature_And_Humidity_Check__c> TAHCList=new List<Temperature_And_Humidity_Check__c>();
        Temperature_And_Humidity_Check__c TAHC1=new Temperature_And_Humidity_Check__c();
        TAHC1.Name='test01';
        TAHC1.Temperature_And_Humidity_Zone__c='A区';
        TAHC1.Temperature_Check_Result__c='OK';
        TAHC1.Humidity_Check_Result__c='OK';
        TAHC1.Internal_asset_location__c='北京 备品中心';
        TAHC1.Inventory_Time__c='202002202';
        TAHCList.add(TAHC1);

        Temperature_And_Humidity_Check__c TAHC2=new Temperature_And_Humidity_Check__c();
        TAHC2.Name='test02';
        TAHC2.Temperature_And_Humidity_Zone__c='B区';
        TAHC2.Temperature_Check_Result__c='OK';
        TAHC2.Humidity_Check_Result__c='OK';
        TAHC2.Internal_asset_location__c='北京d1';
        TAHC2.Inventory_Time__c='202002202';
        TAHCList.add(TAHC2);

        Temperature_And_Humidity_Check__c TAHC3=new Temperature_And_Humidity_Check__c();
        TAHC3.Name='test03';
        TAHC3.Temperature_And_Humidity_Zone__c='C区';
        TAHC3.Temperature_Check_Result__c='OK';
        TAHC3.Humidity_Check_Result__c='OK';
        TAHC3.Internal_asset_location__c='北京d1';
        TAHC3.Inventory_Time__c='202002202';
        TAHCList.add(TAHC3);

        Temperature_And_Humidity_Check__c TAHC4=new Temperature_And_Humidity_Check__c();
        TAHC4.Name='test03';
        TAHC4.Temperature_And_Humidity_Zone__c='A区';
        TAHC4.Temperature_Check_Result__c='OK';
        TAHC4.Humidity_Check_Result__c='OK';
        TAHC4.Internal_asset_location__c='上海 备品中心';
        TAHC4.Inventory_Time__c='202002202';
        TAHCList.add(TAHC4);

        Temperature_And_Humidity_Check__c TAHC5=new Temperature_And_Humidity_Check__c();
        TAHC5.Name='test03';
        TAHC5.Temperature_And_Humidity_Zone__c='A区';
        TAHC5.Temperature_Check_Result__c='OK';
        TAHC5.Humidity_Check_Result__c='OK';
        TAHC5.Internal_asset_location__c='广州 备品中心';
        TAHC5.Inventory_Time__c='202002202';
        TAHCList.add(TAHC5);
        
        insert TAHCList;

        TAHC1.Temperature_And_Humidity_Zone__c='A区';
        TAHC1.Internal_asset_location__c='上海 备品中心';
        TAHC1.Inventory_Time__c='202002202';
        update TAHC1;
    }
}