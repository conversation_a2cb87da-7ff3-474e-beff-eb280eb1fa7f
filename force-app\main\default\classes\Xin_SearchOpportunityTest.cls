@isTest
private class Xin_SearchOpportunityTest {
    private static String loginId{get;set;}
    private static User u1{get;set;}
    private static User u2{get;set;}
    private static User u3{get;set;}
    private static Account accHP{get;set;}
    private static Account accDepClass{get;set;}
    private static Account accDep{get;set;}
    private static Contact con1{get;set;}
    private static Contact con2{get;set;}
    private static Contact con3{get;set;}
    private static Contact con4{get;set;}
    private static Contact con5{get;set;}
    private static Opportunity opp1{get;set;}
    private static Opportunity opp2{get;set;}
    private static Opportunity opp3{get;set;}
    private static Opportunity opp4{get;set;}
    private static Opportunity opp5{get;set;}
    private static Maintenance_Contract__c mc1{get;set;}
    private static Maintenance_Contract__c mc2{get;set;}
    private static String accDepId{get;set;}
    private static String accDepName{get;set;}

    private static void init() {
        loginId = UserInfo.getUserId();
        
        Profile p = [select id from Profile where id =:System.Label.ProfileId_SystemAdmin];
        
        u3 = new User(Test_staff__c = true);
        u3.LastName = '_サンブリッジ';
        u3.Batch_User__c = true;
        u3.FirstName = 'う';
        u3.Alias = 'う';
        u3.Email = '<EMAIL>';
        u3.Username = '<EMAIL>';
        u3.CommunityNickname = 'う';
        u3.IsActive = true;
        u3.EmailEncodingKey = 'ISO-2022-JP';
        u3.TimeZoneSidKey = 'Asia/Tokyo';
        u3.LocaleSidKey = 'ja_JP';
        u3.LanguageLocaleKey = 'ja';
        u3.ProfileId = p.id;
        u3.Job_Category__c = '支援';
        u3.Province__c = '東京';
        insert u3;
        
        u1 = new User(Test_staff__c = true);
        u1.LastName = '_サンブリッジ';
        u1.FirstName = 'あ';
        u1.Batch_User__c = true;
        u1.Alias = 'あ';
        u1.Email = '<EMAIL>';
        u1.Username = '<EMAIL>';
        u1.CommunityNickname = 'あ1';
        u1.IsActive = true;
        u1.EmailEncodingKey = 'ISO-2022-JP';
        u1.TimeZoneSidKey = 'Asia/Tokyo';
        u1.LocaleSidKey = 'ja_JP';
        u1.LanguageLocaleKey = 'ja';
        u1.ProfileId = p.id;
        u1.Job_Category__c = '销售服务';
        u1.Province__c = '東京';
        u1.ManagerId = u3.id;
        insert u1;
        
        u2 = new User(Test_staff__c = true);
        u2.LastName = '_サンブリッジ';
        u2.FirstName = 'い';
        u2.Batch_User__c = true;
        u2.Alias = 'い';
        u2.Email = '<EMAIL>';
        u2.Username = '<EMAIL>';
        u2.CommunityNickname = 'い';
        u2.IsActive = true;
        u2.EmailEncodingKey = 'ISO-2022-JP';
        u2.TimeZoneSidKey = 'Asia/Tokyo';
        u2.LocaleSidKey = 'ja_JP';
        u2.LanguageLocaleKey = 'ja';
        u2.ProfileId = p.id;
        u2.Job_Category__c = '销售推广';
        u2.Province__c = '東京';
        insert u2;
        
        RecordType rtHP = [select id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName =:'HP'];
        RecordType rtDep = [select id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName =:'Department_ENT'];
        RecordType rtDoc = [select id from RecordType where IsActive = true and SobjectType = 'Contact' and DeveloperName =:'Doctor'];
        

        system.runAs(u2){
        Address_Level__c al = new Address_Level__c();
        al.Name = '東京';
        al.Level1_Code__c = 'CN-99';
        al.Level1_Sys_No__c = '999999';
        insert al;
        
        Address_Level2__c al2 = new Address_Level2__c();
        al2.Level1_Code__c = 'CN-99';
        al2.Level1_Sys_No__c = '999999';
        al2.Level1_Name__c = '東京';
        al2.Name = '渋谷区';
        al2.Level2_Code__c = 'CN-9999';
        al2.Level2_Sys_No__c = '9999999';
        al2.Address_Level__c = al.id;
        insert al2;
        
        accHP = new Account();
        accHP.Name = '病院1';
        accHP.Grade__c = '一般';
        accHP.OCM_Category__c = '一般';
        accHP.Attribute_Type__c = '保険省';
        accHP.Speciality_Type__c = '総合病院';
        accHP.Is_Active__c = '有効';
        accHP.State_Master__c = al.id;
        accHP.City_Master__c = al2.id;
        accHP.RecordTypeId = rtHP.id;
        accHP.Valid_To__c = Date.today() + 2;
        insert accHP;

        accDepClass = [select Id from Account where ParentId = :accHP.Id and Department_Class_Label__c = '耳鼻喉科'];
        
        accDep = new Account();
        accDep.Name = '診療科1';
        accDep.Department_Class_Label__c = '診療科1';
        accDep.Hospital__c = accHP.id;
        accDep.ParentId = accDepClass.id;
        accDep.Department_Class__c = accDepClass.id;
        accDep.Department_Name__c = '診療科1';
        accDep.CurrencyIsoCode = 'CNY';
        accDep.RecordTypeId = rtDep.id;
        insert accDep;
        accDepId = accDep.Id;
        accDep = [select Name from Account where Id = :accDep.Id];
        accDepName = accDep.Name;

        }
    }

    @isTest 
    static void test_method_one() {
        init();
        
        XinDailyReportController t = new XinDailyReportController();
        t.init();
        System.runAs(u3) {
     
            System.Test.startTest();
            Xin_SearchOpportunity blgso = new Xin_SearchOpportunity();
            PageReference pageRef3 = Page.Xin_SearchOpportunity;
            pageRef3.getParameters().put('q', '::11');
            pageRef3.getParameters().put('r', '');

            Test.setCurrentPage(pageRef3);
            blgso.search();
            pageRef3.getParameters().put('q', '');
            Test.setCurrentPage(pageRef3);
            blgso.search();
            
            Account accTmp = [Select Id, Hospital__c from Account where id =: accDep.Id];
            List<Opportunity> oppTmp = [Select Id from Opportunity where StageName IN ('引合', '注残', '出荷')];
            pageRef3.getParameters().put('q', '::');
            pageRef3.getParameters().put('r', accDep.Id);
            Test.setCurrentPage(pageRef3);
            blgso.search();
            System.Test.stopTest();

        }
    }
    

    
}