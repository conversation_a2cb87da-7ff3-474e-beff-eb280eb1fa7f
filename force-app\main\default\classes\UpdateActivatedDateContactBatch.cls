global class UpdateActivatedDateContactBatch implements Database.Batchable<sObject> {
    public String query;
    public String OppId;

    global UpdateActivatedDateContactBatch() {
        this.query = query;
    }
    global UpdateActivatedDateContactBatch(String OppId) {
        this.query = query;
        this.OppId = OppId;
    }

    global Database.QueryLocator start(Database.BatchableContext bc) {
        Date dt = Date.today();
        dt = dt.addMonths(-2);
        System.debug(LoggingLevel.INFO, '*** dt: ' + dt);
        // 20220824 ssm 清空数据的判断条件：
        // 1.没有做合同申请(8.合同申请日)
        // 2.没有WIN(SAP上传(WIN))
        // 3.没有关联招标项目(招标项目名（招标）)
        // 4.没有中标确认结果
        // 20220929 ssm 增加条件
        // 5.没有7.中标日
         // SWAG-CGH3WS【委托】 【FY23询价改善】询价页面授权日到期自动清空 fy start OriginalAuthorizationApplicationCod__c
        query  = 'select Id, Autholization_Activated_Date__c,Bidding_Project_Name_Bid__c,OriginalAuthorizationApplicationCod__c, ';
         // SWAG-CGH3WS【委托】 【FY23询价改善】询价页面授权日到期自动清空 fy end OriginalAuthorizationApplicationCod__c
        query += 'Assistant_Applied_Date__c, Authorized_DB_No__c,Authorized_Finish_Sales__c, Authorized_Date__c ';
        query += 'from Opportunity where Autholization_Activated_Date__c<=:dt ';
        //DB202311044376 WYL 增加条件  排除变更用户询价=真的数据 2023-11-20 start
        if (this.OppId != null && this.OppId != '') {
            query += ' and id = :OppId ';
        }
        query += 'and (Assistant_Applied_Date__c = null and SAP_Send_OK__c = false and Bidding_Project_Name_Bid__c = null and ConfirmationofAward__c = null and Closing_Bid_Date__c = null and If_Account_Change__c != true) ';
        //DB202311044376 WYL 增加条件  排除变更用户询价=真的数据 2023-11-20 end
        System.debug(LoggingLevel.INFO, '*** query: ' + query);
        return Database.getQueryLocator(query);
    }

    global void execute(Database.BatchableContext BC, list<Opportunity> scope) {
        System.debug(LoggingLevel.INFO, '*** in scope: ');
        List<Opportunity> Oplist = new List<Opportunity>();
        for (Opportunity oca : scope) {
                // SWAG-CGH3WS【委托】 【FY23询价改善】询价页面授权日到期自动清空 fy start
                oca.OriginalAuthorizationApplicationCod__c = oca.Authorized_DB_No__c;
                // SWAG-CGH3WS【委托】 【FY23询价改善】询价页面授权日到期自动清空 fy end
                oca.Autholization_Activated_Date__c = null;
                oca.Authorized_DB_No__c = null;
                oca.Authorized_Finish_Sales__c = null;
                oca.Authorized_Date__c = null;
                Oplist.add(oca);
                // OpMap.put(oca.Id, oca);
                System.debug(LoggingLevel.INFO, '*** Oplist' + Oplist);
        }
        if(Oplist.size()>0){
            System.debug(LoggingLevel.INFO, '*** update: ');
            StaticParameter.EscapeOppandStaTrigger = true;
            StaticParameter.EscapeOpportunityHpDeptUpdTrigger = true;
            StaticParameter.EscapeNFM007Trigger = true;
            update Oplist;
        }
        
    }

    global void finish(Database.BatchableContext BC) {

    }

    // Database.executeBatch(new UpdateActivatedDateContactBatch(oca.Id),10);
}