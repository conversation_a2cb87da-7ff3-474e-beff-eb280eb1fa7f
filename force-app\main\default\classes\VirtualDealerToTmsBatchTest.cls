@Istest
public with sharing class VirtualDealerToTmsBatchTest {
    @TestSetup
    private static void init() {

        //RecordType rtDepClass = [select id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName =:'Department_Class_ENT'];           // 耳鼻喉科

        //Address_Level__c al = [select id from Address_Level__c where Level1_Code__c =:'CN-01'];
        //Address_Level2__c al2 = [select id from Address_Level2__c where Name =:'延庆县'];
            Address_Level__c al = new Address_Level__c();
            al.Name = '山东省';
            al.Level1_Code__c = 'CN-99';
            al.Level1_Sys_No__c = '999999';
            insert al;

            Address_Level2__c al2 = new Address_Level2__c();
            al2.Level1_Code__c = 'CN-99';
            al2.Level1_Sys_No__c = '999999';
            al2.Level1_Name__c = '東京';
            al2.Name = '渋谷区';
            al2.Level2_Code__c = 'CN-9999';
            al2.Level2_Sys_No__c = '9999999';
            al2.Address_Level__c = al.id;
            insert al2;
            Account accDep = new Account();
            accDep.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'Agency'].id;
            accDep.Name = '山东省虚拟经销商';
            accDep.Is_Active__c = '有效';
            accDep.TradeComplianceStatus__c = '警示名单';
            accDep.State_Master__c = al.Id;
            accDep.Aim_Price__c = ********;
            accDep.ParentId = '001C8000002SYrpIAG';
            accDep.ET_SP_Dealer__c = true;
            accDep.ISVirtualDealer__c = true;

            insert accDep;

            List<Opportunity> oppList = new List<Opportunity>();
            Opportunity opp1 = new Opportunity();
            opp1.Name = '::山东省 省目标';
            opp1.OwnerId = '005C8000000212xIAA';
            opp1.Opportunity_Category__c = 'ET';
            opp1.Trade__c = '外貿';
            opp1.StageName = '目標';
            opp1.SAP_Province__c = '山东省';
            opp1.Amount = 31231;
            opp1.CurrencyIsoCode = 'CNY';
            opp1.Close_Forecasted_Date__c = date.today().addMonths(1);
            opp1.CloseDate = date.today().addMonths(1);
            opp1.Opportunity_stage__c = '确定了对手参数';
            opp1.Competitor__c = 'E';
            opp1.Target_category__c = '省目标';
            opp1.RecordTypeId = '012C80000000NICIA2';

            opp1.Sales_Root__c = 'OCM直接販売';
            opp1.StageName = '引合';
            opp1.Target_Source__c = 'SetProvinceTarget';
            opp1.OCM_Target_period__c = '157P';
            oppList.add(opp1);
            insert oppList;
            System.debug('opp1.Name:' + opp1.Name);
    }
    static testMethod void testInitOne(){
        Account acc = [select id,name ,OCM_man_province_HP__c  from Account where ISVirtualDealer__c = true limit 1];
        Dealer_ConsumableManage__c dc = new Dealer_ConsumableManage__c();
        dc.Dealer_Name__c = acc.id;
        dc.TMS_Key__c = acc.id+'-'+'ET'+'-'+'FY2025'+'-'+acc.OCM_man_province_HP__c;
        dc.ET_ENG__c = 'ET';
        dc.FY__c = 'FY2025';
        insert dc;
        Database.executeBatch(new VirtualDealerToTmsBatch(),200);
    }
    static testMethod void testInitOTwo(){
      Database.executeBatch(new VirtualDealerToTmsBatch(),200);
    }
}