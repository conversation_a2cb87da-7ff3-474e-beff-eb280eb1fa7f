({
    // 初始化函数
    doInit : function(cmp, event, helper) {
        // 设置明细页表格的构造
        helper.getColumnAndAction(cmp);
        // 从数据库里面读取数据
        helper.geteSign(cmp);
        // document.documentElement.clientWidth;
        // document.documentElement.clientHeight;
        cmp.set('v.QRWidth', document.documentElement.clientWidth);
        cmp.set('v.QRHeight', document.documentElement.clientWidth);
    },
    // 明细页表格的翻页下一页
    handleNext : function(cmp, event, helper) {
        helper.handleNext(cmp);
    },
     // 明细页表格的翻页上一页
    handlePrev : function(cmp, event, helper) {
        helper.handlePrev(cmp);
    },
    // 明细页表格的翻页首页
    handleHome : function(cmp, event, helper) {
        helper.handleHome(cmp);
    },
     // 明细页表格的翻页尾页
    handleLast : function(cmp, event, helper) {
        helper.handleLast(cmp);
    },
    // 表格确认更改
    handleSaveEdition: function (cmp, event, helper) {
        //获取更改内容
        var draftValues = event.getParam('draftValues');
        helper.saveEdition(cmp, draftValues);
    },
	// 文件上传结果后的处理
    handleUploadFinished: function (cmp, event) {
        // 获取上传成功文件的信息
        var uploadedFiles = event.getParam("files");
        if(uploadedFiles.length > 0){
            // 之所以在这里写这个弹出框，因为写在helper里面不知道为啥弹不出来；
            alert("请您点击提交按钮进行上传！");
        }
        // 打印上传成功文件的名字
        uploadedFiles.forEach(file => console.log(file.name));
    },
    submitClick: function(cmp, event, helper){
        helper.submitClick(cmp);
    },
    // 主页跳转至明细页
    handleHomePageNextClick : function (cmp, event, helper) {
        helper.handleHomePageNextClick(cmp);
    },
    // 明细页跳转至文件上传页
    handleDetailsPageNextClick : function (cmp, event, helper) {
        helper.handleDetailsPageNextClick(cmp , event);
    },
    BackToHomePage : function(cmp, event, helper){
        if(confirm('确认回到首页吗？')){
                var messageEvent = cmp.getEvent('componentEvent');
                // messageEvent.setParam('DNName',cmp.get('v.DNName'));
                // messageEvent.setParam('IsAgencyShow',cmp.get('v.IsSubmit'));
                messageEvent.fire();
                cmp.set("v.HomePage", false);
            }
        

        // helper.BackToHomePage(cmp);
    },
    handleShowFielePageNextClick : function(cmp, event, helper){
        helper.handleShowFielePageNextClick(cmp);
    },
    handleShowPageNextClick : function(cmp, event, helper){
        helper.handleShowPageNextClick(cmp);
    },
    OpinionsTODetailsPage : function(cmp, event, helper){
        helper.OpinionsTODetailsPage(cmp);
    },
    NextOpinions : function(cmp, event, helper){
        helper.NextOpinions(cmp);
    },
    searchByCaseNumber : function(cmp, event, helper){
        helper.searchByCaseNumber(cmp,event);
    },
    showHPQR : function(cmp, event, helper){
        var check = cmp.get('v.chkQR');
        if(check){
            cmp.set('v.showQR',true);
        }else{
            cmp.set('v.showQR',false);
        }
        
    },
    hideHPQR:function(cmp, event, helper){
        cmp.set('v.showQR',false);
        cmp.set('v.chkQR',false);
    }
    

})