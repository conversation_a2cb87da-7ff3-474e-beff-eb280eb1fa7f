<aura:component
    implements="flexipage:availableForAllPageTypes,forceCommunity:availableForAllPageTypes,lightning:isUrlAddressable"
    access="global" controller="eSignHomePageController">

    <aura:attribute name="DNNameInput" type="Boolean" default="true" />
    <aura:attribute name="agencyPage" type="Boolean" default="false" />
    <aura:attribute name="agencyConfirmPage" type="Boolean" default="false" />
    <aura:attribute name="agencyConfirmReadOnlyPage" type="Boolean" default="false" />

    <aura:attribute name="HPPage" type="Boolean" default="false" />
    <aura:attribute name="HPPConfirmage" type="Boolean" default="false" />
    <aura:attribute name="eSignHospitalConfirmReadOnlyPage" type="Boolean" default="false" />

    <aura:attribute name="LoadError" type="Boolean" default="false" />
    <!-- <aura:attribute name="isButtonShow" type="Boolean" default="true"/> -->
    <!-- 判断经销商可以查看还是编辑 -->
    <aura:attribute name="IsAgencyShow" type="Boolean" default="" />
    <!-- 判断经销商可以查看还是编辑 -->
    <aura:attribute name="IsHPShow" type="Boolean" default="" />
    <!-- 经销商扫描日 -->
    <aura:attribute name="agencyScanDayBack" type="Date" default="" />
    <!-- 经销商签收日 -->
    <aura:attribute name="agencySignUpDateBack" type="Date" default="" />
    <!-- 经销商确认日 -->
    <aura:attribute name="agencyConfirmDateBack" type="Date" default="" />


    <!-- 医院扫描日 -->
    <aura:attribute name="HPScanDay" type="Date" default="" />
    <!-- 医院签收日 -->
    <aura:attribute name="HPSignUpDate" type="Date" default="" />
    <!-- 医院反馈日 -->
    <aura:attribute name="salesHPManageFBDate" type="Date" default="" />
    <!--  医院反馈确认日-->
    <aura:attribute name="salesHPManageConfirmDate" type="Date" default="" />
    <!-- 医院确认日 -->
    <aura:attribute name="HPConfirmDate" type="Date" default="" />
    <!-- 判断录入类型 -->
    <aura:attribute name="entryType" type="string" default="" />
    <!-- 设置样式 -->
    <aura:attribute name="cssStyle" type="string" default="" />
    <aura:attribute name="allWidth" type="string" default="" />
    <aura:attribute name="floatWidth" type="string" default="" />
    <aura:attribute name="floatWidth1" type="string" default="" />
    <!-- 判断直销还是分销 -->
    <aura:attribute name="DirectFlag" type="Boolean" default="" />
    <!--
        0 可以医院签收
        1 可以医院确认
        2 不可以点击医院按钮
    -->
    <aura:attribute name="HPStatus" type="integer" default="2" />
    <!--
        0 可以经销商签收
        1 可以经销商确认
        2 不可以点击经销商按钮
    -->
    <aura:attribute name="AgencyStatus" type="integer" default="2" />
    <aura:attribute name="buttonGroup" type="Boolean" default="false" />
    <aura:attribute name="eSignForm" type="object" />
    <aura:attribute name="DNName" type="string" default="" />
    <!-- 进口单证 fy start  -->
    <aura:attribute name="DNId" type="string" default="" />
    <!-- 进口单证 fy end  -->
    <aura:attribute name="errorMessage" type="String" description="失败消息" />
    <!-- 判断直销还是分销 -->
    <aura:attribute name="isDirectSales" type="Boolean" default="" description="判断直销还是分销" />
    <!-- DN清单页面 -->
    <aura:attribute name="DNListPage" type="Boolean" default="false" description="DN清单页面" />
    <!-- 客户id -->
    <aura:attribute name="accountId" type="string" default="" description="客户id" />
    <!-- 记录类型名 -->
    <aura:attribute name="recordTypeName" type="string" default="" description="记录类型名" />
    <!-- 经销商名称 -->
    <aura:attribute name="agencyName" type="string" default="" description="经销商名称" />
    <!-- 多少条数据 -->
    <aura:attribute name="lineItemSize" type="string" default="" description="多少条数据" />
    <!-- 共有多少页 -->
    <aura:attribute name="allPage" type="string" default="" description="共有多少页" />
    <!-- DNData -->
    <aura:attribute name="eSignData" type="Object[]" description="所有DN的信息" />
    <!-- 网址 -->
    <aura:attribute name="url" type="string"
        default="https://stagefull-ocm.cs113.force.com/eSignSystem/s/EsignDataEntry?DNName=" description="记录类型名" />
    <!-- 校验code -->
    <aura:attribute name="code" type="string" default="" />
    <aura:attribute name="decodeCode" type="string" default="" />
    <!-- 20230302 进口单证改造 fy start -->
    <aura:attribute name="AgencyIDM" type="string" default="" />
    <!-- 20230302 进口单证改造 fy end -->


    <aura:attribute name="isLastPage" type="Boolean" default="false" description="是否最后一页，如果是最后一页，那么表格分页的下一页不可用" />
    <aura:attribute name="data" type="Object[]" description="所有明细的信息" />
    <aura:attribute name="currentData" type="Object[]" description="明细页当前显示的明细的信息" />
    <aura:attribute name="dataSize" type="Integer" default="0" description="明细页当前显示的明细的数量" />
    <aura:attribute name="pageNumber" type="Integer" default="1" description="明细页表格的第几页" />
    <aura:attribute name="pageSize" type="Integer" default="5" description="明细页表格的每页明细容量" />
    <aura:attribute name="searchSize" type="Integer" default="" description="检索数据的长度" />
    <aura:attribute name="searchCase" type="Integer" default="" description="存放检索值，保留缓存" />
    <aura:attribute name="searchCaseKey" type="Integer" default="" description="存放检索值，赋值给searchCase" />
    <aura:attribute name="searchFlag" type="boolean" default="" description="搜索框是否有值" />
    <aura:attribute name="showQR" type="boolean" default="false" />
    <aura:attribute name="QRWidth" type="String" description="用来从父对象接收传递过来的屏幕宽度" default="" />
    <aura:attribute name="QRHeight" type="String" description="用来从父对象接收传递过来的屏幕宽度" default="" />
    <aura:attribute name="ShowDNNameQR" type="String" description="判断返回是否可以打印" default="" />
    <aura:attribute name="AttachmentDisplayflag" type="boolean" default="false" />
    <aura:attribute name="LogisticsDisplayflag" type="boolean" default="false" />

    <!-- 返回首页 start -->
    <aura:attribute name="agencyDNSignUpStatus" type="String" default="" description="DN签收状态(经销商)" />
    <aura:attribute name="HPDNSignUpStatus" type="String" default="" description="DN签收状态(医院)" />
    <aura:attribute name="agencySubmit" type="boolean" default="" description="是否提交" />
    <aura:attribute name="DNNameSpare" type="String" default="" description="备用DN号" />

    <!-- 返回首页 end -->

    <!-- 初始化 -->
    <aura:handler name="init" value="{! this }" action="{! c.init }" />
    <aura:handler name="change" value="{!v.messageAttribute}" action="{!c.handlerMessageChange}" />
    <aura:handler name="componentEvent" event="c:BackToHomePage" action="{!c.handlerMessageEvt}" phase="bubble" />
    <div>
        <lightning:spinner aura:id="mySpinner" alternativeText='请等待' size="large" class="slds-hide" />
    </div>
    <!-- <div id="allDiv" style="overflow-x:scroll ;"> -->

    <!-- 失败toast 组件的实现-->
    <div class="slds-modal slds-fade-in-open slds-modal_small slds-hide" aura:id="errorDiv">
        <!-- <ui:inputText aura:id="articleURL"/> -->
        <div class="demo-only" style="height: 8rem;" id="errorSonDiv1">
            <div class="slds-notify_container slds-is-relative">
                <div class="slds-notify slds-notify_toast slds-theme_error" role="status" id="errorSonDiv2" style="">
                    <div class="">
                        <!--  style="position: absolute;left: 0;top: 0;right: 0;bottom: 0;margin: auto;" -->
                        <div class="slds-notify__content
                        slds-align_absolute-center slds-m-left_none">
                            <lightning:icon alternativeText="error" iconName="utility:error" size="small"
                                class="buttonColor slds-m-right_small" />
                            <!-- style="word-break: break-all;width: 200px;overflow: auto" -->
                            <h2 class="slds-text-heading_small ">
                                <div id="errorSonDiv3" style=" word-break: break-all;overflow: auto;">
                                    {!v.errorMessage}
                                </div>
                            </h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <aura:if isTrue="{!v.DNNameInput}">

        <lightning:card variant="Narrow" title="奥林巴斯电子签收系统" iconName="standard:account">
            <div class='slds-m-around_small'>
                <lightning:input name="input3" value="{!v.code}" label="请输入电子签收ID号" placeholder="样式为：9999999" />
                <br />
                <div style="text-align:right;flex: 1;padding-top: 1px;">
                    <lightning:button variant="brand" label="确认" title="确认" onclick="{! c.getAccountHandler }" />
                </div>
            </div>
        </lightning:card>
    </aura:if>
    <aura:if isTrue="{!v.DNListPage}">
        <lightning:card class="">
            <!-- 加上滚动条 -->
            <div id="header" style="{!'width:'+v.floatWidth+'px;'}">
                <tr>
                    <td class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;color: #0f218b;"
                        align="left">经销商名称：{!v.agencyName}

                    </td>
                </tr>
                <tr>
                    <td class="field-title" title="Field 3" style="font-weight:bold;font-size: 13px;" align="left">
                        （向右滑动查看更多信息）

                    </td>
                </tr>

            </div>
            <!-- 检索 -->
            <div id="searchDiv" class="slds-page-header" role="banner" style="{!'width:'+v.floatWidth+'px;'}">
                <div class="slds-float_center" style="text-align:left">
                    <tr>
                        <!--  style="{!'padding-left:'+v.floatWidth*0.2+'px;'}" 请选择您需要签收的-->
                        <td align="left">共计{!v.lineItemSize}单。DN号/合同号检索(模糊检索)
                            <input id="searchInput" lightning-input_input="" type="text" class="slds-input"
                                value="{!v.searchCase}" name="" onchange="{!c.searchByDNName}"
                                style="{!'width:'+v.floatWidth*0.3+'px;'}" />
                        </td>

                        <!-- <td style="padding-left: 5px;">
            <aura:if isTrue="{!v.searchFlag}">
                    该箱共有{!v.searchSize}条数据。
                <aura:set attribute="else">
                    该单共有{!v.lineItemSize}条数据。
                </aura:set>
            </aura:if>
            </td> -->
                    </tr>
                </div>
            </div>
            <div id="allDiv" style="{!'width:'+v.floatWidth+'px;overflow-x:scroll;'}">
                <!-- 增加列的时候记得调试这个宽度 -->
                <div id="in" style=" width:1121px;margin-right: 4px; ">
                    <!-- 显示在明细页的当前明细的表格 -->
                    <div id="inTable">
                        <!-- <lightning:card> -->
                        <aura:set attribute="title">
                            货物签收单明细
                        </aura:set>

                        <table
                            class="slds-table slds-table_cell-buffer slds-table_bordered slds-hint-parent slds-no-row-hover "
                            style="table-layout: fixed;">
                            <thead>
                                <tr class="slds-line-height_reset slds-text-title_caps">
                                    <!-- 第一列 DN单号 -->
                                    <th class="slds-is-resizable" tabindex="-1" data-label="DN单号" scope="row">

                                        <span class="slds-grid slds-grid_align-spread" style="
                                            width: 90px;text-align: left;display:block;">
                                            <div class="slds-truncate">
                                                <span title="DN单号" class="slds-truncate"
                                                    style="padding-bottom: 3px;">DN单号</span>
                                            </div>
                                        </span>



                                    </th>
                                    <!-- 第二列 合同编号 -->
                                    <th class="slds-is-resizable" tabindex="-1" data-label="合同编号" scope="row"
                                        style="width: 140px;height: 35px; outline: none;">

                                        <span class="slds-grid slds-grid_align-spread"
                                            style="text-align: left;display:block;">
                                            <div class="slds-truncate">
                                                <!-- <span title="合同编号" class="slds-truncate"
                                                    style="padding-bottom: 3px;">合同编号</span> --> <!-- 2023-11-23 WYL 修改-->
                                            </div>

                                        </span>


                                    </th>
                                    <!-- 2023-11-22 WYL 新增 start -->
                                    <th class="slds-is-resizable" tabindex="-1" data-label="合同编号" scope="row"
                                        style="width: 170px;height: 35px; outline: none;">

                                        <span class="slds-grid slds-grid_align-spread"
                                            style="text-align: left;display:block;">
                                            <div class="slds-truncate">
                                                <span title="合同编号" class="slds-truncate"
                                                    style="padding-bottom: 3px;">合同编号</span>
                                            </div>

                                        </span>


                                    </th>
                                    <!-- 2023-11-22 WYL 新增 end -->
                                    <!-- 第三列 医院名称-->
                                    <th class="slds-is-resizable" tabindex="-1" data-label="医院名称" scope="row"
                                        style="width: 249px;height: 35px;">

                                        <span class="slds-grid slds-grid_align-spread"
                                            style="text-align: left;display:block;">
                                            <div class="slds-truncate">
                                                <span title="医院名称" class="slds-truncate"
                                                    style="padding-bottom: 3px;">医院名称</span>


                                            </div>
                                        </span>


                                    </th>

                                    <!-- 第四列 经销商签收状态-->
                                    <th class="slds-is-resizable" tabindex="-1" data-label="经销商签收状态" scope="row"
                                        style="width: 140px;height: 35px;">

                                        <span class="slds-grid slds-grid_align-spread"
                                            style="text-align: center;display:block;">
                                            <div class="slds-truncate">
                                                <span title="经销商签收状态" class="slds-truncate"
                                                    style="padding-bottom: 3px;">经销商签收状态</span>


                                            </div>
                                        </span>



                                    </th>

                                    <!-- 第五列 医院签收状态-->
                                    <th class="slds-is-resizable" tabindex="-1" data-label="医院签收状态" scope="row"
                                        style="width: 140px;height: 35px;">

                                        <span class="slds-grid slds-grid_align-spread"
                                            style="text-align: center;display:block;">
                                            <div class="slds-truncate">
                                                <span title="医院签收状态" class="slds-truncate"
                                                    style="padding-bottom: 3px;">医院签收状态</span>


                                            </div>
                                        </span>



                                    </th>
                                    <!-- 第六列  货物情况-->
                                    <th class="slds-is-resizable" tabindex="-1" data-label="发货日期" scope="row"
                                        style="width: 110px;height: 35px;">

                                        <span class="slds-grid slds-grid_align-spread"
                                            style="text-align: center;display:block;">
                                            <div class="slds-truncate">
                                                <span title="发货日期" class="slds-truncate"
                                                    style="padding-bottom: 3px;">发货日期</span>


                                            </div>
                                        </span>



                                    </th>
                                    <!-- 第七列  进口单证明细 fy-->
                                    <!-- 进口单证 fy start  -->
                                    <th class="slds-is-resizable" tabindex="-1" data-label="进口单证明细" scope="row"
                                        style="width: 110px;height: 35px;">

                                        <span class="slds-grid slds-grid_align-spread"
                                            style="text-align: center;display:block;">
                                            <div class="slds-truncate">
                                                <span title="进口单证明细" class="slds-truncate"
                                                    style="padding-bottom: 3px;">进口单证明细</span>


                                            </div>
                                        </span>



                                    </th>
                                    <th class="slds-is-resizable" tabindex="-1" data-label="OTS物流信息" scope="row"
                                        style="width: 110px;height: 35px;">

                                        <span class="slds-grid slds-grid_align-spread"
                                            style="text-align: center;display:block;">
                                            <div class="slds-truncate">
                                                <span title="OTS物流信息" class="slds-truncate"
                                                    style="padding-bottom: 3px;">OTS物流信息</span>


                                            </div>
                                        </span>



                                    </th>
                                    <!-- 进口单证 fy end  -->
                                    <!-- 第七列 二维码 -->
                                    <th class="slds-is-resizable" tabindex="-1" data-label="医院签收二维码" scope="row"
                                        style="width: 140px;height: 35px;text-align: center;">

                                        <span class="slds-grid slds-grid_align-spread"
                                            style="text-align: center;display:block;">
                                            <div class="slds-truncate">
                                                <span title="医院签收" class="slds-truncate"
                                                    style="padding-bottom: 3px;">医院签收二维码</span>


                                            </div>
                                        </span>



                                    </th>
                                    <!-- 第八列 测试接口 -->
                                    <!-- <th class="slds-is-resizable" tabindex="-1" data-label="测试接口" scope="row" style="width: 140px;height: 35px;text-align: center;">

                <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                        <div class="slds-truncate">
                                                <span title="测试接口" class="slds-truncate" style="padding-bottom: 3px;">测试接口</span>


                </div>
                </span>



            </th> -->


                                </tr>
                            </thead>
                            <aura:if isTrue="{!v.currentData.length > 0}">
                                <tbody>
                                    <aura:iteration items="{!v.currentData}" var="curr" indexVar="idx">
                                        <tr class="slds-hint-parent">
                                            <!-- 行项目也用th 因为用td标签点击列的高光 在苹果设备上 有痕迹 -->
                                            <!-- 行项目 第一列 DN单号 -->
                                            <th class="slds-is-resizable" tabindex="-1" data-label="DN单号" scope="row">

                                                <span class="slds-grid slds-grid_align-spread" style="
                                            width: 90px;
                                            padding-top: 6px;height:30px;
                                        ">
                                                    <!-- <div class="slds-truncate">
                                                <a id="{!curr.Id+':'+idx+':'+curr.DNNameNo0__c}" herf="javascript:void(0);" onclick="{!c.openESignPage}" name="{!curr.DNNameNo0__c}"><lightning:formattedText value="{!curr.DNNameNo0__c}" /></a>
                                                
                                            </div> -->
                                                    <aura:if isTrue="{!curr.VirtualForm__c}">
                                                        <span
                                                            style="border: none;background-color: transparent;color: var(--lwc-brandTextLink,#2574A9);">{!curr.DNNameNo0__c}</span>
                                                        <aura:set attribute="else">
                                                            <a herf="javascript:void(0);">
                                                                <button
                                                                    style="border: none;background-color: transparent;outline: none;padding-left: 0px;text-decoration: underline;"
                                                                    name="{!curr.DNNameNo0__c}"
                                                                    onclick="{!c.openESignPage}">
                                                                    {!curr.DNNameNo0__c}</button>

                                                            </a>
                                                        </aura:set>
                                                    </aura:if>
                                                </span>



                                            </th>

                                            <!-- 行项目 第二列  合同编号-->
                                            <th class="slds-is-resizable" tabindex="-1" data-label="合同编号" scope="row">

                                                <span class="slds-grid slds-grid_align-spread">
                                                    <div class="slds-truncate">
                                                        <!-- <lightning:formattedText value="{!curr.ContractNO__c}" /> --> <!-- 2023-11-23 WYL 修改-->



                                                    </div>
                                                </span>



                                            </th>
                                            <!-- 2023-11-22 WYl 新增 start -->
                                            <th class="slds-is-resizable" tabindex="-1" data-label="合同编号" scope="row">

                                                <span class="slds-grid slds-grid_align-spread">
                                                    <div class="slds-truncate">
                                                        <lightning:formattedText value="{!curr.ContractNO__c}" />



                                                    </div>
                                                </span>



                                            </th>
                                            <!-- 2023-11-22 WYl 新增 end -->
                                            <!-- 行项目 第三列  医院名称-->
                                            <th class="slds-is-resizable" tabindex="-1" data-label="医院名称" scope="row"
                                                style="white-space:nowrap;
                            overflow:hidden;
                            text-overflow: ellipsis;">

                                                <span class="slds-grid slds-grid_align-spread" style="
                                            width: 361px;
                                            padding-top: 6px;height:31px;">
                                                    <div class="slds-truncate">
                                                        <lightning:formattedText
                                                            value="{!curr.Hospital_Name_Text__c}" />


                                                    </div>
                                                </span>



                                            </th>
                                            <!-- 行项目 第四列  经销商签收状态-->
                                            <th class="slds-is-resizable" tabindex="-1" data-label="经销商签收状态"
                                                scope="row">
                                                <aura:if isTrue="{!curr.VirtualForm__c}">
                                                    <span class="slds-grid slds-grid_align-spread"
                                                        style="text-align: center;display:block;">
                                                        <div class="slds-truncate">
                                                            <lightning:formattedText value="无需电子签" />
                                                        </div>
                                                    </span>
                                                    <aura:set attribute="else">
                                                        <span class="slds-grid slds-grid_align-spread"
                                                            style="text-align: center;display:block;">
                                                            <div class="slds-truncate">
                                                                <lightning:formattedText
                                                                    value="{!curr.agencyDNSignUpStatus__c}" />
                                                            </div>
                                                        </span>
                                                    </aura:set>
                                                </aura:if>




                                            </th>
                                            <!-- 行项目 第五列  医院签收状态-->
                                            <th class="slds-is-resizable" tabindex="-1" data-label="医院签收状态" scope="row">
                                                <aura:if isTrue="{!curr.VirtualForm__c}">
                                                    <span class="slds-grid slds-grid_align-spread"
                                                        style="text-align: center;display:block;">
                                                        <div class="slds-truncate">
                                                            <lightning:formattedText value="无需电子签" />
                                                        </div>
                                                    </span>
                                                    <aura:set attribute="else">
                                                        <span class="slds-grid slds-grid_align-spread"
                                                            style="text-align: center;display:block;">
                                                            <div class="slds-truncate">
                                                                <lightning:formattedText
                                                                    id="{!curr.Id+':'+idx+':HPDNSignUpStatus__c'}"
                                                                    value="{!curr.HPDNSignUpStatus__c}" />
                                                            </div>
                                                        </span>
                                                    </aura:set>
                                                </aura:if>




                                            </th>
                                            <!-- 行项目 第六列  发货日期-->
                                            <th class="slds-is-resizable" tabindex="-1" data-label="发货日期" scope="row">

                                                <span class="slds-grid slds-grid_align-spread"
                                                    style="text-align: center;display:block;">
                                                    <div class="slds-truncate">
                                                        <lightning:formattedText value="{!curr.DeliveryDate__c}" />


                                                    </div>
                                                </span>



                                            </th>
                                            <!-- 行项目 第四列  进口单证明细   fy-->
                                            <!-- 进口单证 fy start  -->
                                            <th class="slds-is-resizable" tabindex="-1" data-label="进口单证明细" scope="row">
                                                <span class="slds-grid slds-grid_align-spread"
                                                    style="text-align: center;display:block;">
                                                    <div class="slds-truncate">
                                                        <aura:if isTrue="{!curr.VirtualForm__c}">
                                                            <aura:set attribute="else">
                                                                <button tabindex="-1" class="slds-button ReturnButton"
                                                                    style="width: 40px;" name="{!curr.Id}"
                                                                    onclick="{!c.showAttachmentDisplay}">查看</button>
                                                            </aura:set>
                                                        </aura:if>
                                                        <!-- <lightning:formattedText value="{!curr.AttachmentName}" /> -->
                                                        <!-- <a href="https://sfpi-mebg-test.olympuschina.com:8081/stg/api/file/preview?key=20220419/8cf583ab71c94eee96da7bbfcc1b376b.html">附件名1</a> -->

                                                        <!-- <a href="{!'/eSignSystem/s/AttachmentDisplay?DNId='+ curr.Id}" target="_blank">查看</a> -->
                                                        <!-- <a href="https://ocsm stagefull.sandbox.file.force.com/servlet/servlet.FileDownload?file=00P1000001QkhvQ">{!curr.AttachmentName}</a> -->
                                                    </div>
                                                </span>



                                            </th>
                                            <!-- 行项目 第五列 OTS物流信息 WYL start-->
                                            <th class="slds-is-resizable" tabindex="-1" data-label="OTS物流信息"
                                                scope="row">
                                                <span class="slds-grid slds-grid_align-spread"
                                                    style="text-align: center;display:block;">
                                                    <div class="slds-truncate">
                                                        <button tabindex="-1" class="slds-button ReturnButton"
                                                            style="width: 40px;" name="{!curr.DNName__c}"
                                                            onclick="{!c.showlogisticsDisplay}">查看</button>

                                                        <aura:if isTrue="{!curr.waybill__r.hasNewStatus__c}">
                                                            <span
                                                                style="position: absolute;top: 4px;right: 30px; background-color: red;color: white;padding: 4px;border-radius: 50%;font-size: 12px;"></span>
                                                        </aura:if>
                                                    </div>
                                                </span>
                                            </th>
                                            <!-- 行项目 第五列 OTS物流信息 WYL end-->
                                            <!-- 行项目 第七列  医院签收二维码-->
                                            <th class="slds-is-resizable" tabindex="-1" data-label="" scope="row"
                                                style="text-align: center;">
                                                <aura:if isTrue="{!curr.VirtualForm__c}">
                                                    <aura:set attribute="else">
                                                        <button id="{!curr.skip_Hospital_Sign__c}" tabindex="-1"
                                                            class="slds-button ReturnButton" name="{!curr.DNNameNo0__c}"
                                                            style="width: 40px;"
                                                            value="{!IF(curr.skip_Distribution_Sign__c, 'true', curr.agencySignUpDate__c)}"
                                                            onclick="{!c.showESignQR}">查看
                                                        </button>
                                                    </aura:set>
                                                </aura:if>



                                            </th>
                                            <!-- 行项目 第七列  医院签收二维码-->
                                            <!-- <th class="slds-is-resizable" tabindex="-1" data-label="" scope="row" style="text-align: center;">

                                <button id="testIn" tabindex="-1" class="slds-button ReturnButton" name="测试接口" style="width: 40px;" value="测试接口" onclick="{!c.testIn}">测试接口</button>


                            </th> -->


                                        </tr>
                                    </aura:iteration>
                                </tbody>

                            </aura:if>
                        </table>
                        <aura:if isTrue="{!empty(v.currentData)}">
                            <div class="slds-align_absolute-center">
                                No records found
                            </div>
                        </aura:if>



                    </div>
                </div>
            </div>

            <!-- 分页功能的上一页和下一页， 还是最左侧的显示当前页数的逻辑-->
            <div id="floatDiv" style="{!'width:'+v.floatWidth+'px;'}">
                <div class="slds-clearfix">
                    <div class="slds-page-header" role="banner">
                        <div class="slds-float_center" style="text-align:center">
                            <!-- iconName="utility:chevronleft" iconPosition="left" -->
                            <lightning:button iconName="utility:left" onclick="{!c.handleHome}"
                                disabled="{! v.pageNumber == 1}" />
                            <lightning:button iconName="utility:chevronleft" iconPosition="left"
                                onclick="{!c.handlePrev}" disabled="{! v.pageNumber == 1}" />
                            <span title="当前页">&nbsp;&nbsp;&nbsp;{!v.pageNumber}&nbsp;&nbsp;&nbsp;
                            </span>
                            <lightning:button iconName="utility:chevronright" iconPosition="right"
                                disabled="{! v.isLastPage}" onclick="{!c.handleNext}" />
                            <!-- iconName="utility:chevronright" iconPosition="right" -->
                            <lightning:button iconName="utility:right" disabled="{! v.isLastPage}"
                                onclick="{!c.handleLast}" />

                        </div>

                    </div>
                </div>
                <!-- 上一步 -->
                <div style="text-align:center;flex: 1;padding-top: 1px;">
                    <!-- <p class="slds-page-header__title">
                        共<b>{!v.allPage}</b>页| 当前显示第<b>{!v.pageNumber}</b>页
                    </p>  -->
                    <p style="text-align:center;font-weight:bold;font-size: 15px;" class="">共<span
                            style="text-decoration:underline;">{!v.allPage}</span>页| 当前显示第<span
                            style="text-decoration:underline;">{!v.pageNumber}</span>页</p>


                </div>
                <div class=" " style="text-align:center">
                    <p style="color:#0f218B;text-align:center;font-weight:bold;font-size: 15px;" class="">如有破损请先拍照片以供上传
                    </p>
                </div>

                <!-- <div class="slds-float_left">
                <div  id="attention"  style=" position:relative;margin-right: 4px;  margin-right: 4px; " class="" >
                    
                    <lightning:button variant="brand"
                                  label="上一步" title="上一步" onclick="{! c.handleShowPageNextClick }" />
                </div>
            </div>
           
            <div class="slds-float_right">
                <lightning:button variant="brand" 
                                  label="下一步" title="下一步" onclick="{!c.OpinionsTODetailsPage }" />
            </div> -->

            </div>
        </lightning:card>
    </aura:if>
    <aura:if isTrue="{!v.LoadError}">
        <lightning:layout horizontalAlign="center" class="">
            <lightning:layoutItem padding="around-small" size="12">
                <p> 加载失败，请您联系给您发送邮件的营业助理！</p>
            </lightning:layoutItem>
        </lightning:layout>
    </aura:if>
    <aura:if isTrue="{!v.buttonGroup}">
        <div class="slds-modal__container ">
            <div class="slds-modal__header ">
                <p style="color:#0f218B" class="slds-text-heading--medium">如有破损请先拍照片以供上传</p>
            </div>
            <div class="slds-modal__content  slds-grow slds-p-around--medium ">
                <div class="slds-box">
                    <lightning:layout verticalAlign="certer">
                        <!-- <lightning:layoutItem flexibility="auto" padding="around-small" size="2"  >
                            
                        </lightning:layoutItem> -->
                        <lightning:layoutItem flexibility="auto" padding="around-small" size="12" class="InheritedDIV">
                            <lightning:button variant="brand" class="InheritedButton" label="经销商" title="经销商"
                                onclick="{! c.agencyhandleClick }" />
                        </lightning:layoutItem>
                        <!-- <lightning:layoutItem flexibility="auto" padding="around-small" size="12"  class="InheritedDIV" >
                            <lightning:button variant="brand-outline" class="InheritedButton"
                                      label="经销商" title="经销商" onclick="{! c.agencyhandleClick }"/>
                        </lightning:layoutItem> -->

                        <!-- <lightning:layoutItem flexibility="auto" padding="around-small" size="2"  >
                            
                        </lightning:layoutItem> -->
                    </lightning:layout>
                    <lightning:layout verticalAlign="certer">
                        <!-- <lightning:layoutItem flexibility="auto" padding="around-small" size="2"  >
                            
                        </lightning:layoutItem> -->
                        <lightning:layoutItem flexibility="certer" padding="around-small" size="12"
                            class="InheritedDIV">
                            <lightning:button variant="brand" class="InheritedButton" label="医院" title="医院"
                                onclick="{! c.HPhandleClick }" />
                        </lightning:layoutItem>
                        <!-- <lightning:layoutItem flexibility="certer" padding="around-small" size="12"  class="InheritedDIV">
                            <lightning:button variant="brand-outline" class="InheritedButton"
                                              label="医院" title="医院" onclick="{! c.HPhandleClick }"  />
                        </lightning:layoutItem> -->

                    </lightning:layout>
                    <lightning:layout verticalAlign="certer">
                        <lightning:layoutItem flexibility="auto" padding="around-small" size="6">

                        </lightning:layoutItem>

                    </lightning:layout>
                    <!-- <aura:if isTrue="{!v.isButtonShow}"> -->
                    <lightning:layout verticalAlign="left">
                        <!-- <lightning:layoutItem flexibility="auto" padding="around-small" size="2"  >
                            
                        </lightning:layoutItem> -->
                        <lightning:layoutItem flexibility="auto" padding="around-small" size="12" class="InheritedDIV">
                            <lightning:button variant="brand-outline" class="ReturnButton" label="返回" title="返回"
                                onclick="{! c.backButtonHandler }" />
                        </lightning:layoutItem>

                    </lightning:layout>
                    <!-- </aura:if> -->
                </div>
            </div>

        </div>
    </aura:if>

    <aura:if isTrue="{!v.agencyPage}">
        <!-- 经销商验收页  -->
        <c:eSignAgencyPage DNName="{!v.DNName}" allWidth="{!v.allWidth}" floatWidth="{!v.floatWidth}"
            agencyScanDayBack="{!v.agencyScanDayBack}" agencySignUpDateBack="{!v.agencySignUpDateBack}" />
    </aura:if>

    <aura:if isTrue="{!v.agencyConfirmPage}">
        <c:eSignAgencyConfirmPage DNName="{!v.DNName}" allWidth="{!v.allWidth}" floatWidth="{!v.floatWidth}"
            IsAgencyShow="{!v.IsAgencyShow}" entryType="{!v.entryType}" agencyScanDayBack="{!v.agencyScanDayBack}"
            agencySignUpDateBack="{!v.agencySignUpDateBack}" agencyConfirmDateBack="{!v.agencyConfirmDateBack}"
            HPScanDay="{!v.HPScanDay}" HPSignUpDate="{!v.HPSignUpDate}" salesHPManageFBDate="{!v.salesHPManageFBDate}"
            salesHPManageConfirmDate="{!v.salesHPManageConfirmDate}" HPConfirmDate="{!v.HPConfirmDate}" />
        <!-- 部分验收/拒收 确认页不传打勾标识 -->
        <!-- IsAgencyOrHPShow = "{!v.IsAgencyOrHPShow}" -->
    </aura:if>
    <!-- IsAgencyOrHPShow = "{!v.IsAgencyOrHPShow}"
            entryType = "{!v.entryType}" -->
    <aura:if isTrue="{!v.agencyConfirmReadOnlyPage}">
        <c:eSignAgencyConfirmReadOnlyPage DNName="{!v.DNName}" allWidth="{!v.allWidth}" floatWidth="{!v.floatWidth}" />
        <!-- 部分验收/拒收 确认页不传打勾标识 -->
        <!-- IsAgencyOrHPShow = "{!v.IsAgencyOrHPShow}" -->
    </aura:if>

    <aura:if isTrue="{!v.HPPage}">
        <!--  医院首页 -->
        <!-- 判断直销还是分销 -->
        <!-- agencyScanDayBack="{!v.agencyScanDayBack}"
                        agencySignUpDateBack="{!v.agencySignUpDateBack}"
                        agencyConfirmDateBack = "{!v.agencyConfirmDateBack}"/> -->
        <aura:if isTrue="{!v.isDirectSales}">
            <c:eSignHospitalDirectSalesPage DNName="{!v.DNName}" allWidth="{!v.allWidth}" floatWidth="{!v.floatWidth}"
                IsAgencyShow="{!v.IsAgencyShow}" IsHPShow="{!v.IsHPShow}" />

            <aura:set attribute="else">
                <c:eSignHospitalPage DNName="{!v.DNName}" allWidth="{!v.allWidth}" floatWidth="{!v.floatWidth}"
                    IsAgencyShow="{!v.IsAgencyShow}" IsHPShow="{!v.IsHPShow}" agencyScanDayBack="{!v.agencyScanDayBack}"
                    agencySignUpDateBack="{!v.agencySignUpDateBack}"
                    agencyConfirmDateBack="{!v.agencyConfirmDateBack}" />
            </aura:set>
        </aura:if>

    </aura:if>

    <aura:if isTrue="{!v.HPPConfirmage}">


        <c:eSignHospitalConfirmPage DNName="{!v.DNName}" entryType="{!v.entryType}" allWidth="{!v.allWidth}"
            floatWidth="{!v.floatWidth}" />

    </aura:if>

    <aura:if isTrue="{!v.eSignHospitalConfirmReadOnlyPage}">
        <!--  医院确认 -->

        <c:eSignHospitalConfirmReadOnlyPage DNName="{!v.DNName}" allWidth="{!v.allWidth}" floatWidth="{!v.floatWidth}"
            IsAgencyOrHPShow="{!v.IsAgencyOrHPShow}" entryType="{!v.entryType}" DirectFlag="{!v.DirectFlag}" />
        <!-- 部分验收/拒收 确认页不传打勾标识 -->
        <!-- IsAgencyOrHPShow = "{!v.IsAgencyOrHPShow}" -->
    </aura:if>

    <aura:if isTrue="{!v.showQR}">
        <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true"
            aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open ">
            <!-- {!'min-Height:'+v.QRHeight*0.6+'px;border-style: solid;border-width:6px;border-color:#0f218b'} border-style: solid;border-width:6px;border-color:#0f218b;-->
            <div class="slds-modal__container" style="min-Height:300px;">

                <header class="slds-modal__header" style="{!'max-width:'+v.QRWidth+'px;'}">
                    <lightning:buttonIcon iconName="utility:close" onclick="{! c.closeCancelModel }"
                        alternativeText="close" variant="bare-inverse" class="slds-modal__close" />
                    <h4 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">请保留该二维码提供给医院进行签收</h4>
                </header>
                <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-1"
                    style="{!'min-Height:300px;max-width:'+v.QRWidth+'px;'}">
                    <c:eSignHospitalQR DNName="{!v.DNName}" QRWidth="{! v.QRWidth }" QRHeight="{!v.QRHeight }" />
                </div>

                <footer class="slds-modal__footer" style="{!'max-width:'+v.QRWidth+'px;'}">

                    <!-- <lightning:button variant="brand"
                                              label="确认取消任务"
                                              title="ConfirmCancel"
                                              onclick="{! c.ConfirmCancel }"/> -->
                    <lightning:button variant="neutral" label="关闭" title="Cancel" onclick="{! c.hideHPQR }" />
                </footer>
            </div>
        </section>
    </aura:if>

    <!-- </div> -->
    <!-- 进口单证 fy start  -->
    <aura:if isTrue="{!v.AttachmentDisplayflag}">
        <!--  医院确认 -->
        <label style="float: left;font-size:20px">进口单证明细</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <lightning:button variant="neutral" label="返回" title="Cancel" onclick="{! c.rebackDNListPage }" />
        <!-- <lightning:button variant="neutral"  onclick="{!c.rebackDNListPage}">返回</button> -->
        <c:AttachmentDisplay DNId="{!v.DNId}" />

    </aura:if>
    <!-- 进口单证 fy end  -->
    <aura:if isTrue="{!v.LogisticsDisplayflag}">
        <label style="float: left;font-size:20px">{!v.DNId}物流信息</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <!-- <lightning:button variant="neutral" label="返回" title="Cancel" onclick="{! c.rebackDNListPage2 }" /> -->
        <lightning:button variant="neutral" onclick="{!c.rebackDNListPage2}">返回</lightning:button>
        <c:LogisticsDisplay DNId="{!v.DNId}" />
    </aura:if>
</aura:component>