/**
 * 前提、当日の 0時移行バッチを実行
 */
global class UpdateAssetToCurrentMCBatch implements Database.Batchable<sObject>{

    global final String query;
    global String targetAssetId;
    global Asset lastAsset;
    
     /**
     * コンスタント
     */
    
    global UpdateAssetToCurrentMCBatch() {}
      
     /**
     * コンスタント
     */
    
    global UpdateAssetToCurrentMCBatch(String targetAssetId) {
         this.targetAssetId = targetAssetId;
    }
 
    global Database.Querylocator start(Database.BatchableContext BC) {
        system.debug('start方法开始执行');
        // 找出截止日期为当前日期前一天或者开始日期为当前日期的维修合同
        // 返回保有设备的有效有否标志和当前合同
        Date TDAY = Date.today();
//        Date YESDAY = Date.today().addDays(-10);
        
        if (String.isBlank(this.targetAssetId)){
            
            return Database.getQuerylocator(
            [select Id, Asset__c, Maintenance_Contract__c,
                    Estimate_List_Price_All__c,
                    Maintenance_Contract__r.Status__c,
                    Maintenance_Contract__r.Name,
                    Maintenance_Contract__r.Contract_End_Date__c,
                    Maintenance_Contract__r.Contract_Start_Date__c,
                    Asset__r.Service_Agreement__c, Asset__r.CurrentContract__c,
                    Asset__r.CurrentContract_Start_Date__c, Asset__r.CurrentContract_End_Date__c,
                    Asset__r.The_last_update_date__c,
                    // 20191021 HWAG-BGB5SE Gzw 追加 新合同备品保证提供
                    // EquipmentGuaranteeFlgTxt__c,
                    EquipmentGuaranteeFlgE__c,
                    Maintenance_Contract__r.RecordType_DeveloperName__c
               from Maintenance_Contract_Asset__c
//              where ((Maintenance_Contract__r.Contract_End_Date__c >=: YESDAY
//                        and Maintenance_Contract__r.Contract_Start_Date__c <=: TDAY
//                     )
//                    or (Asset__r.CurrentContract_End_Date__c >=: YESDAY
//                        and Asset__r.CurrentContract_Start_Date__c <=: TDAY
//                    )
//                )
              where (Maintenance_Contract__r.Contract_Start_Date__c <=: TDAY
                    or Asset__r.CurrentContract_Start_Date__c <=: TDAY
                )
              // gzw 20230201 batch优化
              and Maintenance_Contract__r.Status__c = '契約'

              order by Maintenance_Contract__r.Effective__c, Maintenance_Contract__r.Service_Number_Auto__c desc
            ]
            );
        } else {
            return Database.getQuerylocator(
            [select Id, Asset__c, Maintenance_Contract__c,
                    Estimate_List_Price_All__c,
                    Maintenance_Contract__r.Status__c,
                    Maintenance_Contract__r.Name,
                    Maintenance_Contract__r.Contract_End_Date__c,
                    Maintenance_Contract__r.Contract_Start_Date__c,
                    Asset__r.Service_Agreement__c, Asset__r.CurrentContract__c,
                    Asset__r.CurrentContract_Start_Date__c, Asset__r.CurrentContract_End_Date__c,
                    Asset__r.The_last_update_date__c,
                    // 20191021 HWAG-BGB5SE Gzw 追加 新合同备品保证提供
                    //EquipmentGuaranteeFlgTxt__c,
                    EquipmentGuaranteeFlgE__c,
                    Maintenance_Contract__r.RecordType_DeveloperName__c
               from Maintenance_Contract_Asset__c
             where Asset__c = :this.targetAssetId

             and (Maintenance_Contract__r.Contract_Start_Date__c <=: TDAY
                    or Asset__r.CurrentContract_Start_Date__c <=: TDAY
                )
            // gzw 20230201 batch优化
            and Maintenance_Contract__r.Status__c = '契約'
              order by Maintenance_Contract__r.Effective__c, Maintenance_Contract__r.Service_Number_Auto__c desc
             ]);
        }
    }

    global void execute(Database.BatchableContext BC, List<Maintenance_Contract_Asset__c> scope) {
        try {
            Date TDAY = Date.today();
            Map<Id, Asset> uptAssets = new Map<Id, Asset>();
            // gzw 20230201 batch优化
            Map<Id, Asset> uptAssetsMap = new Map<Id, Asset>();


            if(null != lastAsset) {
                uptAssets.put(lastAsset.Id, lastAsset);
            }
         

            for(Maintenance_Contract_Asset__c mca : scope) {
                if (uptAssets.containsKey(mca.Asset__c) == false) {
                    // 対象を初期化
                    Asset a = new Asset(id = mca.Asset__c);
                    a.Service_Agreement__c = '無';
                    uptAssets.put(mca.Asset__c, a);
                    //lastAsset = a;
                }
            }
            
            List<Asset> alist = [ select id,Service_Agreement__c,CurrentContract__c,
                                             CurrentContract_Start_Date__c,CurrentContract_End_Date__c,
                                             The_last_update_date__c
                                             from Asset
                                             where id in: uptAssets.keySet()];

            for(Asset ase : alist){  
                system.debug('更新时间'+ase.The_last_update_date__c);
                if(ase.Service_Agreement__c =='有' && ase.The_last_update_date__c == Date.today()){
                    uptAssets.remove(ase.id);
                }
            }

          
 

            for (Maintenance_Contract_Asset__c mca : scope) {
                system.debug('状态：'+mca.Maintenance_Contract__r.Status__c);
                // AssetのService_Agreement__c を有に更新
                if ((mca.Maintenance_Contract__r.Status__c == '契約'
                        && mca.Maintenance_Contract__r.Contract_Start_Date__c <= TDAY
                        && mca.Maintenance_Contract__r.Contract_End_Date__c > TDAY
                )) {
                    Asset a = uptAssets.get(mca.Asset__c);
                   // system.debug(mca.Maintenance_Contract__r.Contract_Start_Date__c+'++++++++++++++++++++++++++++++++++++++'+a.CurrentContract_Start_Date__c);
                    if (a != null && (a.CurrentContract__c == null 
                            || mca.Maintenance_Contract__r.Contract_Start_Date__c > a.CurrentContract_Start_Date__c)){
                        a.Service_Agreement__c = '有';
                        a.The_last_update_date__c = TDAY;
                        a.CurrentContract__c = mca.Maintenance_Contract__c;
                        a.CurrentContract_Start_Date__c = mca.Maintenance_Contract__r.Contract_Start_Date__c;
                        a.CurrentContract_End_Date__c = mca.Maintenance_Contract__r.Contract_End_Date__c;
                        // LHJ 新维修合同 Start
                        a.CurrentContract_Asset_Price__c = mca.Estimate_List_Price_All__c;
                        // LHJ 新维修合同 End
                        // 20191021 HWAG-BGB5SE Gzw 追加 新合同备品保证提供 服务合同
                        if (mca.Maintenance_Contract__r.RecordType_DeveloperName__c == 'NewMaintenance_Contract') {
                            //a.EGFlg_fromContract_asset__c = mca.EquipmentGuaranteeFlgTxt__c;
                            a.EGFlg_fromContract_asset__c = mca.EquipmentGuaranteeFlgE__c;
                            a.CurrentContract_asset__c    = mca.Id;
                        }
                        // gzw 20230201 batch优化
                        uptAssetsMap.put(a.Id, a);
                        system.debug('有无维修合同：'+a.Service_Agreement__c);
                    } else {
                        // 初期化があるので、ここはロジックなし
                    }
                 
                }
            }
            
            /*for(Maintenance_Contract_Asset__c mca : scope) {
                Asset a = uptAssets.get(mca.Asset__c);
                system.debug('a的值为：'+a);
                if (a != null
                        && a.Service_Agreement__c == mca.Asset__r.Service_Agreement__c
                        && a.CurrentContract__c == mca.Asset__r.CurrentContract__c
                        && a.CurrentContract_Start_Date__c == mca.Asset__r.CurrentContract_Start_Date__c
                        && a.CurrentContract_End_Date__c == mca.Asset__r.CurrentContract_End_Date__c
                ) {
                    // 変化なしのAsset、uptAssetsから削除
                    
                    uptAssets.remove(mca.Asset__c);
                }
            }*/
             
                
              
            //  for(Asset ase : alist) {
            //    Asset a = uptAssets.get(ase.id);
            //    if (a != null
            //            && a.Service_Agreement__c == ase.Service_Agreement__c
            //            && a.CurrentContract__c == ase.CurrentContract__c
            //            && a.CurrentContract_Start_Date__c == ase.CurrentContract_Start_Date__c
            //            && a.CurrentContract_End_Date__c == ase.CurrentContract_End_Date__c

            //    ) {
            //        // 変化なしのAsset、uptAssetsから削除
                    
            //        uptAssets.remove(ase.id);
            //    }

            //}
            
            system.debug('最终数据'+uptAssets.values());
            if(uptAssetsMap.values() != null){
                update uptAssetsMap.values();
            }
            

    //        Database.SaveResult[] lsr = Database.update(uptAssets.values(), false);
    //        String errorMsg = '';
    //        for (Integer sIdx = 0; sIdx < lsr.size(); sIdx++) {
    //            Database.SaveResult sr = lsr[sIdx];
    //            if (!sr.isSuccess()) {
    //                Database.Error emsg = sr.getErrors()[0];
    //                errorMsg += emsg + '\n';
    //            }
    //        }
    //        if (String.isBlank(errorMsg) == false) {
    //            BatchIF_Log__c iflog = new BatchIF_Log__c();
    //            iflog.Type__c = 'PushNotification';
    //            iflog.ErrorLog__c  = 'errorMsg';
    //            if (iflog.ErrorLog__c.length() > 32768) {
    //                iflog.ErrorLog__c = iflog.ErrorLog__c.subString(0, 32760) + ' ...';
    //            }
    //            insert iflog;
    //        }
        } catch (Exception ex) {
            BatchIF_Log__c iflog = new BatchIF_Log__c();
            iflog.Type__c = 'PushNotification';
            iflog.Log__c = ex.getMessage() + '\n';
            iflog.ErrorLog__c = ex.getStackTraceString();
            insert iflog;
        }
    }
    
    
    global void finish(Database.BatchableContext BC){
        //Date tod = Date.today();
        //Asset ass = new Asset();
        //List<Asset> assList = new List<Asset>();
        //if(targetAssetId != null){
        //    ass = [select id,The_last_update_date__c from Asset where id = :targetAssetId];
        //    ass.The_last_update_date__c = null;
        //}
        //}else{
        //    assList = [select id,The_last_update_date__c from Asset where CurrentContract_Start_Date__c <=: tod];
        //    for(Asset aet : assList){
        //        aet.The_last_update_date__c = null;
        //    }
        //}
        //update ass;
        //update assList;

    }

    public static void test1(){
        integer i = 0;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
    }
}