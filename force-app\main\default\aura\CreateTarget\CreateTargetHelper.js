({
    doinit : function(component, event, helper) {
        component.set("v.OCMTerm", this.getOCMTerm());
        component.set("v.OCMTerm2", this.getOCMTerm2());// WYL 20240829 add 
        if (component.get("v.recordId")) {
            component.set("v.button_text", "编辑");
            var recordId = component.get("v.recordId");
            var action = component.get("c.getHospital");
            action.setParams({recordId: recordId});
            action.setCallback(this, function(response) {
            var state = response.getState();
            console.log("recordId:"+recordId);
            console.log("state1:"+state);
                if(state === "SUCCESS") {
                    var ah = response.getReturnValue();
                    var select_ah = ah.Id;
                    var select_ah_name = ah.Name;
                    // component.set('v.record.Agency_Hospital__c', select_ah);
                    //add by Deloitte 2023-6-8
                    let agency = component.get("v.record");
                    agency.Agency_Hospital__c = select_ah;
                    // component.set("v.record", agency);
                    // alert(JSON.stringify(component.find('input-hos').get("v.body")[0].get('v.values')));
                    if ($A.get("$Browser.formFactor") == 'DESKTOP') {
                        console.log("flag1");
                        var values = [{
                            type: 'Agency_Hospital_Link__c',
                            id: select_ah,
                            label: select_ah_name, 
                            icon : {
                                url:'/img/icon/t4v35/custom/custom24_120.png',
                                backgroundColor:'dc71d1',
                                alt:'Agency_Hospital_Link__c'
                            }
                        }];
                        // component.find('input-hos').get("v.body")[0].set('v.values', values);
                        //add by Deloitte 2023-6-8
                        if(component.find('input-hos').get("v.body") && component.find('input-hos').get("v.body").length>0){
                            console.log("11111111111111:");
                            component.find('input-hos').get("v.body")[0].set('v.values', values);
                        }                       
                    } else {
                        // component.find('input-hos').get("v.body")[0].set('v.selectedLabel', select_ah_name);
                        //add by Deloitte 2023-6-8
                        if(component.find('input-hos').get("v.body") && component.find('input-hos').get("v.body").length>0){
                            console.log("222222222222222:");
                            component.find('input-hos').get("v.body")[0].set('v.selectedLabel', select_ah_name);
                        }                            
                    }
                    
                } else if (state === "ERROR") {
                    var errors = response.getError();
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "错误",
                        "type":"error",
                        "message": errors[0].message
                    });
                    toastEvent.fire();
                    
                }
            });
            $A.enqueueAction(action);
        }

        var action = component.get("c.getProductList");
        action.setCallback(this, function(response) {
            var state = response.getState();
            console.log("state2:"+state);
            if(state === "SUCCESS") {
                var opts = response.getReturnValue();
                // alert(JSON.stringify(opts["GI"]));
                component.find("select_GI1").set("v.options", JSON.parse(JSON.stringify(opts["GI"])));
                component.find("select_GI2").set("v.options", JSON.parse(JSON.stringify(opts["GI"])));
                component.find("select_GI3").set("v.options", JSON.parse(JSON.stringify(opts["GI"])));

                component.find("select_GS1").set("v.options", JSON.parse(JSON.stringify(opts["GS"])));
                component.find("select_GS2").set("v.options", JSON.parse(JSON.stringify(opts["GS"])));
                component.find("select_GS3").set("v.options", JSON.parse(JSON.stringify(opts["GS"])));

                component.find("select_GYN1").set("v.options", JSON.parse(JSON.stringify(opts["GYN"])));
                component.find("select_GYN2").set("v.options", JSON.parse(JSON.stringify(opts["GYN"])));
                component.find("select_GYN3").set("v.options", JSON.parse(JSON.stringify(opts["GYN"])));

                component.find("select_URO1").set("v.options", JSON.parse(JSON.stringify(opts["URO"])));
                component.find("select_URO2").set("v.options", JSON.parse(JSON.stringify(opts["URO"])));
                component.find("select_URO3").set("v.options", JSON.parse(JSON.stringify(opts["URO"])));

                component.find("select_ENT1").set("v.options", JSON.parse(JSON.stringify(opts["ENT"])));
                component.find("select_ENT2").set("v.options", JSON.parse(JSON.stringify(opts["ENT"])));
                component.find("select_ENT3").set("v.options", JSON.parse(JSON.stringify(opts["ENT"])));

                component.find("select_OTH1").set("v.options", JSON.parse(JSON.stringify(opts["OTH"])));
                component.find("select_OTH2").set("v.options", JSON.parse(JSON.stringify(opts["OTH"])));
                component.find("select_OTH3").set("v.options", JSON.parse(JSON.stringify(opts["OTH"])));

                component.find("select_ET1").set("v.options", JSON.parse(JSON.stringify(opts["ET"])));
                component.find("select_ET2").set("v.options", JSON.parse(JSON.stringify(opts["ET"])));
                component.find("select_ET3").set("v.options", JSON.parse(JSON.stringify(opts["ET"])));

                component.find("select_BF1").set("v.options", JSON.parse(JSON.stringify(opts["BF"])));
                component.find("select_BF2").set("v.options", JSON.parse(JSON.stringify(opts["BF"])));
                component.find("select_BF3").set("v.options", JSON.parse(JSON.stringify(opts["BF"])));
            } else if (state === "ERROR") {
                var errors = response.getError();
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    title: "错误",
                    type:"error",
                    message: errors[0].message
                });
                toastEvent.fire();
                
            }
        });
        $A.enqueueAction(action);
    },

	toggle_report : function(component, event, helper) {
        var modal_window = component.find('modal_window');
        var modal_bg = component.find('modal_bg');
        $A.util.toggleClass(modal_window, 'disp_none');
        $A.util.toggleClass(modal_bg, 'disp_none');

        if ($A.util.hasClass(modal_window, 'disp_none') && !component.get("v.recordId")) {
            $A.get('e.force:refreshView').fire();
        }
    },

    productcategoryChange : function(component, event, helper, dept, index) {
        var cmpId = 'select_'+dept;
        var myId;
        var o1Id;
        var o2Id;
        if (index == 1) {
            myId = cmpId + '1';
            o1Id = cmpId + '2';
            o2Id = cmpId + '3';
        } else if (index == 2){
            myId = cmpId + '2';
            o1Id = cmpId + '1';
            o2Id = cmpId + '3';
        } else {
            myId = cmpId + '3';
            o1Id = cmpId + '1';
            o2Id = cmpId + '2';
        }

        var product_category_id = component.find(myId).get('v.value');
        var pcId1 = component.find(o1Id).get('v.value');
        var pcId2 = component.find(o2Id).get('v.value');
        var p1bool = pcId1 != '' && pcId1 != null && pcId1 != undefined;
        var p2bool = pcId2 != '' && pcId2 != null && pcId2 != undefined;
        var p0bool = product_category_id != '' && product_category_id != null && product_category_id != undefined;
        if ((p1bool && p0bool && product_category_id == pcId1) || (p2bool && p0bool && product_category_id == pcId2)) {
            this.warning('不能选择同样的产品区分');
            component.find(myId).set('v.value','');
        }
    },

    selectHos : function(component, event, helper) {

    	var accid = component.get("v.record.Agency_Hospital__c");
    	var action = component.get("c.getDeptAmount");
    	action.setParams({ hospitalId : accid });
    	action.setCallback(this, function(response) {
    		var state = response.getState();
    		if (state === "SUCCESS") {
    			var amountMap = response.getReturnValue();
                var GIProduct_Category1 = '';
                var GIProduct_Category2 = '';
                var GIProduct_Category3 = '';
                var GIAim_Price__c1 = '';
                var GIAim_Price__c2 = '';
                var GIAim_Price__c3 = '';
                if (amountMap.GI.length > 0) {
                    GIProduct_Category1 = amountMap.GI[0].Product_Category__c;
                    if (amountMap.GI.length > 1) {
                        GIProduct_Category2 = amountMap.GI[1].Product_Category__c;
                    }
                    if (amountMap.GI.length > 2) {
                        GIProduct_Category3 = amountMap.GI[2].Product_Category__c;
                    }
                    GIAim_Price__c1 = amountMap.GI[0].Aim_Price__c;
                    GIAim_Price__c2 = amountMap.GI[1].Aim_Price__c;
                    GIAim_Price__c3 = amountMap.GI[2].Aim_Price__c;
                    console.warn(GIAim_Price__c1+"-"+GIAim_Price__c2+"-"+GIAim_Price__c3);

                }
                component.find('select_GI1').set('v.value',GIProduct_Category1);
                component.find('select_GI2').set('v.value',GIProduct_Category2);
                component.find('select_GI3').set('v.value',GIProduct_Category3);
                component.find('select_GI4_1').set('v.value',GIAim_Price__c1);
                component.find('select_GI4_2').set('v.value',GIAim_Price__c2);
                component.find('select_GI4_3').set('v.value',GIAim_Price__c3);


                var ETProduct_Category1 = '';
                var ETProduct_Category2 = '';
                var ETProduct_Category3 = '';
                var ETAim_Price__c1 = '';
                var ETAim_Price__c2 = '';
                var ETAim_Price__c3 = '';
                if (amountMap.ET.length > 0) {
                    ETProduct_Category1 = amountMap.ET[0].Product_Category__c;
                    if (amountMap.ET.length > 1) {
                        ETProduct_Category2 = amountMap.ET[1].Product_Category__c;
                    }
                    if (amountMap.ET.length > 2) {
                        ETProduct_Category3 = amountMap.ET[2].Product_Category__c;
                    }
                    ETAim_Price__c1 = amountMap.ET[0].Aim_Price__c;
                    ETAim_Price__c2 = amountMap.ET[1].Aim_Price__c;
                    ETAim_Price__c3 = amountMap.ET[2].Aim_Price__c;
                } 
                component.find('select_ET1').set('v.value',ETProduct_Category1);
                component.find('select_ET2').set('v.value',ETProduct_Category2);
                component.find('select_ET3').set('v.value',ETProduct_Category3);
                component.find('select_ET4_1').set('v.value',ETAim_Price__c1);
                component.find('select_ET4_2').set('v.value',ETAim_Price__c2);
                component.find('select_ET4_3').set('v.value',ETAim_Price__c3);


                var BFProduct_Category1 = '';
                var BFProduct_Category2 = '';
                var BFProduct_Category3 = '';
                var BFAim_Price__c1 = '';
                var BFAim_Price__c2 = '';
                var BFAim_Price__c3 = '';
                if (amountMap.BF.length > 0) {
                    BFProduct_Category1 = amountMap.BF[0].Product_Category__c;
                    if (amountMap.BF.length > 1) {
                        BFProduct_Category2 = amountMap.BF[1].Product_Category__c;
                    }
                    if (amountMap.BF.length > 2) {
                        BFProduct_Category3 = amountMap.BF[2].Product_Category__c;
                    }
                    BFAim_Price__c1 = amountMap.BF[0].Aim_Price__c;
                    BFAim_Price__c2 = amountMap.BF[1].Aim_Price__c;
                    BFAim_Price__c3 = amountMap.BF[2].Aim_Price__c;
                } 
                component.find('select_BF1').set('v.value',BFProduct_Category1);
                component.find('select_BF2').set('v.value',BFProduct_Category2);
                component.find('select_BF3').set('v.value',BFProduct_Category3);
                component.find('select_BF4_1').set('v.value',BFAim_Price__c1);
                component.find('select_BF4_2').set('v.value',BFAim_Price__c2);
                component.find('select_BF4_3').set('v.value',BFAim_Price__c3);
               

                var OTHProduct_Category1 = '';
                var OTHProduct_Category2 = '';
                var OTHProduct_Category3 = '';
                var OTHAim_Price__c1 = '';
                var OTHAim_Price__c2 = '';
                var OTHAim_Price__c3 = '';
                if (amountMap.OTH.length > 0) {

                    OTHProduct_Category1 = amountMap.OTH[0].Product_Category__c;
                    if (amountMap.OTH.length > 1) {
                        OTHProduct_Category2 = amountMap.OTH[1].Product_Category__c;
                    }
                    if (amountMap.OTH.length > 2) {
                        OTHProduct_Category3 = amountMap.OTH[2].Product_Category__c;
                    }
                    OTHAim_Price__c1 = amountMap.OTH[0].Aim_Price__c;
                    OTHAim_Price__c2 = amountMap.OTH[1].Aim_Price__c;
                    OTHAim_Price__c3 = amountMap.OTH[2].Aim_Price__c;
                } 
                component.find('select_OTH1').set('v.value',OTHProduct_Category1);
                component.find('select_OTH2').set('v.value',OTHProduct_Category2);
                component.find('select_OTH3').set('v.value',OTHProduct_Category3);
                component.find('select_OTH4_1').set('v.value',OTHAim_Price__c1);
                component.find('select_OTH4_2').set('v.value',OTHAim_Price__c2);
                component.find('select_OTH4_3').set('v.value',OTHAim_Price__c3);
                
                var GSProduct_Category1 = '';
                var GSProduct_Category2 = '';
                var GSProduct_Category3 = '';
                var GSPAim_Price__c1 = '';
                var GSPAim_Price__c2 = '';
                var GSPAim_Price__c3 = '';
                if (amountMap.GS.length > 0) {
                    GSProduct_Category1 = amountMap.GS[0].Product_Category__c;
                    if (amountMap.GS.length > 1) {
                        GSProduct_Category2 = amountMap.GS[1].Product_Category__c;
                    }
                    if (amountMap.GS.length > 2) {
                        GSProduct_Category3 = amountMap.GS[2].Product_Category__c;
                    }
                    GSPAim_Price__c1 = amountMap.GS[0].Aim_Price__c;
                    GSPAim_Price__c2 = amountMap.GS[1].Aim_Price__c;
                    GSPAim_Price__c3= amountMap.GS[2].Aim_Price__c;
                } 
                component.find('select_GS1').set('v.value',GSProduct_Category1);
                component.find('select_GS2').set('v.value',GSProduct_Category2);
                component.find('select_GS3').set('v.value',GSProduct_Category3);
                component.find('select_GS4_1').set('v.value',GSPAim_Price__c1);
                component.find('select_GS4_2').set('v.value',GSPAim_Price__c2);
                component.find('select_GS4_3').set('v.value',GSPAim_Price__c3);
                
                var UROProduct_Category1 = '';
                var UROProduct_Category2 = '';
                var UROProduct_Category3 = '';
                var UROAim_Price__c1 = '';
                var UROAim_Price__c2 = '';
                var UROAim_Price__c3 = '';
                if (amountMap.URO.length > 0) {

                    UROProduct_Category1 = amountMap.URO[0].Product_Category__c;
                    if (amountMap.URO.length > 1) {
                        UROProduct_Category2 = amountMap.URO[1].Product_Category__c;
                    }
                    if (amountMap.URO.length > 2) {
                        UROProduct_Category3 = amountMap.URO[2].Product_Category__c;
                    }
                    UROAim_Price__c1 = amountMap.URO[0].Aim_Price__c;
                    UROAim_Price__c2 = amountMap.URO[1].Aim_Price__c;
                    UROAim_Price__c3 = amountMap.URO[2].Aim_Price__c;
                } 
                component.find('select_URO1').set('v.value',UROProduct_Category1);
                component.find('select_URO2').set('v.value',UROProduct_Category2);
                component.find('select_URO3').set('v.value',UROProduct_Category3);
                component.find('select_URO4_1').set('v.value',UROAim_Price__c1);
                component.find('select_URO4_2').set('v.value',UROAim_Price__c2);
                component.find('select_URO4_3').set('v.value',UROAim_Price__c3);

               
                var GYNProduct_Category1 = '';
                var GYNProduct_Category2 = '';
                var GYNProduct_Category3 = '';
                var GYNAim_Price__c1 = '';
                var GYNAim_Price__c2 = '';
                var GYNAim_Price__c3 = '';
                if (amountMap.GYN.length > 0) {

                    GYNProduct_Category1 = amountMap.GYN[0].Product_Category__c;
                    if (amountMap.GYN.length > 1) {
                        GYNProduct_Category2 = amountMap.GYN[1].Product_Category__c;
                    }
                    if (amountMap.GYN.length > 2) {
                        GYNProduct_Category3 = amountMap.GYN[2].Product_Category__c;
                    }
                    GYNAim_Price__c1= amountMap.GYN[0].Aim_Price__c;
                    GYNAim_Price__c2 = amountMap.GYN[1].Aim_Price__c;
                    GYNAim_Price__c3 = amountMap.GYN[2].Aim_Price__c;
                } 
                component.find('select_GYN1').set('v.value',GYNProduct_Category1);
                component.find('select_GYN2').set('v.value',GYNProduct_Category2);
                component.find('select_GYN3').set('v.value',GYNProduct_Category3);
                component.find('select_GYN4_1').set('v.value',GYNAim_Price__c1);
                component.find('select_GYN4_2').set('v.value',GYNAim_Price__c2);
                component.find('select_GYN4_3').set('v.value',GYNAim_Price__c3);

                
                var ENTProduct_Category1 = '';
                var ENTProduct_Category2 = '';
                var ENTProduct_Category3 = '';
                var ENTAim_Price__c1 = '';
                var ENTAim_Price__c2 = '';
                var ENTAim_Price__c3 = '';
                if (amountMap.ENT.length > 0) {

                    ENTProduct_Category1 = amountMap.ENT[0].Product_Category__c;
                    if (amountMap.ENT.length > 1) {
                        ENTProduct_Category2 = amountMap.ENT[1].Product_Category__c;
                    }
                    if (amountMap.ENT.length > 2) {
                        ENTProduct_Category3 = amountMap.ENT[2].Product_Category__c;
                    }
                    ENTAim_Price__c1 = amountMap.ENT[0].Aim_Price__c;
                    ENTAim_Price__c2 = amountMap.ENT[1].Aim_Price__c;
                    ENTAim_Price__c3 = amountMap.ENT[2].Aim_Price__c;
                } 
                component.find('select_ENT1').set('v.value',ENTProduct_Category1);
                component.find('select_ENT2').set('v.value',ENTProduct_Category2);
                component.find('select_ENT3').set('v.value',ENTProduct_Category3);
                component.find('select_ENT4_1').set('v.value',ENTAim_Price__c1);
                component.find('select_ENT4_2').set('v.value',ENTAim_Price__c2);
                component.find('select_ENT4_3').set('v.value',ENTAim_Price__c3);

    		}
    	});
		$A.enqueueAction(action);
        console.log(JSON.stringify(component.find('input-hos').get("v.body")[0].get('v')));
    },

    getOCMTerm : function() {
        var today = new Date();
        var year = today.getFullYear();
        var month = today.getMonth();

        if (month < 3) year -= 1;
        // var thisYearOCMPeriod = String(year - 1867) + 'P';
        var thisYearOCMPeriod ='FY'+String(year + 1) ;
        return thisYearOCMPeriod;
    },
    // WYL 2024-08-29
    getOCMTerm2 : function() {
        var today = new Date();
        var year = today.getFullYear();
        var month = today.getMonth();

        if (month < 3) year -= 1;
        var thisYearOCMPeriod = String(year - 1867) + 'P';
        return thisYearOCMPeriod;
    },
    createTarget : function(component, event, helper) {
    	var action = component.get("c.createSFTarget");
    	// alert(component.get("v.GIAmount"));



        var  p1_1 = component.find('select_GI4_1').get('v.value') ; 
        var  p1_2 = component.find('select_GI4_2').get('v.value') ;
        var  p1_3 = component.find('select_GI4_3').get('v.value') ;

        if (p1_1 == undefined || p1_1 == "") {
            p1_1 = 0;
        }
        if (p1_2 == undefined || p1_2 == "") {
            p1_2 = 0;
        }
        if (p1_3 == undefined || p1_3 == "") {
            p1_3 = 0;
        }
        console.warn(p1_1+"-"+p1_2+"-"+p1_3);

        var  p2_1 = component.find('select_ET4_1').get('v.value') ; 
        var  p2_2 = component.find('select_ET4_2').get('v.value') ;
        var  p2_3 = component.find('select_ET4_3').get('v.value') ;
        if (p2_1 == undefined || p2_1 == "") {
            p2_1 = 0;
        }
        if (p2_2 == undefined || p2_2 == "") {
            p2_2 = 0;
        }
        if (p2_3 == undefined || p2_3 == "") {
            p2_3 = 0;
        }


        var  p3_1 = component.find('select_BF4_1').get('v.value') ; 
        var  p3_2 = component.find('select_BF4_2').get('v.value') ;
        var  p3_3 = component.find('select_BF4_3').get('v.value') ;
        if (p3_1 == undefined || p3_1 == "") {
            p3_1 = 0;
        }
        if (p3_2 == undefined || p3_2 == "") {
            p3_2 = 0;
        }
        if (p3_3 == undefined || p3_3 == "") {
            p3_3 = 0;
        }



        var  p4_1 = component.find('select_OTH4_1').get('v.value') ; 
        var  p4_2 = component.find('select_OTH4_2').get('v.value') ;
        var  p4_3 = component.find('select_OTH4_3').get('v.value') ;
        if (p4_1 == undefined || p4_1 == "") {
            p4_1 = 0;
        }
        if (p4_2 == undefined || p4_2 == "") {
            p4_2 = 0;
        }
        if (p4_3 == undefined || p4_3 == "") {
            p4_3 = 0;
        }



        var  p5_1 = component.find('select_GS4_1').get('v.value') ; 
        var  p5_2 = component.find('select_GS4_2').get('v.value') ;
        var  p5_3 = component.find('select_GS4_3').get('v.value') ;
        if (p5_1 == undefined || p5_1 == "") {
            p5_1 = 0;
        }
        if (p5_2 == undefined || p5_2 == "") {
            p5_2 = 0;
        }
        if (p5_3 == undefined || p5_3 == "") {
            p5_3 = 0;
        }



        var  p6_1 = component.find('select_URO4_1').get('v.value') ; 
        var  p6_2 = component.find('select_URO4_2').get('v.value') ;
        var  p6_3 = component.find('select_URO4_3').get('v.value') ;
        if (p6_1 == undefined || p6_1 == "") {
            p6_1 = 0;
        }
        if (p6_2 == undefined || p6_2 == "") {
            p6_2 = 0;
        }
        if (p6_3 == undefined || p6_3 == "") {
            p6_3 = 0;
        }

        var  p7_1 = component.find('select_GYN4_1').get('v.value') ; 
        var  p7_2 = component.find('select_GYN4_2').get('v.value') ;
        var  p7_3 = component.find('select_GYN4_3').get('v.value') ;
        if (p7_1 == undefined || p7_1 == "") {
            p7_1 = 0;
        }
        if (p7_2 == undefined || p7_2 == "") {
            p7_2 = 0;
        }
        if (p7_3 == undefined || p7_3 == "") {
            p7_3 = 0;
        }



        var  p8_1 = component.find('select_ENT4_1').get('v.value') ; 
        var  p8_2 = component.find('select_ENT4_2').get('v.value') ;
        var  p8_3 = component.find('select_ENT4_3').get('v.value') ;
        if (p8_1 == undefined || p8_1 == "") {
            p8_1 = 0;
        }
        if (p8_2 == undefined || p8_2 == "") {
            p8_2 = 0;
        }
        if (p8_3 == undefined || p8_3 == "") {
            p8_3 = 0;
        }


       
       
    	action.setParams({ 
    						"hospitalId" : component.get("v.record.Agency_Hospital__c"), 
    						"GIProduct" : [component.find('select_GI1').get('v.value'),component.find('select_GI2').get('v.value'),component.find('select_GI3').get('v.value'),p1_1,p1_2,p1_3], 
    						"ETProduct" : [component.find('select_ET1').get('v.value'),component.find('select_ET2').get('v.value'),component.find('select_ET3').get('v.value'),p2_1, p2_2,p2_3 ], 
    						"BFProduct" : [component.find('select_BF1').get('v.value'),component.find('select_BF2').get('v.value'),component.find('select_BF3').get('v.value'),p3_1 ,p3_2 ,p3_3 ],
    						"OTHProduct" : [component.find('select_OTH1').get('v.value'),component.find('select_OTH2').get('v.value'),component.find('select_OTH3').get('v.value'),p4_1,p4_2 ,p4_3],
    						"GSProduct" : [component.find('select_GS1').get('v.value'),component.find('select_GS2').get('v.value'),component.find('select_GS3').get('v.value'),p5_1,p5_2,p5_3],
    						"UROProduct" : [component.find('select_URO1').get('v.value'),component.find('select_URO2').get('v.value'),component.find('select_URO3').get('v.value'),p6_1,p6_2 ,p6_2 ],
    						"GYNProduct" : [component.find('select_GYN1').get('v.value'),component.find('select_GYN2').get('v.value'),component.find('select_GYN3').get('v.value'),p7_1,p7_2 ,p7_3 ],
    						"ENTProduct" : [component.find('select_ENT1').get('v.value'),component.find('select_ENT2').get('v.value'),component.find('select_ENT3').get('v.value'),p8_1 ,p8_2 ,p8_3],
                            // "OCMTerm" : component.get("v.OCMTerm")
                            "OCMTerm" : component.get("v.OCMTerm2") //20240829 WYL update
    					});
    	action.setCallback(this, function(response) {
            console.warn(response);
    		var state = response.getState();
    		if (state === "SUCCESS") {
    			var modal_window = component.find('modal_window');
		        var modal_bg = component.find('modal_bg');
		        $A.util.toggleClass(modal_window, 'disp_none');
		        $A.util.toggleClass(modal_bg, 'disp_none');

    			var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    title: "成功",
                    type:"success",
                    message: "保存成功"
                });
                toastEvent.fire();

                $A.get('e.force:refreshView').fire();
    		} else if (state === "INCOMPLETE") {
    			var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    title: "未完成!",
                    type:"error",
                    message: "未完成"
                });
                toastEvent.fire();
    		} else if (state === "ERROR") {
    			var errors = response.getError();
    			var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    title: "错误",
                    type:"error",
                    message: errors[0].message
                });
                toastEvent.fire();
    			
    		}
		});
		$A.enqueueAction(action);
    },

    warning : function(message) {
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams({
            title: "警告!",
            message: message,
            type:"warning",
            duration : "10000"
        });
        toastEvent.fire();
    },
    
    error : function(message) {
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams({
            title: "错误!",
            message: message,
            type:"error",
            duration : "10000"
        });
        toastEvent.fire();
    },

})