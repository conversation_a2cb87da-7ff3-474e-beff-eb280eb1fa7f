public with sharing class TenderDeleteController {
	// 用来获取页面招投标Id
	public String TenIds {get; set; }
	// 手动输入招投标项目Id用，来检索招投标
	// public String TenId { get; set; }
	public Tender_information__c TenInfo { get; set; }
	public boolean SaveErrorflag { get; set; }
	// 存数据库中获取的所有招投标
	// public List<TenInfo> TenInfoList { get; set; }
	public TenderDeleteController() {
		TenIds = ApexPages.currentPage().getParameters().get('id');
	}
	// public TenderDeleteController(ApexPages.StandardController controller) {
	// 	TenIds = ApexPages.currentPage().getParameters().get('id');
	// }
	// 逻辑删除
	public void init() {
		// System.debug('1234567' + TenIds);
		// if (TenInfo!=null) {
		// 	TenInfo.Logical_delete__c = true;
		// 	update TenInfo;
		// }
		TenInfo = [Select Id, InfoId__c, Logical_delete__c, ProjectId__c, Retain_Tender__c From Tender_information__c Where id = : TenIds];
	}

	// 制作sql
	// public String makeSoql(String inputTenId){
//   	String soql = 'select Id, InfoTitle__c, ProjectId__c';
//       soql += ' FROM Tender_information__c' ;
//       if(String.isNotBlank(inputTenId)){
//           soql += ' Where ProjectId__c like \'%' + inputTenId + '%\'';
//       }
//       return soql;
//   }

	//检索
	// public PageReference searchTender() {
	//     //存数据库获取到的所有学生
	//     TenInfoList = new List<TenInfo>();
	//     //检索招投标
	//     String soql = this.makeSoql(inputTenId);
	//     List<Tender_information__c> TenSelected = Database.query(soql);
	//     for(Tender_information__c Tens : TenSelected){
	//         TenInfoList.add(new TenInfo(Tens));
	//     }
	//     return null;
	// }
	// 刷新删除招投标页面
	public PageReference returnFresh() {
		// System.debug('1234567891234567890');
		String url = '/' + TenIds;
		PageReference ref =  new Pagereference(url);
		ref.setRedirect(true);
		return ref;
	}
	//保存
	public PageReference saveTenInfo()  {

		// System.debug('123456789');
		// 更新删除招投标
		List<Tender_information__c> updateTenInfoList = new List<Tender_information__c>();
		// 更新保留招投标
		// List<Tender_information__c> updateBTenList = new List<Tender_information__c>();
		// 如果点击保存后,未选则保留招投标则报错
		// SaveErrorflag = false;
		System.debug('--------' + TenInfo.Retain_Tender__c);
		if (String.isNotBlank(TenInfo.Retain_Tender__c)) {
			System.debug('---------2--------' + TenInfo.Retain_Tender__c);
			//要保留的招投标
			Tender_information__c BTen =  [select Id, InfoId__c From Tender_information__c
			                               Where Id = : TenInfo.Retain_Tender__c];

			// 保留招投标关联的询价
			List<Tender_Opportunity_Link__c> BlinksList = [select Opportunity__c
			        from Tender_Opportunity_Link__c
			        where Tender_information__c = :BTen.Id];
			Set<Id> BlinkOppId = new Set<Id>();
			// if (BlinksList.size() > 0) {
			for (Tender_Opportunity_Link__c Blink : BlinksList) {
				BlinkOppId.add(Blink.Opportunity__c);
			}
			// 删除项目关联但不与保留项目关联的询价关联信息
			List<Tender_Opportunity_Link__c> linksList = [select id, Opportunity__c, Tender_information__c
			        from Tender_Opportunity_Link__c
			        where Tender_information__c = :TenInfo.Id and Opportunity__c
			                                      not in : BlinkOppId];
			// 把删除招投标 关联的询价 赋给 保留招投标上
			List<Tender_Opportunity_Link__c> addlinksList = new List<Tender_Opportunity_Link__c>();
			// 删除招投标关联的询价
			// 增加一个判断 看看是否还会写空进去
			if (linksList != null && linksList.size() > 0) {
				for (Tender_Opportunity_Link__c link : linksList) {
					Tender_Opportunity_Link__c linkinfo =  new Tender_Opportunity_Link__c();
					linkinfo.Tender_information__c = BTen.Id;
					linkinfo.Opportunity__c = link.Opportunity__c;
					linkinfo.Tender_Opportunity_Uniq__c = BTen.Id + '' + link.Opportunity__c;
					linkinfo.IsRelated__c = true;
					// if (BlinksList.contains(linkinfo)) {
					addlinksList.add(linkinfo);
					// }
				}
				// 删除项目删掉关联询价
				// delete linksList;
				// 保留项目新增关联询价
				if (addlinksList.size() > 0) {
					insert addlinksList;
				}
			}
			// }
			// 互换保留招投标与删除招投标的信息Id
			TenInfo.Retain_Tender__c = BTen.Id;
			String BTenInfo = BTen.InfoId__c;
			BTen.InfoId__c = TenInfo.InfoId__c;//保留招投标的信息Id赋给删除招投标的信息Id
			TenInfo.InfoId__c = BTenInfo;//删除招投标的信息Id赋给保留招投标的信息Id
			// 点击保存后 删除招投标上的逻辑删除字段变为true
			TenInfo.Logical_delete__c = true;
			// update TenInfo;
			// 反正要更新 放在一起也是一样的
			updateTenInfoList.add(TenInfo);
			updateTenInfoList.add(BTen);
			if (!TenInfo.Id.equals(BTen.Id)) {
				update updateTenInfoList;
			}
			// updateBTenList.add(BTen);
			// update updateBTenList;
		}
		if (String.isNotBlank(TenInfo.Retain_Tender__c)&&String.isNotBlank(TenInfo.ProjectId__c)&&String.isNotBlank(TenInfo.InfoId__c)) {
			// 调用接口
			NFM504Controller.sendRequest(TenInfo.Id);
		}
		// System.debug('1111111122222' + SaveErrorflag);
		return null;
	}
	public void test() {
		integer i = 0;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
		i++;
	}
	//实体类
	// class TenInfo{
	//     public Tender_information__c Ten { get; set; }
	//     public TenInfo(Tender_information__c t) {
	//         Ten = t;
	//     }
	// }
}