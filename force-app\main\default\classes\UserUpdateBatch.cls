global class UserUpdateBatch implements Database.Batchable<sObject> , Database.AllowsCallouts{
    public String query;

    global UserUpdateBatch() {
        //this.query = query;
    }

    global Database.QueryLocator start(Database.BatchableContext bc) {
        Date dateToday = Date.today();
        return Database.getQueryLocator([select Id, Name,Employee_No__c,Stay_or_not__c,QuitDate__c,SendToComPlat__c,PBI_Enable__c from User where QuitDate__c <= :dateToday and Stay_or_not__c <> '已离职' and SendToComPlat__c <> true]);
    }

    global void execute(Database.BatchableContext BC, List < User > userList) {
        List <User> userNewList = new List <User> ();
        List <String> userIDList = new List <String> ();
        for (User user: userList) {
            user.SendToComPlat__c = true;
            user.PBI_Enable__c = false;
            user.Stay_or_not__c = '已离职';
            userIDList.add(user.Id);
            userNewList.add(user);
        }
        if (userNewList.size() > 0) {
            NFM621Controller.executefuture('',  userIDList);
            update userNewList;
        }

    }

    global void finish(Database.BatchableContext BC) {

    }
}