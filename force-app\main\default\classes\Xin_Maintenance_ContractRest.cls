@RestResource(urlMapping='/Xin_Maintenance_Contract/*')
global with sharing class Xin_Maintenance_ContractRest {

    @HttpPost
    global static void doPost(String query, String visitorPlaceId) {
        system.debug('Xin_Maintenance_ContractRest.start');
        Xin_Maintenance_Contract ctl = new Xin_Maintenance_Contract();
        ctl.search(query, visitorPlaceId);
        // 検索結果をMapのListに変換
        List<Map<String, String>> rsList = new List<Map<String, String>>();
        for (Maintenance_Contract__c sobj : ctl.results) {
            Map<String, String> rs = new Map<String, String>();
            if (rsList.size() == ctl.results.size() - 1 && ctl.getIsOverLimit() == true) {
                rs.put('Id', '');
                rs.put('Name', '');
                rs.put('DisplayName', '检索结果超过了30件');
                rsList.add(0, rs);
            } else {
                rs.put('Id', sobj.Id);
                rs.put('Name', sobj.Name);
                rs.put('DisplayName', sobj.Name);
                rsList.add(rs);
            }
        }
        // JSONを戻す
        RestResponse res = RestContext.response;
        res.addHeader('Content-Type', 'application/json');
        res.statusCode = 200;
        String jsonResponse = '{"status": "Success", "Message": ' + JSON.serialize(rsList) + '}';
        res.responseBody = blob.valueOf(jsonResponse);
        return;
    }
}