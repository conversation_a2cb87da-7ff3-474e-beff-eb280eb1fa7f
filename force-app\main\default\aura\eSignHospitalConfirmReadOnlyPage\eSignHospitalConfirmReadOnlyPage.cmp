<aura:component implements="force:appHostable,flexipage:availableForAllPageTypes"  
                access="global" controller="eSignHospitalConfirmController">
        <aura:attribute name="DNName" type="String" 
                    description="用来从父对象接收传递过来的DN号"
                    default=""/>
        <aura:attribute name="allWidth" type="String"
                    description="用来从父对象接收传递过来的屏幕宽度"
                    default=""/>
        <aura:attribute name="floatWidth" type="String"
                    description="用来从父对象接收传递过来的屏幕宽度"
                    default=""/>

        <aura:attribute name="DNSignUpStatus" type="string" default=""
                     description="用来传签收单状态的，但是看来没啥用似的"/>
        <aura:attribute name="IsSubmit" type="boolean" default="true"
                     description="判断是否录入表需要提交"/>
        <aura:attribute name="hideCheckboxColumn" type="boolean" default="true"
                     description="因为点上一页的时候不知道为什么置成false了所以用变量来设置"/>
        <aura:attribute name="hideButton" type="boolean" default="false"
                     description="只读页隐藏按钮"/>
        <aura:attribute name="AgencyClick" type="boolean" default="false"
                     description="判断是否已提交过一次"/>
        <aura:attribute name="recordId" type="String" default=""
                        description="在明细页点击下一步会上传录入表，
                                     然后返回的录入表ID存在这里，用于上传文件使用"/>
        <aura:attribute name="filetype" type="List" default="['.png', '.jpg', '.jpeg']"
                    description="如果对于上传文件的格式有要求，可以用这个，但是现在还没用"/>

        <aura:attribute name="HomePageDisplay" type="Boolean" default="true"/>
        <aura:attribute name="NextPageDisplay" type="Boolean" default="false"/>
        <!-- checkbox -->
        <aura:attribute name="IsAgencyOrHPShow" type="Boolean" default=""/>
        <aura:attribute name="entryType" type="String" default=""/>
        <aura:attribute name="chk" type="string" default=""/>
        <aura:attribute name="check" type="Boolean" default="true"/>
        <aura:attribute name="eSignForm" type="Object"
                    description="首页信息"/>
        <aura:attribute name="uploadFilePage" type="Boolean" default="false"
                    description="判断是否是文件上传页"/>
        <aura:attribute name="data" type="Object[]"
                        description="所有明细的信息"/>
        <aura:attribute name="currentData" type="Object[]"
                        description="明细页当前显示的明细的信息"/>
        <aura:attribute name="dataSize" type="Integer" default="0"
                        description="明细页当前显示的明细的数量"/> 
        <aura:attribute name="columns" type="List"
                         description="明细页表格的每列构造的设定"/>
            <aura:attribute name="pageNumber" type="Integer" default="1"
                        description="明细页表格的第几页"/>
        <aura:attribute name="pageSize" type="Integer" default="5"
                        description="明细页表格的每页明细容量"/>
        <aura:attribute name="isLastPage" type="Boolean" default="false"
                         description="是否最后一页，如果是最后一页，那么表格分页的下一页不可用"/>
        <aura:attribute name="draftValues" type="Object" default="[]"
                        description="明细页表格的修改内容，保存后从这里读值存到当前表格里面"
                        />
        <aura:attribute name="successMessage" type="String"
                        description="成功消息"/>
        <aura:attribute name="errorMessage" type="String"
                        description="失败消息"/>
        <!-- 适用+检索  start-->
        <aura:attribute name="searchSize" type="Integer" default=""
                    description="检索数据的长度"/>
        <aura:attribute name="searchCase" type="Integer" default=""
                        description="存放检索值，保留缓存"/>
        <aura:attribute name="searchCaseKey" type="Integer" default=""
                        description="存放检索值，赋值给searchCase"/>
        <aura:attribute name="searchFlag" type="boolean" default=""
        description="搜索框是否有值"/>
        <aura:attribute name="lineItemSize" type="Integer" default=""
                        description="存放一共有多少条明细size"/>
        <!-- 医院电子邮件 -->
        <aura:attribute name="HPWorkflowEmailBack" type="String"
                        description="医院邮箱"/>
        <aura:attribute name="applyButton" type="String" default=""
                        description="显示适用按钮"/>
        <aura:attribute name="allApplyButton" type="String" default=""
                        description="显示全部按钮"/>
        <!-- 区分直销、分销的区别标识  传过来 -->
        <aura:attribute name="DirectFlag" type="Boolean" default=""/>
        <aura:attribute name="handlingOpinionsPage" type="Boolean" default="false"
    description="判断是否是处理意见页"/>
        <!-- 适用+检索 end -->
        <aura:handler name="init" action="{!c.doInit}" value="{!this}"
                  description="初始化方法"/>


        <!-- 复选框组 -->
        <aura:attribute name="options" type="List" default="[
        {'label': '上述内容已阅读（默认勾选）点“下一步”到明细页', 'value': 'option1'}
        
        ]"/>
        <aura:attribute name="value" type="List" default="option1"/>
        
        <div>
            <lightning:spinner aura:id="mySpinner" size="large"
                               description="加载中组件，如果不隐藏整个页面就在加载中"/>
        </div>

        <!-- 成功toast 组件的实现-->
        <div class="slds-modal slds-fade-in-open slds-hide" aura:id="successDiv">
            <div class="demo-only" style="height: 8rem;width:40%">
                <div class="slds-notify_container slds-is-relative">
                    <div class="slds-notify slds-notify_toast slds-theme_success" role="alert">
                        <div class=" slds-m-left_xx-large">
                            <div class="slds-notify__content slds-m-left_small slds-align_absolute-center slds-m-left_xx-large">
                                <lightning:icon alternativeText="Success" iconName="utility:success" size="small"
                                                class="buttonColor slds-m-right_small" />
                                <h2 class="slds-text-heading_small ">{!v.successMessage}</h2>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 失败toast 组件的实现-->
        <div class="slds-modal slds-fade-in-open slds-hide" aura:id="errorDiv">
            <div class="demo-only" style="height: 8rem;" id="errorSonDiv1">
                <div class="slds-notify_container slds-is-relative">
                    <div class="slds-notify slds-notify_toast slds-theme_error" role="status" id="errorSonDiv2" style="">
                        <div class="">
                            <div class="slds-notify__content slds-m-left_small slds-align_absolute-center">
                                <lightning:icon alternativeText="error" iconName="utility:error" size="small"
                                                class="buttonColor slds-m-right_small" />
                                <h2 class="slds-text-heading_small ">
                                    <div id="errorSonDiv3" style=" word-break: break-all;overflow: auto;">
                                    {!v.errorMessage}
                                    </div>
                                </h2>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <aura:registerEvent name="componentEvent" type="c:BackToHomePage"/>
        <!-- 首页 -->
        <aura:if isTrue="{!v.HomePageDisplay}">
        <p style="font-size: 17px;">
        首页
        </p>
        <div  id="qs"  style="   margin-bottom: 5%;" class="slds-card" >
            <aura:if isTrue="{!v.DirectFlag}">
                    <!-- 首页，由于与医院确认页公用相同主页，所以使用这个组件,然后可以公用 -->
                    <c:eSignAgencyHomePage
                                   HospitalAcceptanceResults = "{!v.HospitalAcceptanceResults}"
                                   eSignForm = "{!v.eSignForm}"
                                   />
                <aura:set attribute="else">
                    <!-- 首页，由于与医院确认页公用相同主页，所以使用这个组件,然后可以公用 -->
                    <c:eSignHospitalHomePage
                                   HospitalAcceptanceResults = "{!v.HospitalAcceptanceResults}"
                                   eSignForm = "{!v.eSignForm}"
                                   />
                </aura:set>
            </aura:if>
            
        <!-- 高度 -->
       <!--  <div style="height: 20px;"></div> -->
       
        <!-- 注意事项： -->
        <div  id="attention"  style=" position:relative;margin-right: 4px;  margin-right: 4px; " class="" >
            <lightning:layout horizontalAlign="left" class="slds-m-top_large">
                    <!-- 未设置layoutItem的size大小 不涉及并列这种情况 目前自适应 -->
                         <lightning:layoutItem padding="around-small" >
                            <div class="header-column">
                               <p class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;">注意事项 ： </p>
                               <p class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;"> 1、收到货物请在24小时内确认 </p>
                               <p class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;"> 2、货物外观破损请当面与送货人签字确认 </p>
                               <p class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;"> 3、书面通知供货者破损情况并描述破损状态 </p>
                               <p class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;"> 4、必要时请拍照记录 </p>
                               <!-- 须知 -->
                            </div>
                        </lightning:layoutItem>
                </lightning:layout>

            <!-- 须知 -->
                <lightning:layout horizontalAlign="left" class="slds-m-top_small">
                <!-- 未设置layoutItem的size大小 不涉及并列这种情况 目前自适应 -->
                        <lightning:layoutItem padding="around-small">
                            <!-- <lightning:checkboxGroup name="checkbox"
                                 label=""
                                 options="{! v.options }"
                                 value="{! v.value }"
                                 onchange="{! c.handleChange }"/> -->
                                 <lightning:input type="checkbox" label="上述内容已阅读（默认勾选）点“下一步”到明细页" id="chk" name="chk" checked="{!v.check}" disabled="true" />
                        </lightning:layoutItem>
                </lightning:layout>
            
        </div>  

        <!-- 从首页到检索页的上一步按钮 -->
        <div class="slds-float_left">
            <lightning:button variant="brand"
                              label="上一步" title="上一步" onclick="{! c.BackToHomePage }" />
        </div>
        

        <div class="slds-float_right">            
                <lightning:button variant="brand" 
                                  label="下一步" title="下一步" onclick="{! c.hospitalDetailsPageNextClick }" />
        </div>
</div>
   <!--  </lightning:datatable> -->
    </aura:if> 
    <aura:if isTrue="{!v.NextPageDisplay}">
        <div id="header" style="{!'width:'+v.floatWidth+'px;'}">
            <tr>
            <td class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;" align="left">（向右滑动查看更多信息） 
            
            </td>
            </tr>
               
        </div>
        <div id="searchDiv" class="slds-page-header" role="banner"  style="{!'width:'+v.floatWidth+'px;'}">
            <div class="slds-float_center" style="text-align:center">
            <tr>
            <td align="right" style="{!'padding-left:'+v.floatWidth*0.15+'px;'}">箱号:
            <input id="searchInput" lightning-input_input ="" type="text" class="slds-input" value="{!v.searchCase}" name=""  onchange="{!c.searchByCaseNumber}" style="{!'width:'+v.floatWidth*0.2+'px;'}"/>
            </td>

            <td style="padding-left: 5px;">
            <aura:if isTrue="{!v.searchFlag}">
                    该箱共有{!v.searchSize}条数据。
                <aura:set attribute="else">
                    该单共有{!v.lineItemSize}条数据。
                </aura:set>
            </aura:if>
            </td>
            </tr>
            </div>
        </div>       
        <div id="allDiv" style="{!'width:'+v.allWidth+'px;overflow-x:scroll;'}">
        <div  id="in" style=" width:763px;margin-right: 4px; ">
            <!-- 显示在明细页的当前明细的表格 -->
            <div id="inTable" >
                <!-- <lightning:card> -->
                    <aura:set attribute="title">
                        货物签收单明细
                    </aura:set>
                
                    <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-hint-parent slds-no-row-hover " style="table-layout: fixed;">
                        <thead>
                            <tr class="slds-line-height_reset slds-text-title_caps">
                                <!-- 第一列 产品型号 -->
                                <th  class="slds-is-resizable" tabindex="-1" data-label="产品型号"  scope="row">
                                            
                                    <span class="slds-grid slds-grid_align-spread" style="
                                            width: 110px;text-align: left;display:block;">
                                            <div class="slds-truncate">
                                                <span title="产品型号" class="slds-truncate" style="padding-bottom: 3px;">产品型号</span>    </div>
                                    </span>
                                                
                                            
                                    
                                </th>
                                <!-- 第二列 CODE -->
                                <!-- <th  class="slds-is-resizable" tabindex="-1" data-label="CODE"  scope="row" style="width: 137px;height: 35px; outline: none;">
                                          
                                    <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                        <div class="slds-truncate">
                                                <span title="CODE" class="slds-truncate" style="padding-bottom: 3px;">CODE</span>    
                                        </div>          
                                                      
                                    </span>
                                                    
                                    
                                </th> -->
                                
                                <!-- 第三列 产品名称-->
                                <th  class="slds-is-resizable" tabindex="-1" data-label="产品名称"  scope="row" style="width: 361px;height: 35px;">
                                            
                                    <span class="slds-grid slds-grid_align-spread" style="text-align: left;display:block;">
                                        <div class="slds-truncate">
                                                <span title="产品名称" class="slds-truncate" style="padding-bottom: 3px;">产品名称</span>   
                                                
                                            
                                        </div>
                                    </span>
                                        
                                    
                                </th>

                                <!-- 第四列 数量-->
                                <th  class="slds-is-resizable" tabindex="-1" data-label="数量"  scope="row" style="width: 50px;height: 35px;">
                                            
                                    <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                        <div class="slds-truncate">
                                                <span title="数量" class="slds-truncate" style="padding-bottom: 3px;">数量</span>   
                                                
                                            
                                        </div>
                                    </span>
                                                
                                        
                                    
                                </th>
                                
                                <!-- 第五列 箱号-->
                                <th  class="slds-is-resizable" tabindex="-1" data-label="箱号"  scope="row" style="width: 100px;height: 35px;">
                                            
                                    <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                        <div class="slds-truncate">
                                                <span title="箱号" class="slds-truncate" style="padding-bottom: 3px;">箱号</span>   
                                                
                                            
                                        </div>
                                    </span>
                                                
                                        
                                    
                                </th>
                                <!-- 第六列  货物情况-->
                                <th  class="slds-is-resizable" tabindex="-1" data-label="货物情况"  scope="row" style="width: 110px;height: 35px;">
                                            
                                    <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                        <div class="slds-truncate">
                                                <span title="货物情况" class="slds-truncate" style="padding-bottom: 3px;">货物情况</span>   
                                                
                                            
                                        </div>
                                    </span>
                                                
                                        
                                    
                                </th>
                                <!-- 第七列  处理意见-->
                                <!-- <th  class="slds-is-resizable" tabindex="-1" data-label="处理意见"  scope="row" style="width: 170px;height: 35px;">
                                            
                                    <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                        <div class="slds-truncate">
                                                <span title="处理意见" class="slds-truncate" style="padding-bottom: 3px;">处理意见</span>   
                                                
                                            
                                        </div>
                                    </span>
                                                
                                        
                                    
                                </th> -->
                                <!-- 第八列  货物情况-->
                                <!-- <th  class="slds-is-resizable" tabindex="-1" data-label="经销商确认结果"  scope="row" style="width: 140px;height: 35px;">
                                            
                                    <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                        <div class="slds-truncate">
                                                <span title="经销商确认结果" class="slds-truncate" style="padding-bottom: 3px;">经销商确认结果</span>   
                                                
                                            
                                        </div>
                                    </span>
                                                
                                        
                                    
                                </th> -->
                                
                                
                            </tr>
                        </thead>
                        <aura:if isTrue="{!v.currentData.length > 0}">
                            <tbody>
                                <aura:iteration items="{!v.currentData}" var="curr" indexVar="idx">
                            <tr class="slds-hint-parent">
                            <!-- 行项目也用th 因为用td标签点击列的高光 在苹果设备上 有痕迹 -->
                            <!-- 行项目 第一列 产品型号 -->
                            <th  class="slds-is-resizable" tabindex="-1" data-label="产品型号"  scope="row">
                                
                                        <span class="slds-grid slds-grid_align-spread" style="
                                            width: 110px;
                                            padding-top: 6px;height:30px;
                                        ">
                                            <div class="slds-truncate">
                                                    <lightning:formattedText value="{!curr.Asset_Model_No__c}" />                        
                                            </div>
                                        </span>
                                    
                                
                        
                            </th>
                                        
                            <!-- 行项目 第二列  CODE-->
                            <!-- <th  class="slds-is-resizable" tabindex="-1" data-label="CODE"  scope="row">
                              
                                        <span class="slds-grid slds-grid_align-spread">
                                            <div class="slds-truncate">
                                                    <lightning:formattedText value="{!curr.OTCode__c}" />
                                                    
                                                    
                                                
                                            </div>
                                        </span>
                                    
                                
                        
                            </th> -->
                            <!-- 行项目 第三列  产品名称-->          
                            <th  class="slds-is-resizable" tabindex="-1" data-label="产品名称"  scope="row" style="white-space:nowrap;
                            overflow:hidden;
                            text-overflow: ellipsis;">
                                
                                        <span class="slds-grid slds-grid_align-spread" style="
                                            width: 361px;
                                            padding-top: 6px;height:31px;">
                                            <div class="slds-truncate">
                                                    <lightning:formattedText value="{!curr.productName__c}" />
                                                    
                                                
                                            </div>
                                        </span>
                                    
                            
                        
                            </th>
                            <!-- 行项目 第四列  数量-->           
                            <th  class="slds-is-resizable" tabindex="-1" data-label="数量"  scope="row" >
                              
                                        <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                            <div class="slds-truncate">
                                                    <lightning:formattedText value="{!curr.Count_Text__c}" />
                                                    
                                                
                                            </div>
                                        </span>
                                    
                                
                        
                            </th>
                            <!-- 行项目 第五列  箱号-->          
                            <th  class="slds-is-resizable" tabindex="-1" data-label="箱号"  scope="row">
                             
                                        <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                            <div class="slds-truncate">
                                                    <lightning:formattedNumber id="{!curr.Id+':'+idx+':CaseNumber__c'}" value="{!curr.CaseNumber__c}"/>
                                                    
                                                
                                            </div>
                                        </span>
                                    
                                
                        
                            </th>
                            <!-- 行项目 第六列  货物情况-->          
                            <th  class="slds-is-resizable" tabindex="-1" data-label="货物情况"  scope="row">
                             
                                        <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                            <div class="slds-truncate">
                                                    <lightning:formattedText value="{!curr.HPGoodStatus__c}" />
                                                    
                                                
                                            </div>
                                        </span>
                                    
                                
                        
                            </th>
                            <!-- 行项目 第七列  处理意见-->          
                            <!-- <th  class="slds-is-resizable" tabindex="-1" data-label="处理意见"  scope="row">
                             
                                        <span class="slds-grid slds-grid_align-spread" style="text-align: center;display:block;">
                                            <div class="slds-truncate">
                                                    <lightning:formattedText value="{!curr.handleOpinion__c}" />
                                                    
                                                
                                            </div>
                                        </span>
                                    
                                
                        
                            </th> -->

                            <!-- 行项目 第八列  经销商确认结果-->          
                            <!-- <th  class="slds-is-resizable" tabindex="-1" data-label="经销商确认结果"  scope="row">
                                        
                                        <input id="{!curr.Id}" lightning-input_input ="" type="text" class="slds-input" value="{!curr.agencyConfirmResult__c}"    readonly="readonly" style="border-style:hidden;"/>
                                    
                                        
                            </th> -->
                 
                            </tr>
                        </aura:iteration>
                    </tbody>
                    
                </aura:if>
            </table>
                    
                    <!-- <div class="slds-box">
                        
                        <div align="center" style="float: left;padding-left: 370px;">
                            <lightning:button variant="neutral"
                                                              label="cancel" title="cancel" onclick=""  />
                        </div>
                        
                        <div align="center" style="padding-right: 370px;">
                            <lightning:button variant="brand"
                                                      label="save" title="save" onclick=""/>
                        </div>      
                    </div> -->
                    <aura:if isTrue="{!empty(v.currentData)}">
                        <div class="slds-align_absolute-center">
                            No records found
                        </div>
                    </aura:if>
        <!-- </lightning:card> -->



            </div>
            </div>
            </div>

            <!-- 分页功能的上一页和下一页， 还是最左侧的显示当前页数的逻辑-->
            <div id="floatDiv" style="{!'width:'+v.floatWidth+'px;'}">
            <div class="slds-clearfix">
                <div class="slds-page-header" role="banner">
                    <div class="slds-float_center" style="text-align:center">
                        <!-- iconName="utility:chevronleft" iconPosition="left" -->
                        <lightning:button  iconName="utility:left"
                                          onclick="{!c.handleHome}" disabled="{! v.pageNumber == 1}"/>
                        <lightning:button  iconName="utility:chevronleft" iconPosition="left"
                                          onclick="{!c.handlePrev}" disabled="{! v.pageNumber == 1}"/>
                        <span title="当前页">&nbsp;&nbsp;&nbsp;{!v.pageNumber}&nbsp;&nbsp;&nbsp;
                            </span>
                        <lightning:button  iconName="utility:chevronright" iconPosition="right"
                                          disabled="{! v.isLastPage}" onclick="{!c.handleNext}"/>
                        <!-- iconName="utility:chevronright" iconPosition="right" -->
                        <lightning:button  iconName="utility:right" 
                                          disabled="{! v.isLastPage}" onclick="{!c.handleLast}"/>

                    </div>
                    
                </div>
            </div>
            <!-- 明细页跳转至文件上传页的按钮 -->
            <div class="slds-float_left">
                <div  id="attention"  style=" position:relative;margin-right: 4px;  margin-right: 4px; " class="" >
                    <!-- 确认提交 -->
                    <lightning:button variant="brand"
                                  label="上一步" title="上一步" onclick="{! c.handleShowPageNextClick }" />
                </div>
            </div>

            <aura:if isTrue="{!v.DirectFlag}">
                    <div class="slds-float_right">
                        <lightning:button variant="brand" 
                                          label="下一步" title="下一步" onclick="{!c.OpinionsTODetailsPage }" />
                    </div>
                <aura:set attribute="else">
                    <!-- 返回首页 -->
                    <div class="slds-float_right">
                        <lightning:button variant="brand" 
                                          label="退回首页" title="退回首页" onclick="{! c.BackToHomePage }" />
                    </div>
                </aura:set>
            </aura:if>

            </div>


    </aura:if> 

    <aura:if isTrue="{!v.handlingOpinionsPage}">
        <div class="slds-box">
        <div id="page-footer" style="   margin-bottom: 5%;" class="slds-card" >
            <lightning:layout multipleRows="true">
                <lightning:layoutItem padding="around-xsmall" size="12">
                    <div class="page-section page-header" style="height: 25px;">
                        <h2><b>营业担当处理意见如下：</b></h2>
                    </div>
                     <div class="page-section page-right" style="{!'width:'+v.floatWidth*0.8+'px;'}" >
                                
                    <p style="word-break: break-word;text-indent: 2em;">{!v.eSignForm.handleOpinionHP__c}
                    </p>
                </div>
                </lightning:layoutItem>
                
                
            </lightning:layout>
            
        </div>
            <p style="word-break: break-word;text-indent: 2em;">对以上处理意见无疑义，请点击确认。如有其它问题，请联系奥林巴斯当地营业人员。</p>

            <!-- <lightning:input aura:id="inputCmp" label="如有需要，请填写您的邮箱:" name="fullName" value=""
    /> -->
            
    </div>
        <div id="footerDiv" style="{!'width:'+v.floatWidth+'px;display: flex;flex-direction: row;'}">
            <!-- class="slds-float_left" -->
            <div style="text-align:left;flex: 1;padding-top: 1px;">
                <lightning:button variant="brand" 
                                  label="上一步" title="上一步" onclick="{!c.NextOpinions}" />
            </div>
            <!-- style="text-align:center" -->
            <!-- <div style="text-align:center;flex: 1;padding-top: 1px;">
                    <lightning:button variant="brand" 
                                      label="确认" title="确认" onclick="{!c.submitClick }" />
            </div> -->
            <!-- class="slds-float_right" -->
            <div style="text-align:right;flex: 1;padding-top: 1px;">
                <lightning:button variant="brand" 
                                  label="返回" title="返回" onclick="{!c.BackToHomePage }" />
            </div>
        </div>
        
    </aura:if>

    <aura:if isTrue="{!v.uploadFilePage}">
        <!-- 文件上传组件 -->
        <lightning:fileUpload label="上传照片"
                              name="fileUploader"
                              recordId="{!v.recordId}"
                              multiple="true"
                              onuploadfinished="{!c.handleUploadFinished}"
                              />
        <p class="field-title" title="Field 3" style="font-weight:bold;font-size: 15px;padding-top: 7px;">（如不需要上传，则请关闭。）
        <!-- 如需上传，请选择照片。 -->
        </p>
        
    </aura:if>

</aura:component>