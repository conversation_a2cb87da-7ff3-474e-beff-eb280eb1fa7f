@isTest
private class UpdateRentalApplyBatchTest {
    final static Integer okStatus = 99;

    static void setupTestData() {
        // OLY_OCM-643 追加EscapeNFM001Trigger
        ControllerUtil.EscapeNFM001Trigger = true;
        // 病院を作る
        Account hospital = new Account();
        hospital.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'HP'].id;
        hospital.Name = 'test hospital';
        insert hospital;
        StaticParameter.EscapeAccountTrigger = true;

        // 戦略科室を得る
        Account[] strategicDep = [SELECT ID, Name FROM Account WHERE parentId = :hospital.Id AND recordType.DeveloperName = 'Department_Class_OTH'];
        // 診療科を作る
        Account dep = new Account();
        dep.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'Department_OTH'].id;
        dep.Name = 'test dep';
        dep.AgentCode_Ext__c = '9999998';
        dep.ParentId = strategicDep[0].Id;
        dep.Department_Class__c = strategicDep[0].Id;
        dep.Hospital__c = hospital.Id;
        insert dep;

        // 产品
        Product2 pro1 = new Product2(Name='name01',IsActive=true,Family='GI',Asset_Model_No__c='n01',Serial_Lot_No__c='S/N tracing',
            Fixture_Model_No__c='pc01',Fixture_Model_No_T__c = 'pc01',ProductCode_Ext__c='pc01',Manual_Entry__c=false);
        insert pro1;

        //保有设备
        Asset asset1 = new Asset(Asset_Owner__c = 'Olympus');
        asset1.RecordTypeId = System.Label.Asset_RecordType;
        asset1.SerialNumber = 'lw0001';
        asset1.Name = '测试设备0001';
        asset1.AccountId = dep.Id;
        asset1.Department_Class__c = strategicDep[0].Id;
        asset1.Hospital__c = hospital.Id;
        asset1.Product2Id = pro1.Id;
        asset1.Status = '使用中';
        asset1.Quantity = 1;
        asset1.Manage_type__c = '个体管理';
        insert asset1;

        // システム管理者
        User user = new User(Test_staff__c = true);
        user.LastName = '_サンブリッジ';
        // user.FirstName = 'う';
        user.Alias = 'う';
        user.Email = '<EMAIL>';
        user.Username = '<EMAIL>';
        user.CommunityNickname = 'う';
        user.IsActive = true;
        user.EmailEncodingKey = 'ISO-2022-JP';
        user.TimeZoneSidKey = 'Asia/Tokyo';
        user.LocaleSidKey = 'ja_JP';
        user.LanguageLocaleKey = 'ja';
        user.ProfileId = System.Label.ProfileId_SystemAdmin;
        user.Job_Category__c = '销售推广';
        user.Province__c = '上海市';
        user.Use_Start_Date__c = Date.today().addMonths(-6);
        user.Dept__c = '医疗华北营业本部';
        insert user;

        Repair__c repair1 = new Repair__c();
        repair1.Service_Repair_No__c = 'repair1';
        repair1.Hospital__c            = hospital.Id;
        repair1.Department_Class__c    = strategicDep[0].Id;
        repair1.Account__c             = dep.Id;
        repair1.Delivered_Product__c = asset1.Id;
        repair1.Repair_List_Price__c = 100;
        repair1.SalesOfficeCode_selection__c = '北京';
        repair1.Failure_Occurrence_Date__c = Date.today().addDays(-2);
        repair1.Service_contract_judege_day__c = Date.today().addDays(-2);
        repair1.Final_complete_day__c = Date.today().addDays(-1);
        repair1.Status__c = '3.维修阶段';
        repair1.Return_Without_Repair__c = true;
        repair1.Return_Without_Repair_Reason__c = '13.現象再現せず';
        repair1.Repair_Returned_To_HP_Date__c = Date.today();
        repair1.Repair_Shipped_Date__c = Date.today();
        insert repair1;

        // 备品配套(只有附属品)
        Fixture_Set__c fsObjC1 = new Fixture_Set__c();
        fsObjC1.Name = 'setC1';
        fsObjC1.Fixture_Set_Body_Model_No__c = 'modelNoC1';
        fsObjC1.Loaner_name__c = 'nameC1';
        insert fsObjC1;

        //Rental_Apply__c rentalApply = new Rental_Apply__c();
        //rentalApply.OwnerId = user.Id;
        //rentalApply.CurrencyIsoCode = 'CNY';
        //rentalApply.Status__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Cao_An_Zhong.ordinal());
        //rentalApply.Asset_loaner_start_day__c = Date.today();
        //rentalApply.Asset_loaner_closed_day__c = Date.today().addDays(10);
        //rentalApply.Answer_for_request__c = '123';
        //rentalApply.Repair__c = repair1.Id;
        //rentalApply.applyUser__c = UserInfo.getUserId();
        //rentalApply.direct_send__c = '直送';
        //rentalApply.direct_shippment_address__c = 'test';
        //rentalApply.Request_shipping_day__c = Date.today();
        //rentalApply.Hope_Lonaer_date_Num__c = 29;
        //// rentalApply.Request_return_day__c = Date.today() + 30;
        //rentalApply.Rental_Received_Day__c = null;
        //rentalApply.DataMigration_Flag__c = false;
        //rentalApply.HP_received_sign_day__c = Date.today();
        //rentalApply.AssetManageConfirm__c = true;
        //rentalApply.HP_received_Confirmed__c = true;
        //rentalApply.HP_received_sign_rich__c = 'test';
        //rentalApply.Demo_purpose1__c = '其他';
        //rentalApply.Demo_purpose2__c = '其他';
        //insert rentalApply;

        Rental_Apply__c raObj = new Rental_Apply__c();
        raObj.Name = 'testra';
        raObj.Product_category__c = 'GI';
        raObj.Demo_purpose1__c = '其他';
        raObj.demo_purpose2__c = '其他';
        raObj.direct_send__c = '医疗机构';
        raObj.Loaner_received_staff__c = '王五';
        raObj.Loaner_received_staff_phone__c = '110';
        raObj.direct_shippment_address__c = '北京市';
        raObj.Hospital__c = hospital.Id;
        raObj.Strategic_dept__c = strategicDep[0].Id;
        raObj.Account__c = dep.Id;
        raObj.Request_shipping_day__c = Date.toDay();
        raObj.Request_return_day__c = Date.toDay();
        raObj.Status__c = '草案中';
        raObj.SalesdeptSelect__c = '医疗华北营业本部';
        raObj.Hope_Lonaer_date_Num__c = 1;
        insert raObj;

    }

    static Fixture_Set__c fscObj { get {
        List<Fixture_Set__c> fscObjs = [select Id from Fixture_Set__c];
        System.assertEquals(fscObjs.size(), 1);
        Fixture_Set__c fscObj = fscObjs[0];

        return fscObj;
    }}

    static Rental_Apply__c raObj { get {
        List<Rental_Apply__c> raObjs = [select Id FROM Rental_Apply__c];
        System.assertEquals(raObjs.size(), 1);
        Rental_Apply__c raObj = raObjs[0];

        return raObj;
    }}

    static Rental_Apply_Equipment_Set_Detail__c raesdObj {
        get {
            List<Rental_Apply_Equipment_Set_Detail__c> raesdObjs = [select Id FROM Rental_Apply_Equipment_Set_Detail__c];
            System.assertEquals(raesdObjs.size(), 1);
            Rental_Apply_Equipment_Set_Detail__c raesdObj = raesdObjs[0];

            return raesdObj;
        }
    }

    static void makeCalendar() {
        Date sDate = Date.today().addMonths(-1);
        Date eDate = Date.today().addMonths(2);
        Integer cnt = sDate.daysBetween(eDate);
        List<OlympusCalendar__c> ocList = new List<OlympusCalendar__c>();
        for (Integer i=0; i<cnt; i++) {
            Date d = sDate.addDays(i);
            OlympusCalendar__c oc = new OlympusCalendar__c(Date__c = d);
            ocList.add(oc);
        }
        insert ocList;
    }

    static Date getWD_now(Date d) {
        List<OlympusCalendar__c> workday = [
                select Id, Date__c, IsWorkDay__c
                  from OlympusCalendar__c
                 where Date__c >= :d
                   and IsWorkDay__c = 1
                 order by Date__c
                 limit 1];
        Date selectDate = workday[0].Date__c;
        return selectDate;
    }

    /**
    * @description Repair_Shipped_Date__c, ReplaceDeliveryDate__c 更新
    **/
    static testMethod void testExecute1() {
        User user = new User(Test_staff__c = true);
        user.LastName = '_サンブリッジ';
        // user.FirstName = 'う';
        user.Alias = 'う';
        user.Email = '<EMAIL>';
        user.Username = '<EMAIL>';
        user.CommunityNickname = 'う';
        user.IsActive = true;
        user.EmailEncodingKey = 'ISO-2022-JP';
        user.TimeZoneSidKey = 'Asia/Tokyo';
        user.LocaleSidKey = 'ja_JP';
        user.LanguageLocaleKey = 'ja';
        user.ProfileId = System.Label.ProfileId_SystemAdmin;
        user.Job_Category__c = '销售推广';
        user.Province__c = '上海市';
        user.Use_Start_Date__c = Date.today().addMonths(-6);
        user.Dept__c = '医疗华北营业本部';
        user.Batch_User__c = true;
        insert user;
        System.runAs(user) {
            StaticParameter.EscapeNFM001AgencyContractTrigger = true;
            ControllerUtil.EscapeNFM001Trigger = true;
            Oly_TriggerHandler.bypass('AssetHandler');
            Oly_TriggerHandler.bypass('PowerBIBaseHandler');
            Oly_TriggerHandler.bypass('AgencyOppUpdHandler');
            Oly_TriggerHandler.bypass('UpdateContractAimAmountHandler');
            //Profile prof = [select Id from Profile where Name ='系统管理员'];
            // 病院を作る
            Account hospital = new Account();
            hospital.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'HP'].id;
            hospital.Name = 'test hospital';
            insert hospital;
            // 产品
            Product2 pro1 = new Product2(Name='name01',IsActive=true,Family='GI',Asset_Model_No__c='n01',Serial_Lot_No__c='S/N tracing',ProductCode_Ext__c='pc01',Manual_Entry__c=false);
            Product2 pro2 = new Product2(Name='name02',IsActive=true,Family='GI',Asset_Model_No__c='n02',Serial_Lot_No__c='Lot tracing',ProductCode_Ext__c='pc02',Manual_Entry__c=false);
            Product2 pro3 = new Product2(Name='name03',IsActive=true,Family='GI',Asset_Model_No__c='n03',Serial_Lot_No__c='S/N tracing',ProductCode_Ext__c='pc03',Manual_Entry__c=false);
            insert new Product2[] {pro1, pro2, pro3};
            RecordType rectOpp = [select id from RecordType where IsActive = true and SobjectType = 'Opportunity' and DeveloperName = 'Opportunity' ];
            //创建用户
            //user MyUser_Test = [select id,name from user where name =:'SB 葛'];
            // 戦略科室を得る
            Account[] strategicDep = [SELECT ID, Name FROM Account WHERE parentId = :hospital.Id AND recordType.DeveloperName = 'Department_Class_OTH'];
            // 診療科を作る
            Account dep = new Account();
            dep.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'Department_OTH'].id;
            dep.Name = 'test dep';
            dep.AgentCode_Ext__c = '9999998';
            dep.ParentId = strategicDep[0].Id;
            dep.Department_Class__c = strategicDep[0].Id;
            dep.Hospital__c = hospital.Id;
            insert dep;

            Contact contact2 = new Contact();
            contact2.AccountId = dep.Id;
            // contact2.FirstName = '責任者';
            contact2.LastName = 'test1经销商';
            insert contact2;

            // 资产
            Asset asset1 = new Asset(Asset_Owner__c = 'Olympus');
            asset1.RecordTypeId = System.Label.Asset_RecordType;
            asset1.SerialNumber = 'ass01';
            asset1.Name = 'ass01';
            asset1.AccountId = dep.Id;
            asset1.Department_Class__c = strategicDep[0].Id;
            asset1.Hospital__c = hospital.Id;
            asset1.Product2Id = pro1.Id;
            asset1.Quantity = 1;
            asset1.Manage_type__c = '个体管理';

            // Asset asset2 = new Asset(Asset_Owner__c = 'Olympus');
            // asset2.RecordTypeId = System.Label.Asset_RecordType;
            // asset2.SerialNumber = 'ass02';
            // asset2.Loaner_accsessary__c = true;
            // asset2.Name = 'ass02';
            // asset2.AccountId = dep.Id;
            // asset2.Department_Class__c = strategicDep[0].Id;
            // asset2.Hospital__c = hospital.Id;
            // asset2.Quantity = 1;
            // asset2.Product2Id = pro2.Id;

            // Asset asset3 = new Asset(Asset_Owner__c = 'Olympus');
            // asset3.RecordTypeId = System.Label.Asset_RecordType;
            // asset3.SerialNumber = 'ass03';
            // asset3.Loaner_accsessary__c = true;
            // asset3.Name = 'ass03';
            // asset3.AccountId = dep.Id;
            // asset3.Department_Class__c = strategicDep[0].Id;
            // asset3.Hospital__c = hospital.Id;
            // asset3.Quantity = 1;
            // asset3.Product2Id = pro3.Id;
            insert new Asset[] {asset1};

            // Opportunity opp1 = new Opportunity(
            //     Name='testOpp1',
            //     StageName='引合',
            //     CloseDate=Date.today(),
            //     AccountId=dep.Id,
            //     Competitor__c ='A',
            //     Click_Close_Date__c = null,
            //     RecordType = rectOpp
            // );
            // Opportunity opp2 = new Opportunity(
            //     Name='testOpp1',
            //     StageName='引合',
            //     CloseDate=Date.today(),
            //     AccountId=dep.Id,
            //     Sales_Root__c = 'OCM直接販売',
            //     Competitor__c ='A',
            //     Click_Close_Date__c = null,
            //     RecordType = rectOpp
            // );
            // insert new Opportunity[] {opp2, opp1};
            // //注残
            // Statu_Achievements__c Sac = new Statu_Achievements__c(
            //     name = 'zhucan_one',
            //     Opportunity__c = opp2.id,
            //     DeliveryDate__c = Date.today(),
            //     ContractAmount__c = 0
            // );
            // insert Sac;
            // 修理を作成する01
            Repair__c repair01 = new Repair__c();
            repair01.Account__c = dep.Id;
            repair01.Department_Class__c = strategicDep[0].Id;
            repair01.Hospital__c = hospital.Id;
            repair01.Delivered_Product__c = asset1.Id;
            repair01.SERVICE_CONTRACT_JUDEGE_DAY__C = Date.today().addDays( -11);   // 维修合同判断日がサービス契約開始日の前日
            repair01.Repair_Start_Date__c = Date.today().addDays(-9);
            repair01.Repair_Final_Inspection_Date__c = null;
            repair01.Repair_Ordered_Date__c = Date.today();
            repair01.SAP_not_accept_repair_result__c = Date.today();

            insert repair01;

            QIS_Report__c qr = new QIS_Report__c();
            qr.RC__c = UserInfo.getUserId();
            qr.Damage_For_Doc_Or_Pat__c = '有';
            qr.Relation_With_The_Problem__c = '有可能';
            qr.Report_For_Goz__c = '不知道';
            insert qr;

            //新建备品借出申请-保修用户
            Rental_Apply__c raObj = new Rental_Apply__c();
            raObj.Name='*';
            raObj.Product_category__c = 'GI';
            //raObj.Person_In_Charge__c = MyUser_Test.id;
            //raObj.applyUser__c =  MyUser_Test.id;
            raObj.Person_In_Charge__c = user.Id;
            raObj.applyUser__c =  user.Id;
            raObj.Hospital__c = hospital.Id;//医院
            raObj.Strategic_dept__c = strategicDep[0].id;//战略科室
            raObj.Demo_purpose1__c = '维修代用';
            raObj.demo_purpose2__c ='索赔QIS';
            raObj.Demo_purpose_text__c ='测试用的备品借出申请';
            raObj.Request_shipping_day__c = Date.today()+5;
            raObj.QIS_number__c = qr.Id;
            raObj.QISRepair__c = repair01.Id;
            // raObj.Hope_Lonaer_date_Num__c = 16;
            //raObj.Request_return_day__c = Date.today() +20;
            raObj.Loaner_received_staff__c = '测试用户';
            raObj.Loaner_received_staff_phone__c = '********';
            raObj.direct_send__c = '上门自提';
            raObj.Repair__c = repair01.id;
            // raObj.Statu_Achievements__c = Sac.id;
            raObj.Account__c = dep.id;//科室
            raObj.pickup_time__c = Datetime.now()+8;
            raObj.direct_shippment_address__c = '233333的地址';
            raObj.Phone_number__c = '**********';
            raObj.Loaner_medical_Staff__c = contact2.Id;
            raObj.Status__c = '草案中';
            insert new Rental_Apply__c[]{raObj} ;

            Rental_Apply_Equipment_Set__c DemoSetLine3 = new Rental_Apply_Equipment_Set__c();
            DemoSetLine3.Rental_Apply__c = raObj.id;
            DemoSetLine3.IndexFromUniqueKey__c = 1;
            DemoSetLine3.UniqueKey__c = '1:1';
            DemoSetLine3.Repair_Status_Text__c = '0.删除';
            repair01.RepairOrderStatusCode__c = 'A99';
            repair01.IncorrectDataComment__c = 'Test';
            repair01.Repair_Shipped_Date__c = Date.today();

            qr.ReplaceDeliveryDate__c = Date.today();
            System.Test.startTest();
            update repair01;
            update qr;
            insert new Rental_Apply_Equipment_Set__c[]{DemoSetLine3};

            //System.assertEquals(RaesList[0].Final_reply_day__c  ,Date.today());
            Id execBTId = Database.executeBatch(new UpdateRentalApplyBatch(), 5);
            System.Test.StopTest();

            List<Rental_Apply__c> raList = [SELECT Id
                        , Demo_purpose2__c
                        , QIS_Repair_Shipped_Date__c
                        , QISRepair__r.Repair_Shipped_Date__c
                        , QIS_ReplaceDeliveryDate__c
                        , QIS_number__r.ReplaceDeliveryDate__c
                        , Repair__r.SAP_not_accept_repair_result__c
                     FROM Rental_Apply__c];
            System.assertEquals(1, raList.size());
            System.assertEquals(raList[0].QIS_Repair_Shipped_Date__c, raList[0].QISRepair__r.Repair_Shipped_Date__c);
            System.assertEquals(raList[0].QIS_ReplaceDeliveryDate__c, raList[0].QIS_number__r.ReplaceDeliveryDate__c);

        }
    }

        static void setupTestData1(Boolean haveFCheck) {
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        Oly_TriggerHandler.bypass(ContactTriggerHandler.class.getName());
        Oly_TriggerHandler.bypass(AgencyHospitalHandler.class.getName());

        Rental_Apply__c raObj = new Rental_Apply__c();
        // 保有设备
        Fixture_Set_Detail__c fsdObjA1 = new Fixture_Set_Detail__c();
        Rental_Apply_Equipment_Set__c raesObj = new Rental_Apply_Equipment_Set__c();
        Rental_Apply_Equipment_Set__c raesObj1 = new Rental_Apply_Equipment_Set__c();

        Asset asset1 = new Asset(Asset_Owner__c = 'Olympus');
        Fixture_OneToOne_Link__c foLink1 = new Fixture_OneToOne_Link__c();
        Fixture_OneToOne_Link__c foLink2 = new Fixture_OneToOne_Link__c();
        // MIXED_DML_OPERATION, DML operation on setup object is not permitted Error
        System.runAs(new User(Id = Userinfo.getUserId())) {
            StaticParameter.EscapeNFM001AgencyContractTrigger = true;
            StaticParameter.EscapeNFM001Trigger = true;
            Oly_TriggerHandler.bypass(ContactTriggerHandler.class.getName());
            Oly_TriggerHandler.bypass(AgencyHospitalHandler.class.getName());

            // 省
            Address_Level__c al = new Address_Level__c();
            al.Name = '東京';
            al.Level1_Code__c = 'CN-99';
            al.Level1_Sys_No__c = '999999';
            insert al;
            // 市
            Address_Level2__c al2 = new Address_Level2__c();
            al2.Level1_Code__c = 'CN-99';
            al2.Level1_Sys_No__c = '999999';
            al2.Level1_Name__c = '東京';
            al2.Name = '渋谷区';
            al2.Level2_Code__c = 'CN-9999';
            al2.Level2_Sys_No__c = '9999999';
            al2.Address_Level__c = al.id;
            insert al2;

            // 病院を作る
            Account hospital = new Account();
            hospital.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'HP'].id;
            hospital.Name = 'test hospital';
            hospital.Is_Active__c = '有効';
            hospital.Attribute_Type__c = '卫生部';
            hospital.Speciality_Type__c = '综合医院';
            hospital.Grade__c = '一级';
            hospital.OCM_Category__c = 'SLTV';
            hospital.Is_Medical__c = '医疗机构';
            hospital.State_Master__c = al.id;
            hospital.City_Master__c = al2.id;
            hospital.Town__c = '东京';
            insert hospital;

            StaticParameter.EscapeAccountTrigger = true;
            // 戦略科室を得る
            Account[] strategicDep = [SELECT ID, Name FROM Account WHERE parentId = :hospital.Id AND recordType.DeveloperName = 'Department_Class_OTH'];
            // 診療科を作る
            Account dep = new Account();
            dep.recordtypeId = [Select Id FROM RecordType WHERE IsActive = true and SobjectType = 'Account' and DeveloperName = 'Department_OTH'].id;
            dep.Name = 'test dep1';
            dep.AgentCode_Ext__c = '9999999';
            dep.ParentId = strategicDep[0].Id;
            dep.Department_Class__c = strategicDep[0].Id;
            dep.Hospital__c = hospital.Id;
            insert dep;

            // 产品
            Product2 pro1 = new Product2(Name='name01',IsActive=true,Family='GI',
                    Fixture_Model_No__c='n01',Serial_Lot_No__c='S/N tracing',
                    Fixture_Model_No_T__c = 'n01', Asset_Model_No__c = 'Pro1',
                    ProductCode_Ext__c='pc01',Manual_Entry__c=false);
            Product2 pro2 = new Product2(Name='name02',IsActive=true,Family='GI',
                    Fixture_Model_No__c='n02',Serial_Lot_No__c='Lot tracing',
                    Fixture_Model_No_T__c = 'n02', Asset_Model_No__c = 'Pro2',
                    ProductCode_Ext__c='pc02',Manual_Entry__c=false);
            Product2 pro3 = new Product2(Name='name03',IsActive=true,Family='GI',
                    Fixture_Model_No__c='n03',Serial_Lot_No__c='Lot tracing',
                    Fixture_Model_No_T__c = 'n03', Asset_Model_No__c = 'Pro3',
                    ProductCode_Ext__c='pc03',Manual_Entry__c=false);
            Product2 pro4 = new Product2(Name='name04',IsActive=true,Family='GI',
                    Fixture_Model_No__c='n04',Serial_Lot_No__c='Lot tracing',
                    Fixture_Model_No_T__c = 'n04', Asset_Model_No__c = 'Pro4',
                    ProductCode_Ext__c='pc04',Manual_Entry__c=false);
            insert new Product2[] {pro1, pro2, pro3, pro4};

            //备品借出申请
            raObj.Name = 'testra';
            raObj.Product_category__c = 'GI';
            raObj.Demo_purpose1__c = '其他';
            raObj.demo_purpose2__c = '其他';
            raObj.direct_send__c = '医疗机构';
            raObj.Loaner_received_staff__c = '王五';
            raObj.Loaner_received_staff_phone__c = '110';
            raObj.direct_shippment_address__c = '北京市';
            raObj.Hospital__c = hospital.Id;
            raObj.Strategic_dept__c = strategicDep[0].Id;
            raObj.Account__c = dep.Id;
            raObj.Request_shipping_day__c = Date.toDay();
            raObj.Request_return_day__c = Date.toDay();
            raObj.Status__c = '草案中';
            raObj.SalesdeptSelect__c = '医疗华北营业本部';
            Oly_TriggerHandler.bypass(RentalApplyTriggerHandler.class.getName());
            insert raObj;

            // 保有设备
            asset1.RecordTypeId = System.Label.Asset_RecordType;
            asset1.SerialNumber = 'asset1';
            asset1.Name = 'asset1';
            asset1.AccountId = dep.Id;
            asset1.Department_Class__c = strategicDep[0].Id;
            asset1.Hospital__c = hospital.Id;
            asset1.Product2Id = pro1.Id;
            asset1.Quantity = 1;
            asset1.Status = '不明';
            asset1.Manage_type__c = '个体管理';
            asset1.Loaner_accsessary__c = false;
            asset1.Out_of_wh__c = 0;
            asset1.Salesdepartment__c = '1.华北营业本部';
            asset1.Internal_asset_location__c = '北京 备品中心';
            asset1.Product_category__c = 'GI';
            asset1.Equipment_Type__c = '产品试用';
            asset1.SalesProvince__c = '北京';
            asset1.CompanyOfEquipment__c = '北京';
            asset1.Internal_Asset_number__c = '0001';
            asset1.WH_location__c = '货架号1';
            insert new Asset[] {asset1};


            // 备品配套
            Fixture_Set__c fsObj1 = new Fixture_Set__c();
            fsObj1.Name = 'set1';
            fsObj1.Fixture_Set_Body_Model_No__c = 'modelNo1';
            fsObj1.Loaner_name__c = 'name1';
            insert fsObj1;

            // 备品配套明细
            fsdObjA1.Name = '备品配套明细名1';
            fsdObjA1.Name_CHN_Created__c = '中文名称1';
            fsdObjA1.Product2__c = pro1.Id;
            fsdObjA1.Fixture_Set__c = fsObj1.Id;
            fsdObjA1.Is_Body__c = true;
            fsdObjA1.Is_Optional__c = false;
            fsdObjA1.UniqueKey__c = fsObj1.Id + ':' + pro1.Id;
            fsdObjA1.SortInt__c = 1;
            fsdObjA1.Quantity__c = 1;

            insert new Fixture_Set_Detail__c[] {fsdObjA1};

            // 借出备品配套一览
            raesObj.Rental_Apply__c = raObj.Id;
            raesObj.Fixture_Set__c = fsObj1.Id;
            raesObj.Cancel_Select__c = false;
            raesObj.Rental_Start_Date__c = Date.toDay();
            raesObj.Rental_End_Date__c = Date.toDay();
            raesObj.IndexFromUniqueKey__c = 1;
            raesObj.UniqueKey__c = '1:'+ fsObj1.Id + ':1';

            //备品借出历史
            raesObj1.Rental_Apply__c = raObj.Id;
            raesObj1.CurrencyIsoCode = 'CNY';
            raesObj1.Shipment_request_time__c = Datetime.now();
            raesObj1.Shippment_loaner_time__c = Datetime.now()-20;
            raesObj1.Rental_Start_Date__c = getWD_now(Date.today());
            raesObj1.Rental_End_Date__c = getWD_now(Date.today());
            raesObj1.Repair_Status_Text__c = '1.受理完毕';
            raesObj1.IndexFromUniqueKey__c = 1;
            raesObj1.UniqueKey__c = '1:1';
            raesObj1.Received_Confirm_NG_Not_Return_Text__c = 2;
            raesObj1.Loaner_received_time__c = getWD_now(Date.today()-20);
            raesObj1.Received_Confirm__c = 'OK';
            raesObj1.Received_ng_detail__c = 'test';
            raesObj1.Final_reply_day_text__c = Date.today().addMonths(-1);
            raesObj1.Received_Confirm_Status_Text__c = '申请者收货NG';
            raesObj1.Extend_Status__c = '批准';
            raesObj1.Extend_request_reason__c = 'test';
            raesObj1.Request_extend_day__c = Date.today().addMonths(-2);
            raesObj1.Rental_Date_byHand__c = Date.today().addMonths(-2);
            if (haveFCheck) {
                insert new Rental_Apply_Equipment_Set__c[]{raesObj, raesObj1};
            }
            else {
                insert raesObj;
            }
        }
        Rental_Apply_Equipment_Set_Detail__c raesdObj1 = new Rental_Apply_Equipment_Set_Detail__c();
        System.runAs(new User(Id = Userinfo.getUserId())) {
            // 借出备品配套一览明细
            raesdObj1.Rental_Apply__c = raObj.Id;
            raesdObj1.Fixture_Set_Detail__c = fsdObjA1.Id;
            raesdObj1.Rental_Num__c = 1;
            raesdObj1.Queue_Number__c = null;
            raesdObj1.Is_Body__c = true;
            raesdObj1.Rental_Apply_Equipment_Set__c = raesObj.Id;
            raesdObj1.IndexFromUniqueKey__c = 1;
            raesdObj1.UniqueKey__c = '1:'+ raesObj.Id + ':' + fsdObjA1.Id + ':1';
            raesdObj1.FSD_OneToOneAccessory_Cnt__c = 2;
            raesdObj1.FSD_Is_Optional__c = false;
            raesdObj1.FSD_Is_OneToOne__c = false;
            raesdObj1.ApplyPersonAppended__c = false;
            raesdObj1.FSD_Fixture_Model_No__c = 'n01';
            raesdObj1.Fixture_Model_No_text__c = 'n01';
            raesdObj1.Salesdepartment_before__c = '1.华北营业本部';
            raesdObj1.Internal_asset_location_before__c = '北京 备品中心';
            raesdObj1.Product_category_text__c = 'GI';
            raesdObj1.Equipment_Type_text__c = '产品试用';
            insert new Rental_Apply_Equipment_Set_Detail__c[] {raesdObj1};
        }
        System.runAs(new User(Id = Userinfo.getUserId())) {
            // 申请单
            raObj.Status__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
            raObj.Status_Text__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
            raObj.NotWatch_RA_Status__c = true;
            raObj.HP_received_sign_day__c = Date.toDay();
            raObj.HP_received_sign_rich__c = 'OK';
            raObj.AssetManageConfirm__c = true;
            raObj.Request_demo_time__c = Date.newInstance(2019, 7, 1);
            update raObj;

            // 申请者收货操作
            raesObj.Received_Confirm__c = 'OK';
            raesObj.Loaner_received_time__c = Datetime.now();
            update raesObj;

            FixtureDeliverySlip__c fdsObj = new FixtureDeliverySlip__c();
            fdsObj.Name = '00001';
            fdsObj.DeliveryCompany__c = '利讯';
            fdsObj.Distributor_method__c = '陆运';
            fdsObj.DeliveryType__c = '发货';
            fdsObj.Shippment_loaner_time__c = System.now();
            insert fdsObj;

            // 出库前点检操作
            raesdObj1.Select_Time__c = Date.toDay();
            raesdObj1.Asset__c = asset1.Id;
            raesdObj1.Loaner_accsessary__c = false;
            raesdObj1.FSD_Name_CHN__c = 'name01';
            raesdObj1.Shipment_Status_Text__c = FixtureUtil.raesdStatusMap.get(FixtureUtil.HistoryStatus.Yi_Fen_Pei.ordinal());
            raesdObj1.Shipment_request_time2__c = Date.toDay();
            raesdObj1.Shipment_request__c = true;
            raesdObj1.StockDown__c = true;
            raesdObj1.StockDown_time__c = Date.toDay();
            raesdObj1.StockDown_staff__c = Userinfo.getUserId();
            raesdObj1.Shipment_Status_Text__c = FixtureUtil.raesdStatusMap.get(FixtureUtil.HistoryStatus.Yi_Xia_Jia.ordinal());

            raesdObj1.Inspection_result__c = 'OK';
            raesdObj1.Pre_inspection_time__c = System.now();
            raesdObj1.Inspection_staff__c = Userinfo.getUserId();
            // 发货操作
            raesdObj1.DeliverySlip__c = fdsObj.Id;
            update new Rental_Apply_Equipment_Set_Detail__c[] {raesdObj1};
        }
    }

    /**
    * @description 7天和10天工作日更新
    **/
    static testMethod void testExecute7() {
        System.runAs(new User(Id = Userinfo.getUserId())) {
            StaticParameter.EscapeNFM001AgencyContractTrigger = true;
            StaticParameter.EscapeNFM001Trigger = true;
            Oly_TriggerHandler.bypass(ContactTriggerHandler.class.getName());
            Oly_TriggerHandler.bypass(AgencyHospitalHandler.class.getName());
            makeCalendar();
            // 把今天强行改成工作日
            OlympusCalendar__c oc = [SELECT Id,IsWorkDay__c, ChangeToWorkday__c FROM OlympusCalendar__c WHERE DATE__c = Today];
            if(oc.IsWorkDay__c == 0) {
                oc.ChangeToWorkday__c = true;
                update oc;
            }
            System.Test.StartTest();
            setupTestData1(false);
            // 重新打开RentalApplyEquipmentSetHandler
            Oly_TriggerHandler.clearAllBypasses();
            Id execBTId = Database.executeBatch(new UpdateRentalApplyBatch(), 5);
            System.Test.StopTest();
            List<OlympusCalendar__c> workday = [
                select Id, Date__c, IsWorkDay__c
                  from OlympusCalendar__c
                 where Date__c >= Today
                   and IsWorkDay__c = 1
                 order by Date__c
                 limit 11];
            List<Rental_Apply__c> raesList = [SELECT Id
                        , Bollow_Date__c
                        , Bollow_Date_Add_7_WD__c
                        , Bollow_Date_Add_10_WD__c
                     FROM Rental_Apply__c];
            System.assertEquals(1, raesList.size());
            System.assertEquals(11, workday.size());
            System.assertEquals(workday[7].Date__c, raesList[0].Bollow_Date_Add_7_WD__c);
            System.assertEquals(workday[10].Date__c, raesList[0].Bollow_Date_Add_10_WD__c);
        }
    }

    /*static testMethod void testSchedule() {
        // This test runs a scheduled job at midnight Sept. 3rd. 2022
        String CRON_EXP = '0 0 0 3 9 ? 3999';

        System.Test.startTest();
        // Schedule the test job
        String jobId = system.schedule('UpdateRentalApplyBatchScheduleTest', CRON_EXP, new UpdateRentalApplyBatchSchedule());
        // Get the information from the CronTrigger API object
        CronTrigger ct = [SELECT Id, CronExpression, TimesTriggered, NextFireTime FROM CronTrigger WHERE id = :jobId];
        // Verify the expressions are the same
        System.assertEquals(CRON_EXP, ct.CronExpression);
        // Verify the job has not run
        System.assertEquals(0, ct.TimesTriggered);
        // Verify the next time the job will run
        System.assertEquals('3999-09-03 00:00:00', String.valueOf(ct.NextFireTime));
        System.Test.StopTest();
    }*/
}