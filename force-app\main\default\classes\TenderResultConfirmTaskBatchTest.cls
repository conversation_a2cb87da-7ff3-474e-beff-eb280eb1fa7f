@isTest
private class TenderResultConfirmTaskBatchTest {

    @IsTest
    static void myTest1() {

        StaticParameter.EscapeTOLinkTrigger = true;
        ControllerUtil.EscapeNFM001Trigger = true;
        ControllerUtil.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
        StaticParameter.EscapeMaintenanceContractAfterUpdateTrigger = true;
        Oly_TriggerHandler.bypass('TenderInformationHandler');


        Profile p = [select id from Profile where id =:System.Label.ProfileId_SystemAdmin];
        String loginId = UserInfo.getUserId();
        User sys = [select id from User where Id = :loginId];
        User u1 = new User(Test_staff__c = true);
        u1.LastName = '123';
        // u1.FirstName = '2';
        u1.Batch_User__c = true;
        u1.Alias = '2';
        u1.Email = '<EMAIL>';
        u1.Username = '<EMAIL>';
        u1.CommunityNickname = 'あ1';
        u1.IsActive = true;
        u1.EmailEncodingKey = 'ISO-2022-JP';
        u1.TimeZoneSidKey = 'Asia/Tokyo';
        u1.LocaleSidKey = 'ja_JP';
        u1.LanguageLocaleKey = 'ja';
        u1.ProfileId = p.id;
        u1.Job_Category__c = '销售服务';
        u1.Province__c = '東京';
        insert u1;

        User u2 = new User(Test_staff__c = true);
        u2.LastName = '_サンブリッジ';
        // u2.FirstName = 'い';
        u2.Batch_User__c = true;
        u2.Alias = 'い';
        u2.Email = '<EMAIL>';
        u2.Username = '<EMAIL>';
        u2.CommunityNickname = 'い';
        u2.IsActive = true;
        u2.EmailEncodingKey = 'ISO-2022-JP';
        u2.TimeZoneSidKey = 'Asia/Tokyo';
        u2.LocaleSidKey = 'ja_JP';
        u2.LanguageLocaleKey = 'ja';
        u2.ProfileId = p.id;
        u2.Job_Category__c = '销售推广';
        u2.Province__c = '東京';
        u2.IsActive = true;
        insert u2;

        //创建医院
        RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
        Account acc = new Account();
        acc.RecordTypeId = rectCo.Id;
        acc.Name = 'HP test1';
        acc.Assume_Change__c = true;
        insert acc;

        //创建招标项目1
        Tender_information__c info1 = new Tender_information__c();
        info1.Name = 'TEST001';
        info1.OpportunityStatus__c = '跟进中';
        info1.InfoType__c = '3：结果';
        info1.subInfoType__c = '3-5：中标通知';
        info1.OwnerId = u1.Id;
        info1.OpportunityNum__c = 1;
        info1.ResultDate__c = Date.today();
        info1.IsRelateProject__c = '是';
        info1.Hospital__c = acc.Id;
        insert info1;

        //询价1
        Opportunity opp1 = new Opportunity(
            StageName = '引合',
            Name = 'tenderTest询价1',
            ETPromoteSale__c  = true,
            Close_Forecasted_Date__c = Date.today().addDays(-5),
           // Bidding_Project_Name_Bid__c = info1.Id,
           CloseDate = Date.today()
        );
        insert opp1;

        Test.StartTest();

        System.runAs(u1){
            // 插入关联关系
            Tender_Opportunity_Link__c link1 = new Tender_Opportunity_Link__c();
            link1.Tender_information__c = info1.Id;
            link1.Opportunity__c = opp1.Id;
            insert link1;
        }

        System.runAs(u2){
            //新建招标项目2
            Tender_information__c info2 = new Tender_information__c();
            info2.Name = 'TEST002';
            info2.InfoType__c = '3：结果';
            info2.subInfoType__c = '3-5：中标通知';
            info2.ResultDate__c = Date.today();
            insert info2;

            //新建询价2
            Opportunity opp2 = new Opportunity(
                StageName = '引合',
                Name = 'tenderTest询价2',
                Contract_DB_complite_day__c = Date.today(),
                Close_Forecasted_Date__c = Date.today().addDays(-5),
                CloseDate = Date.today(),
                Contract_Authorize_Lock__c = true
            );
            insert opp2;

            // 插入关联关系
            Tender_Opportunity_Link__c link2 = new Tender_Opportunity_Link__c();
            link2.Tender_information__c = info2.Id;
            link2.Opportunity__c = opp2.Id;
            insert link2;
        }


        //新建询价3
        Opportunity opp3 = new Opportunity(
            StageName = '敗戦',
            Name = 'tenderTest询价3',
            Close_Forecasted_Date__c = Date.today().addDays(-5),
            CloseDate = Date.today()
        );
        insert opp3;

        //新建 询价失单/取消报告
        Lost_cancel_report__c lcr = new Lost_cancel_report__c(
            Opportunity__c = opp3.Id
        );
        insert lcr;

        opp3.Lost_Cancel_Report__c = lcr.Id;
        update opp3;
        

        // //新建询价4
        // Opportunity opp4 = new Opportunity(
        //     StageName = '敗戦',
        //     Name = 'tenderTest询价4',
        //     Close_Forecasted_Date__c = Date.today().addDays(-5),
        //     CloseDate = Date.today(),
        //     ConfirmationofAward__c = '竞争对手中标'
        // );
        // insert opp4;    
        // //新建 询价失单/取消报告
        // Lost_cancel_report__c lcr1 = new Lost_cancel_report__c(
        //     Opportunity__c = opp4.Id
        // );
        // insert lcr1;
        
        // opp4.Lost_Cancel_Report__c = lcr.Id;
        // // update opp4;

        List<Tender_Opportunity_Link__c> slist = [select id,name,Tender_information__r.InfoType__c,Tender_information__r.subInfoType__c,Tender_information__r.status__c,Tender_information__r.LastModifiedDate,Tender_information__r.OpportunityStatus__c,Tender_information__r.OpportunityNum__c,
                                                    Opportunity__r.StageName__c,Opportunity__r.If_Need_PriceApply__c,Opportunity__r.Contract_DB_complite_day__c,Opportunity__r.Contract_Authorize_Lock__c
                                                    from Tender_Opportunity_Link__c];
        
        System.assertEquals(2, slist.size());
        System.assertEquals('3：结果', slist[1].Tender_information__r.InfoType__c);
        System.assertEquals('3-5：中标通知', slist[1].Tender_information__r.subInfoType__c);
        // System.assertEquals('跟进中',slist[0].Tender_information__r.OpportunityStatus__c);
        // System.assertEquals(1,slist[0].Tender_information__r.OpportunityNum__c);
        // System.assertEquals('05.询价中',slist[0].Tender_information__r.status__c);
        // System.assertEquals('询价',slist[0].Opportunity__r.StageName__c);
        System.assertEquals(Date.today(),slist[1].Opportunity__r.Contract_DB_complite_day__c);
        System.assertEquals(true,slist[1].Opportunity__r.Contract_Authorize_Lock__c);
		Database.executeBatch(new TenderResultConfirmTaskBatch());
		Test.stopTest();
    }
}