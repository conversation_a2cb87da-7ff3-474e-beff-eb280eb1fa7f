public without sharing class TemperatureAndHumidityCheckHandler extends <PERSON>ly<PERSON>TriggerHandler {
	private Map<Id, Temperature_And_Humidity_Check__c> newMap;
    private Map<Id, Temperature_And_Humidity_Check__c> oldMap;
    private List<Temperature_And_Humidity_Check__c> newList;
    private List<Temperature_And_Humidity_Check__c> oldList;

	public TemperatureAndHumidityCheckHandler() {
        this.newMap = (Map<Id, Temperature_And_Humidity_Check__c>) Trigger.newMap;
        this.oldMap = (Map<Id, Temperature_And_Humidity_Check__c>) Trigger.oldMap;
        this.newList = (List<Temperature_And_Humidity_Check__c>) Trigger.new;
        this.oldList = (List<Temperature_And_Humidity_Check__c>) Trigger.old;
    }
    
    protected override void afterInsert() {
        closeTask();
    }

    private void closeTask() {
        // 暂时注释，后续可能继续使用
        /*List<String> zoneList = new List<String>();
        List<Schema.PicklistEntry> pick_list_values = Temperature_And_Humidity_Check__c.Temperature_And_Humidity_Zone__c.getDescribe().getPicklistValues();

        for (Schema.PicklistEntry a : pick_list_values) {
            zoneList.add(a.getValue());
        }

        List<task__c> updTaskList = new List<task__c>();
        for (Temperature_And_Humidity_Check__c nObj : newList) {
            // 判断同备品中心是否完成所有分区的盘点
            List<Temperature_And_Humidity_Check__c> tahcObjList = [SELECT Id, Temperature_And_Humidity_Zone__c FROM Temperature_And_Humidity_Check__c WHERE Internal_asset_location__c =: nObj.Internal_asset_location__c AND Inventory_Time__c =: nObj.Inventory_Time__c];
            Map<String, String> zoneMap = new Map<String, String>();
            for (Temperature_And_Humidity_Check__c tachObj : tahcObjList) {
                zoneMap.put(tachObj.Temperature_And_Humidity_Zone__c, '1');
            }
            zoneMap.put(nObj.Temperature_And_Humidity_Zone__c, '1');

            Boolean isFinished = true;
            for (String temp : zoneList) {
                if (!zoneMap.containskey(temp)) {
                    isFinished = false;
                }
            }

            if (isFinished) {
                String roleName = null;
                if (nObj.Internal_asset_location__c == '北京 备品中心') {
                    roleName = '备品中心北方管理成员';
                } else if (nObj.Internal_asset_location__c == '上海 备品中心') {
                    roleName = '备品中心华东管理成员';
                } else if (nObj.Internal_asset_location__c == '广州 备品中心') {
                    roleName = '备品中心南方管理成员';
                }

                List<task__c> taskList = [SELECT Id, taskStatus__c FROM task__c WHERE (taskStatus__c = '01 分配' OR taskStatus__c = '02 接受' OR taskStatus__c = '05 延期') AND RecordType.Name = '温湿度检查计划' AND assignee__r.RoleName_wave__c =: roleName];
                for (task__c t : taskList) {
                    t.taskStatus__c = '03 完成';
                    updTaskList.add(t);
                }
            }
        }*/

        List<String> roleNameList = new List<String>();
        for (Temperature_And_Humidity_Check__c nObj : newList) {
            if (nObj.Temperature_And_Humidity_Zone__c == 'A区') {
                String roleName = null;
                if (nObj.Internal_asset_location__c == '北京 备品中心') {
                    roleName = '备品中心北方管理成员';
                } else if (nObj.Internal_asset_location__c == '上海 备品中心') {
                    roleName = '备品中心华东管理成员';
                } else if (nObj.Internal_asset_location__c == '广州 备品中心') {
                    roleName = '备品中心南方管理成员';
                }

                if (!String.isBlank(roleName)) {
                    roleNameList.add(roleName);
                }
            }
        }

        Date today = Date.today();
        List<task__c> taskList = [SELECT Id, taskStatus__c FROM task__c WHERE (taskStatus__c = '01 分配' OR taskStatus__c = '02 接受' OR taskStatus__c = '05 延期') AND RecordType.Name = '温湿度检查计划' AND assignee__r.RoleName_wave__c IN: roleNameList];
        for (task__c t : taskList) {
            t.taskStatus__c = '03 完成';
            t.Finished_Date__c = today;
        }

        if (!taskList.isEmpty()) {
            FixtureUtil.withoutUpdate(taskList);
        }
    }
}