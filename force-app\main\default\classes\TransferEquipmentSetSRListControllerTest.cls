@isTest
private class TransferEquipmentSetSRListControllerTest {
    // static TransferTestDataFactory Factory;
    // //查到备品明细 备品中心之间
    // static testMethod void testMethod1() {
    //     Test.startTest();
    //     Factory = new TransferTestDataFactory();
    //     Factory.CreateTa('CenterToCenter');
    //     TransferApply__c raObj1 = [SELECT Id,Name,OwnerId,Status__c,Status_Text__c,Request_time__c,Request_approval_time__c  FROM TransferApply__c WHERE RecordType.DeveloperName = 'CenterToCenter' limit 1];       
    //     raObj1.Status__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
    //     raObj1.Status_Text__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
    //     raObj1.Request_time__c = System.now();
    //     raObj1.Request_approval_time__c = System.now();
    //     update raObj1;

    //     System_UserSetting__c config = System_UserSetting__c.getInstance(UserInfo.getUserId());
    //     System.assertEquals(null, config.TAS_Action_Type__c);
    //     PageReference ref = new PageReference('/apex/TransferEquipmentSetSRList?id=' + raObj1.Name );
    //     Test.setCurrentPage(ref);
    //     TransferEquipmentSetSRListController controller1 = new TransferEquipmentSetSRListController();
    //     //Test.startTest();
    //     controller1.init();
    //     controller1.selectedType = '发货';
    //     controller1.setType();
    //     Test.stopTest();
    //     config = System_UserSetting__c.getInstance(UserInfo.getUserId());
    //     System.assertEquals('发货', config.TAS_Action_Type__c);
    // }
    // //查到备品明细 备品中心移管至非集中管理部门
    // static testMethod void testMethod2() {
    //     Test.startTest();
    //     Factory = new TransferTestDataFactory();
    //     Factory.CreateTa('CenterToOther');
    //     TransferApply__c raObj1 = [SELECT Id,Name,OwnerId,Status__c,Status_Text__c,Request_time__c,Request_approval_time__c  FROM TransferApply__c WHERE RecordType.DeveloperName = 'CenterToOther' limit 1];       
    //     raObj1.Status__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
    //     raObj1.Status_Text__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
    //     raObj1.Request_time__c = System.now();
    //     raObj1.Request_approval_time__c = System.now();
    //     update raObj1;

    //     System_UserSetting__c config = System_UserSetting__c.getInstance(UserInfo.getUserId());
    //     System.assertEquals(null, config.TAS_Action_Type__c);
    //     PageReference ref = new PageReference('/apex/TransferEquipmentSetSRList?id=' + raObj1.Name );
    //     Test.setCurrentPage(ref);
    //     TransferEquipmentSetSRListController controller1 = new TransferEquipmentSetSRListController();
    //     //Test.startTest();
    //     controller1.init();
    //     controller1.selectedType = '发货';
    //     controller1.setType();
    //     Test.stopTest();
    //     config = System_UserSetting__c.getInstance(UserInfo.getUserId());
    //     System.assertEquals('发货', config.TAS_Action_Type__c);
    // }
    // //查到备品明细 同备品中心
    // static testMethod void testMethod3() {
    //     Test.startTest();
    //     Factory = new TransferTestDataFactory();
    //     Factory.CreateTa('InsideCenter');
    //     TransferApply__c raObj1 = [SELECT Id,Name,OwnerId,Status__c,Status_Text__c,Request_time__c,Request_approval_time__c  FROM TransferApply__c WHERE RecordType.DeveloperName = 'InsideCenter' limit 1];       
    //     raObj1.Status__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
    //     raObj1.Status_Text__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
    //     raObj1.Request_time__c = System.now();
    //     raObj1.Request_approval_time__c = System.now();
    //     update raObj1;

    //     System_UserSetting__c config = System_UserSetting__c.getInstance(UserInfo.getUserId());
    //     System.assertEquals(null, config.TAS_Action_Type__c);
    //     PageReference ref = new PageReference('/apex/TransferEquipmentSetSRList?id=' + raObj1.Name );
    //     Test.setCurrentPage(ref);
    //     TransferEquipmentSetSRListController controller1 = new TransferEquipmentSetSRListController();
    //     //Test.startTest();
    //     controller1.init();
    //     controller1.selectedType = '发货';
    //     controller1.setType();
    //     Test.stopTest();
    //     config = System_UserSetting__c.getInstance(UserInfo.getUserId());
    //     System.assertEquals('发货', config.TAS_Action_Type__c);
    // }
    // //AgencyToCenter  办事处到备品中心
    // static testMethod void testMethod4() {
    //     Test.startTest();
    //     Factory = new TransferTestDataFactory();
    //     Factory.CreateTa('AgencyToCenter');
    //     TransferApply__c raObj1 = [SELECT Id,Name,OwnerId,Status__c,Status_Text__c,Request_time__c,Request_approval_time__c  FROM TransferApply__c WHERE RecordType.DeveloperName = 'AgencyToCenter' limit 1];       
    //     raObj1.Status__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
    //     raObj1.Status_Text__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal());
    //     raObj1.Request_time__c = System.now();
    //     raObj1.Request_approval_time__c = System.now();
    //     update raObj1;

    //     System_UserSetting__c config = System_UserSetting__c.getInstance(UserInfo.getUserId());
    //     System.assertEquals(null, config.TAS_Action_Type__c);
    //     PageReference ref = new PageReference('/apex/TransferEquipmentSetSRList?id=' + raObj1.Name );
    //     Test.setCurrentPage(ref);
    //     TransferEquipmentSetSRListController controller1 = new TransferEquipmentSetSRListController();
    //     //Test.startTest();
    //     controller1.init();
    //     controller1.selectedType = '发货';
    //     controller1.setType();
    //     Test.stopTest();
    //     config = System_UserSetting__c.getInstance(UserInfo.getUserId());
    //     System.assertEquals('发货', config.TAS_Action_Type__c);
    // }
    @isTest
    static void testMock1(){
        TransferEquipmentSetSRListController.testMock1();
    }
}