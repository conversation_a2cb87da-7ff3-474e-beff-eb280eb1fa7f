@isTest
private class UpdateTenderInformationBatchTest {
    //不赋值询价状态,但是数量应为3
        static testMethod void testForTest() {
        UpdateTenderInformationBatch.justForTest();

        List<BatchIF_Transfer__c> Transfers = new List<BatchIF_Transfer__c>();
        BatchIF_Transfer__c transfer2 = new BatchIF_Transfer__c();
        transfer2.Table__c          = 'Tender_information__c';
        transfer2.Column__c = 'UpdOppFlg';
        Transfers.add(transfer2);
        BatchIF_Transfer__c transfer3 = new BatchIF_Transfer__c();
        transfer3.Table__c          = 'ReportOppLinkFromOppBatch';
        transfer3.Column__c = 'UpdOppFlg';
        Transfers.add(transfer3);
        insert Transfers;

        Id execBTId = Database.executeBatch(new UpdateTenderInformationBatch('123'));
    }
     static testMethod void testForTest2() {
        List<BatchIF_Transfer__c> Transfers = new List<BatchIF_Transfer__c>();
        BatchIF_Transfer__c transfer2 = new BatchIF_Transfer__c();
        transfer2.Table__c          = 'Tender_information__c';
        transfer2.Column__c = 'UpdOppFlg';
        Transfers.add(transfer2);
        BatchIF_Transfer__c transfer3 = new BatchIF_Transfer__c();
        transfer3.Table__c          = 'ReportOppLinkFromOppBatch';
        transfer3.Column__c = 'UpdOppFlg';
        Transfers.add(transfer3);
        insert Transfers;

        List<String> list1 = new List<String>();
        List1.add('123');
        Id execBTId = Database.executeBatch(new UpdateTenderInformationBatch(List1));
    }
     static testMethod void testForTest3() {

        List<BatchIF_Transfer__c> Transfers = new List<BatchIF_Transfer__c>();
        BatchIF_Transfer__c transfer2 = new BatchIF_Transfer__c();
        transfer2.Table__c          = 'Tender_information__c';
        transfer2.Column__c = 'UpdOppFlg';
        Transfers.add(transfer2);
        BatchIF_Transfer__c transfer3 = new BatchIF_Transfer__c();
        transfer3.Table__c          = 'ReportOppLinkFromOppBatch';
        transfer3.Column__c = 'UpdOppFlg';
        Transfers.add(transfer3);
        insert Transfers;

        Id execBTId = Database.executeBatch(new UpdateTenderInformationBatch('123',true));
    }
     static testMethod void testForTest4() {
        List<BatchIF_Transfer__c> Transfers = new List<BatchIF_Transfer__c>();
        BatchIF_Transfer__c transfer2 = new BatchIF_Transfer__c();
        transfer2.Table__c          = 'Tender_information__c';
        transfer2.Column__c = 'UpdOppFlg';
        Transfers.add(transfer2);
        BatchIF_Transfer__c transfer3 = new BatchIF_Transfer__c();
        transfer3.Table__c          = 'ReportOppLinkFromOppBatch';
        transfer3.Column__c = 'UpdOppFlg';
        Transfers.add(transfer3);
        insert Transfers;

        List<String> list1 = new List<String>();
        List1.add('123');
        Id execBTId = Database.executeBatch(new UpdateTenderInformationBatch('123',true,List1));
    }
    static testMethod void testMethod1() {
        ControllerUtil.EscapeNFM001Trigger = true;
        ControllerUtil.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
        StaticParameter.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeOpportunityownerUpdate = true;
        Oly_TriggerHandler.bypass('UpdateContractAimAmountHandler');
        
        RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName IN ('Department_GI', 'Department_BF') order by DeveloperName desc];
        
        Account acc1 = new Account();
        acc1.RecordTypeId = rectCo.Id;
        acc1.Name = 'HP test1';
        insert acc1;

        Account acc2 = new Account();
        acc2.RecordTypeId = rectCo.Id;
        acc2.Name = 'HP test2';
        insert acc2;

        Account acc3 = new Account();
        acc3.RecordTypeId = rectCo.Id;
        acc3.Name = 'HP test3';
        insert acc3;
        
        List<Account> dept = [select Id, Name,Department_Class_Label__c from Account where ParentId = :acc1.Id and Department_Class_Label__c IN ('消化科', '呼吸科') order by Department_Class_Label__c];
        
        Account depart1 = new Account();
        depart1.RecordTypeId = rectDpt[0].Id;
        depart1.Name         = '*';
        depart1.Department_Name__c  = 'Gastoro Intestin Test';
        depart1.ParentId            = dept[0].Id;
        depart1.Department_Class__c = dept[0].Id;
        depart1.Hospital__c         = acc1.Id;
        
        Account depart2 = new Account();
        depart2.RecordTypeId = rectDpt[1].Id;
        depart2.Name         = '*';
        depart2.Department_Name__c  = '診療科2';
        depart2.ParentId            = dept[2].Id;
        depart2.Department_Class__c = dept[2].Id;
        depart2.Hospital__c         = acc1.Id;
        insert new Account[] {depart1, depart2};
        
        List<Account> dept2 = [select Id, Name from Account where ParentId = :acc2.Id and Department_Class_Label__c IN ('消化科', '呼吸科') order by Department_Class_Label__c];
        
        Account depart3 = new Account();
        depart3.RecordTypeId = rectDpt[0].Id;
        depart3.Name         = '*';
        depart3.Department_Name__c  = 'Gastoro Intestin Test';
        depart3.ParentId            = dept2[0].Id;
        depart3.Department_Class__c = dept2[0].Id;
        depart3.Hospital__c         = acc2.Id;
        insert depart3;
        
        // Account depart4 = new Account();
        // depart4.RecordTypeId = rectDpt[1].Id;
        // depart4.Name         = '*';
        // depart4.Department_Name__c  = '診療科2';
        // depart4.ParentId            = dept2[1].Id;
        // depart4.Department_Class__c = dept2[1].Id;
        // depart4.Hospital__c         = acc2.Id;
        // insert depart4;
        
        List<Account> dept3 = [select Id, Name from Account where ParentId = :acc3.Id and Department_Class_Label__c IN ('消化科', '呼吸科') order by Department_Class_Label__c];
        
        Account depart5 = new Account();
        depart5.RecordTypeId = rectDpt[0].Id;
        depart5.Name         = '*';
        depart5.Department_Name__c  = 'Gastoro Intestin Test';
        depart5.ParentId            = dept3[0].Id;
        depart5.Department_Class__c = dept3[0].Id;
        depart5.Hospital__c         = acc3.Id;
        insert depart5;
        
        // Account depart6 = new Account();
        // depart6.RecordTypeId = rectDpt[1].Id;
        // depart6.Name         = '*';
        // depart6.Department_Name__c  = '診療科2';
        // depart6.ParentId            = dept3[1].Id;
        // depart6.Department_Class__c = dept3[1].Id;
        // depart6.Hospital__c         = acc3.Id;
        // insert depart6;
        
        RecordType oppVND = [select id from RecordType where IsActive = true and SobjectType = 'Opportunity' and DeveloperName = 'Opportunity'];

        //招标项目
        Tender_information__c te1 = new Tender_information__c();
        te1.Name = 'TestZhaoBiao1';
        te1.IsReactionOpp__c = true;
        insert te1;

        Tender_information__c te2 = new Tender_information__c();
        te2.Name = 'TestZhaoBiao2';
        te2.IsReactionOpp__c = true;
        insert te2;

        Tender_information__c te3 = new Tender_information__c();
        te3.Name = 'TestZhaoBiao3';
        te3.IsReactionOpp__c = true;
        insert te3;

        ////询价
        //Opportunity opp1 = new Opportunity(
        //    Name = 'test opp1',
        //    StageName = '引合',
        //    CurrencyIsoCode = 'USD',
        //    CloseDate = Date.today(),
        //    AccountId = depart1.Id,
        //    RecordTypeId = oppVND.Id,
        //    Closing_Bid_Date__c = Date.today().addDays(-5),
        //    Hospital__c = acc1.Id,
        //    Competitor__c = 'A',
        //    Bidding_Project_Name_Bid__c = te1.Id
        //);
        //Opportunity opp2 = new Opportunity(
        //    Name = 'test opp2',
        //    StageName = '引合',
        //    CurrencyIsoCode = 'USD',
        //    CloseDate = Date.today(),
        //    AccountId = depart3.Id,
        //    RecordTypeId = oppVND.Id,
        //    Closing_Bid_Date__c = Date.today().addDays(-5),
        //    Hospital__c = acc2.Id,
        //    Competitor__c = 'B',
        //    Bidding_Project_Name_Bid__c = te1.Id
        //);
        //Opportunity opp3 = new Opportunity(
        //    Name = 'test opp3',
        //    StageName = '引合',
        //    CurrencyIsoCode = 'USD',
        //    CloseDate = Date.today(),
        //    AccountId = depart5.Id,
        //    RecordTypeId = oppVND.Id,
        //    Closing_Bid_Date__c = Date.today().addDays(-5),
        //    Hospital__c = acc3.Id,
        //    Competitor__c = 'C',
        //    Bidding_Project_Name_Bid__c = te2.Id
        //);
        //insert new Opportunity[] {opp1, opp2,opp3};

        List<BatchIF_Transfer__c> Transfers = new List<BatchIF_Transfer__c>();
        BatchIF_Transfer__c transfer2 = new BatchIF_Transfer__c();
        transfer2.Table__c          = 'Tender_information__c';
        transfer2.Column__c = 'UpdOppFlg';
        Transfers.add(transfer2);
        BatchIF_Transfer__c transfer3 = new BatchIF_Transfer__c();
        transfer3.Table__c          = 'ReportOppLinkFromOppBatch';
        transfer3.Column__c = 'UpdOppFlg';
        Transfers.add(transfer3);
        insert Transfers;

        System.Test.StartTest(); 

        //opp3.Bidding_Project_Name_Bid__c = te2.Id;
        //update opp3;  

        Id execBTId = Database.executeBatch(new UpdateTenderInformationBatch());
        System.Test.StopTest();
    }

    //询价状态为失单,数量为3
    static testMethod void testMethod2() {
        ControllerUtil.EscapeNFM001Trigger = true;
        ControllerUtil.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
        StaticParameter.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeOppandStaTrigger = true;
        Oly_TriggerHandler.bypass('UpdateContractAimAmountHandler');

        RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName IN ('Department_GI', 'Department_BF') order by DeveloperName desc];
        
        Account acc = new Account();
        acc.RecordTypeId = rectCo.Id;
        acc.Name = 'HP test1';
        insert acc;
        
        List<Account> dept = [select Id, Name,Department_Class_Label__c from Account where ParentId = :acc.Id and Department_Class_Label__c IN ('消化科', '呼吸科') order by Department_Class_Label__c];
        
        Account depart1 = new Account();
        depart1.RecordTypeId = rectDpt[0].Id;
        depart1.Name         = '*';
        depart1.Department_Name__c  = 'Gastoro Intestin Test';
        depart1.ParentId            = dept[0].Id;
        depart1.Department_Class__c = dept[0].Id;
        depart1.Hospital__c         = acc.Id;
        
        Account depart2 = new Account();
        depart2.RecordTypeId = rectDpt[1].Id;
        depart2.Name         = '*';
        depart2.Department_Name__c  = '診療科2';
        depart2.ParentId            = dept[2].Id;
        depart2.Department_Class__c = dept[2].Id;
        depart2.Hospital__c         = acc.Id;
        insert new Account[] {depart1, depart2};
        
        RecordType oppVND = [select id from RecordType where IsActive = true and SobjectType = 'Opportunity' and DeveloperName = 'Opportunity'];

        //招标项目
        Tender_information__c te1 = new Tender_information__c();
        te1.Name = 'TestZhaoBiao1';
        insert te1;

        Tender_information__c te2 = new Tender_information__c();
        te2.Name = 'TestZhaoBiao2';
        insert te2;

        //询价
        Opportunity opp1 = new Opportunity(
            Name = 'test opp1',
            StageName = '敗戦',
            CurrencyIsoCode = 'USD',
            CloseDate = Date.today(),
            AccountId = depart1.Id,
            RecordTypeId = oppVND.Id,
            Closing_Bid_Date__c = Date.today().addDays(-5),
            Hospital__c = acc.Id,
            Competitor__c = 'A'
        );
        Opportunity opp2 = new Opportunity(
            Name = 'test opp2',
            StageName = '敗戦',
            CurrencyIsoCode = 'USD',
            CloseDate = Date.today(),
            AccountId = depart2.Id,
            RecordTypeId = oppVND.Id,
            Closing_Bid_Date__c = Date.today().addDays(-5),
            Hospital__c = acc.Id,
            Competitor__c = 'B', 
            Bidding_Project_Name_Bid__c = te1.Id
        );
        Opportunity opp3 = new Opportunity(
            Name = 'test opp3',
            StageName = '敗戦',
            CurrencyIsoCode = 'USD',
            CloseDate = Date.today(),
            AccountId = depart2.Id,
            RecordTypeId = oppVND.Id,
            Closing_Bid_Date__c = Date.today().addDays(-5),
            Hospital__c = acc.Id,
            Competitor__c = 'C',
            Bidding_Project_Name_Bid__c = te2.Id
        );
        Oly_TriggerHandler.bypass('TenderOpportunityLinkHandler');
        Oly_TriggerHandler.bypass('TenderInformationHandler');
        insert new Opportunity[] {opp1, opp2,opp3};

        opp1.Bidding_Project_Name_Bid__c = te1.Id;
        opp2.Bidding_Project_Name_Bid__c = te2.Id;
        opp3.Bidding_Project_Name_Bid__c = null;

        update opp1;
        update opp2;
        update opp3;

        List<BatchIF_Transfer__c> Transfers = new List<BatchIF_Transfer__c>();
        BatchIF_Transfer__c transfer2 = new BatchIF_Transfer__c();
        transfer2.Table__c          = 'Tender_information__c';
        transfer2.Column__c = 'UpdOppFlg';
        Transfers.add(transfer2);
        BatchIF_Transfer__c transfer3 = new BatchIF_Transfer__c();
        transfer3.Table__c          = 'ReportOppLinkFromOppBatch';
        transfer3.Column__c = 'UpdOppFlg';
        Transfers.add(transfer3);
        insert Transfers;


        System.Test.StartTest();
        Id execBTId = Database.executeBatch(new UpdateTenderInformationBatch());
        System.Test.StopTest();

        te1 = [SELECT id,OpportunityStatus__c,OpportunityNum__c FROM Tender_information__c WHERE Id = :te1.Id];

        //System.assertEquals('失单',te1.OLYConfirmStatus__c);
        //System.assertEquals(3,te1.OpportunityNum__c);

    }

    //成交
    static testMethod void testMethod3() {
        StaticParameter.EscapeOppandStaTrigger = true;
        ControllerUtil.EscapeNFM001Trigger = true;
        ControllerUtil.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
        StaticParameter.EscapeMaintenanceContractAfterUpdateTrigger = true;
        Oly_TriggerHandler.bypass('UpdateContractAimAmountHandler');

        RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName IN ('Department_GI', 'Department_BF') order by DeveloperName desc];
        
        Account acc = new Account();
        acc.RecordTypeId = rectCo.Id;
        acc.Name = 'HP test1';
        insert acc;
        
        List<Account> dept = [select Id, Name,Department_Class_Label__c from Account where ParentId = :acc.Id and Department_Class_Label__c IN ('消化科', '呼吸科') order by Department_Class_Label__c];
        
        Account depart1 = new Account();
        depart1.RecordTypeId = rectDpt[0].Id;
        depart1.Name         = '*';
        depart1.Department_Name__c  = 'Gastoro Intestin Test';
        depart1.ParentId            = dept[0].Id;
        depart1.Department_Class__c = dept[0].Id;
        depart1.Hospital__c         = acc.Id;
        
        Account depart2 = new Account();
        depart2.RecordTypeId = rectDpt[1].Id;
        depart2.Name         = '*';
        depart2.Department_Name__c  = '診療科2';
        depart2.ParentId            = dept[2].Id;
        depart2.Department_Class__c = dept[2].Id;
        depart2.Hospital__c         = acc.Id;
        insert new Account[] {depart1, depart2};
        
        RecordType oppVND = [select id from RecordType where IsActive = true and SobjectType = 'Opportunity' and DeveloperName = 'Opportunity'];

        //招标项目
        Tender_information__c te1 = new Tender_information__c();
        te1.Name = 'TestZhaoBiao1';
        insert te1;

        Tender_information__c te2 = new Tender_information__c();
        te2.Name = 'TestZhaoBiao2';
        insert te2;

        //询价
        Opportunity opp1 = new Opportunity(
            Name = 'test opp1',
            StageName = '完了',
            CurrencyIsoCode = 'USD',
            CloseDate = Date.today(),
            AccountId = depart1.Id,
            RecordTypeId = oppVND.Id,
            Closing_Bid_Date__c = Date.today().addDays(-5),
            Hospital__c = acc.Id,
            Competitor__c = 'A',
            // SAP_Send_OK__c = true,
            SAP_Send_OK__c = false, // ******** ljh update
            Authorized_DB_No__c = '123',
            Contract_DB_SalesDept_complite_day__c = Date.today(),
            NotesApprovedNo__c = '123'
        );
        Opportunity opp2 = new Opportunity(
            Name = 'test opp2',
            StageName = '完了',
            CurrencyIsoCode = 'USD',
            CloseDate = Date.today(),
            AccountId = depart2.Id,
            RecordTypeId = oppVND.Id,
            Closing_Bid_Date__c = Date.today().addDays(-5),
            Hospital__c = acc.Id,
            Competitor__c = 'B',
            // SAP_Send_OK__c = true,
            SAP_Send_OK__c = false, // ******** ljh update
            Authorized_DB_No__c = '123',
            Contract_DB_SalesDept_complite_day__c = Date.today(),
            NotesApprovedNo__c = '123',
            Bidding_Project_Name_Bid__c = te1.Id
        );
        Opportunity opp3 = new Opportunity(
            Name = 'test opp3',
            StageName = '完了',
            CurrencyIsoCode = 'USD',
            CloseDate = Date.today(),
            AccountId = depart2.Id,
            RecordTypeId = oppVND.Id,
            Closing_Bid_Date__c = Date.today().addDays(-5),
            Hospital__c = acc.Id,
            Competitor__c = 'C',
            // SAP_Send_OK__c = true,
            SAP_Send_OK__c = false, // ******** ljh update
            Authorized_DB_No__c = '123',
            Contract_DB_SalesDept_complite_day__c = Date.today(),
            NotesApprovedNo__c = '123',
            Bidding_Project_Name_Bid__c = te2.Id
        );
        Oly_TriggerHandler.bypass('TenderOpportunityLinkHandler');
        Oly_TriggerHandler.bypass('TenderInformationHandler');
        insert new Opportunity[] {opp1, opp2,opp3};

        opp1.Bidding_Project_Name_Bid__c = te1.Id;
        opp2.Bidding_Project_Name_Bid__c = te2.Id;
        opp3.Bidding_Project_Name_Bid__c = null;

        update opp1;
        update opp2;        
        update opp3;

        List<BatchIF_Transfer__c> Transfers = new List<BatchIF_Transfer__c>();
        BatchIF_Transfer__c transfer2 = new BatchIF_Transfer__c();
        transfer2.Table__c          = 'Tender_information__c';
        transfer2.Column__c = 'UpdOppFlg';
        Transfers.add(transfer2);
        BatchIF_Transfer__c transfer3 = new BatchIF_Transfer__c();
        transfer3.Table__c          = 'ReportOppLinkFromOppBatch';
        transfer3.Column__c = 'UpdOppFlg';
        Transfers.add(transfer3);
        insert Transfers;

        System.Test.StartTest();
        Id execBTId = Database.executeBatch(new UpdateTenderInformationBatch());
        System.Test.StopTest();

        te1 = [SELECT id,OpportunityStatus__c,OpportunityNum__c FROM Tender_information__c WHERE Id = :te1.Id];

        //System.assertEquals('成交',te1.OLYConfirmStatus__c);
        //System.assertEquals(3,te1.OpportunityNum__c);
        StaticParameter.EscapeOppandStaTrigger = false;

    }

    //部分WIN部分失单 : 部分成交
    static testMethod void testMethod4() {
        StaticParameter.EscapeOppandStaTrigger = true;
        ControllerUtil.EscapeNFM001Trigger = true;
        ControllerUtil.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
        StaticParameter.EscapeMaintenanceContractAfterUpdateTrigger = true;
        Oly_TriggerHandler.bypass('UpdateContractAimAmountHandler');

        RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName IN ('Department_GI', 'Department_BF') order by DeveloperName desc];
        
        Account acc = new Account();
        acc.RecordTypeId = rectCo.Id;
        acc.Name = 'HP test1';
        insert acc;
        
        List<Account> dept = [select Id, Name,Department_Class_Label__c from Account where ParentId = :acc.Id and Department_Class_Label__c IN ('消化科', '呼吸科') order by Department_Class_Label__c];
        
        Account depart1 = new Account();
        depart1.RecordTypeId = rectDpt[0].Id;
        depart1.Name         = '*';
        depart1.Department_Name__c  = 'Gastoro Intestin Test';
        depart1.ParentId            = dept[0].Id;
        depart1.Department_Class__c = dept[0].Id;
        depart1.Hospital__c         = acc.Id;
        
        Account depart2 = new Account();
        depart2.RecordTypeId = rectDpt[1].Id;
        depart2.Name         = '*';
        depart2.Department_Name__c  = '診療科2';
        depart2.ParentId            = dept[2].Id;
        depart2.Department_Class__c = dept[2].Id;
        depart2.Hospital__c         = acc.Id;
        insert new Account[] {depart1, depart2};
        
        RecordType oppVND = [select id from RecordType where IsActive = true and SobjectType = 'Opportunity' and DeveloperName = 'Opportunity'];

        //招标项目
        Tender_information__c te1 = new Tender_information__c();
        te1.Name = 'TestZhaoBiao1';
        insert te1;

        Tender_information__c te2 = new Tender_information__c();
        te2.Name = 'TestZhaoBiao2';
        insert te2;

        //询价
        Opportunity opp1 = new Opportunity(
            Name = 'test opp1',
            StageName = '完了',
            CurrencyIsoCode = 'USD',
            CloseDate = Date.today(),
            AccountId = depart1.Id,
            RecordTypeId = oppVND.Id,
            Closing_Bid_Date__c = Date.today().addDays(-5),
            Hospital__c = acc.Id,
            Competitor__c = 'A',
            // SAP_Send_OK__c = true,
            SAP_Send_OK__c = false, // ******** ljh update
            Authorized_DB_No__c = '123',
            Contract_DB_SalesDept_complite_day__c = Date.today(),
            NotesApprovedNo__c = '123'
        );
        Opportunity opp2 = new Opportunity(
            Name = 'test opp2',
            StageName = '完了',
            CurrencyIsoCode = 'USD',
            CloseDate = Date.today(),
            AccountId = depart2.Id,
            RecordTypeId = oppVND.Id,
            Closing_Bid_Date__c = Date.today().addDays(-5),
            Hospital__c = acc.Id,
            Competitor__c = 'B',
            // SAP_Send_OK__c = true,
            SAP_Send_OK__c = false,// ******** ljh update
            Authorized_DB_No__c = '123',
            Contract_DB_SalesDept_complite_day__c = Date.today(),
            NotesApprovedNo__c = '123',
            Bidding_Project_Name_Bid__c = te1.Id
        );
        Opportunity opp3 = new Opportunity(
            Name = 'test opp3',
            StageName = '敗戦',
            CurrencyIsoCode = 'USD',
            CloseDate = Date.today(),
            AccountId = depart2.Id,
            RecordTypeId = oppVND.Id,
            Closing_Bid_Date__c = Date.today().addDays(-5),
            Hospital__c = acc.Id,
            Competitor__c = 'C',
            Bidding_Project_Name_Bid__c = te2.Id
        );

        Oly_TriggerHandler.bypass('TenderOpportunityLinkHandler');
        Oly_TriggerHandler.bypass('TenderInformationHandler');
        insert new Opportunity[] {opp1, opp2,opp3};

        opp1.Bidding_Project_Name_Bid__c = te1.Id;
        opp2.Bidding_Project_Name_Bid__c = te2.Id;
        opp3.Bidding_Project_Name_Bid__c = null;

        update opp1;
        update opp2; 
        update opp3;      

        List<BatchIF_Transfer__c> Transfers = new List<BatchIF_Transfer__c>();
        BatchIF_Transfer__c transfer2 = new BatchIF_Transfer__c();
        transfer2.Table__c          = 'Tender_information__c';
        transfer2.Column__c = 'UpdOppFlg';
        Transfers.add(transfer2);
        BatchIF_Transfer__c transfer3 = new BatchIF_Transfer__c();
        transfer3.Table__c          = 'ReportOppLinkFromOppBatch';
        transfer3.Column__c = 'UpdOppFlg';
        Transfers.add(transfer3);
        insert Transfers;

        System.Test.StartTest();
        Id execBTId = Database.executeBatch(new UpdateTenderInformationBatch());
        System.Test.StopTest();

        te1 = [SELECT id,OpportunityStatus__c,OpportunityNum__c FROM Tender_information__c WHERE Id = :te1.Id];

        //System.assertEquals('成交',te1.OLYConfirmStatus__c);
       // System.assertEquals(3,te1.OpportunityNum__c);
        //System.assertEquals(null,te1);
        StaticParameter.EscapeOppandStaTrigger = false;

    }

    //webService
    static testMethod void testMethod5() {
        ControllerUtil.EscapeNFM001Trigger = true;
        ControllerUtil.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
        StaticParameter.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeOpportunityownerUpdate = true;
        Oly_TriggerHandler.bypass('UpdateContractAimAmountHandler');
        RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName IN ('Department_GI', 'Department_BF') order by DeveloperName desc];
        
        Account acc1 = new Account();
        acc1.RecordTypeId = rectCo.Id;
        acc1.Name = 'HP test1';
        insert acc1;

        Account acc2 = new Account();
        acc2.RecordTypeId = rectCo.Id;
        acc2.Name = 'HP test2';
        insert acc2;

        Account acc3 = new Account();
        acc3.RecordTypeId = rectCo.Id;
        acc3.Name = 'HP test3';
        insert acc3;
        
        List<Account> dept = [select Id, Name, Department_Class_Label__c from Account where ParentId = :acc1.Id and Department_Class_Label__c IN ('消化科', '呼吸科') order by Department_Class_Label__c ];
        
        System.debug('********---lt---rectDpt:'+rectDpt);
        System.debug('********---lt---dept:'+dept);

        Account depart1 = new Account();
        depart1.RecordTypeId = rectDpt[0].Id;
        depart1.Name         = '*';
        depart1.Department_Name__c  = 'Gastoro Intestin Test';
        depart1.ParentId            = dept[0].Id;
        depart1.Department_Class__c = dept[0].Id;
        depart1.Hospital__c         = acc1.Id;
        
        Account depart2 = new Account();
        depart2.RecordTypeId = rectDpt[1].Id;
        depart2.Name         = '*';
        depart2.Department_Name__c  = '診療科2';
        depart2.ParentId            = dept[2].Id;
        depart2.Department_Class__c = dept[2].Id;
        depart2.Hospital__c         = acc1.Id;
        
        System.debug('********---lt---depart1.Parent.Department_Class_Label__c:'+depart1.Parent.Department_Class_Label__c);
        System.debug('********---lt---depart2.Parent.Department_Class_Label__c:'+depart2.Parent.Department_Class_Label__c);
        System.debug('********---lt---depart1:'+depart1);
        System.debug('********---lt---depart2:'+depart2);
        insert new Account[] {depart1, depart2};
        
        List<Account> dept2 = [select Id, Name from Account where ParentId = :acc2.Id and Department_Class_Label__c IN ('消化科', '呼吸科') order by Department_Class_Label__c];
        
        Account depart3 = new Account();
        depart3.RecordTypeId = rectDpt[0].Id;
        depart3.Name         = '*';
        depart3.Department_Name__c  = 'Gastoro Intestin Test';
        depart3.ParentId            = dept2[0].Id;
        depart3.Department_Class__c = dept2[0].Id;
        depart3.Hospital__c         = acc2.Id;
        insert depart3;
        
        // Account depart4 = new Account();
        // depart4.RecordTypeId = rectDpt[1].Id;
        // depart4.Name         = '*';
        // depart4.Department_Name__c  = '診療科2';
        // depart4.ParentId            = dept2[1].Id;
        // depart4.Department_Class__c = dept2[1].Id;
        // depart4.Hospital__c         = acc2.Id;
        // insert depart4;
        
        List<Account> dept3 = [select Id, Name from Account where ParentId = :acc3.Id and Department_Class_Label__c IN ('消化科', '呼吸科') order by Department_Class_Label__c];
        
        Account depart5 = new Account();
        depart5.RecordTypeId = rectDpt[0].Id;
        depart5.Name         = '*';
        depart5.Department_Name__c  = 'Gastoro Intestin Test';
        depart5.ParentId            = dept3[0].Id;
        depart5.Department_Class__c = dept3[0].Id;
        depart5.Hospital__c         = acc3.Id;
        insert depart5;
        
        // Account depart6 = new Account();
        // depart6.RecordTypeId = rectDpt[1].Id;
        // depart6.Name         = '*';
        // depart6.Department_Name__c  = '診療科2';
        // depart6.ParentId            = dept3[1].Id;
        // depart6.Department_Class__c = dept3[1].Id;
        // depart6.Hospital__c         = acc3.Id;
        // insert depart6;
        
        RecordType oppVND = [select id from RecordType where IsActive = true and SobjectType = 'Opportunity' and DeveloperName = 'Opportunity'];

        //招标项目
        Tender_information__c te1 = new Tender_information__c();
        te1.Name = 'TestZhaoBiao1';
        te1.IsReactionOpp__c = true;
        insert te1;

        Tender_information__c te2 = new Tender_information__c();
        te2.Name = 'TestZhaoBiao2';
        te2.IsReactionOpp__c = true;
        insert te2;

        Tender_information__c te3 = new Tender_information__c();
        te3.Name = 'TestZhaoBiao3';
        te3.IsReactionOpp__c = true;
        insert te3;

        ////询价
        //Opportunity opp1 = new Opportunity(
        //    Name = 'test opp1',
        //    StageName = '完了',
        //    CurrencyIsoCode = 'USD',
        //    CloseDate = Date.today(),
        //    AccountId = depart1.Id,
        //    RecordTypeId = oppVND.Id,
        //    Closing_Bid_Date__c = Date.today().addDays(-5),
        //    Hospital__c = acc1.Id,
        //    Competitor__c = 'A',
        //    // SAP_Send_OK__c = true,
        //    SAP_Send_OK__c = false,// ******** ljh update
        //    Authorized_DB_No__c = '123',
        //    Contract_DB_SalesDept_complite_day__c = Date.today(),
        //    NotesApprovedNo__c = '123',
        //    Bidding_Project_Name_Bid__c = te1.Id
        //);
        //Opportunity opp2 = new Opportunity(
        //    Name = 'test opp2',
        //    StageName = '完了',
        //    CurrencyIsoCode = 'USD',
        //    CloseDate = Date.today(),
        //    AccountId = depart3.Id,
        //    RecordTypeId = oppVND.Id,
        //    Closing_Bid_Date__c = Date.today().addDays(-5),
        //    Hospital__c = acc2.Id,
        //    Competitor__c = 'B',
        //    // SAP_Send_OK__c = true,
        //    SAP_Send_OK__c = false,// ******** ljh update
        //    Authorized_DB_No__c = '123',
        //    Contract_DB_SalesDept_complite_day__c = Date.today(),
        //    NotesApprovedNo__c = '123',
        //    Bidding_Project_Name_Bid__c = te1.Id
        //);
        //Opportunity opp3 = new Opportunity(
        //    Name = 'test opp3',
        //    StageName = '敗戦',
        //    CurrencyIsoCode = 'USD',
        //    CloseDate = Date.today(),
        //    AccountId = depart5.Id,
        //    RecordTypeId = oppVND.Id,
        //    Closing_Bid_Date__c = Date.today().addDays(-5),
        //    Hospital__c = acc3.Id,
        //    Competitor__c = 'C',
        //    Bidding_Project_Name_Bid__c = te2.Id
        //);

        //insert new Opportunity[] {opp1, opp2,opp3};

        System.Test.StartTest();

        //opp2.Bidding_Project_Name_Bid__c = te2.Id;
        //update opp2;  

        List<String> ids = new List<String> ();
        ids.add(String.valueOf(te1.Id).substring(0,15));
        ids.add(String.valueOf(te2.Id).substring(0,15));
        ids.add(String.valueOf(te3.Id).substring(0,15));

        UpdateTenderInformationBatch.updateOpportunityInformation(ids);    
        System.Test.StopTest();    
    }

    // ******** ljh add
    
    static testMethod void testMethod07() {
        ControllerUtil.EscapeNFM001Trigger = true;
        ControllerUtil.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
        StaticParameter.EscapeMaintenanceContractAfterUpdateTrigger = true;
        Oly_TriggerHandler.bypass('UpdateContractAimAmountHandler');

        RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName IN ('Department_GI', 'Department_BF') order by DeveloperName desc];
        
        Account acc = new Account();
        acc.RecordTypeId = rectCo.Id;
        acc.Name = 'HP test1';
        acc.Assume_Change__c = true;
        acc.Is_Active__c = '有効';
        insert acc;
        
        List<Account> dept = [select Id, Name,Department_Class_Label__c from Account where ParentId = :acc.Id and Department_Class_Label__c IN ('消化科', '呼吸科') order by Department_Class_Label__c];
        
        Account depart1 = new Account();
        depart1.RecordTypeId = rectDpt[0].Id;
        depart1.Name         = '*';
        depart1.Department_Name__c  = 'Gastoro Intestin Test';
        depart1.ParentId            = dept[0].Id;
        depart1.Department_Class__c = dept[0].Id;
        depart1.Hospital__c         = acc.Id;
        
        Account depart2 = new Account();
        depart2.RecordTypeId = rectDpt[1].Id;
        depart2.Name         = '*';
        depart2.Department_Name__c  = '診療科2';
        depart2.ParentId            = dept[2].Id;
        depart2.Department_Class__c = dept[2].Id;
        depart2.Hospital__c         = acc.Id;
        insert new Account[] {depart1, depart2};
        
        RecordType oppVND = [select id from RecordType where IsActive = true and SobjectType = 'Opportunity' and DeveloperName = 'Opportunity'];

        //招标项目
        Tender_information__c te1 = new Tender_information__c();
        te1.Name = 'TestZhaoBiao1';
        te1.IsReactionOpp__c = false;
        te1.Hospital__c = acc.Id;
        insert te1;

        Tender_information__c te2 = new Tender_information__c();
        te1.IsReactionOpp__c = false;
        te2.Name = 'TestZhaoBiao2';
        insert te2;
        //询价
        // Opportunity opp1 = new Opportunity(
        //     Name = 'test opp1',
        //     StageName = '敗戦',
        //     CurrencyIsoCode = 'USD',
        //     CloseDate = Date.today(),
        //     AccountId = depart1.Id,
        //     RecordTypeId = oppVND.Id,
        //     Closing_Bid_Date__c = Date.today().addDays(-5),
        //     Hospital__c = acc.Id,
        //     Competitor__c = 'A'
        // );
        // Opportunity opp2 = new Opportunity(
        //     Name = 'test opp2',
        //     StageName = '敗戦',
        //     CurrencyIsoCode = 'USD',
        //     CloseDate = Date.today(),
        //     AccountId = depart2.Id,
        //     RecordTypeId = oppVND.Id,
        //     Closing_Bid_Date__c = Date.today().addDays(-5),
        //     Hospital__c = acc.Id,
        //     Competitor__c = 'B', 
        //     Bidding_Project_Name_Bid__c = te1.Id
        // );
        // Opportunity opp3 = new Opportunity(
        //     Name = 'test opp3',
        //     StageName = '敗戦',
        //     CurrencyIsoCode = 'USD',
        //     CloseDate = Date.today(),
        //     AccountId = depart2.Id,
        //     RecordTypeId = oppVND.Id,
        //     Closing_Bid_Date__c = Date.today().addDays(-5),
        //     Hospital__c = acc.Id,
        //     Competitor__c = 'C',
        //     Bidding_Project_Name_Bid__c = te2.Id
        // );
        // insert new Opportunity[] {opp1, opp2,opp3};

        // opp1.Bidding_Project_Name_Bid__c = te1.Id;
        // opp2.Bidding_Project_Name_Bid__c = te2.Id;
        // opp3.Bidding_Project_Name_Bid__c = null;

        // update opp1;
        // update opp2;
        // update opp3;

        List<String> tempList = new List<String>();
        tempList.add(te1.Id);
        tempList.add(te2.Id);

        List<BatchIF_Transfer__c> Transfers = new List<BatchIF_Transfer__c>();
        BatchIF_Transfer__c transfer2 = new BatchIF_Transfer__c();
        transfer2.Table__c          = 'Tender_information__c';
        transfer2.Column__c = 'UpdOppFlg';
        Transfers.add(transfer2);
        BatchIF_Transfer__c transfer3 = new BatchIF_Transfer__c();
        transfer3.Table__c          = 'ReportOppLinkFromOppBatch';
        transfer3.Column__c = 'UpdOppFlg';
        Transfers.add(transfer3);
        insert Transfers;

        System.Test.StartTest();
        UpdateTenderInformationBatch batch = new UpdateTenderInformationBatch('',false);
        Id execBTId = Database.executeBatch(batch);
        System.Test.StopTest();
    }

    static testMethod void testMethod08() {
        ControllerUtil.EscapeNFM001Trigger = true;
        ControllerUtil.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001AgencyContractTrigger2 = true;
        StaticParameter.EscapeMaintenanceContractAfterUpdateTrigger = true;
        StaticParameter.EscapeOppandStaTrigger = true;
        Oly_TriggerHandler.bypass('UpdateContractAimAmountHandler');

        RecordType rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName = 'Hp'];
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and DeveloperName IN ('Department_GI', 'Department_BF') order by DeveloperName desc];
        
        Account acc = new Account();
        acc.RecordTypeId = rectCo.Id;
        acc.Name = 'HP test1';
        insert acc;
        
        List<Account> dept = [select Id, Name,Department_Class_Label__c from Account where ParentId = :acc.Id and Department_Class_Label__c IN ('消化科', '呼吸科') order by Department_Class_Label__c];
        
        Account depart1 = new Account();
        depart1.RecordTypeId = rectDpt[0].Id;
        depart1.Name         = '*';
        depart1.Department_Name__c  = 'Gastoro Intestin Test';
        depart1.ParentId            = dept[0].Id;
        depart1.Department_Class__c = dept[0].Id;
        depart1.Hospital__c         = acc.Id;
        
        Account depart2 = new Account();
        depart2.RecordTypeId = rectDpt[1].Id;
        depart2.Name         = '*';
        depart2.Department_Name__c  = '診療科2';
        depart2.ParentId            = dept[2].Id;
        depart2.Department_Class__c = dept[2].Id;
        depart2.Hospital__c         = acc.Id;
        insert new Account[] {depart1, depart2};
        
        RecordType oppVND = [select id from RecordType where IsActive = true and SobjectType = 'Opportunity' and DeveloperName = 'Opportunity'];

        //招标项目
        Tender_information__c te1 = new Tender_information__c();
        te1.Name = 'TestZhaoBiao1';
        insert te1;

        Tender_information__c te2 = new Tender_information__c();
        te2.Name = 'TestZhaoBiao2';
        insert te2;
        //询价
        Opportunity opp1 = new Opportunity(
            Name = 'test opp1',
            StageName = '敗戦',
            CurrencyIsoCode = 'USD',
            CloseDate = Date.today(),
            AccountId = depart1.Id,
            RecordTypeId = oppVND.Id,
            Closing_Bid_Date__c = Date.today().addDays(-5),
            Hospital__c = acc.Id,
            Competitor__c = 'A'
        );
        Opportunity opp2 = new Opportunity(
            Name = 'test opp2',
            StageName = '敗戦',
            CurrencyIsoCode = 'USD',
            CloseDate = Date.today(),
            AccountId = depart2.Id,
            RecordTypeId = oppVND.Id,
            Closing_Bid_Date__c = Date.today().addDays(-5),
            Hospital__c = acc.Id,
            Competitor__c = 'B', 
            Bidding_Project_Name_Bid__c = te1.Id
        );
        Opportunity opp3 = new Opportunity(
            Name = 'test opp3',
            StageName = '敗戦',
            CurrencyIsoCode = 'USD',
            CloseDate = Date.today(),
            AccountId = depart2.Id,
            RecordTypeId = oppVND.Id,
            Closing_Bid_Date__c = Date.today().addDays(-5),
            Hospital__c = acc.Id,
            Competitor__c = 'C',
            Bidding_Project_Name_Bid__c = te2.Id
        );
        Oly_TriggerHandler.bypass('TenderOpportunityLinkHandler');
        Oly_TriggerHandler.bypass('TenderInformationHandler');
        insert new Opportunity[] {opp1, opp2,opp3};

        opp1.Bidding_Project_Name_Bid__c = te1.Id;
        opp2.Bidding_Project_Name_Bid__c = te2.Id;
        opp3.Bidding_Project_Name_Bid__c = null;

        update opp1;
        update opp2;
        update opp3;

        List<String> tempList = new List<String>();
        tempList.add(te1.Id);
        tempList.add(te2.Id);

        List<BatchIF_Transfer__c> Transfers = new List<BatchIF_Transfer__c>();
        BatchIF_Transfer__c transfer2 = new BatchIF_Transfer__c();
        transfer2.Table__c          = 'Tender_information__c';
        transfer2.Column__c = 'UpdOppFlg';
        Transfers.add(transfer2);
        BatchIF_Transfer__c transfer3 = new BatchIF_Transfer__c();
        transfer3.Table__c          = 'ReportOppLinkFromOppBatch';
        transfer3.Column__c = 'UpdOppFlg';
        Transfers.add(transfer3);
        insert Transfers;

        System.Test.StartTest();
        UpdateTenderInformationBatch batch = new UpdateTenderInformationBatch(opp1.Id,false,tempList);
        Id execBTId = Database.executeBatch(batch);
        System.Test.StopTest();
    }

    static testMethod void testMethod09() {
        Tender_information__c te1 = new Tender_information__c();
        te1.Name = 'TestZhaoBiao1';
        insert te1;

        Tender_information__c te2 = new Tender_information__c();
        te2.Name = 'TestZhaoBiao2';
        insert te2;
        System.Test.StartTest();
        UpdateTenderInformationBatch batch = new UpdateTenderInformationBatch(te1.Id);
        System.Test.StopTest();
    }


    static testMethod void testMethod10() {
        Tender_information__c te1 = new Tender_information__c();
        te1.Name = 'TestZhaoBiao1';
        insert te1;

        Tender_information__c te2 = new Tender_information__c();
        te2.Name = 'TestZhaoBiao2';
        insert te2;
        List<String> tempList = new List<String>();
        tempList.add(te1.Id);
        tempList.add(te2.Id);
        System.Test.StartTest();
        UpdateTenderInformationBatch batch = new UpdateTenderInformationBatch(tempList);
        System.Test.StopTest();
    }
}