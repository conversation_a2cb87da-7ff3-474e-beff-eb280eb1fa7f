<aura:component implements="force:lightningQuickActionWithoutHeader,force:hasRecordId,lightning:actionOverride,lightning:isUrlAddressable" access="global" 
                controller="NewAgencyOpportunityController">
    <aura:html tag="style">
        .slds-modal__container{
            max-width: 53rem !important;
            width:100% !important;
        }
    </aura:html>
    <aura:attribute name = "recordId" type = "Id" default = ""/>
    <aura:attribute name = "recordTypeId" type = "Id" default = ""/>
    <aura:attribute name="ac_name" type="string"/>
    <aura:attribute name="title" type="string"/>
    <aura:handler name="init" value="{!this}" action="{!c.doInit}" />
    <aura:attribute name="layout" type="LayoutDescriberHelper.LayoutWrapper"/>
    <aura:attribute name="record_data" type="Map"/>
    <aura:attribute name="section_names" type="List"/>
    <aura:attribute name="showSpinner" type="Boolean" default = "False"/>
    <aura:attribute name="showSpinner2" type="Boolean" default = "False"/>
    <aura:attribute name="staticResource" type="Map"/>
    <aura:attribute name="pi_fields_map" type="Map"/>
    <aura:attribute name="isClone" type="Boolean" default = "False"/>
    <ltng:require scripts="{! $Resource.AWSService+'/AWSService.js' }" />
    <ltng:require scripts="{! $Resource.jquery183minjs }" />
    <aura:attribute name="isModalOpen" type="boolean" default="false"/>
    <aura:attribute name="searchKeyWord" type="string" default=""/>
    <aura:attribute name="container_class" type="string" default=""/>
    <aura:attribute name="contactAWSIds" type="List"/>
    <aura:attribute name="login" type="Boolean" default="false"/>
    <aura:attribute name="show" type="Boolean" default="false"/>
    <aura:attribute name="showNewAgencyContact" type="Boolean" default="false"/>
    <aura:handler name="NewAgencyContactEvent" event="c:NewAgencyContactEvent" action="{!c.closeAgencyContact}"/>
    <aura:attribute name="agencyHospitalId" type="String" default = ""/>
    <lightning:navigation aura:id="navService"/>

    <!-- deloitte-zhj 20231027 是否是内部用户 -->
    <aura:attribute name="isStandard" type="Boolean" default="false"/>

    <!-- deloitte-zhj 20231104 -->
    <aura:attribute name="iconName" type="string" default=""/>

    
    
	<div class="{! v.container_class}">
        <!-- header -->
        <header class="slds-modal__header">
            <h2 id="modal-heading-01" class="slds-modal__title slds-hyphenate">
                {!v.title}
            </h2>
        </header>

        <div class="slds-modal__content slds-p-around_medium">
            <aura:if isTrue="{!v.showSpinner}">
                <lightning:spinner alternativeText="Loading" size="medium" />
            </aura:if>
            <lightning:recordEditForm objectApiName="Agency_Opportunity__c" onload="{!c.handleLoad}">
                <lightning:accordion activeSectionName="{! v.section_names }" allowMultipleSectionsOpen="true" class="greyyyy" >
                    <aura:iteration items="{!v.layout}" var="section">
                        <aura:if isTrue="{! section.editHeading }">
                            <lightning:accordionSection name="{! section.label }" label="{! section.label }">
                                <aura:if isTrue="{!v.isStandard }">
                                    <lightning:layout multipleRows="true">
                                        <aura:iteration items="{! section.layoutColumns}" var="col" indexVar="index">
                                            <!-- deloitte-zhj-20231031 直接分为2列，奇数在左边，偶数在右边 -->
                                            <!-- <lightning:layout multipleRows="true"> -->
                                                <aura:if isTrue="{! mod(index, 2) == 0 }">
                                                    <lightning:layoutItem size="6">
                                                        <lightning:layout multipleRows="true">
                                                            <aura:iteration items="{! col.layoutItems}" var="field">
                                                                <lightning:layoutItem size="12">
                                                                    <lightning:layout multipleRows="true">
                                                                        <lightning:layoutItem size="1"></lightning:layoutItem>
                                                                        <lightning:layoutItem size="10">
                                                                            <aura:if isTrue="{!field.field != 'Agency_Contact__c'}">
                                                                                <lightning:inputField readonly="{! field.behavior == 'Readonly' }" required="{! field.behavior == 'Required' }" onchange="{!c.dataChange}" aura:id="field" fieldName="{! field.field}" value="{! field.value}" />
                                                                            </aura:if>
                                                                            <aura:if isTrue="{!field.field == 'Agency_Contact__c' }">
                                                                                <lightning:layout>
                                                                                    <lightning:layoutItem size="12">
                                                                                        <div class="slds-form-element__control slds-input-has-icon slds-input-has-icon_right">
                                                                                            <lightning:input required="{! field.behavior == 'Required' }" label="客户人员" value = "{! field.value}" variant = "label-inline" style="margin-left: 0cm; display: none;" onclick="{! c.openModel }"/>
                                                                                            <lightning:input required="{! field.behavior == 'Required' }" label="客户人员" value = "{! v.ac_name}" variant = "label-inline" style="margin-left: 0cm; " onclick="{! c.openModel }"/>
                                                                                            <lightning:icon iconName="{!v.iconName}"
                                                                                                size="x-small"
                                                                                                class="iconMargin slds-icon slds-input__icon slds-input__icon_right slds-icon-text-default"
                                                                                                onclick="{!c.clearName}"
                                                                                                style="cursor: pointer; pointer-events: auto;"/>
                                                                                        </div>
                                                                                    </lightning:layoutItem>
                                                                                </lightning:layout>
                                                                            </aura:if>
                                                                        </lightning:layoutItem>
                                                                    </lightning:layout>
                                                                </lightning:layoutItem>
                                                            </aura:iteration>
                                                        </lightning:layout>
                                                    </lightning:layoutItem>
                                                </aura:if>

                                                <aura:if isTrue="{! mod(index, 2) != 0 }">
                                                    <lightning:layoutItem size="6">
                                                        <lightning:layout multipleRows="true">
                                                            <aura:iteration items="{! col.layoutItems}" var="field">
                                                                <lightning:layoutItem size="12">
                                                                    <lightning:layout multipleRows="true">
                                                                        <lightning:layoutItem size="1"></lightning:layoutItem>
                                                                        <lightning:layoutItem size="10">
                                                                            <aura:if isTrue="{!field.field != 'Agency_Contact__c'}">
                                                                                <lightning:inputField readonly="{! field.behavior == 'Readonly' }" required="{! field.behavior == 'Required' }" onchange="{!c.dataChange}" aura:id="field" fieldName="{! field.field}" value="{! field.value}" />
                                                                            </aura:if>
                                                                            <aura:if isTrue="{!field.field == 'Agency_Contact__c' }">
                                                                                <lightning:layout>
                                                                                    <lightning:layoutItem size="12">
                                                                                        <lightning:input required="{! field.behavior == 'Required' }" label="客户人员" value = "{! field.value}" variant = "label-inline" style="margin-left: 0cm; display: none;" onclick="{! c.openModel }"/>
                                                                                        <lightning:input required="{! field.behavior == 'Required' }" label="客户人员" value = "{! v.ac_name}" variant = "label-inline" style="margin-left: 0cm; " onclick="{! c.openModel }"/>
                                                                                    </lightning:layoutItem>
                                                                                </lightning:layout>
                                                                            </aura:if>
                                                                        </lightning:layoutItem>
                                                                    </lightning:layout>
                                                                </lightning:layoutItem>
                                                            </aura:iteration>
                                                        </lightning:layout>
                                                    </lightning:layoutItem>
                                                </aura:if>
                                            <!-- </lightning:layout> -->
                                            <!-- <aura:iteration items="{! col.layoutItems}" var="field">
                                                <lightning:layoutItem size="6">
                                                    <lightning:layout multipleRows="true">
                                                        <lightning:layoutItem size="1"></lightning:layoutItem>
                                                        <lightning:layoutItem size="10">
                                                            <aura:if isTrue="{!and(field.behavior != 'Readonly', field.field != 'Agency_Contact__c')}">
                                                                <lightning:inputField required="{! field.behavior == 'Required' }" aura:id="field" fieldName="{! field.field}" value="{! field.value}" />
                                                            </aura:if>
                                                            <aura:if isTrue="{!field.field == 'Agency_Contact__c' }">
                                                                <lightning:layout>
                                                                    <lightning:layoutItem size="12">
                                                                        <lightning:input required="{! field.behavior == 'Required' }" label="客户人员" value = "{! field.value}" variant = "label-inline" style="margin-left: 0cm; display: none;" onclick="{! c.openModel }"/>
                                                                        <lightning:input required="{! field.behavior == 'Required' }" label="客户人员" value = "{! v.ac_name}" variant = "label-inline" style="margin-left: 0cm; " onclick="{! c.openModel }"/>
                                                                    </lightning:layoutItem>
                                                                </lightning:layout>
                                                            </aura:if>
                                                        </lightning:layoutItem>
                                                    </lightning:layout>
                                                </lightning:layoutItem>
                                            </aura:iteration> -->
                                        </aura:iteration>
                                    </lightning:layout>
                                </aura:if>
                                <aura:if isTrue="{!!v.isStandard }">
                                    <aura:iteration items="{! section.layoutColumns}" var="col">
                                        <aura:iteration items="{! col.layoutItems}" var="field">
                                            <aura:if isTrue="{!field.field != 'Agency_Contact__c'}">
                                                <lightning:inputField readonly="{! field.behavior == 'Readonly' }" required="{! field.behavior == 'Required' }" onchange="{!c.dataChange}" aura:id="field" fieldName="{! field.field}" value="{! field.value}" />
                                            </aura:if>
                                            <aura:if isTrue="{!field.field == 'Agency_Contact__c' }">
                                                <lightning:layout>
                                                    <lightning:layoutItem size="12">
                                                        <lightning:input required="{! field.behavior == 'Required' }" label="客户人员" value = "{! field.value}" variant = "label-inline" style="margin-left: 0cm; display: none;" onclick="{! c.openModel }"/>
                                                        <lightning:input required="{! field.behavior == 'Required' }" label="客户人员" value = "{! v.ac_name}" variant = "label-inline" style="margin-left: 0cm; " onclick="{! c.openModel }"/>
                                                    </lightning:layoutItem>
                                                </lightning:layout>
                                            </aura:if>
                                        </aura:iteration>
                                    </aura:iteration>
                                </aura:if>
                            </lightning:accordionSection>
                        </aura:if>
                    </aura:iteration>
                </lightning:accordion>
            </lightning:recordEditForm>
			
            <div  style="text-align:center;margin: 5px;">
                <lightning:button class="slds-button slds-button_neutral" label="取消" onclick="{! c.cancelClick }" />
                <lightning:button class="slds-button slds-button_brand" variant="brand" label="保存" onclick="{! c.saveClick }" />
            </div>
        </div>
            <aura:if isTrue="{!v.isModalOpen}">

                <!-- Modal/Popup Box starts here-->
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" 
                aria-modal="true" aria-describedby="modal-content-id-1"   
                    class="slds-modal slds-fade-in-open" >



                    <aura:renderIf isTrue="{!v.login}">
                        <div class="slds-spinner_container height100vh">
                            <div class="slds-spinner--brand slds-spinner slds-spinner--medium" role="alert">
                                <span id="aa" class="slds-assistive-text">Loading</span>
                                <div class="slds-spinner__dot-a"></div>
                                <div class="slds-spinner__dot-b"></div>
                            </div>
                        </div>
                    </aura:renderIf>



                    <div class="slds-modal__container">
                    <!-- Modal/Popup Box Header Starts here-->
                    <header class="slds-modal__header">
                        <lightning:buttonIcon iconName="utility:close"
                            onclick="{! c.closeModel }"
                            alternativeText="close"
                            variant="bare-inverse"
                            class="slds-modal__close"/>
                        <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">
                                搜索客户人员
                        </h2>
                    </header>


                    <aura:if isTrue="{!v.showSpinner2}">
                        <lightning:spinner alternativeText="Loading" size="medium" />
                    </aura:if>

                    <!--Modal Body Start here-->
                    <div class="slds-modal__content slds-p-around_medium modal-body" id="modal-content-id-1">
                        <lightning:layout>
                            <lightning:layoutItem size="7">
                                <ui:inputText label="请输入客户姓名" class="field" value="{!v.searchKeyWord}"/>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="2" class = "searchButton">
                                <ui:button aura:id="button" buttonTitle="Click to see agency contact" class="button" label="搜索" press="{!c.searchAgencyContact}" disabled="false"/>
                            </lightning:layoutItem>
                            <lightning:layoutItem size="3" class = "searchButton">
                                <ui:button aura:id="newAgencyContact" buttonTitle="Click to see agency contact" class="newAgencyContact" label="新建.客户人员"  disabled="false" press="{!c.showNewAgency}"/>
                            </lightning:layoutItem>
                        </lightning:layout>
                        <div id="QueryResult" style = "margin-top:20px;">
                        </div>
                    </div>
                        
                        <!-- <aura:if isTrue="{!v.showNewAgencyContact}">
                            <section role="dialog" tabindex="-1" aria-modal="true" aria-labelledby="modal-heading-01" class="slds-modal slds-fade-in-open"  aura:id="NewAgencyContact" id="NewAgencyContact">
                        		<c:NewAgencyContact2 />
                            </section>
                        </aura:if> -->

                        <aura:if isTrue="{!v.showNewAgencyContact}">
                            <section role="dialog" tabindex="-1" aria-modal="true" aria-labelledby="modal-heading-01"
                                class="slds-modal slds-fade-in-open" aura:id="NewAgencyContact"
                                id="NewAgencyContact" aria-describedby="modal-content-id-2" style="z-index: 9999;">
                                <c:NewAgencyContact2 agencyHospitalId="{!v.agencyHospitalId}"/>
                            </section>
                            <div class="slds-backdrop2 slds-backdrop_open2"></div>
                        </aura:if>
                        
                    </div>                               
                </section>             
                <div class="slds-backdrop slds-backdrop_open"></div>  
        </aura:if>           

    </div>
</aura:component>