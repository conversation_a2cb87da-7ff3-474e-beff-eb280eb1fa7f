/*
 * @Description: 
 * @Author: [lijinhuan]
 * @Date: 2023-08-14 22:10:56
 * @LastEditors: [lijinhuan]
 * @LastEditTime: 2023-08-17 11:59:57
 */
({
    handleLoad: function (component, event, helper) {
        console.log('URF_Contract__c: 123444');
        console.log('URF_Contract__c: ',event.getParams().recordUi.record.fields.URF_Contract__c.value);
        var rdn = event.getParams().recordUi.record.fields.RecordType_DeveloperName__c.value;
        // 追加限次跳转
        var urf = event.getParams().recordUi.record.fields.URF_Contract__c.value;
        var urf = event.getParams().recordUi.record.fields.URF_Contract__c.value;
        // zzm 20250120 追加上限合同跳转
        var limit = event.getParams().recordUi.record.fields.agree_Upper_limit__c.value;
        var oldLimit = event.getParams().recordUi.record.fields.oldLimit__c.value;
        console.log('location: ',window.location.origin);
        if(rdn == 'NewMaintenance_Quote'){
            if (urf == true) {
                // window.open("/apex/SelectAssetEstimateURF?id={!URLENCODE("+component.get('v.recordId')+")}", "_self")
                window.open(window.location.origin+"/lightning/n/lexSelectAssetEstimateURF#id="+component.get('v.recordId'), "_self");
            }else if(limit == true && urf == false && oldLimit==false) { //上限合同
                window.open(window.location.origin+"/lightning/n/lexSelectAssetEstimateLimit#id="+component.get('v.recordId'), "_self");
            }

            else{
                // window.open("/apex/SelectAssetEstimateVM?id={!URLENCODE("+component.get('v.recordId')+")}", "_self");
                window.open(window.location.origin+"/lightning/n/lexSelectAssetEstimateVM#id="+component.get('v.recordId'), "_self");
            }
            
        }else{
            window.open("/apex/SelectAssetEstimate?id="+component.get('v.recordId'), "_self");
            // window.open("/lightning/n/lexSelectAssetEstimate#id="+component.get('v.recordId'), "_self");
        }
    }
})