@isTest
private class UpdateThreeMonthlyContactBatchTest {

    @isTest static void test_method_one() {
        // Implement test code
        Profile prof = [select Id from Profile where id = :System.Label.ProfileId_SystemAdmin];
        List<RecordType> rectHp = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];

        List<String> classDeptStr = new List<String>();
        classDeptStr.add('Department_Class_GI');
        classDeptStr.add('Department_Class_ET');
        classDeptStr.add('Department_Class_BF');
        classDeptStr.add('Department_Class_GS');
        classDeptStr.add('Department_Class_URO');
        classDeptStr.add('Department_Class_GYN');
        classDeptStr.add('Department_Class_ENT');
        classDeptStr.add('Department_Class_OTH');
        List<RecordType> rectDept = [select Id, developername from RecordType where IsActive = true and SobjectType = 'Account' and developername  in:classDeptStr];

        Map<String, String> classDeptMap = new Map<String, String>();
        if (rectHp.size() == 0) {
            return;
        }
        for (String Str : classDeptStr) {
            for (RecordType rt : rectDept) {
                if (rt.developername == Str) {
                    classDeptMap.put(Str, rt.Id);
                }
            }
        }
        User hpOwner = new User(Test_staff__c = true, LastName = 'hp', FirstName = 'owner', Alias = 'hp', CommunityNickname = 'hpOwner', Email = '<EMAIL>', Username = '<EMAIL>', IsActive = true, EmailEncodingKey = 'ISO-2022-JP', TimeZoneSidKey = 'Asia/Tokyo', LocaleSidKey = 'ja_JP', LanguageLocaleKey = 'ja', ProfileId = prof.id);
        insert hpOwner;
        Campaign cam = new Campaign();
        cam.Name = 'cam';
        cam.Name2__c = '1234';
        cam.StartDate = Date.today().addDays(15);
        cam.EndDate = Date.today().addDays(18);
        insert cam;
        Account myAccount1 = new Account(name = 'testaccount001', RecordTypeId = rectHp[0].Id, OwnerId = hpOwner.Id );
        insert myAccount1;
        Contact core = new Contact(email = '<EMAIL>',
                                   firstname = 'Joe',
                                   lastname = 'Plumber',
                                   accountid = myAccount1.id ,
                                   OnJobState__c = '在岗',
                                   Follow_state__c = 'perfect',
                                   ProcessingWork__c = 'study',
                                   teachmonth__c = 2,
                                   Campaign__c = cam.id,
                                   JobStatusUpdateDate__c = Date.today()
                                  );
        insert core;
        Database.executebatch(new UpdateThreeMonthlyContactBatch(), 50);
        list<string> contactidlist = new list<string>();
        contactidlist.add(core.id);
        Database.executebatch(new UpdateThreeMonthlyContactBatch(contactidlist), 50);
        Database.executebatch(new UpdateThreeMonthlyContactBatch(core.id), 50);
    }

}