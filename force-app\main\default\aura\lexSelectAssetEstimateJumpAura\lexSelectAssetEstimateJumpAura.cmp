<!--
 * @Description: 
 * @Author: [lijinh<PERSON>]
 * @Date: 2023-08-14 22:10:56
 * @LastEditors: [lijinhuan]
 * @LastEditTime: 2023-08-17 13:13:34
-->
<!--
  2023/04/24  维修合同（合同报价）编辑页面跳转
-->
<!-- <aura:component implements="flexipage:availableForRecordHome,force:lightningQuickActionWithoutHeader,force:hasRecordId,lightning:actionOverride,lightning:isUrlAddressable" access="global"> -->
<aura:component
    implements="lightning:actionOverride,lightning:isUrlAddressable,force:lightningQuickAction,force:hasRecordId"
    access="global">
    <aura:attribute name="recordId" type="String"/>
    <div style="display: none;">
        <lightning:recordEditForm recordId="{!v.recordId}" objectApiName="Maintenance_Contract_Estimate__c" onload="{!c.handleLoad}">
            <lightning:outputField fieldName="RecordType_DeveloperName__c" />
            <lightning:outputField fieldName="URF_Contract__c" />
            <!-- zzm 20250120 拆分上限合同 -->
            <lightning:outputField fieldName="agree_Upper_limit__c" /> 
            <lightning:outputField fieldName="oldLimit__c" /> 
        </lightning:recordEditForm>
    </div>  
</aura:component>