({
    helperMethod : function() {
    
    },
    // 设置table 各栏属性
    getColumnAndAction : function(cmp) {
        var actions = [
            {label: 'Edit', name: 'edit'},
            {label: 'Delete', name: 'delete'},
            {label: 'View', name: 'view'}
        ];
        // 为了锁行，强行设置了宽度
        cmp.set('v.columns', [
            // {label: 'CODE', fieldName: 'OTCode__c', type: 'text', wrapText:false 
            // , hideDefaultActions: true ,fixedWidth: 110  },
            {label: '产品型号', fieldName: 'Asset_Model_No__c', type: 'text' , wrapText:false
            , hideDefaultActions: true ,fixedWidth: 110 },
            {label: 'CODE', fieldName: 'OTCode__c', type: 'text', wrapText:false 
            , hideDefaultActions: true ,fixedWidth: 137  },
            {label: '产品名称', fieldName: 'productName__c', type: 'text', wrapText:false 
             , hideDefaultActions: true ,fixedWidth: 340  },
            {label: '数量', fieldName: 'Count_Text__c', type: 'text', wrapText:false 
            , hideDefaultActions: true ,fixedWidth: 50  },
            {label: '箱号', fieldName: 'CaseNumber__c', type: 'text', wrapText:false 
            , hideDefaultActions: true ,fixedWidth: 100  },
            {label: '货物情况', fieldName: 'HPGoodStatus__c', type: 'text', wrapText:true 
            , hideDefaultActions: true , editable: true,fixedWidth: 110  },
        ]);

        var width = cmp.get('v.floatWidth');
        var width1 = cmp.get('v.allWidth');
        console.log('allWidth:'+width);
        console.log('floatWidth:'+width1);
    },
    // 获取数据
    geteSign : function(cmp) {
        // this.showSpinner(cmp);
        var action = cmp.get("c.geteSigns");
        var DNName = cmp.get("v.DNName").toString();
        action.setParams({
            'DNName' : DNName,
            // 
        });
        console.log("已经进到这里了");
        action.setCallback(this,function(response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var resultData = response.getReturnValue();
                if(!!resultData && !!resultData.eSignFormLineItems
                    && !!resultData.eSignForm){
                    var data = resultData.eSignFormLineItems;
                    cmp.set('v.lineItemSize', data.length);
                    var pageSize = cmp.get('v.pageSize');
                    cmp.set("v.data", data);
                    var eSignForm = resultData.eSignForm;
                    //CHAN-BWCBL8 精琢技术 wql 20201225 start 
                    //如果驳回理由有值 并且经销商驳回复选框为true并且经销商审批状态不批准的时候才显示
                    if(eSignForm.HPDenyReason__c&&eSignForm.HPReject__c&&eSignForm.HPSignUpStatus__c=='不批准'){
                        cmp.set("v.ISDenyReason", true);
                    }
                    //CHAN-BWCBL8 精琢技术 wql 20201225 end 
                    cmp.set("v.eSignForm", resultData.eSignForm);
                    //判断签收页面上的已提交的勾要不要显示
                    // var isAgencyOrHp = resultData.eSignForm.agencySignUpDate__c;
                    // if(isAgencyOrHp){
                    //     cmp.set("v.isAgencyOrHp", false);
                    // }else{
                    //     cmp.set("v.isAgencyOrHp", true);
                    // }
                    var tempData = this.getTempData(data, 1,pageSize);
                    cmp.set("v.isLastPage", this.isLastPage(data,tempData));
                    cmp.set("v.dataSize", tempData.length);
                    cmp.set("v.currentData", tempData);
                    
                    this.hideSpinner(cmp);
                }else{
                    cmp.set("v.errorMessage", '加载失败，请重新打开此页面！');
                    this.showErrorToast(cmp);
                    this.hideSpinner(cmp);
                }
                
            }else{
                cmp.set("v.errorMessage", '加载失败，请重新打开此页面！');
                this.showErrorToast(cmp);
                this.hideSpinner(cmp);
            }
           
        });
        $A.enqueueAction(action);
    },
    // 明细页 table 首页功能实现
    handleHome : function(cmp) {
        var pageSize = cmp.get('v.pageSize');
        var pageNumber =  cmp.get('v.pageNumber');
        // var data = cmp.get('v.data');
        var currentData = cmp.get('v.currentData');
        //获取输入的value值
        var key = document.getElementById('searchInput').value;
        var data = cmp.get('v.data');
        var tempDataList = [];
        var pageSize = cmp.get('v.pageSize');
        
        if(key){
            for(var i = 0; i<data.length; i++){
                //获取所有name为箱号的input 批量更新
                if(data[i].CaseNumber__c == key){
                    tempDataList.push(data[i]);
                }
            
            }
            //设置分页
            var tempData = this.getTempData(tempDataList,1,pageSize );
            cmp.set('v.pageNumber', 1 );
            
            cmp.set('v.currentData',tempData);
            cmp.set('v.data',data);
            cmp.set("v.isLastPage", this.isLastPage(tempDataList,tempData));
            cmp.set("v.searchSize",tempDataList.length);


        }else{
            //显示全部箱
            var tempData = this.getTempData(data,1,pageSize );
            cmp.set('v.pageNumber', 1 );
            
            cmp.set('v.currentData',tempData);
            cmp.set('v.data',data);
            cmp.set("v.isLastPage", this.isLastPage(data,tempData));
            cmp.set('v.searchSize', null);

        }


    },
    // 明细页 table 尾页功能实现
    handleLast : function(cmp) {
        var pageSize = cmp.get('v.pageSize');
        var pageNumber =  cmp.get('v.pageNumber');
        var data = cmp.get('v.data');
        //获取数据长度
        var size = data.length;
        //获取尾页页码
        var pages=size%pageSize==0?(size/pageSize):(Math.floor(size/pageSize)+1);

        var currentData = cmp.get('v.currentData');
        //获取输入的value值
        var key = document.getElementById('searchInput').value;
        var tempDataList = [];

        if(key){
            for(var i = 0; i<data.length; i++){
                //获取所有name为箱号的input 批量更新
                if(data[i].CaseNumber__c == key){
                    tempDataList.push(data[i]);
                }
            
            }
            //设置分页
            var tempData = this.getTempData(tempDataList,pages,pageSize );
            cmp.set('v.pageNumber', pages );
            console.log('currentData1:'+currentData);
            cmp.set('v.currentData',tempData);
            cmp.set('v.data',data);
            cmp.set("v.isLastPage", this.isLastPage(tempDataList,tempData));
            cmp.set("v.searchSize",tempDataList.length);


        }else{
            //显示全部箱
            var tempData = this.getTempData(data,pages,pageSize );
            cmp.set('v.pageNumber', pages );
            console.log('currentData1:'+currentData);
            cmp.set('v.currentData',tempData);
            cmp.set('v.data',data);
            cmp.set("v.isLastPage", this.isLastPage(data,tempData));
            cmp.set('v.searchSize', null);

        }


    },
    // 明细页 table 下一页功能实现
    handleNext : function(cmp) {
        var pageSize = cmp.get('v.pageSize');
        var pageNumber =  cmp.get('v.pageNumber');
        var data = cmp.get('v.data');
        var currentData = cmp.get('v.currentData');
        //获取输入的value值
        var key = document.getElementById('searchInput').value;
        var tempDataList = [];

        if(key){
            for(var i = 0; i<data.length; i++){
                //获取所有name为箱号的input 批量更新
                if(data[i].CaseNumber__c == key){
                    tempDataList.push(data[i]);
                }
            
            }
            //设置分页
            var tempData = this.getTempData(tempDataList,pageNumber + 1,pageSize );
            cmp.set('v.pageNumber', pageNumber+1 );
            console.log('currentData1:'+currentData);
            cmp.set('v.currentData',tempData);
            cmp.set('v.data',data);
            cmp.set("v.isLastPage", this.isLastPage(tempDataList,tempData));
            cmp.set("v.searchSize",tempDataList.length);


        }else{
            //显示全部箱
            var tempData = this.getTempData(data,pageNumber + 1,pageSize );
            cmp.set('v.pageNumber', pageNumber+1 );
            console.log('currentData1:'+currentData);
            cmp.set('v.currentData',tempData);
            cmp.set('v.data',data);
            cmp.set("v.isLastPage", this.isLastPage(data,tempData));
            cmp.set('v.searchSize', null);

        }


    },
    // 明细页 table 上一页功能实现
    handlePrev : function(cmp) {
        var pageSize = cmp.get('v.pageSize');
        var pageNumber =  cmp.get('v.pageNumber');
        var data = cmp.get('v.data');
        var currentData = cmp.get('v.currentData');
        //获取输入的value值
        var key = document.getElementById('searchInput').value;
        var tempDataList = [];

        if(key){
            for(var i = 0; i<data.length; i++){
                //获取所有name为箱号的input 批量更新
                if(data[i].CaseNumber__c == key){
                    tempDataList.push(data[i]);
                }
            
            }
            //设置分页
            var tempData = this.getTempData(tempDataList,pageNumber - 1,pageSize );
            
            cmp.set('v.pageNumber', pageNumber - 1 );
            cmp.set('v.currentData',tempData);
            cmp.set('v.data',data);
            cmp.set("v.isLastPage", this.isLastPage(tempDataList,tempData));
            cmp.set("v.searchSize",tempDataList.length);


        }else{
            //显示全部箱
            var tempData = this.getTempData(data,pageNumber - 1,pageSize );
            
            cmp.set('v.pageNumber', pageNumber - 1 );
            cmp.set('v.currentData',tempData);
            cmp.set('v.data',data);
            cmp.set("v.isLastPage", this.isLastPage(data,tempData));
            cmp.set('v.searchSize', null);

        }
    },
    // 计算并返回当前页码的数据
    getTempData: function(data, pageNumber,pageSize){
        var tempData = data.slice((pageNumber-1) * pageSize,pageNumber * pageSize );
        return tempData;
    },
    // 判断当前页是否是最后一页
    isLastPage : function(data,tempData){
        if(tempData.length == 0 ||
            tempData[tempData.length-1].Id == data[data.length-1].Id ){
            return true;
        } else{
                return false;
        }
    },
    // 保存更改内容到当前明细内容
    saveEdition: function (cmp, draftValues) {
        if(!!draftValues && draftValues.length > 0){
            var currentData = cmp.get('v.currentData');
            for(var i = 0; i<currentData.length; i++){
                for(var j = 0; j<draftValues.length; j++){
                    if(currentData[i].Id == draftValues[j].Id){
                        currentData[i].HPGoodStatus__c = draftValues[j].HPGoodStatus__c;
                        break;
                    }
                }
            }
            cmp.set('v.currentData',currentData);
        }
        cmp.set('v.draftValues', []);
    },
    // 判断是否部分签收
    IsWhole: function (cmp) {
        var currentData = cmp.get('v.currentData');
        if(currentData){

                for(var j = 0; j<currentData.length; j++){

                        if(currentData[j].HPGoodStatus__c != '完好'){
                            //如果有明细不完好的置成true
                            cmp.set('v.IsWhole',true);
                            break;
                        }
                }

        }
    },
    // 明细页点击下一步保存录入表数据到数据库，
    // 如果保存成功，跳转文件上传页
    // 如果保存失败，弹出错误toast，保存在当页
    saveeSign : function (cmp){
        var action = cmp.get("c.saveeSignFormEntry");
        var data = cmp.get('v.data');
        var eSignForm = cmp.get('v.eSignForm');
        var IsSubmit = cmp.get('v.IsAgencyShow');
        var IsHPSubmit = cmp.get('v.IsHPShow');
        //增加扫描标识，batch取消驳回勾选 2020/12/30 wql start
        var IsHPScan = cmp.get('v.IsHPScan'); 
        //增加扫描标识，batch取消驳回勾选 2020/12/30 wql end

        var agencyScanDayBack = cmp.get('v.agencyScanDayBack');
        var agencySignUpDateBack = cmp.get('v.agencySignUpDateBack');
        var agencyConfirmDateBack = cmp.get('v.agencyConfirmDateBack');
        var HPWorkflowEmailBack = cmp.get('v.HPWorkflowEmailBack');
        console.log('HPWorkflowEmailBack:'+HPWorkflowEmailBack);
        action.setParams({
            'eSignFormLineItems' : data,
            'eSignForm' : eSignForm,
            'IsSubmit' : IsSubmit,
            'IsHPSubmit':IsHPSubmit,
            'IsHPScan':IsHPScan,
            'entryType' : '医院收货',
            'agencyScanDayBack':agencyScanDayBack,
            'agencySignUpDateBack':agencySignUpDateBack,
            'agencyConfirmDateBack':agencyConfirmDateBack,
            'HPWorkflowEmailBack':HPWorkflowEmailBack
        });
        this.showSpinner(cmp);
        action.setCallback(this,function(response) {
            this.hideSpinner(cmp);
            var state = response.getState();
            if (state === "SUCCESS") {
                var resultData = response.getReturnValue();
                if( resultData.isSuccess == true ){
                    cmp.set('v.recordId',resultData.result);
                    cmp.set('v.parentId',resultData.result);
                    alert('提交成功！');
                    if(IsHPSubmit){
                        //返回首页带值start
                        cmp.set('v.agencyDNSignUpStatus',resultData.agencyDNSignUpStatus); 
                        cmp.set('v.HPDNSignUpStatus',resultData.HPDNSignUpStatus);
                        cmp.set('v.agencySubmit',resultData.agencySubmit);
                        cmp.set('v.DNNameSpare',resultData.DNNameSpare);

                        cmp.set('v.IsHPShow',false);
                     }
                    //2020/12/02 提交成功后显示退回首页按钮 start
                    cmp.set("v.submitAfterShow", true);
                    const icon = document.getElementById('submitAgency');
                    icon.style.textAlign = 'center';
                    //2020/12/02 提交成功后显示退回首页按钮 end
                    var spanList = document.getElementById("uploadFileDiv").getElementsByTagName("span");
                    if(spanList.size()>0){
                        spanList[2].innerText = '上传附件';
                    }
                }
                else{
                    cmp.set("v.errorMessage", resultData.result);
                    this.showErrorToast(cmp);
                }
            }else{
                cmp.set("v.errorMessage", '保存失败，请重新加载！');
                this.showErrorToast(cmp);
            }
            
        });
        $A.enqueueAction(action);

    },
    hospitalDetailsPageNextClick : function(cmp) {
        //存一下检索框里的值 不然会被清掉 
        cmp.set('v.searchCase',cmp.get('v.searchCaseKey'));  
        console.log('searchCaseKey:'+cmp.get('v.searchCaseKey'));
    	var check = cmp.get("v.check");
    	if(check){
	    		if(confirm('请再次确认DN号/收货单位与随货同行单一致。')){
                //隐藏列
                cmp.set('v.hideCheckboxColumn',true);
	            cmp.set('v.HomePageDisplay',false);
	            cmp.set('v.NextPageDisplay',true);
	        }
    	}else{
    		// if(confirm('请勾选上述内容已阅读。')){
                
      //       }
            alert('请勾选上述内容已阅读。');
            return;
    	}
        
    },
    // 明细也跳转至文件上传页逻辑，先提示是否填写完毕
    handleDetailsPageNextClick : function(cmp) {
        //存一下检索框里的值 不然会被清掉
        var key = document.getElementById('searchInput').value;  
        cmp.set('v.searchCaseKey',key);  
        var AgencyClick = cmp.get('v.AgencyClick');
        if(AgencyClick){
                    //直接跳转明细页
                    cmp.set('v.NextPageDisplay',false);
                    cmp.set('v.uploadFilePage',true);
                    var spanList = document.getElementById("uploadFileDiv").getElementsByTagName("span");
                    if(spanList.size()>0){
                        spanList[2].innerText = '上传附件';
                    }
        }else{
                    if(confirm('请确认是否填写成货物情况？')){
                        //增加扫描标识，batch取消驳回勾选 2020/12/30 wql start
                        cmp.set('v.IsHPScan',true);
                        //增加扫描标识，batch取消驳回勾选 2020/12/30 wql end
                        this.saveeSign(cmp);
                        cmp.set('v.NextPageDisplay',false);
                    cmp.set('v.uploadFilePage',true);
                    }
             }
        
    },
    //返回首页
    handleShowPageNextClick : function(cmp) {
                //存一下检索框里的值 不然会被清掉
                var key = document.getElementById('searchInput').value;  
                cmp.set('v.searchCaseKey',key);  
                cmp.set('v.HomePageDisplay',true);
                cmp.set('v.NextPageDisplay',false);
    },
    //返回明细页
    handleShowFielePageNextClick : function(cmp) {
                //存一下检索框里的值 不然会被清掉 
                cmp.set('v.searchCase',cmp.get('v.searchCaseKey'));  
                console.log('searchCaseKey:'+cmp.get('v.searchCaseKey'));
                cmp.set('v.NextPageDisplay',true);
                cmp.set('v.uploadFilePage',false);
                //2020/12/02 点击上一步的时候隐藏退回按钮
                cmp.set("v.submitAfterShow", false);
    },
    //给提交按钮一个默认的值
    submitClick : function(cmp,event){
        var IsWhole = cmp.get('v.IsWhole');
        var IsHaveFile = cmp.get('v.IsHaveFile');
        var IsUpLoad = cmp.get('v.IsUpLoad');
        var AgencyClick = cmp.get('v.AgencyClick');
        //获取经销商邮箱
        var value = cmp.find("inputCmp").get("v.value");   
        console.log('email:'+value);

        //判断邮箱是否有效
        var validity = cmp.find("inputCmp").get("v.validity");
        console.log(validity.valid); 

        //①存在不完好的货物明细 为ture ②size<0说明没有上传文件
        //勾选
        
        if(!IsHaveFile){
            alert('请您上传照片后再点提交！');
            
        }else if(!IsUpLoad&&IsHaveFile){
            alert('请您点击上传文件按钮上传！');
        }else if(IsUpLoad &&!IsHaveFile){
            alert('您并没有选择文件!');
        }else{
            if(validity.valid){
                cmp.set('v.HPWorkflowEmailBack',value);
                //勾选
                if(confirm('请确认是否提交？')){
                    cmp.set('v.IsHPShow', true);
                    //扫描勾选
                    cmp.set('v.IsHPScan', true);
                    //设置一个flag用于避免提交两次
                    cmp.set('v.AgencyClick', true);
                    this.saveeSign(cmp);
                    this.BackToHomePage(cmp);
                    
                }
                
            }else{
                alert('请您填写正确的邮箱地址或者不填写！');

            }
            
        }
        
        
       
        // alert('已经保存好了！请您点击录入照片或者关闭此页面。');
    },
    //弹出 成功提示toast 方法
    showSuccessToast : function(cmp) {
        $A.util.removeClass(cmp.find('successDiv'), 'slds-hide');
        window.setTimeout($A.getCallback(function() {
            $A.util.addClass(cmp.find('successDiv'), 'slds-hide');
            }),
            5000
        );
    },
    //弹出 错误提示toast 方法
    showErrorToast : function(cmp) {
        const width = document.documentElement.clientWidth;
        const contentDOM1 = document.getElementById('errorSonDiv1');
        contentDOM1.style.width = width*0.6 + 'px';
        const contentDOM2 = document.getElementById('errorSonDiv2');
        contentDOM2.style.width = width*0.38 + 'px';
        contentDOM2.style.height = '4rem';
        const contentDOM3 =document.getElementById('errorSonDiv3');
        contentDOM3.style.width = width-150 + 'px';
        $A.util.removeClass(cmp.find('errorDiv'), 'slds-hide');
        window.setTimeout($A.getCallback(function() {
            $A.util.addClass(cmp.find('errorDiv'), 'slds-hide');
            }),
            5000
        );
    },
    // 展示 等待框
    showSpinner: function(cmp) {
        // remove slds-hide class from mySpinner
        var spinner = cmp.find("mySpinner");
        $A.util.removeClass(spinner, "slds-hide");
    },
    // 隐藏 等待框
    hideSpinner : function(cmp){
        // add slds-hide class from mySpinner
        var spinner = cmp.find("mySpinner");
        $A.util.addClass(spinner, "slds-hide");
    },
    //返回明细页
    handleShowFielePageNextClick : function(cmp) {

        cmp.set('v.NextPageDisplay',true);
        cmp.set('v.uploadFilePage',false);
        //设置成 默认 false
        cmp.set('v.IsWhole',false);
        cmp.set('v.IsHaveFile',false);
        //隐藏列
        cmp.set('v.hideCheckboxColumn',true);
    },
    GoodsChanged:function(cmp){
        var currentData = cmp.get('v.currentData');
            for(var i = 0; i<currentData.length; i++){
                //获取id
                // var id = currentData[i].Id;
                // //获取页面上的货物情况
                // var goodStatus = document.getElementById(id).value;
                // currentData[i].HPGoodStatus__c =goodStatus;
                // //设置打勾样式
                // var idx = id+i;
                //获取id
                var idx = currentData[i].Id+':'+i+':'+currentData[i].CaseNumber__c;
                //获取页面上的货物情况
                var goodStatus = document.getElementById(idx).value;
                currentData[i].HPGoodStatus__c =goodStatus;
                // document.getElementById(idx).style.display = '';
                // document.getElementById(id).style.border = 'hidden';
    
            }
            cmp.set('v.currentData',currentData);
    },
    //主页跳首页
    BackToHomePage : function(cmp, event, helper){
        if(confirm('确认回到首页吗？')){
                // var messageEvent = cmp.getEvent('componentEvent');
                // messageEvent.setParam('DNName',cmp.get('v.DNName'));
                // messageEvent.setParam('IsAgencyShow',cmp.get('v.IsAgencyShow'));
                
                // messageEvent.fire();
                cmp.set("v.HomePage", false);
                cmp.set("v.uploadFilePage", false);
                cmp.set("v.HomePageDisplay", true);
            }
        

        // helper.BackToHomePage(cmp);
    },
        //明细页 适用按钮 根据箱号划分数组data 
    //①当前页面js赋值
    //②后台data数据实际修改值
    BatchUpdateByCase : function(cmp, event){
        //获取按钮中属性name  ==  获取参数 箱号
        var caseNumber = event.target.name;
        //获取按钮中的属性 id  == 获取参数 id
        var idx = event.target.id;
        //拼接 货物情况的id
        var goodId = idx+caseNumber;
        //获取这一箱第一个的的货物情况
        var goodStatus = document.getElementById(goodId).value;

        var data = cmp.get('v.data');

            for(var i = 0; i<data.length; i++){
                //获取所有name为箱号的input 批量更新
                if(data[i].CaseNumber__c == caseNumber){
                    data[i].HPGoodStatus__c = goodStatus;
                }
    
            }
        //后台data变了 但页面不能及时变
        cmp.set('v.data',data);

        //获取分页的行项目 利用value赋值 及时变
        var currentData = cmp.get('v.currentData');
        for(var j = 0; j<currentData.length; j++){
                if(currentData[j].CaseNumber__c == caseNumber){
                    //拼接id
                    var id = currentData[j].Id+':'+j+':'+currentData[j].CaseNumber__c;
                    document.getElementById(id).value =currentData[j].HPGoodStatus__c ;
                    id = '';
                }else{
                    //避免影响其他其他列修改

                    //获取id
                    var idx = currentData[j].Id+':'+j+':'+currentData[j].CaseNumber__c;
                    //获取页面上的货物情况
                    var goodStatus = document.getElementById(idx).value;
                    currentData[j].HPGoodStatus__c =goodStatus;
                }
    
            }
        //最后更新后台data
        cmp.set('v.currentData',currentData);
        //当点击适用按钮以后，页面上显示全部适用按钮
        cmp.set('v.applyButton', false);
        cmp.set('v.allApplyButton', true);

    },
    //明细页 全部适用按钮 根据箱号划分数组data 
    //①当前页面js赋值
    //②后台data数据实际修改值
    allBatchUpdateByCase : function(cmp,event){
        //获取按钮中属性name  ==  获取参数 箱号
        var caseNumber = event.target.name;
        //获取按钮中的属性 id  == 获取参数 id
        var idx = event.target.id;
        //拼接 货物情况的id
        var goodId = idx+caseNumber;
        //获取这一箱第一个的的货物情况
        var goodStatus = document.getElementById(goodId).value;

        var data = cmp.get('v.data');

            for(var i = 0; i<data.length; i++){
                //全部更新成 上述value值的货物情况
                
                 data[i].HPGoodStatus__c = goodStatus;
                
    
            }
        //后台data变了 但页面不能及时变
        cmp.set('v.data',data);

        //获取分页的行项目 利用value赋值 及时变
        var currentData = cmp.get('v.currentData');
        for(var j = 0; j<currentData.length; j++){
                
            //拼接id
            var id = currentData[j].Id+':'+j+':'+currentData[j].CaseNumber__c;
            document.getElementById(id).value =currentData[j].HPGoodStatus__c ;
            id = '';
                
    
            }
        //最后更新后台data
        cmp.set('v.currentData',currentData);

    },
    //页面上检索功能
    searchByCaseNumber :function(cmp,event){
        //获取输入的value值
        var key = document.getElementById('searchInput').value;
        var data = cmp.get('v.data');
        //定义一个空数组 用于存放按箱分类后的data
        var tempDataList = [];

        var pageSize = cmp.get('v.pageSize');
        //判断搜索框内是否有值
        if(key){
            cmp.set('v.searchFlag',true);
            for(var i = 0; i<data.length; i++){
                //获取所有name为箱号的input 批量更新
                if(data[i].CaseNumber__c == key){
                    tempDataList.push(data[i]);
                }
            
            }
            //设置分页
            var tempData = this.getTempData(tempDataList, 1,pageSize);
            cmp.set("v.isLastPage", this.isLastPage(tempDataList,tempData));
            cmp.set("v.dataSize", tempData.length);
            cmp.set("v.currentData", tempData);
            cmp.set('v.pageNumber', 1);
            cmp.set("v.searchSize",tempDataList.length);

        }else{
            cmp.set('v.searchFlag',false);
            //显示全部箱
            var data = cmp.get('v.data');
            var tempData = this.getTempData(data, 1,pageSize);
            cmp.set("v.isLastPage", this.isLastPage(data,tempData));
            cmp.set("v.dataSize", tempData.length);
            cmp.set("v.currentData", tempData);
            cmp.set('v.pageNumber', 1);
            cmp.set('v.searchSize', null);

        }

            
    },
    MAX_FILE_SIZE: 4608000, //Max file size 4.5 MB
    CHUNK_SIZE: 750000, //Chunk Max size 750Kb
    uploadHelper: function(component, event) {
        // start/show the loading spinner
        component.set("v.showLoadingSpinner", true);
        // get the selected files using aura:id [return array of files]
        var fileInput = component.find("fileId").get("v.files");
        // get the first file using array index[0]
        var file = fileInput[0];
        //var test = this.compress(file);
        var self = this;
        var pdf =file.type;
        //增加可以上传的图片类型  精琢技术 thh 2021-09-13 
        if(pdf =='image/jpeg' || pdf =='image/png'){
            // create a FileReader object
            var objFileReader = new FileReader();
            // set onload function of FileReader object
            objFileReader.onload = $A.getCallback(function() {
                var image = new Image();
                image.src = objFileReader.result;
                var fileContents = objFileReader.result;
                image.onload = function() {
                    var maxSize;
                    var imgWidth ;
                    var imgHeight ;
                    //图片小于300k则不压缩
                    if(file.size >0 && file.size <307200){
                        imgWidth  = this.width;
                        imgHeight = this.height;
                    }else{
                        if(Math.sqrt(this.width*this.height)/Math.sqrt(2) >1200){
                            maxSize = 1200;
                        } else {
                            maxSize =Math.sqrt(this.width*this.height)/Math.sqrt(2);
                        }
                        var imgScale = self.imgScaleW(maxSize,this.width,this.height);
                        imgWidth = imgScale.width,
                        imgHeight = imgScale.height;
                    }

                    var canvas = document.createElement("canvas");
                    var ctx = canvas.getContext("2d");

                    canvas.width = imgWidth;
                    canvas.height = imgHeight;

                    ctx.drawImage(this, 0, 0, imgWidth, imgHeight);
                    //增加可以上传的图片类型  精琢技术 thh 2021-09-13 start
                    if(pdf =='image/jpeg' ){
                        fileContents = canvas.toDataURL("image/jpeg", 0.9);
                    }else if(pdf =='image/png'){
                        fileContents = canvas.toDataURL("image/png", 0.9);
                    }
                    //增加可以上传的图片类型  精琢技术 thh 2021-09-13 end
                    var base64 = "base64,";
                    var dataStart = fileContents.indexOf(base64) + base64.length;

                    fileContents = fileContents.substring(dataStart);
                    // call the uploadProcess method
                    self.uploadProcess(component, file, fileContents);      
                };
            });
        }else{
            //event
            var objFileReader = new FileReader();
            // set onload function of FileReader object
            objFileReader.onload = $A.getCallback(function() {

            var fileContents = objFileReader.result;
            var base64 = "base64,";
            var dataStart = fileContents.indexOf(base64) + base64.length;

                if (fileContents.length > 4608000) {
                    component.set("v.showLoadingSpinner", false);
                    alert("提醒，文件压缩大小不能超过4.5M,请重新上传。");
                    return;
                } else{
                    fileContents = fileContents.substring(dataStart);
                    // call the uploadProcess method
                    self.uploadProcess(component, file, fileContents);
                }
            });
        }
        objFileReader.readAsDataURL(file);
    },

  uploadProcess: function(component, file, fileContents) {
    // set a default size or startpostiton as 0
    var startPosition = 0;
    // calculate the end size or endPostion using Math.min() function which is return the min. value
    var endPosition = Math.min(
      fileContents.length,
      startPosition + this.CHUNK_SIZE
    );
    // start with the initial chunk, and set the attachId(last parameter)is null in begin
    this.uploadInChunk(
      component,
      file,
      fileContents,
      startPosition,
      endPosition,
      ""
    );
  },

  uploadInChunk: function(
    component,
    file,
    fileContents,
    startPosition,
    endPosition,
    attachId
  ) {
    // call the apex method 'saveChunk'
    var getchunk = fileContents.substring(startPosition, endPosition);
    //增加文件列表删除功能  精琢技术 thh 2021-08-31 start
    //把压缩完的src和文件名存入map
    var filesrc = component.get("v.FileSrc");
    var emptyflag = true;
    for(var key in filesrc) {
        var emptyflag = false;
    }
    if(emptyflag){
        filesrc[file.name] = getchunk;
    } else {
        for(var key in filesrc){
            if(key == file.name){
               filesrc[file.name] = filesrc[file.name] + getchunk;
            }else {
                filesrc[file.name] = getchunk;
                console.log('filesrc：'+filesrc);
            }
        }
    }
    component.set('v.FileSrc', filesrc);
    //增加文件列表删除功能  精琢技术 thh 2021-09-01 end
    var baseFile = component.get("v.baseFile");
    var base64List = component.get("v.base64List");
    //用于判断上传同一个文件提示
    var messageFlag = component.get("v.messageFlag");
    //判断是否出现过网络不好，上传失败的情况
    var errorFlag = component.get("v.errorFlag");
    if (!component.get("v.parentId")) {
        alert('parentID获取失败，请重新扫码');
        component.set("v.showLoadingSpinner", false);
    }
    else if(!base64List){
        if(baseFile != getchunk){
            //用于判断上传同一个文件提示
            component.set("v.messageFlag",false);
            messageFlag = component.get("v.messageFlag");
            var action = component.get("c.saveChunk");
            action.setParams({
              parentId: component.get("v.parentId"),
              fileName: file.name,
              base64Data: encodeURIComponent(getchunk),
              contentType: file.type,
              fileId: attachId
            });
            // set call back
            action.setCallback(this, function(response) {
              // store the response / Attachment Id
              attachId = response.getReturnValue();
              //增加文件列表删除功能  精琢技术 thh 2021-09-02 start
              //关联附件ID和附件名
              var attachmentID = component.get("v.attachmentID");
              attachmentID[file.name] = attachId;
              component.set("v.attachmentID", attachmentID);
              //增加文件列表删除功能  精琢技术 thh 2021-09-02 end
              var state = response.getState();
              if (state === "SUCCESS") {
                // update the start position with end postion
                startPosition = endPosition;
                endPosition = Math.min(
                  fileContents.length,
                  startPosition + this.CHUNK_SIZE
                );
                // check if the start postion is still less then end postion
                // then call again 'uploadInChunk' method ,
                // else, diaply alert msg and hide the loading spinner
                if (startPosition < endPosition) {
                  this.uploadInChunk(
                    component,
                    file,
                    fileContents,
                    startPosition,
                    endPosition,
                    attachId
                  );
                } else {
                  //判断上传文件内容 提示一次信息
                  var base = component.get("v.getchunk");
                  if(!base || (base&&base!=getchunk)){
                    // alert("您已经成功上传文件。");
                    component.set("v.getchunk", getchunk);
                  }
                  
                  alert("您已经成功上传文件。\n请点击提交后退出。");
                  //用于提交按钮判断是否有已上传的文件
                  component.set('v.IsUpLoad',true);
                  //用于判断上传同一个文件提示
                  component.set("v.messageFlag",true);
                  messageFlag =component.get("v.messageFlag");
                  component.set("v.showLoadingSpinner", false);
                  //标识预览图片名字  精琢技术 thh 2021-09-15 start
                  var isUploadName = component.get("v.isUploadName");
                  var fileName = component.find("fileId").get("v.files")[0]['name'];
                  if(isUploadName[fileName]) {
                      var str = '<span>当前文件：</span><span id="nowfile" class="field-title" style="color:Green;" title="' + fileName + '">' + fileName + '</span>';
                      document.getElementById("uploadicon").style.display = 'inline-block';
                    } else{
                      var str = '<span>当前文件：</span><span id="nowfile" class="field-title" style="color:Gray;" title="' + fileName + '">' + fileName + '</span>';
                      document.getElementById("uploadicon").style.display = 'none';
                  }
                  var obj = document.getElementById('filenow');
                  obj.innerHTML = str;
                  //标识预览图片名字  精琢技术 thh 2021-09-15 end
                  //上传成功后文件列表的文件名变色  精琢技术 thh 2021-09-26 start
                  var count = component.get("v.fileUpLoadSize");
                  var fileName = component.get("v.flUpLoadName");
                  if (count > 0) {
                    //列出文件名01 所有文件名都用逗号隔开 根据逗号拆分，存放数组
                    var nameList = fileName.split(',');
                    for (var i = 0; i < nameList.length; i++) {
                        for (var j = i + 1; j < nameList.length; j++) {
                            if (nameList[i] == nameList[j]) { //第一个等同于第二个，splice方法删除第二个
                                nameList.splice(j, 1);
                                j--;
                            }
                        }
                    }
                    //存放到变量中 用于上一页下一页连点
                    component.set('v.nameUpLoadList', nameList);
                    //重新定义选中文件的数量
                    component.set('v.fileSize', nameList.length);
                    console.log("nameList:" + component.get('v.nameUpLoadList')); 
                    var isUploadName = component.get("v.isUploadName");
                    if (nameList.length > 0) {
                        var strList = '';
                        for (var i = 0; i < nameList.length; i++) {
                            var num = Number(i) + 1;
                            var id = 'file0' + num;
                            //标识文件是否已经上传  精琢技术 thh 2021-09-26 start
                            if(isUploadName[nameList[i]]) {
                                var str = '<button id="' + id + '" class="slds-button" title="Field 3" style="text-align:left;width:250px;font-weight:bold;font-size: 15px;color:Green;" name="' + nameList[i] + '">' + nameList[i] + '</button><br>';
                            } else{
                                var str = '<button id="' + id + '" class="slds-button" title="Field 3" style="text-align:left;width:250px;font-weight:bold;font-size: 15px;color:Gray;" name="' + nameList[i] + '">' + nameList[i] + '</button><br>';
                            }            
                            //标识文件是否已经上传  精琢技术 thh 2021-09-26 end
                            //拼接p标签 列出文件名id不同
                            strList += str;
                        }
                        var obj = document.getElementById('file00');
                        obj.innerHTML = strList;
                    }
                  }
                  //上传成功后文件列表的文件名变色  精琢技术 thh 2021-09-26 end
                }
                // handel the response errors
              } else if (state === "INCOMPLETE") {
                alert("From server: " + response.getReturnValue());
                component.set("v.showLoadingSpinner", false);
                component.set("v.errorFlag", true);

              } else if (state === "ERROR") {
                var errors = response.getError();
                component.set("v.showLoadingSpinner", false);
                component.set("v.errorFlag", true);
                if (errors) {
                  if (errors[0] && errors[0].message) {
                    console.log("Error message: " + errors[0].message);
                  }
                } else {
                  console.log("Unknown error");
                }
              }
            });
            // enqueue the action
            $A.enqueueAction(action);

        }

    }
    else if(!base64List.includes(getchunk) || (base64List.includes(getchunk)&&errorFlag)){
    //网络波动进来以后 置成false
    component.set("v.errorFlag", false);
    //用于判断上传同一个文件提示
    component.set("v.messageFlag",false);
    messageFlag = component.get("v.messageFlag");
    var action = component.get("c.saveChunk");
    action.setParams({
      parentId: component.get("v.parentId"),
      fileName: file.name,
      base64Data: encodeURIComponent(getchunk),
      contentType: file.type,
      fileId: attachId
    });

    // set call back
    action.setCallback(this, function(response) {
      // store the response / Attachment Id
      attachId = response.getReturnValue();
      //增加文件列表删除功能  精琢技术 thh 2021-09-02 start
      //关联附件ID和附件名
      var attachmentID = component.get("v.attachmentID");
      attachmentID[file.name] = attachId;
      component.set("v.attachmentID", attachmentID);
      //增加文件列表删除功能  精琢技术 thh 2021-09-02 end
      var state = response.getState();
      if (state === "SUCCESS") {
        // update the start position with end postion
        startPosition = endPosition;
        endPosition = Math.min(
          fileContents.length,
          startPosition + this.CHUNK_SIZE
        );
        // check if the start postion is still less then end postion
        // then call again 'uploadInChunk' method ,
        // else, diaply alert msg and hide the loading spinner
        if (startPosition < endPosition) {
          this.uploadInChunk(
            component,
            file,
            fileContents,
            startPosition,
            endPosition,
            attachId
          );
        } else {
          //判断上传文件内容 提示一次信息
          var base = component.get("v.getchunk");
          if(!base || (base&&base!=getchunk)){
            // alert("您已经成功上传文件。");
            component.set("v.getchunk",getchunk );
          }
          alert("您已经成功上传文件。\n请点击提交后退出。");
          //用于提交按钮判断是否有已上传的文件
          component.set('v.IsUpLoad',true);
          //用于判断上传同一个文件提示
          component.set("v.messageFlag",true);
          messageFlag =component.get("v.messageFlag");
          component.set("v.showLoadingSpinner", false);
          //标识预览图片名字  精琢技术 thh 2021-09-15 start
          var isUploadName = component.get("v.isUploadName");
          var fileName = component.find("fileId").get("v.files")[0]['name'];
          if(isUploadName[fileName]) {
              var str = '<span>当前文件：</span><span id="nowfile" class="field-title" style="color:Green;" title="' + fileName + '">' + fileName + '</span>';
              document.getElementById("uploadicon").style.display = 'inline-block';
            } else{
              var str = '<span>当前文件：</span><span id="nowfile" class="field-title" style="color:Gray;" title="' + fileName + '">' + fileName + '</span>';
              document.getElementById("uploadicon").style.display = 'none';
          }
          var obj = document.getElementById('filenow');
          obj.innerHTML = str;
          //标识预览图片名字  精琢技术 thh 2021-09-15 end
          //上传成功后文件列表的文件名变色  精琢技术 thh 2021-09-26 start
          var count = component.get("v.fileUpLoadSize");
          var fileName = component.get("v.flUpLoadName");
          if (count > 0) {
            //列出文件名01 所有文件名都用逗号隔开 根据逗号拆分，存放数组
            var nameList = fileName.split(',');
            for (var i = 0; i < nameList.length; i++) {
                for (var j = i + 1; j < nameList.length; j++) {
                    if (nameList[i] == nameList[j]) { //第一个等同于第二个，splice方法删除第二个
                        nameList.splice(j, 1);
                        j--;
                    }
                }
            }
            //存放到变量中 用于上一页下一页连点
            component.set('v.nameUpLoadList', nameList);
            //重新定义选中文件的数量
            component.set('v.fileSize', nameList.length);
            console.log("nameList:" + component.get('v.nameUpLoadList')); 
            var isUploadName = component.get("v.isUploadName");
            if (nameList.length > 0) {
                var strList = '';
                for (var i = 0; i < nameList.length; i++) {
                    var num = Number(i) + 1;
                    var id = 'file0' + num;
                    //标识文件是否已经上传  精琢技术 thh 2021-09-26 start
                    if(isUploadName[nameList[i]]) {
                        var str = '<button id="' + id + '" class="slds-button" title="Field 3" style="text-align:left;width:250px;font-weight:bold;font-size: 15px;color:Green;" name="' + nameList[i] + '">' + nameList[i] + '</button><br>';
                    } else{
                        var str = '<button id="' + id + '" class="slds-button" title="Field 3" style="text-align:left;width:250px;font-weight:bold;font-size: 15px;color:Gray;" name="' + nameList[i] + '">' + nameList[i] + '</button><br>';
                    }            
                    //标识文件是否已经上传  精琢技术 thh 2021-09-26 end
                    //拼接p标签 列出文件名id不同
                    strList += str;
                }
                var obj = document.getElementById('file00');
                obj.innerHTML = strList;
            }
          }
          //上传成功后文件列表的文件名变色  精琢技术 thh 2021-09-26 end
        }
        // handel the response errors
      } else if (state === "INCOMPLETE") {
        alert("From server: " + response.getReturnValue());
        component.set("v.showLoadingSpinner", false);
        component.set("v.errorFlag", true);
      } else if (state === "ERROR") {
        var errors = response.getError();
        component.set("v.showLoadingSpinner", false);
        component.set("v.errorFlag", true);
        if (errors) {
          if (errors[0] && errors[0].message) {
            console.log("Error message: " + errors[0].message);
          }
        } else {
          console.log("Unknown error");
        }
      }
    });
    // enqueue the action
    $A.enqueueAction(action);
    }else{
     if(messageFlag){
          alert("您已经上传过该文件了！")
          component.set("v.showLoadingSpinner", false);
      }

    }
    //存放上传过的文件base64 数组
    var baseFile = component.get("v.baseFile");
    var base64List = component.get("v.base64List");
    if(baseFile){
        component.set('v.baseFile',baseFile+','+getchunk);
        // nameList.pust(name);
    }else{
        component.set('v.baseFile',getchunk);
    }
    baseFile = component.get("v.baseFile");
    var base64List =baseFile.split(',');
    component.set("v.base64List",base64List);
    // component.set("v.showLoadingSpinner", false);
    // baseFile = getchunk;
    
  },
 /**
 * 图片压缩
 * @param maxWidth 最大宽度或最大高度
 * @param width 宽度
 * @param height 高度
 * @returns {___anonymous1968_1969}
 */
imgScaleW:function(maxWidth,width,height){
    var imgScale = {};
    var w = 0;
    var h = 0;
    if(width <= maxWidth && height <= maxWidth){ // 如果图片宽高都小于限制的最大值,不用缩放
        imgScale = {
                width:width,
                height:height
        };
    }else{
        if(width >= height){ // 如果图片宽大于高
            w = maxWidth;
            h = Math.ceil(maxWidth * height / width);
        }else{     // 如果图片高大于宽
            h = maxWidth;
            w = Math.ceil(maxWidth * width / height);
        }
        imgScale = {
                width:w,
                height:h
        };
    }
    return imgScale;
}, 
DrawImage1:function(maxWidth,width,height){
    var imgScale = {};
    var w = 0;
    var h = 0;
    if (width > 0 && height > 0) {
        if (width / height >= 1) {
            if (width > maxWidth) {
  
                imgScale = {
                        width:maxWidth,
                        height:(height * maxWidth) / width
                };
            } else {
                imgScale = {
                        width:width,
                        height:height
                };
            }
        } else {
            if (height > maxWidth) {
                imgScale = {
                        width:maxWidth,
                        height:(width * maxWidth) / height
                };
            } else {
                imgScale = {
                        width:width,
                        height:height
                };
            }
        }
    }
    
    return imgScale;
},
})