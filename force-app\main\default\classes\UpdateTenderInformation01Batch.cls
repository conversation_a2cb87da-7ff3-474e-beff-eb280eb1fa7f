/*2022-04-29  ljh
* 更新招标信息的所有人（有询价时）
*/
global class UpdateTenderInformation01Batch implements Database.Batchable<sObject> {
    public String query;

    global UpdateTenderInformation01Batch() {
        this.query = query;
    }

    global Database.QueryLocator start(Database.BatchableContext bc) {
        query = 'select Opportunity__c,Tender_information__c from Tender_Opportunity_Link__c where OwnerIsChange__c = true';
        return Database.getQueryLocator(query);
    }

    global void execute(Database.BatchableContext BC, list<Tender_Opportunity_Link__c> tolList) {
        //定义List封装需要空更新的招投标项目
        List<Tender_information__c> EmptyUpdateTenderList = new List<Tender_information__c>();
        Map<String,String> TenderIdMap = new Map<String,String>();
        for(Tender_Opportunity_Link__c tol:tolList){
            if(!TenderIdMap.containsKey(tol.Tender_information__c)){
                Tender_information__c temp = new Tender_information__c();
                temp.Id = tol.Tender_information__c;
                EmptyUpdateTenderList.add(temp);
            }
            TenderIdMap.put(tol.Tender_information__c,tol.Tender_information__c);
        }
        if(EmptyUpdateTenderList.size()>0){
            //空更新招投标
            StaticParameter.EscapeOtherUpdateTenOwner = false;
            Database.SaveResult[] EmptySaveTenderResult = Database.update(EmptyUpdateTenderList,false);
            StaticParameter.EscapeOtherUpdateTenOwner = true;
        }
    }
    global void finish(Database.BatchableContext BC) {
        Id execBTId = Database.executeBatch(new UpdateInquiryFormConfirmationBatch(), 100); 
        //询价流程改善 fy start
        Id execBTId2 = Database.executeBatch(new UpdateTenderInformationBatch2(),100);
        //询价流程改善 fy end
    }
     public static void justForTest() {
        Integer i = 0;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
    }
}