@isTest
private class UpdateAgencyOppProductTargetBatchTest {

    public static Account account1 = new Account();
    public static Account account2 = new Account();
    public static Contact contact1 = new Contact();
    public static Contact contact2 = new Contact();
    public static User user = new User();
    public static Agency_Hospital_Link__c agency_hospital_link = new Agency_Hospital_Link__c();
    public static Agency_Contact__c agency_contact = new Agency_Contact__c();
    public static ProductTypes__c product_types = new ProductTypes__c();
    public static ProductTypes__c product_types2 = new ProductTypes__c();
    public static ProductTypes__c product_types3 = new ProductTypes__c();
    public static OlympusCalendar__c olympus_calendar = new OlympusCalendar__c();
    
    public static String report_id;
    public static String report_header_id;

    private static void testInit() {
        // 取引先
        account1.Name = 'test1医院';
        account1.RecordTypeId = '01210000000QemG';
        insert account1;
        
        account2.Name = 'test1经销商';
        account2.RecordTypeId = '01210000000Qem1';
        insert account2;
        
        // 取引先責任者
        contact1.AccountId = account1.Id;
        contact1.FirstName = '責任者';
        contact1.LastName = 'test1医院';
        insert contact1;
        
        contact2.AccountId = account2.Id;
        contact2.FirstName = '責任者';
        contact2.LastName = 'test1经销商';
        insert contact2;
        
        // ユーザー
        Profile p = [select Id from Profile where Name = '901_经销商周报'];
        user.ProfileId = p.Id;
        user.ContactId = contact2.Id;
        user.FirstName = 'ユーザー';
        user.LastName = 'テスト';
        user.Email = '<EMAIL>';
        user.emailencodingkey='UTF-8';
        user.languagelocalekey='zh_CN';
        user.localesidkey='ja_JP';
        user.timezonesidkey='Asia/Shanghai';
        user.Username = '<EMAIL>';
        user.Alias = 'テユ';
        user.CommunityNickname = 'テストユーザー';
        user.Batch_User__c = true;
        insert user;
        
        // 代理店医院
        agency_hospital_link.Name = 'test1代理店医院';
        agency_hospital_link.Hospital__c = account1.Id;
        agency_hospital_link.Agency__c = account2.Id;
        agency_hospital_link.OwnerId = user.Id;
        insert agency_hospital_link;
        
        // 先生
        agency_contact.Name = 'test1医院先生';
        agency_contact.Doctor_Division1__c = '院长';
        agency_contact.Type__c = '医生';
        agency_contact.Agency_Hospital__c = agency_hospital_link.Id;
        agency_contact.OwnerId = user.Id;
        insert agency_contact;
        
        // 製品区分
        product_types.Name = '製品区分1';
        product_types.Name2__c = '製品区分1';
        product_types.OwnerId = user.Id;
        product_types.Department_Cateogy__c = 'GI;ET;BF;GS;URO;GYN;ENT;OTH';
        product_types.DeleteFlg__c = false;
        insert product_types;

        product_types2.Name = '製品区分2';
        product_types2.Name2__c = '製品区分2';
        product_types2.OwnerId = user.Id;
        product_types2.Department_Cateogy__c = 'GI;ET;BF;GS;URO;GYN;ENT;OTH';
        product_types2.DeleteFlg__c = false;
        insert product_types2;

        product_types3.Name = '製品区分3';
        product_types3.Name2__c = '製品区分3';
        product_types3.OwnerId = user.Id;
        product_types3.Department_Cateogy__c = 'GI;ET;BF;GS;URO;GYN;ENT;OTH';
        product_types3.DeleteFlg__c = false;
        insert product_types3;

        // 引合
        Agency_Opportunity__c agency_opportunity = new Agency_Opportunity__c();
        agency_opportunity.RecordTypeId = [select Id,DeveloperName from RecordType where IsActive = true and SobjectType = 'Agency_Opportunity__c' and DeveloperName = 'Opportunity'].Id;
        agency_opportunity.Name = '引合1';
        agency_opportunity.Hospital_Target__c = account1.Id;
        agency_opportunity.Agency__c = account2.Id;
        agency_opportunity.Agency_Hospital__c = agency_hospital_link.Id;
        agency_opportunity.StageName__c = '还没申请预算';
        agency_opportunity.OwnerId = user.Id;
        agency_opportunity.Department_Cateogy__c = 'GI';
        agency_opportunity.Product_Category1__c = product_types2.Id;
        agency_opportunity.Product_Category2__c = product_types.Id;
        agency_opportunity.Close_Forecasted_Date__c = Date.today();
        insert agency_opportunity;

        agency_opportunity = new Agency_Opportunity__c();
        agency_opportunity.RecordTypeId = [select Id,DeveloperName from RecordType where IsActive = true and SobjectType = 'Agency_Opportunity__c' and DeveloperName = 'Opportunity'].Id;
        agency_opportunity.Name = '引合2';
        agency_opportunity.Hospital_Target__c = account1.Id;
        agency_opportunity.Agency__c = account2.Id;
        agency_opportunity.Agency_Hospital__c = agency_hospital_link.Id;
        agency_opportunity.StageName__c = '还没申请预算';
        agency_opportunity.OwnerId = user.Id;
        agency_opportunity.Department_Cateogy__c = 'GI';
        agency_opportunity.Product_Category1__c = product_types.Id;
        agency_opportunity.Product_Category2__c = product_types2.Id;
        agency_opportunity.Product_Category3__c = product_types3.Id;
        agency_opportunity.Close_Forecasted_Date__c = Date.today();
        insert agency_opportunity;

        agency_opportunity = new Agency_Opportunity__c();
        agency_opportunity.RecordTypeId = [select Id,DeveloperName from RecordType where IsActive = true and SobjectType = 'Agency_Opportunity__c' and DeveloperName = 'Opportunity'].Id;
        agency_opportunity.Name = '引合3';
        agency_opportunity.Hospital_Target__c = account1.Id;
        agency_opportunity.Agency__c = account2.Id;
        agency_opportunity.Agency_Hospital__c = agency_hospital_link.Id;
        agency_opportunity.StageName__c = '还没申请预算';
        agency_opportunity.OwnerId = user.Id;
        agency_opportunity.Department_Cateogy__c = 'GI';
        agency_opportunity.Product_Category1__c = product_types.Id;
        agency_opportunity.Close_Forecasted_Date__c = Date.today();
        insert agency_opportunity;
        
        // オリンパスカレンダー
        olympus_calendar.Date__c = Date.valueOf('2017-04-10');
        olympus_calendar.OwnerId = user.Id;
        insert olympus_calendar;
    }
    
    @isTest static void test_method_one() {
        testInit();
        System.runAs(user) {
            // 病院リスト取得
            CreateTargetCmp.createSFTarget(agency_hospital_link.Id, false, new String[]{product_types.Id,product_types2.Id,product_types3.Id}, new String[]{product_types2.Id,'',''}, 
                    new String[]{product_types3.Id,'',''}, new String[]{product_types.Id,'',''}, new String[]{product_types.Id,'',''}, new String[]{product_types.Id,'',''}, new String[]{product_types.Id,'',''}, new String[]{product_types.Id,'',''}, '150P');
        
            System.Test.StartTest();
            UpdateAgencyOppProductTargetBatch b1 = new UpdateAgencyOppProductTargetBatch();
            Database.executeBatch(b1, 100);
            System.Test.StopTest();
        }
        
        String rid = [select Id,DeveloperName from RecordType where IsActive = true and SobjectType = 'Agency_Opportunity__c' and DeveloperName = 'Target'].Id;
        Agency_Opportunity__c target = [select Id,Product_Category_Opp_Count__c from Agency_Opportunity__c where RecordTypeId =:rid and Department_Cateogy__c = 'GI' and Product_Category__c =:product_types.Id];
        System.assertEquals(3, target.Product_Category_Opp_Count__c);

        target = [select Id,Product_Category_Opp_Count__c from Agency_Opportunity__c where RecordTypeId =:rid and Department_Cateogy__c = 'OTH'];
        System.assertEquals(null, target.Product_Category_Opp_Count__c);

        target = [select Id,Product_Category_Opp_Count__c,Product_Category__c from Agency_Opportunity__c where RecordTypeId =:rid and Department_Cateogy__c = 'GI' and Product_Category__c =:product_types2.Id];
        System.assertEquals(2, target.Product_Category_Opp_Count__c);

        target = [select Id,Product_Category_Opp_Count__c,Product_Category__c from Agency_Opportunity__c where RecordTypeId =:rid and Department_Cateogy__c = 'GI' and Product_Category__c =:product_types3.Id];
        System.assertEquals(1, target.Product_Category_Opp_Count__c);

        
    }
    
}