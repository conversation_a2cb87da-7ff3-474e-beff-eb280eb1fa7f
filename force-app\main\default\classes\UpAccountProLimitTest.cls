@isTest
private class UpAccountProLimitTest
{
    @isTest
    static void proLimitTest()
    {
        List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '販売店'];
        if (rectCo.size() == 0) {
            return;
        }
        Profile prof = [select Id from Profile where Name ='901_经销商社区普通权限'];
        Product2 prod01 = new Product2(Name='Test01',ProductCode='Test01',Asset_Model_No__c = 'Test01',SFDA_Status__c = '有効',Dealer_special_Object__c = true);
        Product2 prod02 = new Product2(Name='Test02',ProductCode='Test02',Asset_Model_No__c = 'Test02',SFDA_Status__c = '有効',Dealer_special_Object__c = true);
        insert new Product2[]{prod01,prod02};
        prod01.SFDA_Status__c = '不要';
        prod02.SFDA_Status__c = '不要';
        update prod01;
        update prod02;
        // Product2__c pro1 = new Product2__c(Name='Pro001',OT_CODE_Text__c='Test001',Product2__c = prod01.Id);
        // Product2__c pro2 = new Product2__c(Name='Pro002',OT_CODE_Text__c='Test002',Product2__c = prod02.Id);
        // insert new Product2__c[] {pro1, pro2};
     
        Account myAccount2 = new Account(Name='Testaccount002',Dealer_discount__c =20,RecordTypeId = rectCo[0].Id,Product_Limit_Date__c = 'Test01|2|4');
        insert myAccount2;
        Contact core = new Contact(email='<EMAIL>', firstname='Joe',lastname='Plumber',accountid=myAccount2.id);
        insert core;
        user MyUser_Test = New User(ContactId = core.id,Alias = 'newUser',Email='<EMAIL>',EmailEncodingKey='UTF-8', LastName='TestUser', LanguageLocaleKey='zh_CN',LocaleSidKey='zh_CN', ProfileId = prof.Id,TimeZoneSidKey='Asia/Shanghai', UserName='<EMAIL>',UserPro_Type__c = 'ET');
        // System.runAs(new User(Id = UserInfo.getUserId())){
        //     insert MyUser_Test;
        // }
        // system.runAs(MyUser_Test){
            Product2__c pro1 = new Product2__c(Name='Pro001',OT_CODE_Text__c='Test001',Product2__c = prod01.Id);
            Product2__c pro2 = new Product2__c(Name='Pro002',OT_CODE_Text__c='Test002',Product2__c = prod02.Id);
            insert new Product2__c[] {pro1, pro2};
            String productLimit = 'Test01|2|4,Test02|3|5';
            String resultText = UpAccountProLimit.upAccount(myAccount2.Id,productLimit,'ET');
            List<Account> accountre = [select Id,Product_Limit_Date__c from Account where Id = :myAccount2.Id ];
            List<Product2__c> pro2List = [select Id,Estimation_Entry_Possibility__c from Product2__c];
            System.assertEquals(2, pro2List.size());
            System.assertEquals('○', pro2List[0].Estimation_Entry_Possibility__c);
            // System.assertEquals('Test01|2|4,Test02|3|5', accountre[0].Product_Limit_Date__c);
        // }

    }
}