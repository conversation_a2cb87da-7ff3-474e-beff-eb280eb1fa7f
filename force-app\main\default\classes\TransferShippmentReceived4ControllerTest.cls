@isTest
private class TransferShippmentReceived4ControllerTest {
    static void setupTestData1() {
        StaticParameter.EscapeNFM001AgencyContractTrigger = true;
        StaticParameter.EscapeNFM001Trigger = true;
        Oly_TriggerHandler.bypass(ContactTriggerHandler.class.getName());
        Oly_TriggerHandler.bypass(AgencyHospitalHandler.class.getName());
        Oly_TriggerHandler.bypass(AssetHandler.class.getName());
        Oly_TriggerHandler.bypass(TransferApplyDetailHandler.class.getName());
        ControllerUtil.EscapeNFM001Trigger = true;
        StaticParameter.EscapeAccountTrigger = true;

        // 创建省市数据
        Address_Level__c al = new Address_Level__c(
            Name = '北京',
            Level1_Code__c = 'CN-11',
            Level1_Sys_No__c = '110000'
        );
        insert al;

        Address_Level2__c al2 = new Address_Level2__c(
            Level1_Code__c = 'CN-11',
            Level1_Sys_No__c = '110000',
            Level1_Name__c = '北京',
            Name = '朝阳区',
            Level2_Code__c = 'CN-1101',
            Level2_Sys_No__c = '110100',
            Address_Level__c = al.id
        );
        insert al2;

        // 创建医院
        Account hospital = new Account(
            RecordTypeId = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' AND DeveloperName = 'HP' LIMIT 1].Id,
            Name = 'Test Hospital',
            Is_Active__c = '有效',
            Attribute_Type__c = '卫生部',
            Speciality_Type__c = '综合医院',
            Grade__c = '一级',
            OCM_Category__c = 'SLTV',
            Is_Medical__c = '医疗机构',
            State_Master__c = al.id,
            City_Master__c = al2.id,
            Town__c = '北京'
        );
        insert hospital;

        // 创建战略科室
        Account strategicDep = new Account(
            RecordTypeId = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' AND DeveloperName = 'Department_Class_OTH' LIMIT 1].Id,
            Name = 'Strategic Department',
            Department_Class_Label__c = '消化科',
            ParentId = hospital.Id
        );
        insert strategicDep;

        // 创建诊疗科室
        Account dep = new Account(
            RecordTypeId = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' AND DeveloperName = 'Department_GI' LIMIT 1].Id,
            Name = 'Test Department',
            ParentId = strategicDep.Id,
            Department_Class__c = strategicDep.Id,
            Hospital__c = hospital.Id
        );
        insert dep;

        // 创建联系人
        Contact contact = new Contact(
            LastName = '_サンブリッジ',
            AccountId = dep.Id
        );
        insert contact;

        // 创建产品
        Product2 pro1 = new Product2(
            Name = 'CLH-250:内窥镜冷光源',
            IsActive = true,
            Family = 'GI',
            Fixture_Model_No__c = 'CLH-250',
            Serial_Lot_No__c = 'S/N tracing',
            Fixture_Model_No_T__c = 'CLH-250',
            Asset_Model_No__c = 'Pro1',
            ProductCode_Ext__c = '4604362',
            Manual_Entry__c = false
        );
        insert pro1;

        // 创建资产
        Asset asset = new Asset(
            RecordTypeId = System.Label.Asset_RecordType,
            SerialNumber = 'T1',
            Name = 'CLH-250:内窥镜冷光源',
            AccountId = dep.Id,
            Department_Class__c = strategicDep.Id,
            Hospital__c = hospital.Id,
            Product2Id = pro1.Id,
            Quantity = 1,
            Status = '使用中',
            Manage_type__c = '个体管理',
            Loaner_accsessary__c = false,
            Out_of_wh__c = 0,
            Salesdepartment__c = '0.备品中心',
            Internal_asset_location__c = '北京 备品中心',
            Product_category__c = 'GI',
            Equipment_Type__c = '产品试用',
            SalesProvince__c = '北京',
            CompanyOfEquipment__c = '北京',
            Internal_Asset_number__c = '0001',
            WH_location__c = '货架号3',
            AssetManageConfirm__c = true,
            EquipmentSet_Managment_Code__c = '123',
            Asset_loaner_category__c = '固定资产'
        );
        insert asset;

        // 创建备品配套
        Fixture_Set__c fs = new Fixture_Set__c(
            Name = 'Test Set',
            Fixture_Set_Body_Model_No__c = 'CLH-250',
            Loaner_name__c = 'Test Loaner'
        );
        insert fs;

        // 创建备品配套明细
        Fixture_Set_Detail__c fsd = new Fixture_Set_Detail__c(
            Name = 'Test Detail',
            Product2__c = pro1.Id,
            Fixture_Set__c = fs.Id,
            Is_Body__c = true,
            Is_Optional__c = false,
            Quantity__c = 1,
            SortInt__c = 1,
            UniqueKey__c = fs.Id + ':' + pro1.Id
        );
        insert fsd;

        // 创建调拨申请
        TransferApply__c ta = new TransferApply__c(
            Name = 'TA-001',
            From_Location__c = '北京 备品中心',
            Destination_Location__c = '上海 备品中心',
            RecordTypeId = Schema.SObjectType.TransferApply__c.getRecordTypeInfosByDeveloperName().get('CenterToCenter').getRecordTypeId(),
            Status__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal()),
            Status_Text__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Yi_Pi_Zhun.ordinal()),
            Request_time__c = System.now(),
            Request_approval_time__c = System.now()
        );
        insert ta;

        // 创建调拨申请汇总
        TransferApplySummary__c tas = new TransferApplySummary__c(
            TransferApply__c = ta.Id,
            Fixture_Set__c = fs.Id,
            Cancel_Select__c = false,
            IndexFromUniqueKey__c = 1
        );
        insert tas;

        // 创建发货运输单
        FixtureDeliverySlip__c fds = new FixtureDeliverySlip__c(
            order_number__c = 'TA-001',
            DeliveryType__c = '回寄',
            DeliveryCompany__c = '顺丰',
            Distributor_method__c = '陆运',
            Wh_Staff__c = UserInfo.getUserId()
        );
        insert fds;

        // 创建调拨申请明细
        TransferApplyDetail__c tad = new TransferApplyDetail__c(
            TransferApply__c = ta.Id,
            Fixture_Set_Detail__c = fsd.Id,
            TransferApplySummary__c = tas.Id,
            Asset__c = asset.Id,
            IndexFromUniqueKey__c = 1,
            FSD_OneToOneAccessory_Cnt__c = 2,
            ApplyPersonAppended__c = false,
            TransferCount__c = 1,
            FSD_SortInt__c = 1,
            Shipment_request_time2__c = Date.today(),
            StockDown__c = true,
            StockDown_time__c = Date.today(),
            StockDown_staff__c = Userinfo.getUserId(),
            Shipment_Status_Text__c = FixtureUtil.raesdStatusMap.get(FixtureUtil.HistoryStatus.Yi_Xia_Jia.ordinal()),
            DeliverySlip__c = fds.Id,
            Shippment_loaner_time__c = System.now()
        );
        insert tad;
    }

    static void setupTestData2() {
        setupTestData1();
        TransferApply__c ta = [SELECT Id FROM TransferApply__c LIMIT 1];
        ta.RecordTypeId = Schema.SObjectType.TransferApply__c.getRecordTypeInfosByDeveloperName().get('CenterToAgency').getRecordTypeId();
        // ta.From_Location__c = '北京 备品中心';
        // ta.Destination_Location__c = '大连';
        update ta;

        // ta.Status__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Shen_Qing_Zhong.ordinal());
        // ta.Status_Text__c = FixtureUtil.raStatusMap.get(FixtureUtil.RaStatus.Shen_Qing_Zhong.ordinal());
        // update ta;

        List<TransferApplyDetail__c> erList = [SELECT Id FROM TransferApplyDetail__c WHERE TransferApply__c = :ta.Id];
        for(TransferApplyDetail__c tad : erList) {
            tad.Asset_return_time__c = System.now();
            tad.Return_DeliverySlip__c = UserInfo.getUserId();
        }
        update erList;
    }
    
    @isTest
    static void test_init_CenterToCenter() {
        Test.startTest();
        setupTestData1();
        
        List<TransferApply__c> taList = [SELECT Id,OwnerId,Status__c FROM TransferApply__c]; 
        List<TransferApplySummary__c> esList = [SELECT TransferApply__r.Loaner_centre_mail_address__c, TransferApply__r.Name, 
                                               TransferApply__c, Id, Name
                                               FROM TransferApplySummary__c 
                                               WHERE TransferApply__c = :taList[0].Id];
        
        PageReference ref = new PageReference('/apex/TransferShippmentReceived4?id=' + esList[0].id);
        Test.setCurrentPage(ref);
        TransferShippmentReceived4Controller controller = new TransferShippmentReceived4Controller();
        controller.init();
        
        System.assertNotEquals(0, controller.esdList.size());
        Test.stopTest();
    }
    
//     @isTest
//     static void test_init_CenterToAgency() {
//         Test.startTest();
//         setupTestData2();
        
//         List<TransferApply__c> taList = [SELECT Id,OwnerId,Status__c FROM TransferApply__c]; 
//         List<TransferApplySummary__c> esList = [SELECT TransferApply__r.Loaner_centre_mail_address__c, TransferApply__r.Name, 
//                                                TransferApply__c, Id, Name
//                                                FROM TransferApplySummary__c 
//                                                WHERE TransferApply__c = :taList[0].Id];
        
//         PageReference ref = new PageReference('/apex/TransferShippmentReceived4?id=' + esList[0].id);
//         Test.setCurrentPage(ref);
//         TransferShippmentReceived4Controller controller = new TransferShippmentReceived4Controller();
//         controller.init();
        
//         System.assertNotEquals(0, controller.esdList.size());
//         Test.stopTest();
//     }
    
    @isTest
    static void test_save() {
        Test.startTest();
        setupTestData1();
        
        List<TransferApply__c> taList = [SELECT Id,OwnerId,Status__c FROM TransferApply__c]; 
        List<TransferApplySummary__c> esList = [SELECT TransferApply__r.Loaner_centre_mail_address__c, TransferApply__r.Name, 
                                               TransferApply__c, Id, Name
                                               FROM TransferApplySummary__c 
                                               WHERE TransferApply__c = :taList[0].Id];
        
        PageReference ref = new PageReference('/apex/TransferShippmentReceived4?id=' + esList[0].id);
        Test.setCurrentPage(ref);
        TransferShippmentReceived4Controller controller = new TransferShippmentReceived4Controller();
        controller.init();
        
        for(TransferShippmentReceived4Controller.EsdInfo info : controller.esdList) {
            info.editable = true;
            info.rec.Check_lost_Item__c = '欠品';
            info.rec.Lost_item_check_staff__c = UserInfo.getUserId();
            info.rec.Lost_item_check_time__c = System.now();
        }
        
        controller.save();
        System.assertEquals(true, controller.done_flg);
        
        List<TransferApplyDetail__c> updatedList = [SELECT Id, Check_lost_Item__c, CDS_complete__c 
                                                   FROM TransferApplyDetail__c];
        for(TransferApplyDetail__c detail : updatedList) {
            System.assertEquals('欠品', detail.Check_lost_Item__c);
            System.assertEquals(false, detail.CDS_complete__c);
        }
        
        Test.stopTest();
    }
    
    @isTest
    static void test_init_error() {
        Test.startTest();
        PageReference ref = new PageReference('/apex/TransferShippmentReceived4?id=1234');
        Test.setCurrentPage(ref);
        TransferShippmentReceived4Controller controller = new TransferShippmentReceived4Controller();
        controller.init();
        System.assertEquals(true, controller.saveBtnDisabled);
        Test.stopTest();
    }
}