/**
 * 
 * CreateBy lt
 * CreateTime 2024-6-4
 * Reason 逻辑删除跳过了删除link的触发器，恢复不回去了，把删除link触发器里的内容写到batch里
 * 
 * 
 * 传link
 * 1.TenderOpportunityLinkHandler.updateOppotunityBeforeDelete:删除link时，任务取消，清空询价中标信息
 * updateOppotunityBeforeDelete(this.oldList);
 * 
 * 2.TenderOpportunityLinkHandler.updateOppotunityByDelete：
 * updateOppotunityByDelete(this.oldList)
 * 
 * 3.TenderOpportunityLinkHandler.updateTender(this.oldList)：更新招标信息
 * updateTender(this.oldList);
 * 
 * 删除link的触发器里更新：
 * 1.<招标项目>
 * 是否反应询价（后台用），询价数量，关联询价时间，询价关联科室
 * 
 * 2.<询价>
 * 项目:项目名，项目阶段(文本)，漏报数，项目:招标日，招标数(累计)，中标确认结果，中标任务创建日，中标结果确认日，
 * 失单任务确认日，是否按时确认，失单任务创建日
 */
global class TenderDeleteLinkBtach implements Database.Batchable<sObject> , Database.AllowsCallouts, Database.Stateful {

    private BatchIF_Log__c iflog;
    public List <String> linkstenList;
    public List <Tender_Opportunity_Link__c> addLinksList;

    public List<String> fintenlinks;  // 取link的招标项目
    public List<String> finoppIds;    //询价id
    public List<String> fintenIds;  
    public Set<Id> oppIdSet1; //20240726 lt add

    global TenderDeleteLinkBtach() {
    }

    global TenderDeleteLinkBtach(List<String> linkstenList) {
        this.linkstenList = linkstenList;
    }

    global TenderDeleteLinkBtach(List<String> linkstenList,List<Tender_Opportunity_Link__c> addLinksList) {
        this.linkstenList = linkstenList;
        this.addLinksList = addLinksList;
    }

    global Database.QueryLocator start(Database.BatchableContext bc) {
        system.debug('执行start');
        iflog = new BatchIF_Log__c();
        iflog.Type__c = 'PushNotification';
        iflog.Log__c  = 'TenderDeleteLinkBtach start\n';
        iflog.ErrorLog__c = '';
        insert iflog;
        

        String query = 'select Id,name,Tender_information__c,Opportunity__c '
                     + 'From Tender_Opportunity_Link__c '
                    ;
        if (linkstenList != null && linkstenList.size() > 0) {
            query += ' Where Tender_information__c IN: linkstenList ';
        }

        System.debug('20240604---1---query:'+query);

        return Database.getQueryLocator(query);
    }

    global void execute(Database.BatchableContext BC, list<Tender_Opportunity_Link__c> OldLinksList) {
        
        oppIdSet1 = new Set<Id>();//20240726 lt add

        if(linkstenList != null && linkstenList.size() > 0){
            //传入的参数为删除招标项目的id
            //删除招标项目 清空关联关系 清空以下字段
            List<Tender_information__c> UpdDelTenders = new List<Tender_information__c>(); 

            for (String TenderId: linkstenList){
                Tender_information__c delten =new Tender_information__c();
                delten.Id                          = TenderId;
                delten.IsReactionOpp__c            = true;     //是否反应询价（后台用）
                delten.OpportunityNum__c           = 0;        //询价数量
                delten.RelateOppTime__c            = null;     //关联询价时间
                delten.Strategic_department_Opp__c = '';       //询价关联科室
                //20240625 add
                delten.BidWinningNumber__c         = 0;        //中标数
                delten.NumberOfBids__c             = 0;        //应标数
                delten.Tender_Number__c            = 0;        //招标数
                delten.OlyNumberHosts__c           = 0;        //oly主机台数
                delten.RivalHostsNumber__c         = 0;        //对手主机台数
                delten.TotalNumberHosts__c         = 0;        //询价主机台数
                //20240625 add
                delten.Logical_delete__c           = true;     //逻辑删除  //20240702add
                UpdDelTenders.add(delten);
            }

            if(UpdDelTenders != null && UpdDelTenders.size() > 0){
                StaticParameter.EscapeTenderInformationUpdate =false;
                StaticParameter.EscapeOtherUpdateTenOwner = false;
                StaticParameter.EscapeOpportunityownerUpdate = true; //20240722 lt add
                update UpdDelTenders;
                StaticParameter.EscapeOpportunityownerUpdate = false; //20240722 lt add
                StaticParameter.EscapeOtherUpdateTenOwner = true;
                StaticParameter.EscapeTenderInformationUpdate =true;
            }

        }
        
        if(OldLinksList != null && OldLinksList.size() > 0){

            List<String> tenlinks = new List<String>();  // 取link的招标项目
            List<String> tenlink = new List<String>();   // 取当前删除的link

            Set<String> oppTens = new Set<String>();     //询价，招标项目 拼接
            List<String> oppIds = new List<String>();    //询价id

            List<Opportunity> UpdOppList = new List<Opportunity>();  //更新询价 - 统一list

            Set<Id> oppIdSet = new Set<Id>(); //计算漏单，传入参数set

            for (Tender_Opportunity_Link__c record: OldLinksList) {
                if(!tenlinks.contains(record.Tender_information__c)){
                    tenlinks.add(record.Tender_information__c);
                }  
                if(!tenlink.contains(record.id)){
                    tenlink.add(record.id);
                } 

                String oppid =String.valueOf(record.Opportunity__c);
                String tenid =String.valueOf(record.Tender_information__c);
                oppTens.add(oppid.subString(0,15)+tenid.subString(0,15));

                if (!oppIds.contains(record.Opportunity__c)) {
                    oppIds.add(record.Opportunity__c);
                }

                oppIdSet1.add(record.Opportunity__c); //20240726 lt add
            } 
              
            System.debug('20240604---2.1---tenlinks:'+tenlinks);
            System.debug('20240604---2.2---tenlink:'+tenlink);

            System.debug('20240604---6.1---oppTens:'+oppTens);
            System.debug('20240604---6.2---oppIds:'+oppIds);

            //询价是否终止的赋值batch
            if(tenlinks.size()>0 && oppIds.size() > 0){
                fintenlinks = tenlinks;
                finoppIds = oppIds;
            }

            //20240611 start 
            if (oppIds.size() > 0) {
                //招标项目插入时，是否需要发送询价任务 新的招标项目
                List<String> tenIds = new List<String>();
                List<Opportunity> opportunities1 = 
                    [SELECT id, Bidding_Project_Name_Bid__c,Tender_Number__c,Opp_Order__c,DirectLossFLG__c,
                            TenderBeginDate_New__c 
                     FROM Opportunity 
                     WHERE id in :oppIds];
                
                List<task__c> taskList = 
                    [SELECT id,taskStatus__c,RecordType.Name,Tender_information_Task__c,OpportunityId__c 
                     FROM task__c 
                     WHERE ((RecordType.Name ='失单报告任务' and OpportunityId__c in:oppIds) 
                            OR (RecordType.Name ='中标结果确认' and Opp_Tender__c in :oppTens)) 
                     AND taskStatus__c <> '03 完成'];

                System.debug('20240611---1---opportunities1:'+opportunities1);
                System.debug('20240604---1.2---taskList:'+taskList);

                for(task__c tsk : taskList){
                    if(tsk.RecordType.Name =='失单报告任务' && oppIds.contains(tsk.OpportunityId__c) && tsk.taskStatus__c !='03 完成'){
                        //不做操作
                    }else{
                        tsk.taskStatus__c = '04 取消';               //任务状态2
                        tsk.cancelDate__c = date.today();            //取消时间
                        tsk.cancelReasonSelect__c = '取消询价关联';  //取消理由(选项)
                    }
                    
                }
                update taskList;
                System.debug('20240604---7.2.1---taskList:'+taskList);

                if(taskList.size() > 0){
                    for (task__c tlink : taskList) {
                        for (Opportunity opp : opportunities1) {
                            if (opp.Id == tlink.OpportunityId__c) {
                                if(tlink.RecordType.Name =='失单报告任务' && tlink.taskStatus__c !='03 完成'){
                                    //不做操作
                                }else{
                                    opp.ConfirmationofAward__c = null;            //中标确认结果
                                    opp.Task_createTime__c = null;                //中标任务创建日
                                    opp.ConfirmationofAward_createTime__c =null;  //中标结果确认日
                                    opp.LostTask_comfirmTime__c =null;            //失单任务确认日
                                    opp.Is_ConfirmationofAward__c =null;          //是否按时确认
                                    opp.LostTask_createTime__c =null;             //失单任务创建日
                                }
                            }  
                        }
                    }
                }

                if (opportunities1.size() > 0) {
                    List<Tender_Opportunity_Link__c> links = 
                        [SELECT id, Opportunity__c, Tender_information__c, Tender_information__r.InfoType__c,
                                Tender_information__r.subInfoType__c,Tender_information__r.Tender_Order__c,
                                Tender_information__r.TenderBeginTime__c,Tender_information__r.TenderDate__c,
                                Opportunity__r.TenderBeginDate_New__c, Tender_information__r.Logical_delete__c 
                         FROM Tender_Opportunity_Link__c 
                         WHERE Opportunity__c in :oppIds 
                         AND Tender_information__r.Logical_delete__c = false //lt 20240625 add
                         ORDER BY Opportunity__c,Tender_information__r.Tender_Order__c DESC, 
                                  Tender_information__r.relativeTime_F__c DESC];
                    List<AggregateResult> sum_list = 
                        [SELECT count(id) cnt, Opportunity__c 
                        FROM Tender_Opportunity_Link__c 
                        WHERE Opportunity__c in :oppIds 
                        GROUP BY Opportunity__c];
                    System.debug('20240611---1.1---links:'+links);
                    System.debug('20240611---1.2---sum_list:'+sum_list);
                    Map<String, Integer> sum_map = new Map<String, Integer>();
                   
                    if (sum_list != null && sum_list.size() > 0) {
                        for (AggregateResult result : sum_list) {
                            sum_map.put(String.valueOf(result.get('Opportunity__c')), Integer.valueOf(result.get('cnt')));
                        }
                    }
                    System.debug('20240611---1.3---sum_map:'+sum_map);

                    //20240701 询价项目名赋值bug add start
                    Map<String, List<Tender_Opportunity_Link__c>> newlinks = new Map<String, List<Tender_Opportunity_Link__c>>();
                    for (Tender_Opportunity_Link__c link : links) {
                        if (!newlinks.keySet().contains(link.Opportunity__c)) {
                            newlinks.put(link.Opportunity__c, new List<Tender_Opportunity_Link__c>());
                        }
                        newlinks.get(link.Opportunity__c).add(link);
                        system.debug('20240702---newlinks:'+newlinks);
                    }
                    //20240701 询价项目名赋值bug add end

                    for (Opportunity opp : opportunities1) {
                        if (sum_map.get(opp.Id) == null || sum_map.get(opp.Id) == 0) {
                            opp.Bidding_Project_Name_Bid__c = null;  //项目:项目名
                            opp.InfoTypeBid_text__c = null;          //项目阶段（文本）
                            opp.LeakageNumber__c = null;             //漏报数
                            opp.TenderBeginDate_New__c = null;       //项目:招标日
                            System.debug('20240611---2.1---opp:'+opp);
                        } else {

                            //20240701 询价项目名赋值bug add start
                            Tender_Opportunity_Link__c link2 = newlinks != null && newlinks.size() > 0 && newlinks.keySet().contains(opp.Id) ? newlinks.get(opp.Id).get(0) : null;
                            system.debug('20240702---link2:'+link2);
                            opp.Bidding_Project_Name_Bid__c = link2 != null ? link2.Tender_information__c : null;
                            opp.InfoTypeBid_text__c = link2 != null ? link2.Tender_information__r.InfoType__c : null;
                            if(link2 != null && String.isNotBlank(link2.Tender_information__r.InfoType__c) && link2.Tender_information__r.InfoType__c=='3：结果' && String.isNotBlank(link2.Tender_information__r.subInfoType__c) && (link2.Tender_information__r.subInfoType__c=='3-5：中标通知' || link2.Tender_information__r.subInfoType__c=='3-6：合同公告')){
                                tenIds.add(link2.Opportunity__c); //符合条件生成任务
                            }
                            //20240701 询价项目名赋值bug add end

                            //20240701 询价项目名赋值bug 注释 start
                            // for (Tender_Opportunity_Link__c link : links) {
                            //     if (opp.Id == link.Opportunity__c) {
                            //         //lt 20240625 update ＞ 变 ＜
                            //         if(opp.Opp_Order__c <= link.Tender_information__r.Tender_Order__c){
                            //           opp.Bidding_Project_Name_Bid__c = link.Tender_information__c;  //项目:项目名
                            //           opp.InfoTypeBid_text__c = link.Tender_information__r.InfoType__c;  //项目阶段（文本）
                            //           if(String.isNotBlank(link.Tender_information__r.InfoType__c) && link.Tender_information__r.InfoType__c=='3：结果' && String.isNotBlank(link.Tender_information__r.subInfoType__c) && (link.Tender_information__r.subInfoType__c=='3-5：中标通知'  || link.Tender_information__r.subInfoType__c=='3-6：合同公告')){
                            //             tenIds.add(link.Opportunity__c); //符合条件生成任务
                            //           }
                            //           System.debug('20240611---2.2---opp:'+opp);
                            //           break;
                            //         }
                            //     }
                            // }
                            //20240701 询价项目名赋值bug 注释 end

                            //上一个for循环有可能被break掉，所以新循环一次
                            //删除link的时候计算漏单数
                            for (Tender_Opportunity_Link__c link : links){
                                oppIdSet.add(link.Opportunity__c);
                            }
                        }
                        System.debug('20240611---2.3---oppIdSet:'+oppIdSet);
                        if(oppIdSet.Size() > 0){
                            TenderUtil.UpdLeakageNum(oppIdSet);
                        }
    
                        Integer returncount =0;
                        for (Tender_Opportunity_Link__c link1 : links) {
                            if (opp.Id == link1.Opportunity__c) {
                                if(String.isNotBlank(link1.Tender_information__r.InfoType__c) && link1.Tender_information__r.InfoType__c!='1：预告' && String.isNotBlank(link1.Tender_information__r.subInfoType__c) && link1.Tender_information__r.subInfoType__c!='3-1：废标公告' && link1.Tender_information__r.subInfoType__c!='3-2：流标公告'){
                                    returncount += 1;
                                    System.debug('20240611---3.1---returncount:'+returncount);
                                  }
                                
                             }
                        } 
                        System.debug('20240611---3.2---returncount:'+returncount);
                        opp.Tender_Number__c= returncount;   //招标数(累计)

                    }
                    StaticParameter.EscapeOppandStaTrigger = true;
                    update opportunities1;
                    StaticParameter.EscapeOppandStaTrigger = false;
    
                }
              
                if (tenIds !=null && tenIds.size() > 0) {
                    fintenIds = tenIds;
                //   Database.executeBatch(new TenderResultConfirmTaskBatch(tenIds));
                }
            }
            //20240611 end

        }
    }

    global void finish(Database.BatchableContext BC) {

        //20240613 start
        //删除 删除项目 的 link
        List<Tender_Opportunity_Link__c> DelLinkList = 
                    [SELECT id,name,Tender_information__c,Opportunity__c 
                     FROM Tender_Opportunity_Link__c 
                     WHERE Tender_information__c IN: linkstenList];
        if(DelLinkList !=null && DelLinkList.size() > 0){
            StaticParameter.EscapeTOLinkTrigger = true;
            delete DelLinkList;
            StaticParameter.EscapeTOLinkTrigger = false;
        }
        //20240613 end

        //新增 保留项目 的 link
        if(addLinksList !=null && addLinksList.size()>0){
            insert addLinksList;
        }else{
            //20240726 lt start
            if(oppIdSet1 !=null && oppIdSet1.Size() > 0){
                TenderUtil.UpdLeakageNum(oppIdSet1);
            }
            //20240726 lt end

            if(fintenlinks !=null && finoppIds !=null && fintenlinks.size()>0 && finoppIds.size() > 0){
                System.debug('执行batch==='+fintenlinks);
                TenderinformatioBatch tBatch = new TenderinformatioBatch(finoppIds,fintenlinks);
                DataBase.executeBatch(tBatch);
            }
    
            if (fintenIds !=null && fintenIds.size() > 0) {
                Database.executeBatch(new TenderResultConfirmTaskBatch(fintenIds));
            }
        }

    }

}