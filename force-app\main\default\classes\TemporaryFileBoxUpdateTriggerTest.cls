/**
 * This class contains unit tests for validating the behavior of Apex classes
 * and triggers.
 *
 * Unit tests are class methods that verify whether a particular piece
 * of code is working properly. Unit test methods take no arguments,
 * commit no data to the database, and are flagged with the testMethod
 * keyword in the method definition.
 *
 * All test methods in an organization are executed whenever Apex code is deployed
 * to a production organization to confirm correctness, ensure code
 * coverage, and prevent regressions. All Apex classes are
 * required to have at least 75% code coverage in order to be deployed
 * to a production organization. In addition, all triggers must have some code coverage.
 * 
 * The @isTest class annotation indicates this class only contains test
 * methods. Classes defined with the @isTest annotation do not count against
 * the organization size limit for all Apex scripts.
 *
 * See the Apex Language Reference for more information about Testing and Code Coverage.
 */
@isTest
private class TemporaryFileBoxUpdateTriggerTest {
    // 提出時、添付がない場合エラーになる
    static testMethod void myUnitTest() {
        // recode type を取得
        List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        if (rectCo.size() == 0) {
            throw new ControllerUtil.myException('not found 病院 recodetype');
        }

        // insert
        Account company = new Account();
        company.RecordTypeId = rectCo[0].Id;
        company.Name = 'Katsu テスト';
        insert company;
        
        // 提案書
        TemporaryFileBox__c tfb = new TemporaryFileBox__c(
            Hospital__c = company.Id
        );
        insert tfb;

        ContentVersion cv = new ContentVersion();
        cv.VersionData = Blob.valueOf('Unit Test Attachment Body');
        cv.Title = 'Unit Test Attachment';
        cv.PathOnClient = 'Unit Test Attachment';
        insert cv;

        // Id docId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id].ContentDocumentId; 

        // ContentDocumentLink cdl = new ContentDocumentLink();
        // cdl.ContentDocumentId = docId;
        // cdl.LinkedEntityId = tfb.id;
        // cdl.ShareType = 'I';
        // cdl.visibility = 'AllUsers';
        // insert cdl;
        
        // 草案 に更新、エラーなし
        tfb.Status__c = '草案';
        update tfb;
        tfb = [Select Id, Status__c from TemporaryFileBox__c where id = :tfb.Id];
        System.assertEquals('草案', tfb.Status__c);

        // 已申请 に更新、エラー
        tfb.Status__c = '已申请';
        try {
            update tfb;
        } catch(Exception e) {
            System.assertEquals(true, e.getMessage().indexOf(System.Label.TemporaryFileBoxNeedAttachment) >= 0);
            tfb = [Select Id, Status__c from TemporaryFileBox__c where id = :tfb.Id];
            System.assertEquals('草案', tfb.Status__c);
        }

        // // 添付してから、已申请 に更新
        // Attachment attach = new Attachment(
        //     Name = 'Unit Test Attachment',
        //     body = Blob.valueOf('Unit Test Attachment Body'),
        //     parentId = tfb.id
        // );
        // insert attach;
        
        
        // tfb.Status__c = '已申请';
        // update tfb;
        // tfb = [Select Id, Status__c from TemporaryFileBox__c where id = :tfb.Id];
        // System.assertEquals('已申请', tfb.Status__c);
    }
}