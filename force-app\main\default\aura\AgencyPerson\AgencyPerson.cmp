<aura:component controller="AgencyPersonCmp"  implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,forceCommunity:availableForAllPageTypes,force:hasRecordId" access="global">
	<aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
	<!-- <aura:handler event="c:strike_evt" action="{!c.createAopp}" name="strike_evt_addNewRecord"/> -->
	<!-- <aura:attribute name="contacts" type="List"/> -->
	<aura:attribute name="allselectlist" type="Map"/>
	<aura:attribute name="selected_agency_person" type="String"/>
	<aura:attribute name="recordId" type="Id" />
         <div class="slds-grid slds-wrap">
         	<div class="tr mt5 slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--1-of-12 slds-large-size--1-of-12">
                销售担当
            </div>
        	<div class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--3-of-12 slds-large-size--3-of-12">
                <ui:inputSelect aura:id="select_agency_person" class="slds-select agency_person_select" change="{!c.select_agency_change}"/>
            </div>
            <div class="slds-p-horizontal--small slds-size--1-of-1 slds-medium-size--2-of-12 slds-large-size--2-of-12">
                <ui:button aura:id="save_button" label="保存" press="{!c.save_record}"/>
            </div>
       	</div>
 </aura:component>