<!-- <aura:component implements="flexipage:availableForAllPageTypes,force:lightningQuickActionWithoutHeader,force:hasRecordId,lightning:actionOverride,lightning:isUrlAddressable" access="global">  -->
<aura:component implements="lightning:actionOverride,lightning:isUrlAddressable,force:lightningQuickAction,force:hasRecordId" access="global"> 
    <aura:attribute name="recordId" type="String" />
    <!-- <aura:handler name="render" value="{!this}"  action="{!c.closeModal}" /> -->
    <!-- <aura:handler name="render" value="{!this}"  /> -->
    <aura:attribute name="isDoneRendering" type="Boolean" default="false"/>
    <div class="exampleHolder" style="display: none;">
        <c:lexUploadOBPM recordId="{!v.recordId}" onclosem="{!c.closeModal}"/>
    </div>
</aura:component>