@isTest
private class TenderUtilTest {

    @isTest 
	static void TestMethod1() {
        StaticParameter.EscapeOppandStaTrigger = true;
        Oly_TriggerHandler.bypass('TenderInformationHandler');
        Tender_information__c Ten = new Tender_information__c();
		Ten.Name = 'TenTest01';
        Ten.publicDate__c = Date.today();
        insert Ten;

        Tender_information__c Ten1 = new Tender_information__c();
		Ten1.Name = 'TenTest01';
        Ten1.publicDate__c = Date.today().addDays(-1);
        insert Ten1;

        List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 呼吸科'];
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 消化科'];
        List<RecordType> rectAgency = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '販売店'];

        Account company = new Account();
        company.RecordTypeId = rectCo[0].Id;
        company.Name         = 'NFM206TestCompany';
        insert company;
        Account section = new Account();
        section.RecordTypeId = rectSct[0].Id;
        section.Name         = '*';
        section.Department_Class_Label__c = '消化科';
        section.ParentId                  = company.Id;
        section.Hospital_Department_Class__c = company.Id;
        insert section;
        Account depart = new Account();
        depart.RecordTypeId = rectDpt[0].Id;
        depart.Name         = '*';
        depart.Department_Name__c  = 'NFM206TestDepart';
        depart.ParentId            = section.Id;
        depart.Department_Class__c = section.Id;
        depart.Hospital__c         = company.Id;
        insert depart;

        Account account2  = new Account();
        account2.Name = 'test1经销商';
        account2.RecordTypeId = rectAgency[0].id;
        insert account2;

        Opportunity opp = new Opportunity();
        opp.AccountId           = depart.Id;
        opp.Department_Class__c = section.Id;
        opp.Hospital__c         = company.Id;
        opp.SAP_Send_OK__c      = false;
        opp.Name                = 'Test20231117_1';
        opp.agency1__c 			=  account2.Id;
        opp.Trade__c            = '内貿';
        opp.StageName           = '出荷';
        opp.CloseDate           = Date.Today().addDays(3);
        opp.DirectLossFLG__c = true;
        insert opp;

        // Opportunity opp1 = new Opportunity();
        // opp1.AccountId           = depart.Id;
        // opp1.Department_Class__c = section.Id;
        // opp1.Hospital__c         = company.Id;
        // opp1.SAP_Send_OK__c      = false;
        // opp1.Name                = 'Test20231117_2';
        // opp1.agency1__c 			=  account2.Id;
        // opp1.Trade__c            = '内貿';
        // opp1.StageName           = '出荷';
        // opp1.CloseDate           = Date.Today().addDays(3);
        // opp1.DirectLossFLG__c = true;
        // insert opp1;

        Tender_Opportunity_Link__c link = new Tender_Opportunity_Link__c();
        link.Opportunity__c = opp.Id;
        link.CurrencyIsoCode = 'CNY';
        link.Tender_information__c = Ten.Id;
        insert link;

        Tender_Opportunity_Link__c link1 = new Tender_Opportunity_Link__c();
        link1.Opportunity__c = opp.Id;
        link1.CurrencyIsoCode = 'CNY';
        link1.Tender_information__c = Ten1.Id;
        insert link1;

        Set<Id> oppIdSet = new Set<Id>();
        oppIdSet.add(opp.Id);
        // oppIdSet.add(opp1.Id);
        TenderUtil.UpdLeakageNum(oppIdSet);
        StaticParameter.EscapeOppandStaTrigger = false;


    }

    // @isTest 
	// static void TestMethod2() {
    //     Oly_TriggerHandler.bypass('TenderInformationHandler');
    //     Tender_information__c Ten1 = new Tender_information__c();
	// 	Ten1.Name = 'TenTest01';
    //     Ten1.publicDate__c = Date.today().addDays(120);
    //     insert Ten1;

    //     List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
    //     List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 呼吸科'];
    //     List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 消化科'];
    //     List<RecordType> rectAgency = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '販売店'];

    //     Account company = new Account();
    //     company.RecordTypeId = rectCo[0].Id;
    //     company.Name         = 'NFM206TestCompany';
    //     insert company;
    //     Account section = new Account();
    //     section.RecordTypeId = rectSct[0].Id;
    //     section.Name         = '*';
    //     section.Department_Class_Label__c = '消化科';
    //     section.ParentId                  = company.Id;
    //     section.Hospital_Department_Class__c = company.Id;
    //     insert section;
    //     Account depart = new Account();
    //     depart.RecordTypeId = rectDpt[0].Id;
    //     depart.Name         = '*';
    //     depart.Department_Name__c  = 'NFM206TestDepart';
    //     depart.ParentId            = section.Id;
    //     depart.Department_Class__c = section.Id;
    //     depart.Hospital__c         = company.Id;
    //     insert depart;

    //     Account account2  = new Account();
    //     account2.Name = 'test1经销商';
    //     account2.RecordTypeId = rectAgency[0].id;
    //     insert account2;

    //     Opportunity opp1 = new Opportunity();
    //     opp1.AccountId           = depart.Id;
    //     opp1.Department_Class__c = section.Id;
    //     opp1.Hospital__c         = company.Id;
    //     opp1.SAP_Send_OK__c      = false;
    //     opp1.Name                = 'Test20231117_2';
    //     opp1.agency1__c 			=  account2.Id;
    //     opp1.Trade__c            = '内貿';
    //     opp1.StageName           = '出荷';
    //     opp1.CreatedDate         = Datetime.now();
    //     opp1.CloseDate           = Date.Today().addDays(3);
    //     insert opp1;

    //     Tender_Opportunity_Link__c link1 = new Tender_Opportunity_Link__c();
    //     link1.Opportunity__c = opp1.Id;
    //     link1.CurrencyIsoCode = 'CNY';
    //     link1.Tender_information__c = Ten1.Id;
    //     insert link1;

    //     Set<Id> oppIdSet = new Set<Id>();
    //     oppIdSet.add(opp1.Id);

    //     TenderUtil.UpdLeakageNum(oppIdSet);

    // }

}