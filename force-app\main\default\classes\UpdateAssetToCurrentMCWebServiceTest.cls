@isTest
private class UpdateAssetToCurrentMCWebServiceTest {
public static Account company ;
    public final Integer N_ASSET = 10; //納入機器数
    public final Integer N_CONTRACT = 5; //契約数
    
    static Asset createAsset() {
        List<RecordType> rectCo = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '病院'];
        if (rectCo.size() == 0) {
            return null;
        }
        List<RecordType> rectSct = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '戦略科室分類 呼吸科'];
        if (rectSct.size() == 0) {
            return null;
        }
        List<RecordType> rectDpt = [select Id from RecordType where IsActive = true and SobjectType = 'Account' and Name = '診療科 消化科'];
        if (rectDpt.size() == 0) {
            return null;
        }

        // テストデータ
        company = new Account();
        company.RecordTypeId = rectCo[0].Id;
        company.Name         = 'HistoryTestCompany';
        upsert company;
        Account section = new Account();
        section.RecordTypeId = rectSct[0].Id;
        section.Name         = '*';
        section.Department_Class_Label__c = '消化科';
        section.ParentId                  = company.Id;
        section.Hospital_Department_Class__c = company.Id;
        upsert section;
        Account depart = new Account();
        depart.RecordTypeId = rectDpt[0].Id;
        depart.Name         = '*';
        depart.Department_Name__c  = 'HistoryTestDepart';
        depart.ParentId            = section.Id;
        depart.Department_Class__c = section.Id;
        depart.Hospital__c         = company.Id;
        upsert depart;
        // 再取得
        List<Id> accountIds = new Id[] {company.Id, section.Id, depart.Id};
        // List<Account> accList = [select Management_Code__c, Management_Code_Auto__c, Name, Id from Account where Id In :accountIds order by Management_Code_Auto__c];
        List<Account> accList = [select Management_Code__c, Name, Id from Account where Id In :accountIds];

        List<Product2> prdList = new List<Product2>();
        Product2 prd1 = new Product2();
        prd1.ProductCode_Ext__c     = 'HistoryPrd1';
        prd1.ProductCode            = 'HistoryPrd1';
        prd1.Repair_Product_Code__c = 'HistoryPrd1_RP';
        prd1.Name                   = 'HistoryPrd1';
        prd1.Manual_Entry__c        = false;
        prdList.add(prd1);
        Product2 prd2 = new Product2();
        prd2.ProductCode_Ext__c     = 'HistoryPrd2';
        prd2.ProductCode            = 'HistoryPrd2';
        prd2.Repair_Product_Code__c = 'HistoryPrd2_RP';
        prd2.Name                   = 'HistoryPrd2';
        prd2.Manual_Entry__c        = false;
        prdList.add(prd2);
        insert prdList;

        Asset ast = new Asset();
        ast.Name                   = 'HistoryAst1';
        ast.AccountId              = depart.Id;
        ast.Department_Class__c    = section.Id;
        ast.Hospital__c            = company.Id;
        ast.Product2Id             = prd1.Id;
        ast.SerialNumber           = 'HistorySerialNumber';
        ast.Guarantee_period_for_products__c = Date.today();
        ast.InstallDate                      = Date.today();
        insert ast;
        // ast = [select Id, CurrencyIsoCode, Name, Product_Serial_No__c, AccountId, Department_Class__c, Department_Class__r.Management_Code_Auto__c, Hospital__c, Product2Id, Product2.ProductCode, Product2.Repair_Product_Code__c, SerialNumber 
        //          from Asset
        //         where Id = :ast.Id];
        ast = [select Id, CurrencyIsoCode, Name, Product_Serial_No__c, AccountId, Department_Class__c,  Hospital__c, Product2Id, Product2.ProductCode, Product2.Repair_Product_Code__c, SerialNumber 
        from Asset
       where Id = :ast.Id];

        return ast;
    }
    
    
    static Maintenance_Contract__c creatContract(Asset a, Date endDate, Date startDate, String mcNo ){
        Maintenance_Contract__c crt = new Maintenance_Contract__c();
        crt.Past_update_contract__c = true;
        crt.Name = startDate.format() + endDate.format();
        crt.Department__c = a.AccountId;
        crt.Department_Class__c = a.Department_Class__c;
        crt.Hospital__c = a.Hospital__c;
        crt.Sales_Estimate_Money__c = 3000;
        crt.Closing_Prediction_Day__c = Date.today();
        crt.CurrencyIsoCode = a.CurrencyIsoCode;
        crt.Status__c = '契約';
        crt.Maintenance_Contract_No__c = mcNo;
        crt.Contract_Conclusion_Date__c = Date.today();
        crt.Contract_End_Date__c = endDate;
        crt.Contract_Start_Date__c = startDate;
        crt.SalesOfficeCode_selection__c = '北京RC';
        insert crt;
        crt = [select Id, Contract_End_Date__c
                from Maintenance_Contract__c
                where Id = :crt.Id];
        return crt;
        
    }
    
    static Maintenance_Contract_Asset__c creatMca(Asset a, Maintenance_Contract__c crt){
        Maintenance_Contract_Asset__c newMca = new Maintenance_Contract_Asset__c();
        
        newMca.Asset__c = a.Id;
        newMca.Maintenance_Contract__c = crt.Id;
        insert newMca;
        newMca = [select Id, Asset__c, Maintenance_Contract__c
                  from Maintenance_Contract_Asset__c
                  where Id =: newMca.Id];
        return newMca;
    }
    
    
    static testMethod void myUnitTest01() {
        
        Asset testAst =createAsset();
        Date TDay = Date.today();
        Date YesDay = Date.today().addDays(-1);
        Maintenance_Contract__c testCrt = creatContract(testAst,TDay.addDays(30),TDay, 'crt');
        creatMca(testAst, testCrt);
        
        Test.StartTest();
        UpdateAssetToCurrentMCWebService.UpdateAssetToCurrentMC(testAst.Id);
        Test.StopTest();
    }
}