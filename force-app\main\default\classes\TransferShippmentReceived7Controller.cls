public without sharing class TransferShippmentReceived7Controller {
    public FixtureDeliverySlip__c slip { get; set; }
    public TransferApplySummary__c es { get; set; }
    public List<EsdInfo> esdList { get; set; }
    public Boolean saveBtnDisabled { get; private set; }
    public boolean ReturnRefuse {get;private set;}
    public String ErrorMessage {get; set;}
    public String CDSFinished {get;set;}
    public boolean LostFlg {get;set;}
    public String Step_status { get; private set; }
    public Boolean needCDS { get; private set; }
    public Boolean done_flg { get; set; }
    public String Raid {get;set;}
    public String ApplyId {get;set;}
    public String CheckedId {get;set;}
    public String UnCheckedId {get;set;}
    private String Id;
    // add QCD备品物流可视化 20240112 lc Start
    public Boolean saveOTSBtnDisabled { get; private set; }
    public boolean ots_flg {get;set;}
    public String distributor { get; set; }
    // add QCD备品物流可视化 20240112 lc End

    public Integer getEsdListSize() {
        return esdList.size();
    }

    public TransferShippmentReceived7Controller() {
        Id = ApexPages.currentPage().getParameters().get('id');
        if (Step_status == null) {
            Step_status = ApexPages.currentPage().getParameters().get('step');
        }
        ApplyId = Id;
    }

    public PageReference searchSlip() {
        Map<String,boolean> esdIdMap = new Map<String,boolean>();
        for (EsdInfo esd : esdList) {
            if (!esd.hasSended)
            esdIdMap.put(esd.rec.Id, esd.isChecked);
        }

        String qryString = 'select Combine_Pack__c, Name, Id, DeliveryCompany_SlipNo__c,DeliveryType__c,Distributor_method__c,DeliveryCompany__c,Wh_Staff__c '
                + ', num_of_order__c, shipping_man__c, shipping_tel__c, order_number__c '
                + 'from FixtureDeliverySlip__c '
                + 'where Name =\''+ slip.Name +'\' and DeliveryType__c = \'发货\'';
        if (String.isNotBlank(distributor)) {
            qryString += ' and Distributor_method__c = \''+ distributor +'\'';
        }
        if (String.isNotBlank(slip.DeliveryCompany__c)) {
            qryString += ' and DeliveryCompany__c = \''+ slip.DeliveryCompany__c +'\'';
        }
        List<FixtureDeliverySlip__c> slipList = Database.query(qryString);

        if (slipList.size() > 0) {
            if (slipList.size() > 1) {
                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '当前条件检索到复数条运输单，请追加检索条件!'));
                return null;
            }
            slip = slipList[0];

            List<TransferApplyDetail__c> eList = [select TransferApply__c, TransferApply__r.Shippment_ng_num__c, TransferApply__r.Pre_inspection_ng_num__c, SerialNumber_F__c, TransferApplySummary__r.Inspection_not_finish__c, Fixture_Name_F__c, TransferApplySummary__r.Fixture_Set__r.Name, TransferApplySummary__r.TransferApply__r.Name, TransferApplySummary__r.Name, Pre_inspection_time__c, StockDown__c, StockDown_time__c, Id, Name, Asset__c, Asset__r.Name, Asset__r.SerialNumber, Asset__r.Product_Serial_No__c,
                           TransferApply__r.Name, Asset__r.Remark__c, Asset__r.ImageAsset__c, Asset__r.ImageSerial__c, Asset__r.ImageAssetUploadedTime__c, Asset__r.ImageSerialUploadedTime__c,
                           Loaner_CDS_Info__c, Inspection_result__c, Check_lost_Item__c, Pre_disinfection__c, Water_leacage_check__c, Inspection_result_after__c, Arrival_in_wh__c,
                           Asset__r.Pre_Reserve_TAES_Detail__c, Asset__r.Pre_Reserve_TAES_Detail__r.After_Inspection_time__c,
                           Inspection_result_after_ng__c, Inspection_result_ng__c, Lost_item_giveup__c, CDS_complete__c, Loaner_accsessary__c
                      from TransferApplyDetail__c
                     where (DeliverySlip__c = :slip.Id or Id in :esdIdMap.keySet())
                       and Cancel_Select__c = False and Return_DeliverySlip__c = null
                     order by TransferApplySummary__r.Name, TransferApplySummary__c, Name ];

            esdList.clear();
            for (TransferApplyDetail__c esd : eList) {
                EsdInfo esdinfo;
                if (esdIdMap.containsKey(esd.Id)) {
                    esdinfo = new EsdInfo(esd, esdIdMap.get(esd.Id));
                } else {
                    esdinfo = new EsdInfo(esd, true);
                    esdInfo.hasSended = true;
                }
                esdList.add(esdInfo);
            }
        }
        return null;
    }

    public PageReference filljsQRAction() {
        List<String> ids = ApplyId.split(',');
        Set<String> checkedIds = new Set<String>(CheckedId.split(':'));
        CheckedId = null;
        List<TransferApplySummary__c> esList = [
            SELECT TransferApply__r.Name
                 , First_TAD_Model_No_F__c
                 , First_TAD__r.SerialNumber_F__c
                 , First_TAD__r.Loaner_asset_no__c
                 , TransferApply__c, Id
                 , TAS_Status__c, Name, Shippment_loaner_time2__c
              FROM TransferApplySummary__c
             WHERE (TransferApply__r.Name in :ids or Id in :ids)
               AND Cancel_Select__c = False
          ORDER BY TransferApply__r.Name, Id
        ];
        Set<Id> esIds = new Set<Id>();
        Set<Id> esdIds = new Set<Id>();
        Set<Id> applySet = new Set<Id>();
        for (TransferApplySummary__c raes : esList) {
            esIds.add(raes.Id);
            applySet.add(raes.TransferApply__c);
        }
        Raid = String.join(new List<Id>(applySet), ':');
        for (EsdInfo raesd : esdList) {
            esdIds.add(raesd.rec.Id);
        }
        List<TransferApplyDetail__c> eList = [
                SELECT TransferApply__c, TransferApply__r.Shippment_ng_num__c, TransferApply__r.Pre_inspection_ng_num__c, SerialNumber_F__c, TransferApplySummary__r.Inspection_not_finish__c, Fixture_Name_F__c, TransferApplySummary__r.Fixture_Set__r.Name, TransferApplySummary__r.TransferApply__r.Name, TransferApplySummary__r.Name, Pre_inspection_time__c, StockDown__c, StockDown_time__c, Id, Name, Asset__c, Asset__r.Name, Asset__r.SerialNumber, Asset__r.Product_Serial_No__c,
                       TransferApply__r.Name, Asset__r.Remark__c, Asset__r.ImageAsset__c, Asset__r.ImageSerial__c, Asset__r.ImageAssetUploadedTime__c, Asset__r.ImageSerialUploadedTime__c,
                       Loaner_CDS_Info__c, Inspection_result__c, Check_lost_Item__c, Pre_disinfection__c, Water_leacage_check__c, Inspection_result_after__c, Arrival_in_wh__c,
                       Asset__r.Pre_Reserve_TAES_Detail__c, Asset__r.Pre_Reserve_TAES_Detail__r.After_Inspection_time__c,
                       Inspection_result_after_ng__c, Inspection_result_ng__c, Lost_item_giveup__c, CDS_complete__c, Loaner_accsessary__c
                  FROM TransferApplyDetail__c
                 WHERE TransferApplySummary__c IN :esIds
                   AND DeliverySlip__c = null
                   AND Return_DeliverySlip__c = null
                   AND Cancel_Select__c = False
              ORDER BY TransferApplySummary__r.TransferApply__r.Name, TransferApplySummary__r.Name, TransferApplySummary__c, Name
        ];
        for (TransferApplyDetail__c esd : eList) {
            if (!esdIds.contains(esd.Id)) {
                esdList.add(new EsdInfo(esd, false));
            }
        }

        for (EsdInfo raesd : esdList) {
            if (checkedIds.contains(raesd.rec.TransferApplySummary__c)) {
                raesd.isChecked = true;
            }
        }
        return null;
    }

    // 画面初始化
    public PageReference init() {
        done_flg = false;
        ots_flg = false;
        es = new TransferApplySummary__c();
        slip = new FixtureDeliverySlip__c();
        esdList = new List<EsdInfo>();
        Set<Id> esIds = new Set<Id>();
        saveBtnDisabled = false;
        saveOTSBtnDisabled = false;
        ReturnRefuse = true;
        ErrorMessage = '';
        if (Id != null) {
            // 当前User
            String userid = Userinfo.getUserId();
            User user = [SELECT Id, Name FROM User WHERE Id = :userid];
            List<TransferApplySummary__c> esList;
            List<String> ids = Id.split(',');
            esList = [
                SELECT TransferApply__r.Name
                     , First_TAD_Model_No_F__c
                     , First_TAD__r.SerialNumber_F__c
                     , First_TAD__r.Loaner_asset_no__c
                     , TransferApply__c
                     , Id
                     , TAS_Status__c
                     , Name
                     , Shippment_loaner_time2__c
                     // add QCD备品物流可视化 20240116 lc Start
                     , TransferApply__r.OTSFHInfo__c
                     , TransferApply__r.From_Location__c
                     , TransferApply__r.Destination_location__c
                     , First_TAD__r.Is_Body__c
                     , TransferApply__r.DeliverySlip__c
                     // add QCD备品物流可视化 20240116 lc End
                  FROM TransferApplySummary__c
                 WHERE (TransferApply__r.Name IN :ids OR Id IN:ids)
                   AND Cancel_Select__c = False
              ORDER BY TransferApply__r.Name, Id];
            slip.Wh_Staff__c = userid;
            slip.DeliveryType__c = '发货';
            slip.IsForTransfer__c = true;

            Integer numOrder = 0;
            if (esList.size() > 0) {
                for (TransferApplySummary__c raes : esList) {
                    esIds.add(raes.Id);
                    Raid = raes.TransferApply__c;
                    //add QCD备品物流可视化 20241226 lc Start
                    // 包装件数默认申请单中的主体数量
                    if (raes.First_TAD__r.Is_Body__c) {
                        numOrder++;
                    }
                    //add QCD备品物流可视化 20241226 lc End
                }
            } else {
                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '备品不存在'));
                saveBtnDisabled = true;
                return null;
            }

            // add QCD备品物流可视化 20240116 lc Start
            // 判断北上广备品中心
            List<String> shippingList = new List<String>();
            switch on esList[0].TransferApply__r.From_Location__c {
                when '上海 备品中心' {
                    slip.DeliveryCompany__c = 'Fedex';
                    slip.Distributor_method__c = '优先达';
                    shippingList = System.label.SHShippingInformation.split(',');
                    // 发件人公司名称
                    slip.shipping_company__c = '奥林巴斯上海备品中心';
                }
                when '广州 备品中心' {
                    shippingList = System.label.GZShippingInformation.split(',');
                    slip.DeliveryCompany__c = '莱比特';
                    slip.Distributor_method__c = '空运';
                    // 发件人公司名称
                    slip.shipping_company__c = '奥林巴斯广州备品中心';
                }
                when else {
                    slip.DeliveryCompany__c = '利讯';
                    slip.Distributor_method__c = '陆运';
                    shippingList = System.label.BJShippingInformation.split(',');
                    // 发件人公司名称
                    slip.shipping_company__c = '奥林巴斯北京备品中心';
                }
            }
            System.debug('====================' + shippingList);
            List<String> receivingList = new List<String>();
            switch on esList[0].TransferApply__r.Destination_location__c {
                when '上海 备品中心' {
                    receivingList = System.label.SHShippingInformation.split(',');
                    slip.receiving_company__c = '奥林巴斯上海备品中心';
                }
                when '广州 备品中心' {
                    receivingList = System.label.GZShippingInformation.split(',');
                    slip.receiving_company__c = '奥林巴斯广州备品中心';
                }
                when else {
                    receivingList = System.label.BJShippingInformation.split(',');
                    slip.receiving_company__c = '奥林巴斯北京备品中心';
                }
            }
            System.debug('====================' + receivingList);
            if (esList[0].TransferApply__r.OTSFHInfo__c != null) {
                // 格式为OTS订单号；OTS物流单号；OTS物流公司；OTS物流方式；发件人；发件人电话；包装件数
                List<String> otsInfoList = esList[0].TransferApply__r.OTSFHInfo__c.split(';');
                slip.Name = otsInfoList[1];
                slip.DeliveryCompany__c = otsInfoList[2];
                slip.Distributor_method__c = otsInfoList[3];
                slip.shipping_man__c = otsInfoList[4];
                slip.shipping_tel__c = otsInfoList[5];
                if (String.isNotBlank(otsInfoList[6]) && otsInfoList[6] != 'null') {
                    slip.num_of_order__c = Decimal.valueOf(otsInfoList[6]);
                } else {
                    slip.num_of_order__c = 0;
                }
                slip.order_number__c = otsInfoList[0];
                // 发货省份
                slip.shipping_province__c = shippingList[3];
                // 发货城市
                slip.shipping_city__c = shippingList[4];
                // 发货地址
                slip.shipping_address__c = shippingList[2];
                // 收货人
                slip.receiving_man__c = receivingList[0];
                // 收货方联系方式
                slip.receiving_tel__c = receivingList[1];
                // 收货省份
                slip.receiving_province__c = receivingList[3];
                // 收货城市
                slip.receiving_city__c = receivingList[4];
                // 收货地址
                slip.receiving_address__c = receivingList[2];
            } else {
                slip.Name = null;
                // 发件人
                slip.shipping_man__c = shippingList[0];
                // 发件人电话
                slip.shipping_tel__c = shippingList[1];
                // 发货省份
                slip.shipping_province__c = shippingList[3];
                // 发货城市
                slip.shipping_city__c = shippingList[4];
                // 发货地址
                slip.shipping_address__c = shippingList[2];
                // 收货人
                slip.receiving_man__c = receivingList[0];
                // 收货方联系方式
                slip.receiving_tel__c = receivingList[1];
                // 收货省份
                slip.receiving_province__c = receivingList[3];
                // 收货城市
                slip.receiving_city__c = receivingList[4];
                // 收货地址
                slip.receiving_address__c = receivingList[2];
                // 包装件数
                slip.num_of_order__c = numOrder;
            }
            distributor = slip.Distributor_method__c;

            // OTS物流单号不为空，则已经进行过OTS下单，OTS下单按钮不可再次点击
            if (esList[0].TransferApply__r.OTSFHInfo__c != null || esList[0].TransferApply__r.DeliverySlip__c != null) {
                saveOTSBtnDisabled = true;
            }
            // add QCD备品物流可视化 20240116 lc End
        }

        // 备品set明细
        List<TransferApplyDetail__c> eList = [
                select TransferApply__c, TransferApply__r.Shippment_ng_num__c, TransferApply__r.Pre_inspection_ng_num__c, SerialNumber_F__c, TransferApplySummary__r.Inspection_not_finish__c, Fixture_Name_F__c, TransferApplySummary__r.TAS_Status__c, TransferApplySummary__r.Fixture_Set__r.Name, TransferApplySummary__r.TransferApply__r.Name, TransferApplySummary__r.Name, Pre_inspection_time__c, StockDown__c, StockDown_time__c, Id, Name, Asset__c, Asset__r.Name, Asset__r.SerialNumber, Asset__r.Product_Serial_No__c,
                       TransferApply__r.Name, Asset__r.Remark__c, Asset__r.ImageAsset__c, Asset__r.ImageSerial__c, Asset__r.ImageAssetUploadedTime__c, Asset__r.ImageSerialUploadedTime__c,
                       Loaner_CDS_Info__c, Inspection_result__c, Check_lost_Item__c, Pre_disinfection__c, Water_leacage_check__c, Inspection_result_after__c, Arrival_in_wh__c,
                       Asset__r.Pre_Reserve_TAES_Detail__c, Asset__r.Pre_Reserve_TAES_Detail__r.After_Inspection_time__c,
                       Inspection_result_after_ng__c, Inspection_result_ng__c, Lost_item_giveup__c, CDS_complete__c, Loaner_accsessary__c
                  from TransferApplyDetail__c
                 where TransferApplySummary__c in :esIds and DeliverySlip__c = null and Return_DeliverySlip__c = null//and Shipment_request_time__c <> null
                   and Cancel_Select__c = False
                 order by TransferApplySummary__r.TransferApply__r.Name, TransferApplySummary__r.Name, TransferApplySummary__c, Name
        ];

        needCDS = false;
        for (TransferApplyDetail__c esd : eList) {
            esdList.add(new EsdInfo(esd));
        }

        if (String.isBlank(Step_status)) {
            Step_status = '追加';
        }

        if (esdList.size() <= 0) {
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '没有备品set明细'));
            saveBtnDisabled = true;
            return null;
        }

        return null;
    }

    // add QCD备品物流可视化 20240115 lc Start
    //运输方式 SelectOption
    public List<SelectOption> getDistributorOps() {
        List<SelectOption> opList;
        List<SelectOption> retList = new List<SelectOption>();
        if (String.isBlank(slip.DeliveryCompany__c)) {
            opList = FixtureUtil.bieDistributorOpsMap.get('All');
        } else if (slip.DeliveryCompany__c == 'Fedex') {
            opList = FixtureUtil.bieDistributorOpsMap.get('Fedex');
        } else if (slip.DeliveryCompany__c == '其他') {
            opList = FixtureUtil.bieDistributorOpsMap.get('Other');
        } else {
            opList = FixtureUtil.bieDistributorOpsMap.get('Fast');
        }
        retList.add(new SelectOption('', '--无--'));
        for (SelectOption op : opList) {
            if (String.isNotBlank(op.getValue())) {
                retList.add(new SelectOption(op.getValue(),op.getLabel()));
            }
        }
        return retList;
    }

    public PageReference createOTSOrder() {
        if (esdList.size() <= 0) {
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '没有调拨明细'));
            saveOTSBtnDisabled = true;
            return null;
        }

        NFM711Controller.HJInfo hjInfo = new NFM711Controller.HJInfo();
        // 物流公司
        hjInfo.shipping_company = slip.DeliveryCompany__c;
        // 运输方式
        hjInfo.transport_way = distributor;
        // 发件人
        hjInfo.shipping_man = slip.shipping_man__c;
        // 发件人电话
        hjInfo.shipping_tel = slip.shipping_tel__c;
        // 发件人公司名称
        hjInfo.sender_company = slip.shipping_company__c;
        // 发件省
        hjInfo.shipping_province = slip.shipping_province__c;
        // 发件市
        hjInfo.shipping_city = slip.shipping_city__c;
        // 发件详细地址
        hjInfo.shipping_address = slip.shipping_address__c;
        // 收件人
        hjInfo.receiving_man = slip.receiving_man__c;
        // 收件人电话
        hjInfo.receiving_tel = slip.receiving_tel__c;
        // 收货公司 奥林巴斯北京/上海/广州备品中心
        hjInfo.receiving_company = slip.receiving_company__c;
        // 收货省
        hjInfo.receiving_province = slip.receiving_province__c;
        // 收货城市
        hjInfo.receiving_city = slip.receiving_city__c;
        // 收货地址
        hjInfo.receiving_address = slip.receiving_address__c;
        System.debug('================================' + hjInfo);
        System.debug('=========================' + slip);

        // OTS下单，call 711接口
        String retMsg = NFM711Controller.sendHJRequestNotFuture(esdList[0].rec.TransferApply__c,'TransferApply__c', hjInfo);
        if (retMsg.startsWith('OK')) {
            // 下单成功
            List<String> retList = retMsg.split(';');
            slip.Name = retList[2];
            slip.order_number__c = retList[1];
            saveOTSBtnDisabled = true;
            String otsHJInfo = retMsg.substring(3, retMsg.length());
            otsHJInfo += ';' + slip.num_of_order__c;
            try {
                TransferApply__c taObj = new TransferApply__c();
                taObj.Id = esdList[0].rec.TransferApply__c;
                taObj.OTSFHInfo__c = otsHJInfo;
                update taObj;
                ots_flg = true;
            } catch (Exception ex) {
                system.debug('=====' + ex.getMessage());
                ApexPages.addMessages(ex);
                ots_flg = false;
                // 邮件通知
                Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                OrgWideEmailAddress[] owea = [SELECT Id, Address, DisplayName FROM OrgWideEmailAddress where Address like '%it_sfdc%'];
                if ( owea.size() > 0 ) {
                    email.setOrgWideEmailAddressId(owea.get(0).Id);
                }
                List<String> emailAddresses = System.Label.OTSHJInfoEmail.split(';');
                email.setToAddresses (emailAddresses);
                email.setSubject('OTS下单发货物流信息需手动补充提醒');
                email.setPlainTextBody ( '调拨申请单：' + esdList[0].rec.TransferApply__r.Name + ',\n\n' + '以下信息需要手动更新到申请单' + otsHJInfo);
                Messaging.sendEmail ( new Messaging.SingleEmailMessage[] {email} ); 
                return null;
            }
        } else {
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, 'OTS下单失败'));
            return null;
        }
        return null;
    }
    // add QCD备品物流可视化 20240115 lc End

    // Step 切り替えボタン、发货前-检测
    public PageReference ShippmentDetail() {
        Step_status = '明细';

        if (!String.isBlank(CheckedId)) {
            Map<String, Set<String>> shipcount = new Map<String, Set<String>>();
            Map<String, Integer> allcount = new Map<String, Integer>();
            Set<String> checkedIds = new Set<String>(CheckedId.split(':'));
            for (EsdInfo esd : esdList) {
                boolean checked = checkedIds.contains(esd.rec.TransferApplySummary__c);
                esd.isChecked = checked;
                if (checked) {
                    Set<String> count = new Set<String>();
                    if (shipcount.containsKey(esd.rec.TransferApply__c)) {
                        count = shipcount.get(esd.rec.TransferApply__c);
                    }
                    count.add(esd.rec.TransferApplySummary__c);
                    shipcount.put(esd.rec.TransferApply__c, count);
                    allcount.put(esd.rec.TransferApply__c, Integer.valueOf(esd.rec.TransferApply__r.Shippment_ng_num__c));
                }
            }

            for (String raid : allcount.keySet()) {
                if (allcount.get(raid) > shipcount.get(raid).size()) {
                    Step_status = '追加';
                    //ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '申请单内存在未勾选的配套，请勾选全部配套或分割申请单!'));
                    ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '必须整单发货!'));
                }
            }
        }
        CheckedId = null;

        return null;
    }

    // Step 切り替えボタン、发货-发货运输单号 等
    public PageReference ShippmentAdd() {
        Step_status = '追加';
        return null;
    }

    public PageReference deleteDetail() {
        List<TransferApplyDetail__c> updDetails = new List<TransferApplyDetail__c>();
        for (EsdInfo esd : esdList) {
            if (esd.rec.TransferApplySummary__c == UnCheckedId) {
                if (esd.hasSended) {
                    esd.rec.DeliverySlip__c = null;
                    updDetails.add(esd.rec);
                } else {
                    esd.isChecked = false;
                }
            }
        }
        UnCheckedId = null;
        if (updDetails.size() > 0) {
            update updDetails;
            searchSlip();
        }

        return null;
    }

    // 保存按钮
    public PageReference save() {

        List<String> raids = Raid.split(':');
        slip.Distributor_method__c = distributor;
        String raNameTemp = '';
        //检查是否可以继续
        List<TransferApply__c> RaTarList = [select Campaign__c,Repair__c, Name,
                                                Campaign__r.Status,repair__r.Return_Without_Repair_Date__c,Repair__r.Repair_Final_Inspection_Date__c,Repair__r.Repair_Shipped_Date__c
                                            from TransferApply__c
                                            where id in :raids];
        for (TransferApply__c RaTar : RaTarList) {
            if( RaTar.Campaign__r.Status == '取消'){

                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '学会已取消，不能继续操作了'));
                return null;
            }else if(RaTar.Repair__r.Repair_Final_Inspection_Date__c!=null){

                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '存在修理最终检测日，不能继续了'));
                return null;
            }else if(RaTar.repair__r.Return_Without_Repair_Date__c!=null&&RaTar.repair__c!=null ){

                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '未修理归还日不为空，不能出库'));
                return null;
            }else if(RaTar.Repair__r.Repair_Shipped_Date__c!=null){

                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '存在RC修理返送日，不能继续了'));
                return null;
            }

            raNameTemp += RaTar.Name + ';';
        }

        //检查是否可以继续
        String userid = Userinfo.getUserId();
        Set<Id> eSet = new Set<Id>();
        List<TransferApplyDetail__c> eList = new List<TransferApplyDetail__c>();
        //申请书set
        Set<String> raSet = new Set<String>();
        for (EsdInfo esdInfo : esdList) {
            if (esdInfo.isChecked && !esdInfo.hasSended) {
                TransferApplyDetail__c esd = esdInfo.rec;
                eSet.add(esd.Id);
                raSet.add(esd.TransferApply__c);
            }
            else if(!esdInfo.isChecked && !esdInfo.hasSended) {
                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '必须整单发货'));
                return null;
            }
        }
        if (eSet.size() == 0) {
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '未选择备品set，不能创建发货单'));
            return null;
        }
        eList = [select TransferApply__c, TransferApply__r.Shippment_ng_num__c, TransferApply__r.Pre_inspection_ng_num__c, TransferApplySummary__r.Inspection_not_finish__c, Fixture_Name_F__c, TransferApplySummary__r.Fixture_Set__r.Name, TransferApplySummary__r.TransferApply__r.Name, TransferApplySummary__r.Name, Pre_inspection_time__c, StockDown__c, StockDown_time__c, Id, Name, Asset__c, Asset__r.Name, Asset__r.SerialNumber, Asset__r.Product_Serial_No__c,
                           TransferApply__r.Name, Asset__r.Remark__c, Asset__r.ImageAsset__c, Asset__r.ImageSerial__c, Asset__r.ImageAssetUploadedTime__c, Asset__r.ImageSerialUploadedTime__c,
                           Loaner_CDS_Info__c, Inspection_result__c, Check_lost_Item__c, Pre_disinfection__c, Water_leacage_check__c, Inspection_result_after__c, Arrival_in_wh__c,
                           Asset__r.Pre_Reserve_TAES_Detail__c, Asset__r.Pre_Reserve_TAES_Detail__r.After_Inspection_time__c,
                           Inspection_result_after_ng__c, Inspection_result_ng__c, Lost_item_giveup__c, CDS_complete__c, Loaner_accsessary__c, TransferApply__r.OTSFHInfo__c
                    from TransferApplyDetail__c where Id in :eSet for update];
        Boolean needSaveSet = false;
        Boolean needSaveDetail = false;
        Boolean needDeliverySlip = false;
        Boolean needClearSlipId = false;

        Set<Id> astForLock = new Set<Id>();
        for (TransferApplyDetail__c esd : eList) {
            if (esd.Inspection_result__c <> null && esd.StockDown__c == false) {
                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '备品set未下架，不能填写发货前检查结果'));
                return null;
            }
            astForLock.add(esd.Asset__c);
        }
        List<Asset> astLock = [select Id
                 from Asset
                where id in :astForLock
                  for update];

        if (Step_status == '明细') {
            needDeliverySlip = true;
            needClearSlipId = true;

            // 20230928 ljh Start
            if(String.isBlank(slip.Name)
                || String.isBlank(slip.DeliveryCompany__c)
                || String.isBlank(slip.Distributor_method__c)
                || String.isBlank(slip.Wh_Staff__c)) {
                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '发货物流信息必填！'));
                return null;
            }
            // 20230928 ljh Start

            if (slip.Id != null) {
                needClearSlipId = false;
                slip = [select Combine_Pack__c, Name, Id, DeliveryCompany_SlipNo__c,DeliveryType__c,Distributor_method__c,DeliveryCompany__c,Wh_Staff__c,num_of_order__c, shipping_man__c, shipping_tel__c, order_number__c, Apply_No__c
                        from FixtureDeliverySlip__c
                        where Id =:slip.Id for update];

                if (String.isNotEmpty(slip.Apply_No__c)) {
                    List<String> applyNoList = raNameTemp.split(';');
                    for (String applyNoTemp : applyNoList) {
                        if (!slip.Apply_No__c.contains(applyNoTemp)) {
                            slip.Apply_No__c += ';' + applyNoTemp;
                        }
                    }
                } else {
                    slip.Apply_No__c = raNameTemp.substring(0, raNameTemp.length()-1);
                }
            } else {
                slip.Apply_No__c = raNameTemp.substring(0, raNameTemp.length()-1);

                // 进行OTS补单前，先判断申请单是否存在&正常保存，防止不必要的OTS补单
                List<FixtureDeliverySlip__c> existSlip = [
                        select Name, Id, Distributor_method__c, DeliveryCompany__c 
                        from FixtureDeliverySlip__c 
                        where Name =:slip.Name
                          and DeliveryType__c =:slip.DeliveryType__c
                          and DeliveryCompany__c =:slip.DeliveryCompany__c
                        Order by CreatedDate desc];
                if (existSlip != null && existSlip.size() > 0) {
                    String errorMsg = '系统已存在物流公司:' + existSlip[0].DeliveryCompany__c + '、运输方式:' + existSlip[0].Distributor_method__c + '的相同运输单。如果继续使用该运输单发货，请输入相应信息进行检索，检索后再勾选配套发货。';
                    ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, errorMsg));
                    return null;
                }
            }
            slip.Shippment_loaner_time__c = Datetime.now();
            needSaveDetail = true;

            // add QCD备品物流可视化 20240115 lc Start
            // 若回寄-物流公司是其他时，无需调用OTS物流信息补充或更新接口
            if (slip.Id == null && slip.DeliveryCompany__c != '其他' && esdList.size() > 0) {
                Boolean isNeedNFM713 = false;
                String slipNameTemp = null;
                // 如果为空，则代表没经过OTS下单，则需要调用OTS物流信息补充或更新接口
                if (eList[0].TransferApply__r.OTSFHInfo__c != null) {
                    // 格式为OTS订单号；OTS物流单号；OTS物流公司；OTS物流方式；发件人；发件人电话；包装件数
                    List<String> otsInfoList = eList[0].TransferApply__r.OTSFHInfo__c.split(';');
                    slipNameTemp = otsInfoList[1];
                    // 包装件数
                    Decimal numOfOrder = null;
                    if (String.isNotBlank(otsInfoList[6]) && otsInfoList[6] != 'null') {
                        numOfOrder = Decimal.valueOf(otsInfoList[6]);
                    }
                    // 判断回寄-运输单号，回寄-运输方式、回寄-物流公司、取件时间、发件人、发件人电话、包装件数改变
                    if (otsInfoList[1] != slip.Name
                        || otsInfoList[3] != slip.Distributor_method__c
                        || otsInfoList[2] != slip.DeliveryCompany__c
                        || otsInfoList[4] != slip.shipping_man__c
                        || otsInfoList[5] != slip.shipping_tel__c
                        || numOfOrder != slip.num_of_order__c) {
                        isNeedNFM713 = true;
                    } else {
                        isNeedNFM713 = false;
                    }
                } else {
                    isNeedNFM713 = true;
                }

                // 判断回寄-运输单号，回寄-运输方式、回寄-物流公司、取件时间、发件人、发件人电话、发件省、发件市或发件详细地址改变
                if (isNeedNFM713) {
                    NFM713Controller.TransInfo ti = new NFM713Controller.TransInfo();
                    if (slipNameTemp != slip.Name) {
                        // 运单号
                        ti.waybill_number = slip.Name;
                        // 订单号
                        ti.order_number = FixtureUtil.createOrderNumber(eList[0].TransferApply__r.Name, 'CJ');
                        slip.order_number__c = ti.order_number;
                    } else {
                        // 运单号
                        ti.waybill_number = slip.Name;
                        // 订单号
                        ti.order_number = slip.order_number__c;
                    }
                    
                    // 20250405 add by lc start
                    // 运单号和订单号在BatchIF日志中已经存在的情况下，直接使用已存在的订单号
                    List<BatchIF_Log__c> batchList = [SELECT Id, order_number__c FROM BatchIF_Log__c WHERE Type__c = 'NFM713' AND order_number__c != null AND waybill_number__c =: ti.waybill_number ORDER BY CreatedDate DESC];
                    if (batchList != null && batchList.size() > 0) {
                        // 订单号
                        ti.order_number = batchList[0].order_number__c;
                        slip.order_number__c = ti.order_number;
                    }
                    // 20250405 add by lc end

                    // 发货公司
                    ti.shipping_company = slip.DeliveryCompany__c;
                    // 发货省
                    ti.shipping_province = slip.shipping_province__c;
                    // 发货城市
                    ti.shipping_city = slip.shipping_city__c;
                    // 发货详细地址
                    ti.shipping_address = slip.shipping_address__c;
                    // 发货人
                    ti.shipping_man = slip.shipping_man__c;
                    // 发货人电话
                    ti.shipping_tel = slip.shipping_tel__c;
                    // 收货公司/医院
                    ti.receiving_company = slip.receiving_company__c;
                    // 收货省
                    ti.receiving_province = slip.receiving_province__c;
                    // 收货城市
                    ti.receiving_city = slip.receiving_city__c;
                    // 收货地址
                    ti.receiving_address = slip.receiving_address__c;
                    // 收货人
                    ti.receiving_man = slip.receiving_man__c;
                    // 收货人电话
                    ti.receiving_tel = slip.receiving_tel__c;
                    // 运输方式
                    ti.transport_way = slip.Distributor_method__c;
                    // 承运商编号，请传递中文名例如慧通、莱比特、利讯；联邦暂时不传值
                    if (slip.DeliveryCompany__c == 'Fedex') {
                        ti.carrier = '联邦';
                    } else if (slip.DeliveryCompany__c == '慧通速递') {
                        ti.carrier = '慧通';
                    } else {
                        ti.carrier = slip.DeliveryCompany__c;
                    }
                    // 包装件数
                    ti.num_of_order = 0;
                    System.debug('=========================' + slip);
                    System.debug('=========================' + ti);
                    String retMsg = NFM713Controller.sendRequestNotFuture(ti);
                    if (retMsg != 'OK') {
                        ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '物流信息补充或更新失败，发货不成功'));
                        return null;
                    }
                }
            }

            // 发货-物流公司是其他时，也在现在保存逻辑的基础上为下述发货物流跟踪中行110的字段【发货物流停止跟踪原因】赋值为其他物流
            if (slip.DeliveryCompany__c == '其他') {
                slip.shipping_track_stop_reason__c = '其他物流';
            }
            // add QCD备品物流可视化 20240115 lc End
        }
        List<TransferApply__c> raList = [SELECT Id, DeliverySlip__c FROM TransferApply__c WHERE Id IN :raSet];
        Savepoint sp = Database.setSavepoint();
        try {
            // 调拨发货优化追加 20230706 lc Start
            TransferApplyDetailHandler.skipUpdateAgain = true;
            // 调拨发货优化追加 20230706 lc End

            if (needDeliverySlip) {
                FixtureUtil.withoutUpsertObjects(new List<FixtureDeliverySlip__c>{ slip });
                for (TransferApplyDetail__c esd : eList) {
                    esd.DeliverySlip__c = slip.Id;
                }

                for (TransferApply__c ra : raList) {
                    ra.DeliverySlip__c = slip.Id;
                }
            }
            if (needSaveDetail) {
                FixtureUtil.withoutUpsertObjects(eList);
                FixtureUtil.withoutUpsertObjects(raList);
            }

            done_flg = true;
            return null;
        } catch (Exception ex) {
            String msg = ex.getMessage();
            if (msg != null && msg.contains('DUPLICATE_VALUE') && msg.contains('DeliveryCompany_SlipNo__c')) {
                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, '系统已存在相同的运输单。如果继续使用该运输单发货，请输入运输单号检索，检索后再勾选配套发货。'));
            } else {
                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.Error, msg));
            }
            Database.rollback(sp);
            done_flg = false;
            if (needClearSlipId) {
                slip.Id = null;
            }
            return null;
        }

        // return null;
    }

    public class EsdInfo {
        public TransferApplyDetail__c rec { get; set; }
        public String imageAssetUploadedTime { get; set; }
        public String imageSerialUploadedTime { get; set; }
        // 回库はSet単位で、明細一つ一つのquickCheck要らない
        public boolean quickCheck { get; set; }
        public boolean isChecked { get; set; }
        public boolean hasSended { get; set; }
        public Boolean inspectionFinished {get;set;}
        public String fsName {get;set;}

        public EsdInfo(TransferApplyDetail__c rec, boolean checked) {
            this.rec = rec;
            this.isChecked = checked;
            this.hasSended = false;
            this.inspectionFinished = rec.TransferApplySummary__r.Inspection_not_finish__c == 0;
            this.fsName = rec.TransferApplySummary__r.Name;
            if(String.isNotBlank(rec.TransferApplySummary__r.Fixture_Set__r.Name)){
                this.fsName += ':' + rec.TransferApplySummary__r.Fixture_Set__r.Name;
            }
            if (rec.Asset__r.Pre_Reserve_TAES_Detail__c != null && rec.Asset__r.Pre_Reserve_TAES_Detail__r.After_Inspection_time__c != null) {
                this.quickCheck = Datetime.now() < rec.Asset__r.Pre_Reserve_TAES_Detail__r.After_Inspection_time__c + 30 ? true : false;
            } else {
                this.quickCheck = false;
            }

            if (rec.Asset__r.ImageAssetUploadedTime__c != null) {
                imageAssetUploadedTime = rec.Asset__r.ImageAssetUploadedTime__c.format('yyyy/MM/dd HH:mm');
            }
            if (rec.Asset__r.ImageSerialUploadedTime__c != null) {
                imageSerialUploadedTime = rec.Asset__r.ImageSerialUploadedTime__c.format('yyyy/MM/dd HH:mm');
            }
        }

        public EsdInfo(TransferApplyDetail__c rec) {
            this(rec, false);
        }
    }
    public static void testMock1(){
        Integer i = 0;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
         i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
         i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
        i++;
    }
}