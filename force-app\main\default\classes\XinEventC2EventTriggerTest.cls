@isTest
public class XinEventC2EventTriggerTest {
    
    //before insert, before update
    public static testMethod void myUnitTest() {
        Date now = Date.today();
        
        Daily_Report__c dr1 = new Daily_Report__c(
            Reported_Date__c = Date.today(),
            Status__c = '作成中',
            Reporter__c = Userinfo.getUserId()
        );
        insert dr1;
        
        Event__c ec1 = new Event__c(
            Daily_Report__c = dr1.Id,
            StartDateTime__c = Datetime.newInstance(now.year(), now.month(), now.day(), 10, 0, 0),
            EndDateTime__c = Datetime.newInstance(now.year(), now.month(), now.day(), 11, 0, 0),
            SyncCreatedDate__c = now.adddays(-1),
            ActivityDate__c = now,
            Subject__c = 'event1'
        );
        insert ec1;
        
        List<Event__c> eventList1 = [select id,IsScheduled__c,IsScheduled_StartDateTime__c,IsScheduled_EndDateTime__c,IsScheduled_Location__c,IsScheduled_Subject__c from Event__c where id = :ec1.Id];
        System.assertEquals(true, eventList1[0].IsScheduled__c);
        System.assertEquals(Datetime.newInstance(now.year(), now.month(), now.day(), 10, 0, 0), eventList1[0].IsScheduled_StartDateTime__c);
        System.assertEquals(Datetime.newInstance(now.year(), now.month(), now.day(), 11, 0, 0), eventList1[0].IsScheduled_EndDateTime__c);
        System.assertEquals('event1', eventList1[0].IsScheduled_Subject__c);
        
        ec1.IsScheduled__c = false;
        update ec1;
        
        List<Event__c> eventList2 = [select id,IsScheduled__c,IsScheduled_StartDateTime__c,IsScheduled_EndDateTime__c,IsScheduled_Location__c,IsScheduled_Subject__c from Event__c where id = :ec1.Id];
        System.assertEquals(null, eventList2[0].IsScheduled_StartDateTime__c);
        System.assertEquals(null, eventList2[0].IsScheduled_EndDateTime__c);
        System.assertEquals(null, eventList2[0].IsScheduled_Subject__c);
    }
    
    //after delete, after insert, after update
    public static testMethod void myUnitTest2() {
        Date now = Date.today();
        Date yesterdayDate = now.adddays(-1);
        
        Daily_Report__c dr1 = new Daily_Report__c(
            Reported_Date__c = Date.today(),
            Status__c = '作成中',
            Reporter__c = Userinfo.getUserId()
        );
        insert dr1;
        
        Event e = new Event(
            StartDateTime = Datetime.newInstance(yesterdayDate.year(), yesterdayDate.month(), yesterdayDate.day(), 9, 0, 0),
            EndDateTime = Datetime.newInstance(yesterdayDate.year(), yesterdayDate.month(), yesterdayDate.day(), 10, 0, 0)
        );
        insert e;
        
        Event__c ec1 = new Event__c(
            Daily_Report__c = dr1.Id,
            StartDateTime__c = Datetime.newInstance(now.year(), now.month(), now.day(), 10, 0, 0),
            EndDateTime__c = Datetime.newInstance(now.year(), now.month(), now.day(), 11, 0, 0),
            SyncCreatedDate__c = yesterdayDate,
            ActivityDate__c = now,
            Subject__c = 'event1',
            nextPlanDate__c = now,
            nextPlanTimePurpose__c = '10,10,12,10,test123,123',
            Event_ID__c = e.Id
        );
        insert ec1;
        
        List<Event> eList = [select id,Subject from Event where NextEventC_ID__c = :ec1.id];
        System.assertEquals('test123,123', eList[0].Subject);
        
        ec1.nextPlanTimePurpose__c = '10,10,12,10,test333,444';
        update ec1;
        
        List<Event> eList1 = [select id,Subject from Event where NextEventC_ID__c = :ec1.id];
        System.assertEquals('test333,444', eList1[0].Subject);
        
        delete ec1;
        
        List<Event> eList2 = [select id,Subject from Event where id = :e.id];
        System.assertEquals(0, eList2.size());
    }
}